import type { NextConfig } from "next";
import UnoCSS from '@unocss/webpack'

let BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;
let rewriteObject: { source: string; destination: string }[] = [
  {
    source: '/api/:path*',
    destination: `${BASE_URL}/api/v1/:path*`
  },
  {
    source: '/:lang/web/:path*',
    destination: `${BASE_URL}/web/:path*`,
  },
];
const BACKEND_TYPE = process.env.NEXT_PUBLIC_BACKEND_TYPE

if (BACKEND_TYPE == '6') {
  BASE_URL = 'https://api.v6.daigouxt.com';
  rewriteObject = [
    {
      source: '/api/v1.0/:path*',
      destination: `${BASE_URL}/api/v1.0/:path*`
    }
  ];
  console.log('🔧 Next.js Config: BACKEND_TYPE=6, rewrite rules configured for:', BASE_URL);
}
const nextConfig: NextConfig = {
  // 禁用 React 严格模式
  reactStrictMode: false,
  // 生产环境禁用 sourcemap
  productionBrowserSourceMaps: false,
  // 跳过类型检查
  typescript: {
    ignoreBuildErrors: true,
  },
  // 跳过 ESLint
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: '**',
      }
    ],
  },
  webpack: (config, { dev, isServer }) => {
    if (dev) {
      config.cache = {
        type: 'filesystem',
        buildDependencies: {
          config: [__filename]
        }
      }
    }
    config.plugins.push(
      UnoCSS()
    )
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': require('path').resolve(__dirname, 'src'),
    }
    return config
  },
  transpilePackages: ['@douyinfe/semi-ui', '@douyinfe/semi-icons', '@douyinfe/semi-illustrations'],
  async rewrites() {
    return rewriteObject;
  }
};

export default nextConfig;
