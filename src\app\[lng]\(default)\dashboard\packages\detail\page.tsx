'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useParams, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Api } from '@/request/api';
import Loading from '@/components/Loading';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import { Button, Steps, Descriptions, Tag } from 'antd';
import type { Locale } from '@/config';
import { fixedUrl } from '@/utils';
// 定义订单详情数据类型
interface OrderDetailData {
    id: number;
    orders_id: number;
    ordersn: string;
    user_id: number;
    coupon_id: number;
    recharge_id: number;
    goodsseller: string;
    sellerurl: string;
    amountsend: string;
    oldamountsend: string;
    changeremark: string | null;
    buysendprice: string;
    type: number;
    remark: string | null;
    status_id: number;
    isonepay: number;
    isdomestic: number;
    currencycode: string;
    currencyvalue: Record<string, any>;
    order_address_id: number;
    did: number;
    ip: string;
    clienttype: string;
    createtime: string;
    updatetime: string;
    deletetime: string | null;
    overtime: number;
    collage_id: string | null;
    goods: Array<{
        id: number;
        order_id: number;
        mall_goods_id: number;
        flash_sale_id: number;
        user_id: number;
        payer: string | null;
        sendorder_id: number;
        goodssn: string;
        goodsurl: string;
        goodsname: string;
        cn_goodsname: string | null;
        goodsweight: number;
        goodsvolume: number;
        goodsprice: string;
        oldgoodsprice: string;
        sendprice: string;
        buyprice: string;
        serverfee: number;
        serverdiscount: number;
        sellerorderno: string;
        goodsnum: number;
        goodsimg: string;
        goodsseller: string;
        sellerurl: string;
        goodssite: string;
        goodsremark: string;
        goodstype: string;
        status_id: number;
        isproblem: number;
        isrefill: number;
        ip: string;
        clienttype: string;
        sku_id: string | null;
        skuname: string | null;
        cn_skuname: string | null;
        spare_sku_id: string | null;
        spare_sku_name: string | null;
        cn_spare_sku_name: string | null;
        currencycode: string;
        currencyvalue: Record<string, any>;
        changepriceremark: string | null;
        refusalreason: string;
        denialreason: string;
        createtime: string;
        updatetime: string;
        deletetime: string | null;
        overtime: number;
        payment_url: string;
        cid: number;
        more: any | null;
        servicemoney: number;
        totalgoodsmoney: string;
        totalmoney: string;
        express: string;
        expressname: string;
        expresscode: string;
        warehouse: string;
        expectedtime: string;
        has_photo_service: boolean;
        user_not_read: number;
        typename: any[];
        service: any[];
        status_text: string;
    }>;
    totalgoodsmoney: number;
    totalserverfee: number;
    totalservicemoney: number;
    totalmoney: string;
    timeout_time: number;
    timeout_switch: number;
    recharge: any | null;
    status_text: string;
}

// 订单状态映射
const orderStatusMap: Record<number, { text: string; color: string }> = {
    10: { text: ' 待付款 ', color: 'orange' },
    20: { text: ' 已付款 ', color: 'green' },
    30: { text: ' 处理中 ', color: 'blue' },
    40: { text: ' 已邮寄 ', color: 'purple' },
    50: { text: ' 已完成 ', color: 'green' },
    60: { text: ' 已取消 ', color: 'red' },
};

// 订单流程步骤
const orderSteps = [
    { title: ' 下订单 ' },
    { title: ' 付款 ' },
    { title: ' 仓库质检 ' },
    { title: ' 邮寄 ' },
    { title: ' 签收 ' },
];

export default function OrderDetailPage() {
    const params = useParams();
    const searchParams = useSearchParams();
    const orderId = searchParams.get('orders_id');
    const lng = params.lng as Locale || 'zh-cn';

    const [orderDetail, setOrderDetail] = useState<OrderDetailData | null>(null);
    const [loading, setLoading] = useState(true);

    // 获取订单详情
    useEffect(() => {
        if (!orderId) return;

        const fetchOrderDetail = async () => {

            setLoading(true);
            const response = await Api.getOrderDetail({
                order_id: Number(orderId),
            });

            setOrderDetail(response.data);
            setLoading(false);

        };

        fetchOrderDetail();
    }, [orderId, lng]);

    // 获取当前订单状态对应的步骤
    const getCurrentStep = () => {
        if (!orderDetail) return 0;

        const statusId = orderDetail.status_id;

        if (statusId === 10) return 0; // 待付款
        if (statusId === 20) return 1; // 已付款
        if (statusId === 30) return 2; // 处理中
        if (statusId === 40) return 3; // 已邮寄
        if (statusId === 50) return 4; // 已完成
        return 0; // 默认第一步
    };

    // 格式化价格
    const formatPrice = (price: string | number) => {
        return typeof price === 'string' ? parseFloat(price).toFixed(2) : price.toFixed(2);
    };

    if (loading) {
        return <Loading />;
    }

    if (!orderDetail) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <p className="text-gray-500"> 订单信息不存在或已被删除 </p>
            </div>
        );
    }

    return (
        <AntdConfigProvider>
            <div className="min-h-screen px-6 py-4 max-w-7xl mx-auto">
                {/* 订单基本信息 */}
                <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
                    <h2 className="text-xl font-medium mb-4"> 订单信息 </h2>
                    <Descriptions bordered column={2}>
                        <Descriptions.Item label=" 订单编号 ">{orderDetail.ordersn}</Descriptions.Item>
                        <Descriptions.Item label=" 订单状态 ">
                            <Tag color={orderStatusMap[orderDetail.status_id]?.color || 'default'}>
                                {orderDetail.status_text}
                            </Tag>
                        </Descriptions.Item>
                        <Descriptions.Item label=" 订单类型 ">{orderDetail.type === 2 ? ' 自营订单 ' : ' 普通订单 '}</Descriptions.Item>
                        <Descriptions.Item label=" 下单时间 ">{orderDetail.createtime}</Descriptions.Item>
                        <Descriptions.Item label=" 卖家名称 ">{orderDetail.goodsseller}</Descriptions.Item>
                        <Descriptions.Item label=" 付款方式 "> 在线支付 </Descriptions.Item>
                    </Descriptions>
                </div>

                {/* 商品列表 */}
                <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
                    <h2 className="text-xl font-medium mb-4"> 商品列表 </h2>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> 商品图片 </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> 商品信息 </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> 单价 </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> 数量 </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> 状态 </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> 小计 </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {orderDetail.goods.map((item) => (
                                    <tr key={item.id}>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex-shrink-0 h-20 w-20 relative">
                                                <Image
                                                    src={fixedUrl(item.goodsimg)}
                                                    alt={item.goodsname}
                                                    fill
                                                    className="object-cover rounded-md"
                                                />
                                            </div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="flex flex-col">
                                                <p className="text-sm font-medium text-gray-900 line-clamp-2">{item.goodsname}</p>
                                                {item.skuname && (
                                                    <p className="text-sm text-gray-500 mt-1">{item.skuname}</p>
                                                )}
                                                {item.goodsremark && item.goodsremark !== ' 无 ' && (
                                                    <p className="text-xs text-gray-400 mt-1"> 备注 : {item.goodsremark}</p>
                                                )}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            ¥ {formatPrice(item.goodsprice)}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {item.goodsnum}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <Tag color={item.status_id === 40 ? 'green' : 'blue'}>
                                                {item.status_text}
                                            </Tag>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                            ¥ {formatPrice(Number(item.goodsprice) * item.goodsnum)}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>

                {/* 费用详情 */}
                <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
                    <div className="flex flex-col items-end space-y-2">
                        <div className="flex justify-between w-80">
                            <span className="text-gray-500"> 商品金额 :</span>
                            <span> ¥ {formatPrice(orderDetail.totalgoodsmoney)}</span>
                        </div>
                        {orderDetail.totalserverfee > 0 && (
                            <div className="flex justify-between w-80">
                                <span className="text-gray-500"> 服务费 :</span>
                                <span> ¥ {formatPrice(orderDetail.totalserverfee)}</span>
                            </div>
                        )}
                        {orderDetail.totalservicemoney > 0 && (
                            <div className="flex justify-between w-80">
                                <span className="text-gray-500"> 服务费 :</span>
                                <span> ¥ {formatPrice(orderDetail.totalservicemoney)}</span>
                            </div>
                        )}
                        <div className="flex justify-between w-80">
                            <span className="text-gray-500"> 国内运费 :</span>
                            <span> ¥ {formatPrice(orderDetail.amountsend)}</span>
                        </div>
                        <div className="flex justify-between w-80 pt-4 border-t">
                            <span className="font-medium"> 实付金额 :</span>
                            <span className="text-[#FF6A00] font-medium"> ¥ {formatPrice(orderDetail.totalmoney)}</span>
                        </div>
                    </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex justify-end space-x-4 mt-6">
                    <Button>
                        <Link href={`/${lng}/dashboard/orders`}> 返回订单列表 </Link>
                    </Button>
                </div>
            </div>
        </AntdConfigProvider>
    );
}
