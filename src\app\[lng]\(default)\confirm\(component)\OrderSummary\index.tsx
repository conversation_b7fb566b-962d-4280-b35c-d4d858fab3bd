'use client';

import React, { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Checkbox} from 'antd';
import { Api } from '@/request/api';
import message from '@/components/CustomMessage';
import Button from '@/components/Button';
import { formatCurrency } from '@/utils/currency';
interface Goods {
  id: number;
  goodsimg: string;
  goodsname: string;
  skuname: string;
  goodsprice: string;
  goodsnum: number;
  [key: string]: any;
}
interface Product {
  cartlist?: Goods[];
  serverfee?: number;
  sendmoney?: number;
}

export default function OrderSummary({
  couponIsOpen=false,
  dict,
  products = {},
  onePayOrder=false
}: {
  couponIsOpen: boolean,
  dict: any,
  products?: Product,
  onePayOrder?: boolean
}) {
  const searchParams = useSearchParams();
  const router = useRouter();
  // const [isCouponModalOpen, setIsCouponModalOpen] = useState(false);
  const [couponCode, setCouponCode] = useState('');
  const [couponList, setCouponList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedCouponId, setSelectedCouponId] = useState<number | null>(null);
  const [selectedCoupon, setSelectedCoupon] = useState<CouponItem | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [siteConfig, setSiteConfig] = useState<any>(null);
  const [itemData, setItemData] = useState<any>(null);

  // 解析URL中的itemData参数
  useEffect(() => {
    const itemDataParam = searchParams.get('itemData');
    if (itemDataParam && itemDataParam.trim() !== '') {
      try {
        // 先尝试解码URL参数
        let decodedParam;
        try {
          decodedParam = decodeURIComponent(itemDataParam);
        } catch (decodeError) {
          console.warn('Failed to decode itemData URL parameter, trying direct parse:', decodeError);
          decodedParam = itemDataParam;
        }

        // 然后尝试解析JSON
        const decodedData = JSON.parse(decodedParam);
        setItemData(decodedData);
        console.log('Successfully parsed itemData in OrderSummary:', decodedData);
      } catch (error) {
        console.error('Failed to parse itemData from URL:', error);
        console.log('Raw itemData parameter:', itemDataParam);
        // 设置为null，让组件使用默认数据
        setItemData(null);
      }
    }
  }, [searchParams]);
  
    useEffect(() => {
      const loadConfig = async () => {
        const config = await getConfig();
        setSiteConfig(config);
      };
      loadConfig();
    }, []);
    // 获取站点配置信息
    const getConfig = async () => {
      try {
        if (typeof window !== "undefined") {
          const siteData = localStorage.getItem("siteData");
          if (siteData) {
            try {
              const config = JSON.parse(siteData);
              return config; 
            } catch (parseError) {
              console.error("Failed to parse siteData:", parseError);
            }
          }
        }
        const res = await Api.getConfigList();
        return res.data.site || null; 
        
      } catch (error) {
        console.error("Failed to get configuration:", error);
        return null; 
      }
    }
  interface CouponItem {
    id: number;
    money: string;
    status: string;
    status_text: string;
    endtime_text: string | number;
    type_text: string;
    usetype_text: string;
    coupon_rule: {
      name: string;
      code: string;
    };
  }
  console.log(products,'products')
  // 获取 cart_ids
  const cart_ids = searchParams.get('cart_ids');
  const cart_ids_list = cart_ids ? cart_ids.split(',') : [];

  // 计算订单汇总数据
  const subtotal = (products?.cartlist || []).reduce((sum, product) => {
    // 优先使用URL参数中的goodsnum，如果不存在则使用product.goodsnum
    const goodsnum = itemData?.goodsnum || product.goodsnum || 1;
    return sum + (parseFloat(String(product.goodsprice)) || 0) * goodsnum;
  }, 0);
  
  const discount = 0.00;
  const freight = products?.sendmoney || 0.00; // 使用购物车API返回的运费
  const insurance = 0.00;
  const coupon = selectedCoupon ? parseFloat(selectedCoupon.money) : 0.00;

  // 计算附加服务费用，从window对象中读取
  const getServiceFee = () => {
    if (typeof window === 'undefined' || !window.orderServices) {
      return 0;
    }
    
    const { serverList = {}, productServices = {} } = window.orderServices;
    
    // 遍历所有产品服务，计算总费用
    return Object.entries(productServices).reduce((totalFee, [productId, service]) => {
      let productFee = 0;
      
      // 计算各项服务费
      Object.entries(service.selectedServices).forEach(([key, selected]) => {
        if (selected && serverList[key]) {
          // 照片服务根据数量计算
          if (key === 'photo') {
            productFee += parseFloat(serverList[key].value) * service.photoCount;
          } else {
            productFee += parseFloat(serverList[key].value);
          }
        }
      });
      
      return totalFee + productFee;
    }, 0);
  };
  
  // 动态计算服务费用
  const [serviceFee, setServiceFee] = useState(0);
  // 订单服务费开关状态
  const [orderServerFeeEnabled, setOrderServerFeeEnabled] = useState(true);

  // 获取站点配置信息
  const getSiteConfig = () => {
    try {
      if (typeof window !== 'undefined') {
        const siteData = localStorage.getItem('siteData');
        if (siteData) {
          const config = JSON.parse(siteData);
          return config;
        }
      }
    } catch (error) {
      console.error('Failed to parse siteData:', error);
    }
    return null;
  };

  // 初始化订单服务费开关状态
  useEffect(() => {
    const config = getSiteConfig();
    if (config && typeof config.order_serverfee !== 'undefined') {
      // order_serverfee 为 0 表示关闭，其他值表示开启
      setOrderServerFeeEnabled(Number(config.order_serverfee) !== 0);
    }
  }, []);

  // 更新服务费用
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const updateServiceFee = () => {
        setServiceFee(getServiceFee());
      };

      // 初始化计算一次
      updateServiceFee();

      // 定时更新，以反映用户的选择变化
      const interval = setInterval(updateServiceFee, 500);

      return () => clearInterval(interval);
    }
  }, []);

  // 计算总价 - 根据开关决定是否包含订单服务费
  const serverFeeToAdd = orderServerFeeEnabled ? (products?.serverfee || 0) : 0;
  const total = subtotal + serviceFee + freight + serverFeeToAdd;
  const payableTotal = Math.max(0, total - coupon);

  // 获取优惠券列表
  const fetchCouponList = async () => {
    try {
      setLoading(true)
      const { data } = await Api.getCouponList()
      console.log('优惠券列表', data.data)
      setCouponList(data.data || [])
    } catch (error) {
      message.error('获取优惠券列表失败')
    } finally {
      setLoading(false)
    }
  }

  const handleActivateCoupon = async () => {
    const res = await Api.couponActivate({ code: couponCode })
    if (res.success) {
      message.success('优惠券使用成功')
      fetchCouponList()
    } else {
      message.error('优惠券激活失败！')
    }
  }

  const handleSubmitOrder = async () => {
    // 首先验证条款同意状态
    const agreementCheckbox = document.getElementById('agreement') as HTMLInputElement;
    if (!agreementCheckbox || !agreementCheckbox.checked) {
      message.error(dict?.confirm?.order?.summary?.agreeTermsFirst || '请先阅读并同意服务条款');
      return;
    }

    if (!cart_ids) {
      console.error('缺少订单信息')
      return
    }
    // 获取各个商品的附加服务信息 - 从window对象获取
    const serve = typeof window !== 'undefined' && window.orderServices?.getServiceData ? 
    window.orderServices.getServiceData() : {};

    if(serve.photo){
      let photo: any =  Object.values(serve.photo)
      try{
        // Check if photo service is actually selected (num > 0)
        const hasSelectedPhoto = photo.some((photoService: any) => photoService.num > 0);
        if (hasSelectedPhoto) {
          let remarks = JSON.parse(photo[0].remark)
          const hasEmpty =  remarks.some((item:any) => item.trim() === '');
          if (hasEmpty) {
            message.error('请填写照片备注信息');
            return;
          }
        }
      }catch(e){
      }
    }
    
    setSubmitting(true);
    const params = {
      cart_ids: cart_ids_list.map(id => Number(id)),
      serve: serve, // 从OrderItem组件获取的服务数据
      clienttype: 'pc',
      lang: 'zh-cn'
    }
    const res = await Api.carttoorder(params)

    console.log(siteConfig.needcheck ,'siteConfig.needcheck ')
    if(siteConfig.needcheck === '1' && !onePayOrder){
       router.push(`/dashboard/orders`)
      return
    }
    if (res.success) {
      // 订单提交成功，清理保存的附加服务状态
      if (typeof window !== 'undefined' && window.orderServices?.clearServiceState) {
        window.orderServices.clearServiceState();
      }

      // 如果是一次付款，直接跳转到一次付款订单列表页面
      if (onePayOrder) {
        // 获取当前语言参数
        const currentPath = window.location.pathname;
        const lngMatch = currentPath.match(/^\/([^\/]+)\//);
        const lng = lngMatch ? lngMatch[1] : 'zh-cn';
        router.push(`/${lng}/dashboard/onepayorders`)
      } else {
        router.push(`/pay?order_id=${res.data.join(',')}`)
      }
    } else {
      message.error('提交订单失败!')
      setSubmitting(false);
      router.push('/')
    }
  }

  return (
    <div className="bg-white p-4 rounded-lg sticky top-40">
      <h2 className="text-lg font-medium mb-4">{dict.confirm.order.summary.title}</h2>

      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span>{dict.confirm.order.summary.retailPrice}</span>
          <span>{formatCurrency(subtotal).formatValue}</span>
        </div>
        {/* 添加优惠券代码按钮 */}
        {/* <div className="flex justify-between items-center py-2 border-t border-gray-100">
          <span>使用优惠券代码</span>
          <button
            onClick={() => setIsCouponModalOpen(true)}
            className="text-[var(--base-color)] hover:text-[var(--base-color-hover)]"
          >
            添加
          </button>
        </div> */}

        {discount > 0 && (
          <div className="flex justify-between items-center text-[var(--base-color)]">
            <span>{dict.confirm.order.summary.discount}</span>
            <span>- {formatCurrency(discount).formatValue}</span>
          </div>
        )}
        <div className="flex justify-between items-center">
          <span>{dict.confirm.order.summary.shippingFee}</span>
          <span>{formatCurrency(freight).formatValue}</span>
        </div>
        {coupon > 0 && (
          <div className="flex justify-between items-center text-[var(--base-color)]">
            <span>{dict.confirm.order.summary.coupon}</span>
            <span>- {formatCurrency(coupon).formatValue}</span>
          </div>
        )}
        {serviceFee > 0 && (
          <div className="flex justify-between items-center">
            <span>{dict.confirm.order.summary.extraService}</span>
            <span>{formatCurrency(serviceFee).formatValue}</span>
          </div>
        )}
        {orderServerFeeEnabled && (
          <div className="flex justify-between items-center">
            <span>{dict.confirm.order.summary.serverfee}</span>
            <span>{formatCurrency(products?.serverfee || 0).formatValue}</span>
          </div>
        )}
      </div>

      <div className="mt-4 pt-4 border-t border-gray-100">
        <div className="flex justify-between items-center">
          <div className="text-base font-medium">{dict.confirm.order.summary.total}</div>
          <div className="text-xl font-bold">{formatCurrency(total).formatValue}</div>
        </div>
        {/* <div className="flex justify-between text-sm text-[var(--base-color)] mt-1">
          <span>{dict.confirm.order.summary.saved}</span>
          <span>{formatCurrency(payableTotal).formatValue}</span>
        </div> */}
        <div className="text-xs text-gray-400 mt-1">
          <span>{dict.confirm.order.summary.excludeShipping}</span>
        </div>
      </div>

      {/* 服务协议与链接 */}
      <div className="flex flex-wrap gap-2 mt-3 text-xs text-gray-500">
        {/* todo */}
        <a href={`/information?id=${siteConfig?.return_policy}`} target='_blank' className="text-[var(--base-color)]">{dict.confirm.order.summary.returnPolicy}</a>
        <a href={`/information?id=${siteConfig?.terms_of_service}`} target='_blank' className="text-[var(--base-color)]">{dict.confirm.order.summary.terms}</a>
        <a href={`/information?id=${siteConfig?.order_privacy_policy}`} target='_blank' className="text-[var(--base-color)]">{dict.confirm.order.summary.privacy}</a>
        <a href={`/information?id=${siteConfig?.order_qa}`} target='_blank' className="text-[var(--base-color)]">{dict.confirm.order.summary.faq}</a>
      </div>
      {/* 提交订单区域 */}
      <div className="mt-4">
        <div className="flex items-center mb-3">
          <Checkbox id="agreement" className="mr-2">
            <span className="text-sm">{dict.confirm.order.summary.agreement}</span>
          </Checkbox>
        </div>
        <Button
          className="w-full mb-5 bg-[var(--base-color)] text-white py-3 rounded-md font-medium hover:bg-[var(--base-color-hover)] transition-colors disabled:opacity-70"
          onClick={handleSubmitOrder}
          loading={submitting}
          size="large"
          variant="solid"
          color='primary'
        >
          {dict.confirm.order.summary.submit}
        </Button>
        <div className="bg-gray-100 p-3 rounded-md mb-4">
          <div className="text-sm font-medium">{dict.confirm.order.summary.tips.title}</div>
          <div className="text-xs text-gray-500">{dict.confirm.order.summary.tips.content}</div>
        </div>
      </div>
    </div>
  )
}