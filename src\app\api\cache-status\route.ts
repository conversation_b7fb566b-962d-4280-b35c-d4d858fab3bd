import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    
    // 检查不同的token名称
    const isTp6 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '6'
    const tokenName = isTp6 ? 'access_token' : 'token'
    
    const token = cookieStore.get(tokenName)?.value
    const currency = cookieStore.get('currency')?.value
    const language = cookieStore.get('selectedLanguage')?.value
    
    // 获取用户代理和其他信息
    const userAgent = request.headers.get('user-agent') || ''
    const referer = request.headers.get('referer') || ''
    
    const status = {
      hasToken: !!token,
      tokenName,
      hasCurrency: !!currency,
      hasLanguage: !!language,
      backendType: process.env.NEXT_PUBLIC_BACKEND_TYPE,
      timestamp: new Date().toISOString(),
      userAgent: userAgent.substring(0, 100), // 限制长度
      referer: referer.substring(0, 200) // 限制长度
    }
    
    return NextResponse.json({
      success: true,
      data: status
    })
  } catch (error) {
    console.error('Cache status check error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to check cache status'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body
    
    if (action === 'clear') {
      // 创建响应并清理cookies
      const response = NextResponse.json({
        success: true,
        message: 'Cache cleared successfully'
      })
      
      // 清理主要的cookies
      const cookiesToClear = ['token', 'access_token', 'currency', 'selectedLanguage']
      
      cookiesToClear.forEach(cookieName => {
        response.cookies.set(cookieName, '', {
          expires: new Date(0),
          path: '/'
        })
      })
      
      return response
    }
    
    return NextResponse.json({
      success: false,
      error: 'Invalid action'
    }, { status: 400 })
    
  } catch (error) {
    console.error('Cache action error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to perform cache action'
    }, { status: 500 })
  }
}
