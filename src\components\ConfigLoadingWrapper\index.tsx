'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { locales } from '@/config';
import Loading from '@/components/Loading';
import Cookies from 'js-cookie';
import { Api } from '@/request/api';

interface ConfigLoadingWrapperProps {
  children: React.ReactNode;
  currentLng: string;
}

export default function ConfigLoadingWrapper({
  children,
  currentLng
}: ConfigLoadingWrapperProps) {
  const [isConfigReady, setIsConfigReady] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [configList, setConfigList] = useState<any>({});
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5';

  // 获取配置数据
  useEffect(() => {
    const fetchConfigList = async () => {
      try {
        const response = isTp5 ? await Api.getConfigList() : await Api.getConfigList();
        if (response?.success || response) {
          const config = response?.data || response;
          setConfigList(config);
        }
      } catch (error) {
        console.error('Failed to load config in ConfigLoadingWrapper:', error);
        // 使用默认配置
        setConfigList({
          site: {
            name: 'onebuy',
            language: 'zh-cn',
            currency: 'CNY'
          },
          currency_list: []
        });
      }
    };

    fetchConfigList();
  }, [isTp5]);

  useEffect(() => {
    const initializeConfig = async () => {
      try {
        console.log('🔧 ConfigLoadingWrapper: 开始初始化配置', {
          hasConfigList: !!configList?.site,
          currentLng,
          pathname
        });

        // 确保配置数据存在
        if (!configList?.site) {
          console.warn('⚠️ 配置数据不完整，跳过初始化');
          setIsInitializing(false);
          setIsConfigReady(true);
          return;
        }

        // 检查本地存储的语言设置
        const savedLanguage = localStorage.getItem('selectedLanguage');
        const cookieLanguage = Cookies.get('selectedLanguage');

        // 获取系统默认语言
        const defaultLanguage = configList.site.language;

        console.log('🌐 语言设置检查', {
          savedLanguage,
          cookieLanguage,
          defaultLanguage,
          currentLng
        });

        // 确定应该使用的语言
        let targetLanguage = currentLng;
        let languageSource = 'current';

        // 如果有保存的语言设置，优先使用
        if (savedLanguage && locales.includes(savedLanguage)) {
          targetLanguage = savedLanguage;
          languageSource = 'localStorage';
        } else if (cookieLanguage && locales.includes(cookieLanguage)) {
          targetLanguage = cookieLanguage;
          languageSource = 'cookie';
        } else if (defaultLanguage && locales.includes(defaultLanguage)) {
          // 如果没有保存的语言设置，使用系统默认语言
          targetLanguage = defaultLanguage;
          languageSource = 'default';
          // 保存默认语言到本地存储
          localStorage.setItem('selectedLanguage', defaultLanguage);
          Cookies.set('selectedLanguage', defaultLanguage, { expires: 30, path: '/' });
          console.log('💾 保存默认语言到本地存储:', defaultLanguage);
        }

        console.log('🎯 最终语言选择', {
          targetLanguage,
          source: languageSource,
          needRedirect: targetLanguage !== currentLng
        });

        // 如果目标语言与当前URL语言不同，需要跳转
        if (targetLanguage !== currentLng) {
          const pathSegments = pathname.split('/');
          if (locales.includes(pathSegments[1])) {
            pathSegments[1] = targetLanguage;
          } else {
            pathSegments.unshift(targetLanguage);
          }

          const newPath = pathSegments.join('/');
          const params = new URLSearchParams(searchParams.toString());
          const finalPath = params.toString() ? `${newPath}?${params.toString()}` : newPath;

          console.log('🔄 执行语言跳转', {
            from: pathname,
            to: finalPath
          });

          // 执行跳转
          router.push(finalPath);
          return; // 跳转后不设置ready状态，让新页面重新初始化
        }

        // 设置默认货币
        const savedCurrency = localStorage.getItem('selectedCurrency');
        const cookieCurrency = Cookies.get('currency');
        const defaultCurrency = configList.site.currency;

        console.log('💰 货币设置检查', {
          savedCurrency,
          cookieCurrency,
          defaultCurrency
        });

        if (!savedCurrency && !cookieCurrency && defaultCurrency) {
          localStorage.setItem('selectedCurrency', defaultCurrency);
          Cookies.set('currency', defaultCurrency, { expires: 30, path: '/' });
          console.log('💾 保存默认货币到本地存储:', defaultCurrency);

          // 同时设置默认货币的符号和汇率
          try {
            // 尝试从configList中获取货币数据
            const currencyList = configList.currency_list;
            let defaultCurrencyData = null;

            if (Array.isArray(currencyList)) {
              defaultCurrencyData = currencyList.find((item: any) => item.code === defaultCurrency);
            } else if (typeof currencyList === 'object' && currencyList !== null) {
              defaultCurrencyData = currencyList[defaultCurrency];
            }

            if (defaultCurrencyData) {
              // 设置货币符号
              if (defaultCurrencyData.symbol_left) {
                localStorage.setItem('currencySymbol', defaultCurrencyData.symbol_left);
              } else if (defaultCurrencyData.symbol) {
                localStorage.setItem('currencySymbol', defaultCurrencyData.symbol);
              } else {
                // 如果没有符号，使用货币代码作为符号
                localStorage.setItem('currencySymbol', defaultCurrency);
              }

              // 设置汇率
              if (defaultCurrencyData.value) {
                localStorage.setItem('currentExchangeRate', defaultCurrencyData.value.toString());
              } else if (defaultCurrencyData.rate) {
                localStorage.setItem('currentExchangeRate', defaultCurrencyData.rate.toString());
              } else {
                // 默认汇率为1
                localStorage.setItem('currentExchangeRate', '1');
              }

              console.log('💾 保存默认货币详细信息:', {
                currency: defaultCurrency,
                symbol: defaultCurrencyData.symbol_left || defaultCurrencyData.symbol || defaultCurrency,
                rate: defaultCurrencyData.value || defaultCurrencyData.rate || 1
              });
            } else {
              // 如果找不到货币数据，设置基本默认值
              localStorage.setItem('currencySymbol', defaultCurrency);
              localStorage.setItem('currentExchangeRate', '1');
              console.log('⚠️ 未找到默认货币数据，使用基本默认值');
            }
          } catch (error) {
            console.error('❌ 设置默认货币详细信息失败:', error);
            // 设置基本默认值
            localStorage.setItem('currencySymbol', defaultCurrency);
            localStorage.setItem('currentExchangeRate', '1');
          }
        }

        // 保存站点配置到本地存储
        localStorage.setItem('siteData', JSON.stringify(configList.site));

        console.log('✅ 配置初始化完成，准备显示页面');

        // 短暂延迟确保所有设置都已完成
        setTimeout(() => {
          setIsInitializing(false);
          setIsConfigReady(true);
        }, 50); // 减少延迟时间

      } catch (error) {
        console.error('❌ 配置初始化失败:', error);
        setIsInitializing(false);
        setIsConfigReady(true);
      }
    };

    // 只有在configList加载完成后才执行初始化
    if (Object.keys(configList).length > 0) {
      initializeConfig();
    }
  }, [configList, currentLng, pathname, searchParams, router]);

  // 如果正在初始化或配置未就绪，显示全屏loading
  if (isInitializing || !isConfigReady) {
    return (
      <div className="fixed inset-0 bg-white dark:bg-gray-900 z-50 flex flex-col items-center justify-center">
        <div className="text-center">
          <Loading height="auto" />
        </div>
      </div>
    );
  }

  // 配置就绪，显示页面内容
  return <>{children}</>;
}
