'use client'

import { useEffect, useState } from 'react'
import { Button } from 'antd'
import { getDictionary } from '@/dictionaries'

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  const [dict, setDict] = useState<any>(null);
  const [lng, setLng] = useState('zh-cn');

  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Global Error:', error)

    // Check if this is the specific destructuring error we're trying to fix
    if (error.message && error.message.includes("Cannot destructure property 'auth' of 'e' as it is undefined")) {
      // Try to clear potentially corrupted state
      if (typeof window !== 'undefined') {
        try {
          localStorage.removeItem('info');
          localStorage.removeItem('siteData');
          localStorage.removeItem('exchangeRates');
        } catch (clearError) {
          console.error('Failed to clear localStorage:', clearError);
        }
      }
    }

    // 获取当前语言并加载字典
    if (typeof window !== 'undefined') {
      const pathSegments = window.location.pathname.split('/')
      const currentLng = pathSegments[1] && ['en', 'zh-cn', 'ja'].includes(pathSegments[1])
        ? pathSegments[1]
        : 'zh-cn'

      setLng(currentLng);

      // 加载对应语言的字典
      const loadDictionary = async () => {
        try {
          const dictionary = await getDictionary(currentLng as any);
          setDict(dictionary);
        } catch (error) {
          console.error('Failed to load dictionary:', error);
        }
      };

      loadDictionary();

      // 延迟3秒后自动跳转，给用户时间看到错误信息
      setTimeout(() => {
        try {
          const returnUrl = encodeURIComponent(window.location.pathname + window.location.search);
          const targetUrl = `/${currentLng}/cache-refresh?return=${returnUrl}`;

          window.location.href = targetUrl;
        } catch (error) {
          // Don't show alert for auto-redirect failure, just log it
        }
      }, 3000)
    }
  }, [error])

  const goToCacheRefresh = () => {
    try {
      if (typeof window !== 'undefined') {
        // Ensure we have a valid language, fallback to zh-cn if not
        const currentLng = lng || 'zh-cn';
        const returnUrl = encodeURIComponent(window.location.pathname + window.location.search);
        const targetUrl = `/${currentLng}/cache-refresh?return=${returnUrl}`;

        // Add a small delay to ensure the console log is visible
        setTimeout(() => {
          window.location.href = targetUrl;
        }, 100);
      }
    } catch (error) {
      console.error('Error in goToCacheRefresh:', error);

      // Fallback: try to reload the page
      try {
        window.location.reload();
      } catch (reloadError) {
        console.error('Failed to reload page:', reloadError);
        const alertMessage = lng === 'en'
          ? 'Page redirect failed, please manually refresh the page or clear browser cache'
          : lng === 'ja'
          ? 'ページのリダイレクトに失敗しました。手動でページを更新するか、ブラウザのキャッシュをクリアしてください'
          : '页面跳转失败，请手动刷新页面或清理浏览器缓存';
        alert(alertMessage);
      }
    }
  }

  return (
    <html>
      <body>
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-100">
          <div className="text-center px-4 max-w-md bg-white rounded-2xl shadow-xl p-8">
            <div className="text-orange-500 text-6xl mb-4">
              ⚠️
            </div>
            <h1 className="text-2xl font-bold mb-4 text-gray-800">
              {dict?.error?.systemError || '系统遇到了一些问题'}
            </h1>
            <p className="text-gray-600 mb-6 leading-relaxed">
              {dict?.error?.dontWorry || '别担心，我们正在为您准备解决方案...'}
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <p className="text-blue-700 text-sm">
                🔄 {dict?.error?.autoRedirect || '页面将在 3 秒后自动跳转到修复页面'}
              </p>
            </div>

            <div className="space-y-3">
              <Button
                type="primary"
                size="large"
                onClick={() => {
                  goToCacheRefresh();
                }}
                className="w-full bg-blue-500 hover:bg-blue-600 border-blue-500"
              >
                {dict?.error?.goToFix || '立即前往修复页面'}
              </Button>

              <Button
                type="default"
                size="large"
                onClick={() => {
                  reset();
                }}
                className="w-full"
              >
                {dict?.error?.retryPage || '重试当前页面'}
              </Button>




            </div>

            <div className="mt-6 p-4 bg-gray-50 rounded-lg text-left">
              <h3 className="font-medium text-gray-800 mb-2 text-center">💡 {dict?.error?.whatIsThis || '这是什么问题？'}</h3>
              <ul className="text-gray-600 text-sm space-y-1">
                {dict?.error?.reasons ? dict.error.reasons.map((reason: string, index: number) => (
                  <li key={index}>• {reason}</li>
                )) : (
                  <>
                    <li>• 浏览器缓存数据冲突</li>
                    <li>• 登录状态不同步</li>
                    <li>• 需要刷新页面数据</li>
                  </>
                )}
              </ul>
            </div>
          </div>
        </div>
      </body>
    </html>
  )
}
