/* 360浏览器兼容性修复 */
@keyframes fade-in-down {
    from {
        opacity: 0;
        -webkit-transform: translateY(-1rem);
        -moz-transform: translateY(-1rem);
        -ms-transform: translateY(-1rem);
        -o-transform: translateY(-1rem);
        transform: translateY(-1rem);
    }
    to {
        opacity: 1;
        -webkit-transform: translateY(0);
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
        transform: translateY(0);
    }
}

@keyframes fade-out-up {
    from {
        opacity: 1;
        -webkit-transform: translateY(0);
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
        transform: translateY(0);
    }
    to {
        opacity: 0;
        -webkit-transform: translateY(-1rem);
        -moz-transform: translateY(-1rem);
        -ms-transform: translateY(-1rem);
        -o-transform: translateY(-1rem);
        transform: translateY(-1rem);
    }
}

@keyframes pulse-sign {
    0% {
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1);
        -webkit-box-shadow: 0 0 0 0 rgba(244, 114, 54, 0.7);
        -moz-box-shadow: 0 0 0 0 rgba(244, 114, 54, 0.7);
        box-shadow: 0 0 0 0 rgba(244, 114, 54, 0.7);
    }
    50% {
        -webkit-transform: scale(1.05);
        -moz-transform: scale(1.05);
        -ms-transform: scale(1.05);
        -o-transform: scale(1.05);
        transform: scale(1.05);
        -webkit-box-shadow: 0 0 0 5px rgba(244, 114, 54, 0);
        -moz-box-shadow: 0 0 0 5px rgba(244, 114, 54, 0);
        box-shadow: 0 0 0 5px rgba(244, 114, 54, 0);
    }
    100% {
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1);
        -webkit-box-shadow: 0 0 0 0 rgba(244, 114, 54, 0);
        -moz-box-shadow: 0 0 0 0 rgba(244, 114, 54, 0);
        box-shadow: 0 0 0 0 rgba(244, 114, 54, 0);
    }
}

/* 签到按钮样式 - 360浏览器兼容 */
.btn-signin {
    /* 动画兼容性 */
    -webkit-animation: pulse-sign 1.5s infinite;
    -moz-animation: pulse-sign 1.5s infinite;
    -o-animation: pulse-sign 1.5s infinite;
    animation: pulse-sign 1.5s infinite;

    /* 渐变背景兼容性 - 橙色渐变 */
    background: #f97316; /* 降级方案 */
    background: -webkit-linear-gradient(left, #f97316, #fb923c);
    background: -moz-linear-gradient(left, #f97316, #fb923c);
    background: -o-linear-gradient(left, #f97316, #fb923c);
    background: linear-gradient(to right, #f97316, #fb923c);

    /* 边框圆角兼容性 */
    -webkit-border-radius: 9999px;
    -moz-border-radius: 9999px;
    border-radius: 9999px;

    /* 阴影兼容性 */
    -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);

    /* 过渡效果兼容性 */
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;

    /* Flexbox兼容性 */
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -moz-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}

/* 已签到状态 - 绿色渐变 */
.btn-signin.signed {
    background: #22c55e; /* 降级方案 */
    background: -webkit-linear-gradient(left, #22c55e, #16a34a);
    background: -moz-linear-gradient(left, #22c55e, #16a34a);
    background: -o-linear-gradient(left, #22c55e, #16a34a);
    background: linear-gradient(to right, #22c55e, #16a34a);
    opacity: 0.8;
    cursor: not-allowed;
}

/* 加载状态 - 浅橙色渐变 */
.btn-signin.loading {
    background: #fdba74; /* 降级方案 */
    background: -webkit-linear-gradient(left, #fdba74, #fb923c);
    background: -moz-linear-gradient(left, #fdba74, #fb923c);
    background: -o-linear-gradient(left, #fdba74, #fb923c);
    background: linear-gradient(to right, #fdba74, #fb923c);
    cursor: not-allowed;
}

/* 悬停效果 */
.btn-signin:hover:not(.signed):not(.loading) {
    background: #ea580c; /* 降级方案 */
    background: -webkit-linear-gradient(left, #ea580c, #dc2626);
    background: -moz-linear-gradient(left, #ea580c, #dc2626);
    background: -o-linear-gradient(left, #ea580c, #dc2626);
    background: linear-gradient(to right, #ea580c, #dc2626);
    -webkit-box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.animate-fade-in-down {
    -webkit-animation: fade-in-down 0.3s ease-out forwards;
    -moz-animation: fade-in-down 0.3s ease-out forwards;
    -o-animation: fade-in-down 0.3s ease-out forwards;
    animation: fade-in-down 0.3s ease-out forwards;
}

.animate-fade-out-up {
    -webkit-animation: fade-out-up 0.3s ease-in forwards;
    -moz-animation: fade-out-up 0.3s ease-in forwards;
    -o-animation: fade-out-up 0.3s ease-in forwards;
    animation: fade-out-up 0.3s ease-in forwards;
}

/* 公告模块样式 - 360浏览器兼容 */
.announcement-container {
    /* 渐变背景兼容性 - 橙色到黄色渐变 */
    background: #f97316; /* 降级方案 */
    background: -webkit-linear-gradient(left, #f97316, #fbbf24);
    background: -moz-linear-gradient(left, #f97316, #fbbf24);
    background: -o-linear-gradient(left, #f97316, #fbbf24);
    background: linear-gradient(to right, #f97316, #fbbf24);

    /* 边框圆角兼容性 */
    -webkit-border-radius: 1rem;
    -moz-border-radius: 1rem;
    border-radius: 1rem;

    /* 阴影兼容性 */
    -webkit-box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

    /* 过渡效果兼容性 */
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;

    /* 定位兼容性 */
    position: relative;
    overflow: hidden;
    min-height: 6rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

/* 悬停效果 */
.announcement-container:hover {
    -webkit-box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* 装饰圆圈兼容性 */
.announcement-decoration {
    position: absolute;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
}

.announcement-decoration.top-right {
    top: 0;
    right: 0;
    width: 10rem;
    height: 10rem;
    -webkit-transform: translate(2.5rem, -2.5rem);
    -moz-transform: translate(2.5rem, -2.5rem);
    -ms-transform: translate(2.5rem, -2.5rem);
    -o-transform: translate(2.5rem, -2.5rem);
    transform: translate(2.5rem, -2.5rem);
}

.announcement-decoration.bottom-left {
    bottom: 0;
    left: 2.5rem;
    width: 8rem;
    height: 8rem;
    background-color: rgba(255, 255, 255, 0.05);
    -webkit-transform: translateY(4rem);
    -moz-transform: translateY(4rem);
    -ms-transform: translateY(4rem);
    -o-transform: translateY(4rem);
    transform: translateY(4rem);
}

/* 公告内容区域 */
.announcement-content {
    position: relative;
    z-index: 10;
    /* Flexbox兼容性 */
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    pointer-events: none;
}

/* 公告图标容器 */
.announcement-icon {
    background-color: rgba(255, 255, 255, 0.2);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    /* Flexbox兼容性 */
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -moz-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin-right: 1.25rem;
}

/* 公告文本区域 */
.announcement-text {
    width: 100%;
}

/* 公告内容框 */
.announcement-box {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    -webkit-border-radius: 0.75rem;
    -moz-border-radius: 0.75rem;
    border-radius: 0.75rem;
}

/* 下一条按钮 */
.announcement-next-btn {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 999;
    color: rgba(255, 255, 255, 0.9);
    background-color: rgba(255, 255, 255, 0.1);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    width: 2rem;
    height: 2rem;
    border: none;
    cursor: pointer;
    /* Flexbox兼容性 */
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -moz-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    /* 过渡效果兼容性 */
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.announcement-next-btn:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.2);
}

.announcement-next-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Font Awesome 图标兼容性 - 360浏览器 */
.fas, .fa {
    font-family: "Font Awesome 5 Free", "Font Awesome 5 Pro", "FontAwesome", sans-serif !important;
    font-weight: 900 !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

/* 确保图标正确显示 */
.fa-spinner:before { content: "\f110"; }
.fa-check-circle:before { content: "\f058"; }
.fa-calendar-check:before { content: "\f274"; }
.fa-bell:before { content: "\f0f3"; }
.fa-chevron-right:before { content: "\f054"; }

/* 旋转动画兼容性 */
.fa-spin {
    -webkit-animation: fa-spin 2s infinite linear;
    -moz-animation: fa-spin 2s infinite linear;
    -o-animation: fa-spin 2s infinite linear;
    animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(359deg);
        -moz-transform: rotate(359deg);
        -ms-transform: rotate(359deg);
        -o-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}

/* 360浏览器特定修复 */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    /* Webkit内核浏览器（包括360浏览器）的特定修复 */
    .btn-signin {
        -webkit-appearance: none;
        -webkit-user-select: none;
    }

    .announcement-container {
        -webkit-backface-visibility: hidden;
        -webkit-perspective: 1000;
    }
}

/* IE兼容性（360浏览器兼容模式） */
.btn-signin {
    zoom: 1; /* 触发hasLayout */
    *display: inline; /* IE6/7 hack */
}

.announcement-container {
    zoom: 1; /* 触发hasLayout */
}

/* 确保在所有浏览器中文本颜色正确显示 */
.btn-signin,
.announcement-container {
    color: white !important;
}

.btn-signin * {
    color: inherit !important;
}

.announcement-container * {
    color: inherit !important;
}