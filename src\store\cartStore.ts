import { create } from 'zustand';
import { Api } from '@/request/api';
import { isUserLoggedIn } from '@/request/index';
const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
interface CartState {
  count: number;
  isLoading: boolean;
  fetchCartCount: () => Promise<void>;
  updateCartCount: (count: number) => void;
  incrementCartCount: () => void;
}

export const useCartStore = create<CartState>((set, get) => ({
  count: 0,
  isLoading: false,

  fetchCartCount: async () => {
    set({ isLoading: true });

    try {
      if(isUserLoggedIn()){
        const response = isTp5? await Api.getCartNums(): await Api.getCartList();
        if(response?.success){
          let count = response?.data?.cart_nums || response.data?.totalnum || 0
          set({ count });
        }
      }else{
        // 检查本地是否有存储的购物车数据
        if (typeof window !== 'undefined') {
          const localCartItemsStr = localStorage.getItem('localCartItems');
          const localCartItems = localCartItemsStr ? JSON.parse(localCartItemsStr) : [];
          set({ count: localCartItems.length });
        }
      }


    } catch (error) {
      console.error(' 获取购物车数量失败 :', error);
      // Set count to 0 on error to prevent undefined state
      set({ count: 0 });
    } finally {
      set({ isLoading: false });
    }
  },

  updateCartCount: (count: number) => {
    if (typeof count === 'number' && count >= 0) {
      set({ count });
    }
  },

  incrementCartCount: () => {
    set((state: CartState) => ({ count: Math.max(0, state.count + 1) }));
  }
}));