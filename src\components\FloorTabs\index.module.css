/* 楼层分类标签页专用样式 - 橙色底白色文字 */
.floorTabs {
  --tabs-nav-bg: linear-gradient(to right, #f97316, #ea580c);
}

.floorTabs .ant-tabs-nav {
  background: var(--tabs-nav-bg, linear-gradient(to right, #f97316, #ea580c)) !important;
  padding: 8px 16px !important;
  border-radius: 8px 8px 0 0 !important;
  margin-bottom: 0 !important;
}

/* 更具体的选择器 */
.floorTabs.ant-tabs .ant-tabs-nav {
  background: linear-gradient(to right, #f97316, #ea580c) !important;
}

.floorTabs .ant-tabs-nav::before {
  display: none !important; /* 隐藏默认的底部边框 */
}

.floorTabs .ant-tabs-nav-wrap {
  padding: 0 !important;
}

.floorTabs .ant-tabs-tab {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8) !important;
  padding: 12px 20px;
  font-size: 16px;
  border-radius: 8px 8px 0 0;
  margin-right: 4px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-bottom: none;
  transition: all 0.3s ease;
}

.floorTabs .ant-tabs-tab:hover {
  color: white !important;
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.floorTabs .ant-tabs-tab-active {
  color: #f97316 !important;
  font-weight: 600;
  background: white !important;
  border-color: white !important;
  position: relative;
  z-index: 1;
}

.floorTabs .ant-tabs-ink-bar {
  display: none; /* 隐藏默认的下划线，使用自定义样式 */
}

.floorTabs .ant-tabs-content-holder {
  padding: 0;
  background: white;
}

.floorTabs .ant-tabs-extra-content {
  margin-left: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floorTabs .ant-tabs-tab {
    padding: 8px 16px;
    font-size: 14px;
  }
  
  .floorTabs .ant-tabs-nav {
    padding: 6px 12px;
  }
  
  .floorTabs .ant-tabs-extra-content {
    margin-left: 12px;
  }
}
