'use client';

import { Tabs, TabsProps } from 'antd';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import React, { ReactNode } from 'react';
import styles from './index.module.css';

interface FloorTabsProps extends TabsProps {
  children?: ReactNode;
}

export default function FloorTabs({ children, className, ...props }: FloorTabsProps) {
  const tabsClassName = `${styles.floorTabs} ${className || ''}`.trim();

  // 调试信息
  console.log('FloorTabs styles:', styles);
  console.log('FloorTabs className:', tabsClassName);

  return (
    <AntdConfigProvider>
      <Tabs
        {...props}
        className={tabsClassName}
        style={{
          '--tabs-nav-bg': 'linear-gradient(to right, #f97316, #ea580c)',
          ...props.style
        } as React.CSSProperties}
      >
        {children && children}
      </Tabs>
    </AntdConfigProvider>
  );
}
