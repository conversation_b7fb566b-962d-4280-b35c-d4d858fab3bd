'use client';
import { useEffect, useState, useRef, useMemo } from 'react';
import { useSearchParams } from 'next/navigation';
import Image from 'next/image';
import { Api } from '@/request/api';
import Loading from '@/components/Loading';
import { getDictionary } from '@/dictionaries';
import type { Locale } from '@/config';
import React from 'react';
import Link from 'next/link';
import { md5 } from 'js-md5';
import { message ,Tooltip} from 'antd';
import Toast from '@/components/Toast';
import { useCartStore } from '@/store/cartStore';
import { useRouter } from 'next/navigation';
import { isUserLoggedIn } from '@/request/index';
import { formatCurrency, formatUSDPrice } from '@/utils/currency';
import { prepareImageForNextJs } from '@/utils/imageUtils';
import ToastHelper from '@/utils/toastHelper';
import Guess<PERSON><PERSON>Like from '@/components/GuessYouLike';
import PriceParity from '@/components/PriceParity';
import { isPluginEnabled } from '@/utils/plugin';
import { ERROR_CODES } from '@/utils/errorHandler';

const imgList = [
    '/images/detail-banner.gif',
    '/images/detail-banner.gif',
    '/images/detail-banner.gif',
];

// 定义商品详情数据类型
interface TaobaoItemDetail {
    num_iid: string;
    title: string;
    price: number;
    orginal_price: number;
    nick: string;
    num: number;
    detail_url: string;
    pic_url: string;
    location: string;
    total_sold: string;
    desc: string;
    props_name: string;
    has_favorite:boolean;
    props_list: Record<string, string>;
    item_imgs: Array<{ url: string }>;
    favorite_id: number;
    seller_info?: {
        nick: string;
        shop_name: string;
        zhuy: string;
    };
    props_img: {
        [key: string]: string;
    };
    props_imgs?: {
        prop_img: Array<{
            properties: string;
            url: string;
        }>;
    };
    skus: {
        sku: Array<{
            price: number;
            properties: string;
            properties_name: string;
            quantity: number;
            sku_id: string;
        }>;
    };
    props: Array<{
        name: string;
        value: string;
    }>;
    cn_props_list?: Record<string, string>;
    cn_skus?: {
        sku: Array<{
            price: number;
            properties: string;
            properties_name: string;
            quantity: number;
            sku_id: string;
        }>;
    };
    item_weight?: number;
    volume?: number;
    express_fee?: number; // 运费
    priceRange?: Array<[string, number]>; // 批发价格区间 [数量, 价格]
    estimaterule?:{
        firstFee: string;
        firstUnit: string;
        nextFee: string;
        nextUnit: string;
    }
}

interface SelectedSku {
    [key: string]: string;
}

export default function DetailTaobao({
    params,
}: {
    params: Promise<{ lng: Locale }>;
}) {
    // 使用 React.use() 解包 params
    const resolvedParams = React.use(params);
    const { lng } = resolvedParams;

    return (
        <ClientDetailTaobao lng={lng} />
    );
}

function ClientDetailTaobao({ lng }: { lng: Locale }) {
    const searchParams = useSearchParams();
    const url = searchParams.get('url') || '';
    const [loading, setLoading] = useState(true);
    const [itemData, setItemData] = useState<TaobaoItemDetail | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [dict, setDict] = useState<any>({});
    const [selectedImage, setSelectedImage] = useState<string>('');
    const [quantity, setQuantity] = useState(1);
    const [selectedSku, setSelectedSku] = useState<SelectedSku>({});
    const [currentStock, setCurrentStock] = useState<number>(0);
    const [remark, setRemark] = useState('');
    const [showZoom, setShowZoom] = useState(false);
    const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
    const [addingToCart, setAddingToCart] = useState(false);
    const [isFavorite, setIsFavorite] = useState(0);
    const [currentExpressFee, setCurrentExpressFee] = useState(0);
    const imageRef = useRef<HTMLDivElement>(null);
    const cartBtnRef = useRef<HTMLButtonElement>(null);
    const [messageApi, contextHolder] = message.useMessage();
    const [cartSuccess, setCartSuccess] = useState(false);
    const [siteConfig, setSiteConfig] = useState<any>(null);
    const [currentPrice, setCurrentPrice] = useState<number>(0); // 当前有效价格
    const [usdPrice, setUsdPrice] = useState<{ usdValue: number; formatValue: string } | null>(null);
    const [onePayOrderEnabled, setOnePayOrderEnabled] = useState(false);
    const [isOnePayOrder, setIsOnePayOrder] = useState(false);
    const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
    // 使用 cartStore 中的方法
    const cartStore = useCartStore();
    const { incrementCartCount = () => {}, updateCartCount = () => {} } = cartStore || {};
    const router = useRouter();

    // 稳定的props，避免子组件不必要的重新渲染
    const stableKeyword = useMemo(() => itemData?.title || '', [itemData?.title]);

    // Add error handler for auth destructuring errors
    useEffect(() => {
        const handleError = (event: ErrorEvent) => {
            if (event.error && event.error.message && event.error.message.includes("Cannot destructure property 'auth' of 'e' as it is undefined")) {
                console.error('🔍 Detected auth destructuring error in 1688 page (global handler):', event.error);
                console.error('🔍 Error occurred at:', event.filename, 'line:', event.lineno, 'column:', event.colno);
                console.error('🔍 Error stack:', event.error.stack);

                // Clear potentially corrupted auth state
                try {
                    localStorage.removeItem('info');
                    localStorage.removeItem('siteData');
                    console.log('🧹 Cleared auth-related localStorage data');
                } catch (clearError) {
                    console.error('Failed to clear localStorage:', clearError);
                }

                // Prevent the error from propagating
                event.preventDefault();

                // Show user-friendly error message
                setError(dict?.detail?.authError || '认证状态异常，请刷新页面重试');
            }
        };

        window.addEventListener('error', handleError);

        return () => {
            window.removeEventListener('error', handleError);
        };
    }, []);

    // 检查商品是否已收藏
    useEffect(() => {
        const checkFavoriteStatus = async () => {
            console.log('检查收藏状态 - 开始', {
                isLoggedIn: isUserLoggedIn(),
                isTp5,
                itemData: itemData ? {
                    detail_url: itemData.detail_url,
                    has_favorite: itemData.has_favorite,
                    favorite_id: itemData.favorite_id
                } : null
            });

            // 检查用户登录状态
            if (!isUserLoggedIn()) {
                console.log('用户未登录，设置收藏状态为0');
                setIsFavorite(0); // 未登录时设置为未收藏状态
                return;
            }

            if(isTp5){
                // TP5模式：使用API检查收藏状态
                if (itemData?.detail_url) {
                    try {
                        const res = await Api.isFavorite(itemData.detail_url);
                        console.log('TP5模式 - API收藏检查结果:', res);
                        if (res.success) {
                            setIsFavorite(res.data);//不为0，则有收藏id
                            console.log('TP5模式 - 设置收藏状态为:', res.data);
                        } else {
                            setIsFavorite(0);
                            console.log('TP5模式 - API返回失败，设置收藏状态为0');
                        }
                    } catch (error) {
                        console.error('TP5模式 - 检查收藏状态失败:', error);
                        setIsFavorite(0); // 出错时设置为未收藏状态
                    }
                }
            }else{
                // TP6模式：根据商品详情接口的has_favorite字段判断
                console.log('TP6模式 - 检查has_favorite:', itemData?.has_favorite, 'favorite_id:', itemData?.favorite_id);
                if (itemData?.has_favorite === true) {
                    const favoriteId = itemData.favorite_id || 1; // 如果没有favorite_id，设置为1表示已收藏
                    setIsFavorite(favoriteId);
                    console.log('TP6模式 - has_favorite为true，设置收藏状态为:', favoriteId);
                } else {
                    setIsFavorite(0);
                    console.log('TP6模式 - has_favorite为false，设置收藏状态为0');
                }
            }

        };

        checkFavoriteStatus();
    }, [itemData?.detail_url, itemData?.has_favorite, itemData?.favorite_id, isTp5]);

    // 检查一次付款插件状态
    useEffect(() => {
        const checkOnePayOrderPlugin = async () => {
            try {
                const enabled = await isPluginEnabled('onepayorder');
                setOnePayOrderEnabled(enabled);
            } catch (error) {
                console.error('检查一次付款插件状态失败:', error);
                setOnePayOrderEnabled(false);
            }
        };

        checkOnePayOrderPlugin();
    }, []);

    // 添加浏览记录
    useEffect(() => {
        const addHistoryRecord = async () => {
            if (itemData?.num_iid) {
                try {
                    let data = {
                        "goodsname": itemData.title,
                        "goodsprice":itemData.price,
                        "goodsurl": itemData.detail_url,
                        "goodsimg": itemData.pic_url.startsWith('//') ? `https:${itemData.pic_url}` : itemData.pic_url,
                        "goodsseller": itemData.seller_info?.nick ||  '',
                        "sellerurl": itemData.seller_info?.zhuy || '',
                        "goodssite": "1688",
                        "goods_id": itemData.num_iid
                    }
                    await Api.addHistoryRecord(data)
                } catch (error) {
                    console.error('添加浏览记录失败失败:', error);
                }
            }
        };
      !isTp5 &&  addHistoryRecord();
    }, [itemData]);

    // 获取站点配置信息
    useEffect(() => {
        const loadConfig = async () => {
        const config = await getConfig();
        console.log(config.price_conversion,'siteConfig')
        setSiteConfig(config);
        };
        loadConfig();
    }, []);
    const getConfig = async () => {
        try {
            if (typeof window !== "undefined") {
            const siteData = localStorage.getItem("siteData");
            if (siteData) {
                try {
                const config = JSON.parse(siteData);
                return config; 
                } catch (parseError) {
                console.error("Failed to parse siteData:", parseError);
                }
            }
            }
            const res = await Api.getConfigList();
            return res.data.site || null; 
            
        } catch (error) {
            console.error("Failed to get configuration:", error);
            return null;
        }
    };

    // 根据数量计算批发价格
    const calculateWholesalePrice = (quantity: number): number => {
        if (!itemData || !itemData.priceRange || itemData.priceRange.length === 0) {
            return itemData?.price || 0;
        }

        // 找到适用的价格区间
        let applicablePrice = itemData.price; // 默认价格

        for (let i = 0; i < itemData.priceRange.length; i++) {
            const [minQuantity, price] = itemData.priceRange[i];
            const minQty = parseInt(minQuantity);

            if (quantity >= minQty) {
                applicablePrice = price;
            } else {
                break; // 数量不够，使用前一个价格
            }
        }

        return applicablePrice;
    };

    // 检查是否满足批发价起批量要求
    const checkMinimumWholesaleQuantity = (quantity: number): { isValid: boolean; message?: string; minQuantity?: number } => {
        if (!itemData || !itemData.priceRange || itemData.priceRange.length === 0) {
            return { isValid: true }; // 没有批发价要求，直接通过
        }

        // 获取最低起批量（第一个价格区间的数量）
        const [minQuantityStr] = itemData.priceRange[0];
        const minQuantity = parseInt(minQuantityStr);

        if (quantity < minQuantity) {
            return {
                isValid: false,
                message: dict?.detail?.remarkNoMeet?.replace('{minQuantity}', minQuantity) || `批发商品起批量为${minQuantity}件，请增加数量至${minQuantity}件或以上`,
                minQuantity
            };
        }

        return { isValid: true };
    };

    // 解析 SKU 数据的函数
    const parseSkuData = (itemData: TaobaoItemDetail) => {
        const propertyGroups = new Map<string, Set<string>>();
        const propertyImages: Record<string, Record<string, string>> = {};
        const skuMap: Record<string, number> = {};

        // 兼容不同属性名格式，甚至考虑所有可能的属性名
        const SIZE_PROPERTIES = [' 尺码 ', ' 尺码 ', 'size', 'Size', ' 鞋码 ', ' 型号 ', ' 规格 ', ' 大小 '];
        const COLOR_PROPERTIES = [' 颜色分类 ', ' 颜色分类 ', 'Color classification', ' 颜色 ', ' 颜色 ', 'color', 'Color'];

        console.log(' 完整的原始商品数据 :', itemData);
        console.log(' 中文属性列表 (cn_props_list):', itemData.cn_props_list);
        console.log(' 属性列表 (props_list):', itemData.props_list);
        console.log(' 商品属性 (props):', itemData.props);
        console.log('SKU 数据 :', itemData.skus?.sku || itemData.cn_skus?.sku);
        console.log(' 属性图片 :', itemData.props_img);

        // 直接从 SKU 数据中提取属性
         const skuDataSource =  itemData.skus?.sku || itemData.cn_skus?.sku || [];

        // 先从 SKU properties_name 中提取所有可能的属性
        skuDataSource.forEach(sku => {
            if (sku.properties_name) {
                const propPairs = sku.properties_name.split(';');
                propPairs.forEach(pair => {
                    const propParts = pair.split(':');
                    if (propParts.length >= 4) {
                        const propType = propParts[2];
                        const propValue = propParts[3];

                        // 识别是尺码还是颜色属性
                        let isSizeProp = SIZE_PROPERTIES.some(sizeProp => propType.includes(sizeProp));
                        let isColorProp = COLOR_PROPERTIES.some(colorProp => propType.includes(colorProp));

                        let propName = propType;
                        if (isSizeProp) {
                            propName = ' 尺码 ';
                        } else if (isColorProp) {
                            propName = ' 颜色分类 ';
                        }

                        // 存储属性组
                        if (!propertyGroups.has(propName)) {
                            propertyGroups.set(propName, new Set());
                        }
                        propertyGroups.get(propName)?.add(propValue);

                        // 查找匹配的图片
                        if (itemData.props_img) {
                            const propKey = propParts[0] + ":" + propParts[1];
                            if (itemData.props_img[propKey]) {
                                if (!propertyImages[propName]) {
                                    propertyImages[propName] = {};
                                }
                                propertyImages[propName][propValue] = itemData.props_img[propKey];
                            }
                        }
                    }
                });
            }
        });

        // 如果从 SKU 没有提取到属性，尝试从 cn_props_list 或 props_list 中提取
        if (propertyGroups.size === 0) {
            // 首先使用中文属性 (cn_props_list)
            if (itemData.cn_props_list) {
                console.log(' 从 cn_props_list 中提取属性 ');
                Object.entries(itemData.cn_props_list).forEach(([key, value]) => {
                    const [propName, propValue] = value.split(':');

                    let finalPropName = propName;
                    // 识别属性类型
                    if (SIZE_PROPERTIES.some(p => propName.includes(p))) {
                        finalPropName = ' 尺码 ';
                    } else if (COLOR_PROPERTIES.some(p => propName.includes(p))) {
                        finalPropName = ' 颜色分类 ';
                    }

                    if (!propertyGroups.has(finalPropName)) {
                        propertyGroups.set(finalPropName, new Set());
                    }
                    propertyGroups.get(finalPropName)?.add(propValue);

                    // 处理属性图片
                    if (itemData.props_img && itemData.props_img[key]) {
                        if (!propertyImages[finalPropName]) {
                            propertyImages[finalPropName] = {};
                        }
                        propertyImages[finalPropName][propValue] = itemData.props_img[key];
                    }
                });
            }

            // 从 props 中获取（后备方案）
            if (propertyGroups.size === 0 && itemData.props) {
                console.log(' 从 props 中提取属性 ');
                itemData.props.forEach(prop => {
                    let finalPropName = prop.name;

                    // 识别属性类型
                    if (SIZE_PROPERTIES.some(p => prop.name.includes(p))) {
                        finalPropName = ' 尺码 ';
                    } else if (COLOR_PROPERTIES.some(p => prop.name.includes(p))) {
                        finalPropName = ' 颜色分类 ';
                    }

                    if (finalPropName === ' 尺码 ' || finalPropName === ' 颜色分类 ') {
                        if (!propertyGroups.has(finalPropName)) {
                            propertyGroups.set(finalPropName, new Set());
                        }

                        // 如果值是逗号分隔的，拆分它们
                        const values = prop.value.split(',');
                        values.forEach(value => propertyGroups.get(finalPropName)?.add(value.trim()));
                    }
                });
            }
        }

        // 如果仍然没有找到任何属性，尝试硬编码处理
        if (propertyGroups.size === 0 && skuDataSource.length > 0) {
            console.log(' 使用硬编码方式提取属性 ');

            // 从 skuDataSource 中的第一个 sku 的 properties_name 获取所有属性对
            const firstSku = skuDataSource[0];
            console.log(' 第一个 SKU:', firstSku);

            if (firstSku.properties_name) {
                const propPairs = firstSku.properties_name.split(';');
                console.log(' 属性对 :', propPairs);

                // 假设第一个属性对是尺码，第二个是颜色
                if (propPairs.length >= 2) {
                    const sizeParts = propPairs[0].split(':');
                    const colorParts = propPairs[1].split(':');

                    if (sizeParts.length >= 4 && colorParts.length >= 4) {
                        // 提取所有尺码
                        propertyGroups.set(' 尺码 ', new Set());
                        skuDataSource.forEach(sku => {
                            const props = sku.properties_name.split(';');
                            if (props.length >= 1) {
                                const parts = props[0].split(':');
                                if (parts.length >= 4) {
                                    propertyGroups.get(' 尺码 ')?.add(parts[3]);
                                }
                            }
                        });

                        // 提取所有颜色
                        if (propPairs.length >= 2) {
                            propertyGroups.set(' 颜色分类 ', new Set());
                            skuDataSource.forEach(sku => {
                                const props = sku.properties_name.split(';');
                                if (props.length >= 2) {
                                    const parts = props[1].split(':');
                                    if (parts.length >= 4) {
                                        propertyGroups.get(' 颜色分类 ')?.add(parts[3]);

                                        // 查找匹配的图片
                                        if (itemData.props_img) {
                                            const propKey = parts[0] + ":" + parts[1];
                                            if (itemData.props_img[propKey]) {
                                                if (!propertyImages[' 颜色分类 ']) {
                                                    propertyImages[' 颜色分类 '] = {};
                                                }
                                                propertyImages[' 颜色分类 '][parts[3]] = itemData.props_img[propKey];
                                            }
                                        }
                                    }
                                }
                            });
                        }
                    }
                }
            }
        }

        // 构建 SKU 库存映射
        skuDataSource.forEach((sku) => {
            // 处理 properties_name
            if (sku.properties_name) {
                const propertyValues: string[] = [];
                const propPairs = sku.properties_name.split(';');

                propPairs.forEach(pair => {
                    const parts = pair.split(':');
                    if (parts.length >= 4) {
                        propertyValues.push(parts[3]);
                    }
                });

                // 使用所有属性值组合作为 key
                if (propertyValues.length > 0) {
                    const skuKey = propertyValues.sort().join('＊');
                    skuMap[skuKey] = sku.quantity;
                }
            }
        });

        // 转换为数组格式返回，并确保尺码在前，颜色分类在后
        const propertyGroupsArray = Array.from(propertyGroups.entries())
            .sort(([a, _]) => {
                // 尺码排在前面
                if (a === ' 尺码 ') return -1;
                if (a === ' 颜色分类 ') return 1;
                return 0;
            })
            .map(([name, values]) => ({
                name,
                values: Array.from(values),
                images: propertyImages[name] || {}
            }));

        console.log(' 最终解析后的 SKU 数据 :', {
            propertyGroups: propertyGroupsArray,
            skuMap
        });

        return {
            propertyGroups: propertyGroupsArray,
            skuMap,
        };
    };

    // 更新选中 SKU 时的库存
    const updateSelectedSkuStock = (selectedProperties: Record<string, string>) => {


        if (!itemData) {
            setCurrentStock(0);
            return;
        }

        const skuData = parseSkuData(itemData);

        if(!skuData.propertyGroups.length){
            setCurrentStock(itemData.num);
            return;
        }
        // 获取所有选中的属性值
        const selectedValues = Object.values(selectedProperties).filter(Boolean);

        // 如果没有选择任何SKU，或者选择不完整，计算总库存
        if (selectedValues.length === 0 || selectedValues.length < skuData.propertyGroups.length) {
            const totalStock = Object.values(skuData.skuMap).reduce((sum, stock) => sum + stock, 0);
            setCurrentStock(totalStock);
            return;
        }

        // 使用属性值组合查找特定SKU的库存
        const skuKey = selectedValues.sort().join('＊');
        const stock = skuData.skuMap[skuKey] || 0;

        console.log('Selected SKU Stock:', {
            selectedProperties,
            skuKey,
            stock
        });

        setCurrentStock(stock);
    };

    // 更新选中 SKU 时的价格
    const updateSelectedSkuPrice = (selectedProperties: Record<string, string>) => {
        if (!itemData) {
            setCurrentPrice(0);
            return;
        }

        const skuData = parseSkuData(itemData);

        // 获取所有选中的属性值
        const selectedValues = Object.values(selectedProperties).filter(Boolean);

        // 如果没有选择任何SKU，或者选择不完整，使用基础价格或批发价
        if (selectedValues.length === 0 || selectedValues.length < skuData.propertyGroups.length) {
            const basePrice = calculateWholesalePrice(quantity);
            setCurrentPrice(basePrice);
            return;
        }

        // 查找匹配的SKU价格
        const skuDataSource = itemData.cn_skus?.sku || itemData.skus?.sku || [];

        // 构建选中属性的匹配逻辑
        const selectedSkuInfo = skuDataSource.find(sku => {
            if (!sku.properties_name) return false;

            const propPairs = sku.properties_name.split(';');
            const skuValues: string[] = [];

            propPairs.forEach(pair => {
                const propParts = pair.split(':');
                if (propParts.length >= 4) {
                    skuValues.push(propParts[3]);
                }
            });

            // 检查是否所有选中的值都在这个SKU中
            return selectedValues.every(value => skuValues.includes(value));
        });

        // 如果找到匹配的SKU，使用SKU价格，否则使用批发价计算
        const price = selectedSkuInfo?.price || calculateWholesalePrice(quantity);
        setCurrentPrice(price);

        console.log('Selected SKU Price:', {
            selectedProperties,
            selectedSkuInfo,
            price,
            quantity
        });
    };

    useEffect(() => {
        if (itemData) {
            // 初始化时计算总库存和价格
            updateSelectedSkuStock({});
            updateSelectedSkuPrice({});
        }
    }, [itemData]);

    useEffect(() => {
        if (itemData) {
            updateSelectedSkuStock(selectedSku);
            updateSelectedSkuPrice(selectedSku);
        }
    }, [selectedSku, itemData]);

    // 监听数量变化，更新当前价格
    useEffect(() => {
        if (itemData) {
            // 数量变化时，需要重新计算价格（考虑批发价和SKU价格）
            updateSelectedSkuPrice(selectedSku);
        }
    }, [quantity, itemData, selectedSku]);

    // 监听数量变化，更新运费
    useEffect(() => {
        if (itemData && itemData.num_iid) {
            updateEstimateFee(quantity);
        }
    }, [quantity, itemData]);

    // 获取美元价格
    useEffect(() => {
        const fetchUsdPrice = async () => {
            if (currentPrice || itemData?.price) {
                const price = Number(currentPrice || itemData?.price || 0);
                const usdPriceResult = await formatUSDPrice(price);
                setUsdPrice(usdPriceResult);
            } else {
                setUsdPrice(null);
            }
        };
        fetchUsdPrice();
    }, [currentPrice, itemData?.price]);

    // 自动选择第一个可用的SKU
    const selectFirstAvailableSku = (itemData: TaobaoItemDetail) => {
        const skuData = parseSkuData(itemData);

        // 如果没有SKU属性组，直接返回
        if (skuData.propertyGroups.length === 0) {
            return;
        }

        // 找到第一个有库存的SKU组合
        const availableSkuEntry = Object.entries(skuData.skuMap).find(([, stock]) => stock > 0);

        if (!availableSkuEntry) {
            console.log('没有找到有库存的SKU');
            return;
        }

        const [skuKey] = availableSkuEntry;
        const skuValues = skuKey.split('＊');

        // 构建选中的SKU对象
        const newSelectedSku: SelectedSku = {};

        // 将SKU值按属性组顺序分配
        skuData.propertyGroups.forEach((group) => {
            // 找到属于当前属性组的值
            const matchingValue = group.values.find(value => skuValues.includes(value));
            if (matchingValue) {
                newSelectedSku[group.name] = matchingValue;
            }
        });

        console.log('自动选择的SKU:', newSelectedSku);

        // 设置选中的SKU
        setSelectedSku(newSelectedSku);

        // 如果有对应的图片，更新显示图片
        skuData.propertyGroups.forEach(group => {
            const selectedValue = newSelectedSku[group.name];
            if (selectedValue && group.images[selectedValue]) {
                setSelectedImage(group.images[selectedValue]);
            }
        });
    };

    // 处理 SKU 选择
    const handleSkuSelect = (propertyName: string, value: string) => {
        setSelectedSku(prev => {
            const newSku = { ...prev, [propertyName]: value };

            // 更新库存和价格
            updateSelectedSkuStock(newSku);
            updateSelectedSkuPrice(newSku);

            // 如果是有图片的属性，更新显示图片
            const skuData = parseSkuData(itemData!);
            const propertyGroup = skuData.propertyGroups.find(group => group.name === propertyName);
            if (propertyGroup?.images[value]) {
                setSelectedImage(propertyGroup.images[value]);
            }

            return newSku;
        });
    };

    const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
        if (!imageRef.current) return;

        const { left, top, width, height } = imageRef.current.getBoundingClientRect();
        const x = ((e.clientX - left) / width) * 100;
        const y = ((e.clientY - top) / height) * 100;

        setMousePosition({ x, y });
    };

    useEffect(() => {
        const fetchData = async () => {
            if (!url) {
                setError(dict?.detail?.emptyUrl || ' 商品 URL 不能为空 ');
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                // 加载字典
                const dictionary = await getDictionary(lng);
                setDict(dictionary);

                // 获取商品详情
                console.log('🔍 Calling getItemDetail API for 1688 product...');
                const res = await Api.getItemDetail({
                    tp: '1688',
                    url: url,
                    seller_info: true,
                });
                console.log('🔍 getItemDetail API response:', res);

                if (res.success) {
                    let data = res.data;
                    console.log(' 原始 API 响应 :', data);

                    // 确保关键数据字段存在
                    if (!data.props_list && data.cn_props_list) {
                        data.props_list = data.cn_props_list;
                    }

                    if (!data.skus && data.cn_skus) {
                        data.skus = data.cn_skus;
                    }

                    // 确保从 props 中提取尺码和颜色信息
                    if (data.props && !data.props.some((p: { name: string }) => p.name === ' 尺码 ' || p.name === ' 颜色分类 ' || p.name === ' 尺码 ' || p.name === ' 颜色分类 ')) {
                        // 尝试从 cn_props_list 中提取属性数据
                        const sizeValues = new Set<string>();
                        const colorValues = new Set<string>();

                        Object.values(data.cn_props_list || {}).forEach((value: unknown) => {
                            if (typeof value === 'string') {
                                const [propName, propValue] = value.split(':');
                                // 兼容不同格式
                                if (propName === ' 尺码 ' || propName === ' 尺码 ') {
                                    sizeValues.add(propValue);
                                } else if (propName === ' 颜色分类 ' || propName === ' 颜色分类 ') {
                                    colorValues.add(propValue);
                                }
                            }
                        });

                        if (sizeValues.size > 0) {
                            data.props = [...(data.props || []), {
                                name: ' 尺码 ',
                                value: Array.from(sizeValues).join(',')
                            }];
                        }

                        if (colorValues.size > 0) {
                            data.props = [...(data.props || []), {
                                name: ' 颜色分类 ',
                                value: Array.from(colorValues).join(',')
                            }];
                        }
                    }

                    console.log('🔍 Calling estimate1688 API...');
                    const estimaterule = await Api.estimate1688({
                        iid: data.num_iid,
                        num: quantity,
                    });
                    console.log('🔍 estimate1688 API response:', estimaterule);
                    if(estimaterule.success){
                        data.estimaterule = estimaterule.data;
                        // 初始化运费
                        if (data.estimaterule) {
                            const { firstFee, firstUnit, nextFee, nextUnit } = data.estimaterule;
                            const firstFeeNum = parseFloat(firstFee);
                            const firstUnitNum = parseFloat(firstUnit);
                            const nextFeeNum = parseFloat(nextFee);
                            const nextUnitNum = parseFloat(nextUnit);
                            if(firstUnitNum === 0){
                                data.express_fee = firstFeeNum;
                            }else if(quantity <= firstUnitNum){
                                data.express_fee = firstFeeNum;
                            }else{
                                data.express_fee = firstFeeNum + (quantity - nextUnitNum) * nextFeeNum;
                            }
                            setCurrentExpressFee(data.express_fee);
                        }
                    }
                    setItemData(data);

                    // 初始化当前价格
                    const initialPrice = data.priceRange && data.priceRange.length > 0
                        ? data.priceRange[0][1] // 使用第一个价格区间的价格
                        : data.price;
                    setCurrentPrice(initialPrice);

                    // 设置第一张图片为选中图片
                    if (data?.item_imgs?.[0]?.url) {
                        setSelectedImage(data.item_imgs[0].url);
                    } else if (data?.pic_url) {
                        setSelectedImage(data.pic_url);
                    }

                    // 在数据加载后尝试解析 SKU 并自动选择第一个
                    if (data) {
                        console.log(' 尝试解析 SKU 数据 ');
                        parseSkuData(data);
                        // 自动选择第一个可用的SKU
                        selectFirstAvailableSku(data);
                    }
                }

            } catch (err) {
                console.error(' 获取商品详情失败 ', err);

                // Add specific handling for the auth destructuring error
                if (err && typeof err === 'object' && 'message' in err && typeof err.message === 'string' && err.message.includes("Cannot destructure property 'auth' of 'e' as it is undefined")) {
                    // Clear potentially corrupted auth state
                    try {
                        localStorage.removeItem('info');
                        localStorage.removeItem('siteData');
                    } catch (clearError) {
                        console.error('Failed to clear localStorage:', clearError);
                    }

                    setError(dict?.detail?.authError || '认证状态异常，请刷新页面重试');
                } else {
                    setError(err instanceof Error ? err.message : ' 获取商品详情失败 ');
                }
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [url, lng]);

    const handleQuantityChange = async (delta: number) => {
        const newQuantity = quantity + delta;
        if (newQuantity >= 1 && newQuantity <= currentStock) {
            setQuantity(newQuantity);
            // 更新运费
            await updateEstimateFee(newQuantity);
        }
    };
    
    const updateEstimateFee = async (newQuantity:number) =>{
        if (!itemData?.num_iid) return 0;

        try {
            // 调用1688运费接口
            const estimateResponse = await Api.estimate1688({
                iid: parseInt(itemData.num_iid),
                num: newQuantity
            });

            console.log('🔍 estimate1688 API response for quantity', newQuantity, ':', estimateResponse);

            if (estimateResponse.success && estimateResponse.data) {
                // 更新运费规则
                itemData.estimaterule = estimateResponse.data;

                // 计算新的运费
                const { firstFee, firstUnit, nextFee, nextUnit } = estimateResponse.data;
                const firstFeeNum = parseFloat(firstFee);
                const firstUnitNum = parseFloat(firstUnit);
                const nextFeeNum = parseFloat(nextFee);
                const nextUnitNum = parseFloat(nextUnit);

                let newExpressFee = 0;
                if(firstUnitNum === 0){
                    newExpressFee = firstFeeNum;
                }else if(newQuantity <= firstUnitNum){
                    newExpressFee = firstFeeNum;
                }else{
                    newExpressFee = firstFeeNum + (newQuantity - nextUnitNum) * nextFeeNum;
                }

                itemData.express_fee = newExpressFee;
                setCurrentExpressFee(newExpressFee);
                return newExpressFee;
            }
        } catch (error) {
            console.error('更新运费失败:', error);
        }

        // 如果API调用失败，使用本地计算作为备用方案
        if (itemData?.estimaterule) {
            const { firstFee , firstUnit , nextFee , nextUnit  } = itemData.estimaterule;
            const firstFeeNum = parseFloat(firstFee);
            const firstUnitNum = parseFloat(firstUnit);
            const nextFeeNum = parseFloat(nextFee);
            const nextUnitNum = parseFloat(nextUnit);
            if(firstUnitNum === 0){
                itemData.express_fee = firstFeeNum;
            }else if(newQuantity <= firstUnitNum){
                itemData.express_fee = firstFeeNum;
            }else{
                itemData.express_fee = firstFeeNum + (newQuantity - nextUnitNum) * nextFeeNum;
            }
            setCurrentExpressFee(itemData.express_fee);
            return itemData.express_fee;
        }
        return 0;
    };

    let lock = false;//防用户连点，加锁
    const handleFavorite = async () => {
        if(lock) return;
        lock = true
        if (!itemData) return;

        // 检查用户登录状态
        if (!isUserLoggedIn()) {
            Toast.error('请登录后重试');
            const currentPath = window.location.pathname + window.location.search;
            const loginUrl = `/${lng}/login?callback=${encodeURIComponent(currentPath)}`;
            router.push(loginUrl);
            lock = false;
            return;
        }

        try {
            if (isFavorite!==0) {
                // 如果已收藏,则先获取收藏ID再删除
                    const res = await Api.deleteFavorite(isFavorite);
                    if (res.success) {
                        setIsFavorite(0);
                        // 在TP6模式下，同时更新itemData中的has_favorite字段
                        if (!isTp5 && itemData) {
                            setItemData({
                                ...itemData,
                                has_favorite: false,
                                favorite_id: 0
                            });
                        }
                        messageApi.success(dict.detail?.favoriteRemoved || 'Removed from favorites');

                    }
            } else {
                // 如果未收藏,则添加收藏
                const params = {
                    goodsname: itemData.title || "",
                    goodsprice: itemData.price || 0,
                    goodsurl: itemData.detail_url || "",
                    goodsimg: itemData.pic_url || "",
                    goodsseller: itemData.seller_info?.shop_name || itemData.seller_info?.nick || itemData.nick,
                    sellerurl: itemData.seller_info?.zhuy || "",
                    goodssite: "taobao",
                    mall_goods_id: 0,
                };

                const res = await Api.addFavorite(params);
                if (res.success) {
                    const favoriteId = res.data.id;
                    setIsFavorite(favoriteId);
                    // 在TP6模式下，同时更新itemData中的has_favorite字段
                    if (!isTp5 && itemData) {
                        setItemData({
                            ...itemData,
                            has_favorite: true,
                            favorite_id: favoriteId
                        });
                    }
                    messageApi.success(dict.detail?.favoriteAdded || 'Added to favorites');
                }
            }
        } catch (error) {
            console.error(dict.detail?.favoriteOperationFailed || 'Favorite operation failed:', error);
            messageApi.error(dict.detail?.operationFailed || 'Operation failed, please try again');
        }finally{
            lock = false;
        }
    };

    // 添加一个辅助函数来检查是否所有必需属性都已选择
    const isAllPropertiesSelected = (selectedSku: SelectedSku) => {
        if (!itemData) return false;

        // 获取商品所有的必选属性
        const skuData = parseSkuData(itemData);
        const requiredProps = skuData.propertyGroups.map(group => group.name);

        // 确保所有必选属性都已被选择
        return requiredProps.every(propName => selectedSku[propName]);
    };

    // 创建并执行抛物线动画
    const createParabola = (startX: number, startY: number, endX: number, endY: number) => {
        // 创建小球元素
        const ball = document.createElement('div');
        ball.className = 'parabola-ball';

        // 创建商品图片小球（使用商品缩略图）
        if (itemData?.pic_url) {
            const productImage = document.createElement('img');
            productImage.src = itemData.pic_url.startsWith('//') ? `https:${itemData.pic_url}` : itemData.pic_url;
            productImage.className = 'product-image';
            ball.appendChild(productImage);
        }

        document.body.appendChild(ball);

        // 设置小球初始位置
        ball.style.left = `${startX}px`;
        ball.style.top = `${startY}px`;

        // 设置小球初始缩放
        ball.style.transform = 'scale(0.2)';
        ball.style.opacity = '0';

        // 计算控制点（改进的抛物线路径，更高的顶点让动画更有弧度）
        const controlX = (startX + endX) / 2;
        const controlY = Math.min(startY, endY) - 250; // 增加高度让抛物线更明显

        // 添加延迟，使按钮点击和动画之间有短暂停顿
        setTimeout(() => {
            // 应用开始动画
            ball.style.transition = 'transform 0.2s ease-out, opacity 0.2s ease-out';
            ball.style.transform = 'scale(1)';
            ball.style.opacity = '1';

            // 在开始动画完成后启动抛物线动画
            setTimeout(() => {
                ball.style.transition = ''; // 清除过渡效果

                // 动画开始时间
                const startTime = Date.now();
                const duration = 700; // 稍微缩短持续时间，让动画更快

                // 动画函数
                const animate = () => {
                    const currentTime = Date.now();
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    if (progress < 1) {
                        // 贝塞尔曲线计算当前位置
                        const x = quadraticBezier(startX, controlX, endX, progress);
                        const y = quadraticBezier(startY, controlY, endY, progress);

                        // 更新小球位置
                        ball.style.left = `${x}px`;
                        ball.style.top = `${y}px`;

                        // 改进的动画效果：旋转和脉动
                        const rotate = progress * 720; // 两圈旋转
                        const scaleBase = 1 - 0.3 * progress; // 基础缩放
                        const scalePulse = 0.05 * Math.sin(progress * Math.PI * 6); // 脉动效果
                        const finalScale = scaleBase + scalePulse;

                        ball.style.transform = `scale(${finalScale}) rotate(${rotate}deg)`;

                        // 添加轨迹效果（可选）
                        if (progress > 0.1 && Math.random() > 0.7) {
                            const trail = document.createElement('div');
                            trail.className = 'parabola-trail';
                            trail.style.left = `${x}px`;
                            trail.style.top = `${y}px`;
                            trail.style.opacity = `${0.6 - progress * 0.6}`;
                            trail.style.transform = `scale(${0.5 * Math.random() + 0.3})`;
                            document.body.appendChild(trail);

                            // 淡出并移除轨迹元素
                            setTimeout(() => {
                                trail.style.opacity = '0';
                                setTimeout(() => trail.remove(), 300);
                            }, 100);
                        }

                        requestAnimationFrame(animate);
                    } else {
                        // 达到终点时的弹跳效果
                        ball.style.transition = 'transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.3s ease';
                        ball.style.transform = 'scale(1.5)';
                        ball.style.opacity = '0';

                        // 创建终点处的涟漪效果
                        const ripple = document.createElement('div');
                        ripple.className = 'cart-ripple';
                        ripple.style.left = `${endX - 15}px`; // 调整位置到购物车图标中心
                        ripple.style.top = `${endY - 15}px`;
                        document.body.appendChild(ripple);

                        // 购物车图标跳动效果
                        const cartIcon = document.querySelector('.cart-icon');
                        if (cartIcon) {
                            cartIcon.classList.add('cart-bounce');
                            setTimeout(() => cartIcon.classList.remove('cart-bounce'), 500);
                        }

                        // 动画完成后移除元素
                        setTimeout(() => {
                            ball.remove();
                            setTimeout(() => ripple.remove(), 600);
                        }, 300);
                    }
                };

                // 二次贝塞尔曲线函数
                const quadraticBezier = (p0: number, p1: number, p2: number, t: number) => {
                    return (1 - t) * (1 - t) * p0 + 2 * (1 - t) * t * p1 + t * t * p2;
                };

                // 开始动画
                requestAnimationFrame(animate);
            }, 200); // 等待开始动画完成
        }, 50); // 短暂延迟
    };

    // 处理加入购物车
    const handleAddToCart = async (e: React.MouseEvent<HTMLButtonElement>) => {
        if (!itemData || !isAllPropertiesSelected(selectedSku) || currentStock <= 0 || addingToCart) {
            return;
        }

        // 检查是否满足批发价起批量要求
        const wholesaleCheck = checkMinimumWholesaleQuantity(quantity);
        if (!wholesaleCheck.isValid) {
            Toast.error(wholesaleCheck.message || dict?.detail?.quantityNotMeet || '数量不满足起批量要求');
            return;
        }

        // 获取点击位置作为小球起点
        const btnRect = e.currentTarget.getBoundingClientRect();
        const startX = e.clientX || (btnRect.left + btnRect.width / 2);
        const startY = e.clientY || (btnRect.top + btnRect.height / 2);

        // 获取目标位置（右上角购物车图标位置，若没有则取窗口右上角）
        const cartIconPos = document.querySelector('.cart-icon-wrapper')?.getBoundingClientRect() || {
            right: window.innerWidth - 20,
            top: 20
        };
        const endX = cartIconPos.right;
        const endY = cartIconPos.top;

        try {
            setAddingToCart(true);

            // 执行抛物线动画
            createParabola(startX, startY, endX, endY);

            // 构建 SKU 属性字符串
            const skuText = Object.entries(selectedSku)
                .map(([key, value]) => `${key}:${value}`)
                .join(';');

            // 获取选中 SKU 的价格
            const selectedSkuInfo = itemData.skus?.sku?.find(sku => {
                const skuProperties = sku.properties.split(';').map(prop => {
                    const [, value] = itemData.props_list[prop].split(':');
                    return value;
                });
                return Object.values(selectedSku).every(value => skuProperties.includes(value));
            });

            const finalPrice = selectedSkuInfo?.price || currentPrice || itemData.price;

            const cartItem = {
                goodsname: itemData.title,
                goodsprice: finalPrice,
                sendprice: itemData.express_fee || 0, // express_fee，如果为null则默认为0
                goodsnum: quantity,
                goodsurl: itemData.detail_url,
                goodsimg: itemData.pic_url,
                goodsseller: itemData.seller_info?.shop_name || itemData.seller_info?.nick || itemData.nick,
                sellerurl: itemData.seller_info?.zhuy || "",
                goodssite: "1688", // api_type
                sku_id: skuText,
                skuname: skuText,
                goodsweight: itemData.item_weight || 0,
                goodsvolume: itemData.volume || 0,
                isspec: Object.keys(selectedSku).length > 0 ? 1 : 0,
                goodstype: 0,
                goodsremark: remark,
                mall_goods_id: 0,
                flash_sale_id: "",
                originalprice: itemData.orginal_price || 0,
                clienttype: "h5",
                spare_sku_name: "",
                spare_sku_id: selectedSkuInfo?.sku_id || "",
                quickbuy: 1,
                secret_key: md5(String(finalPrice) + ":" + String(quantity)),
                cid: "",
                checked: true
            };

            if (!isUserLoggedIn()) {
                // 将购物车数据存储起来，登录后再掉加入购物车接口，再批量加入购物车

                // 获取本地存储的购物车数据
                const localCartItems = JSON.parse(localStorage.getItem('localCartItems') || '[]');

                // 添加新商品到本地购物车
                localCartItems.push(cartItem);
                
                // 保存到本地存储
                localStorage.setItem('localCartItems', JSON.stringify(localCartItems));

                updateCartCount(localCartItems.length)

            }else{
                // 调用加入购物车 API
                await Api.addCart(cartItem);
                // 使用 zustand 更新购物车计数
                incrementCartCount();
            }

            // 提示成功
            setCartSuccess(true);
        } catch (error) {
            console.error(' 加入购物车失败 :', error);
            Toast.error(' 加入购物车失败，请重试 ');
        } finally {
            setAddingToCart(false);
        }
    };

    // 使用 useEffect 监听 cartSuccess 状态变化，显示成功消息
    useEffect(() => {
        if (cartSuccess) {
            messageApi.open({
                type: 'success',
                content: dict?.detail?.addToCartSuccess || ' 成功加入购物车！',
            });
            setCartSuccess(false);
        }
    }, [cartSuccess, messageApi]);

    // 在 handleAddToCart 函数前添加新的 handleBuyNow 函数
    const handleBuyNow = async () => {
        if (!itemData || !isAllPropertiesSelected(selectedSku) || currentStock <= 0) {
            return;
        }

        // 检查是否满足批发价起批量要求
        const wholesaleCheck = checkMinimumWholesaleQuantity(quantity);
        if (!wholesaleCheck.isValid) {
            Toast.error(wholesaleCheck.message || dict?.detail?.quantityNotMeet || '数量不满足起批量要求');
            return;
        }

        // 检查用户登录状态
        if (!isUserLoggedIn()) {
            Toast.error('请登录后重试');
            // 可选：跳转到登录页面
            const currentPath = window.location.pathname + window.location.search;
            const loginUrl = `/${lng}/login?callback=${encodeURIComponent(currentPath)}`;
            router.push(loginUrl);
            return;
        }

        try {
            // 确保运费被正确计算
            await updateEstimateFee(quantity);

            // 构建 SKU 属性字符串
            const skuText = Object.entries(selectedSku)
                .map(([key, value]) => `${key}:${value}`)
                .join(';');

            // 获取选中 SKU 的价格
            const selectedSkuInfo = itemData.skus?.sku?.find(sku => {
                const skuProperties = sku.properties.split(';').map(prop => {
                    const [, value] = itemData.props_list[prop].split(':');
                    return value;
                });
                return Object.values(selectedSku).every(value => skuProperties.includes(value));
            });

            // 构建购物车项数据
            const finalPrice = selectedSkuInfo?.price || currentPrice || itemData.price;

            const cartItem = {
                goodsname: itemData.title,
                goodsprice: finalPrice,
                sendprice: itemData.express_fee || 0, // 如果运费为null则默认为0
                goodsnum: quantity,
                goodsurl: itemData.detail_url,
                goodsimg: itemData.pic_url,
                goodsseller: itemData.seller_info?.shop_name || itemData.seller_info?.nick || itemData.nick,
                sellerurl: itemData.seller_info?.zhuy || "",
                goodssite: "1688",
                sku_id: skuText,
                skuname: skuText,
                goodsweight: itemData.item_weight || 0,
                goodsvolume: itemData.volume || 0,
                isspec: Object.keys(selectedSku).length > 0 ? 1 : 0,
                goodstype: 0,
                goodsremark: remark,
                mall_goods_id: 0,
                flash_sale_id: "",
                originalprice: itemData.orginal_price || 0,
                clienttype: "h5",
                spare_sku_name: "",
                spare_sku_id: selectedSkuInfo?.sku_id || "",
                quickbuy: 1,
                secret_key: md5(String(finalPrice) + ":" + String(quantity)),
                cid: "",
                checked: true
            };

            // 调用加入购物车 API
            const response = await Api.addCart(cartItem);

            if (response.success && response.data) {
                // 获取返回的购物车ID
                const cart_ids = response.data.cart_id;

                // 构建商品数据
                const itemDataForConfirm = {
                    goodsname: itemData.title,
                    goodsprice: finalPrice,
                    goodsnum: quantity,
                    goodsurl: itemData.detail_url,
                    goodsimg: itemData.pic_url,
                    goodsseller: itemData.seller_info?.shop_name || itemData.seller_info?.nick || itemData.nick,
                    skuname: skuText,
                    goodsweight: itemData.item_weight || 0,
                    goodsvolume: itemData.volume || 0,
                    goodsremark: remark,
                    originalprice: itemData.orginal_price || 0,
                    sendprice: itemData.express_fee || 0, // 添加运费信息
                    goodssn: itemData.num_iid || '', // 添加商品编号
                    num_iid: itemData.num_iid || '' // 添加商品ID
                };

                // 将商品数据编码为 URL 参数
                console.log('itemDataForConfirm before encoding:', itemDataForConfirm);
                const jsonString = JSON.stringify(itemDataForConfirm);
                console.log('JSON string:', jsonString);
                const encodedItemData = encodeURIComponent(jsonString);
                console.log('Encoded itemData:', encodedItemData);

                // 跳转到确认订单页面，带上商品数据和购物车ID
                // 如果启用了一次付款且用户选择了一次付款，则跳转到专用的一次付款确认页面
                const confirmUrl = onePayOrderEnabled && isOnePayOrder
                    ? `/${lng}/onepayorder-confirm?itemData=${encodedItemData}&cart_ids=${cart_ids}`
                    : `/${lng}/confirm?itemData=${encodedItemData}&cart_ids=${cart_ids}`;
                router.push(confirmUrl);
            } else {
                // 检查是否是登录相关的错误 - 使用标准化错误码
                if (response.code === ERROR_CODES.TOKEN_INVALID || response.msg?.includes('登录') || response.msg?.includes('未授权') || response.msg?.includes('token')) {
                    Toast.error('请登录后重试');
                    const currentPath = window.location.pathname + window.location.search;
                    const loginUrl = `/${lng}/login?callback=${encodeURIComponent(currentPath)}`;
                    router.push(loginUrl);
                } else {
                    // 显示具体的错误信息，如果没有则显示默认信息
                    Toast.error(response.msg || '购买失败，请重试');
                }
            }
        } catch (error: any) {
            console.error('购买失败:', error);

            // Add specific handling for the auth destructuring error
            if (error && typeof error === 'object' && 'message' in error && typeof error.message === 'string' && error.message.includes("Cannot destructure property 'auth' of 'e' as it is undefined")) {
                console.error('🔍 Detected auth destructuring error in handleBuyNow:', error);
                console.error('🔍 Error stack:', 'stack' in error ? error.stack : 'No stack trace available');

                // Clear potentially corrupted auth state
                try {
                    localStorage.removeItem('info');
                    localStorage.removeItem('siteData');
                    console.log('🧹 Cleared auth-related localStorage data');
                } catch (clearError) {
                    console.error('Failed to clear localStorage:', clearError);
                }

                Toast.error(dict?.detail?.authError || '认证状态异常，请刷新页面重试');
                return;
            }

            // 检查是否是登录相关的错误 - 使用标准化错误码
            if (error.response?.status === 401 ||
                error.response?.data?.code === ERROR_CODES.TOKEN_INVALID ||
                error.message?.includes('登录') ||
                error.message?.includes('未授权') ||
                error.message?.includes('token')) {
                ToastHelper.showAuthError(lng);
                const currentPath = window.location.pathname + window.location.search;
                const loginUrl = `/${lng}/login?callback=${encodeURIComponent(currentPath)}`;
                router.push(loginUrl);
            } else {
                // 显示具体的错误信息，如果没有则显示默认信息
                const errorMessage = error.response?.data?.msg || error.message || ToastHelper.getLocalizedMessage('buy_failed', lng);
                Toast.error(errorMessage);
            }
        }
    };

    if (loading) return <Loading />;
    if (error) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <p>{error}</p>
                </div>
            </div>
        );
    }
    if (!itemData) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
                    <p>{dict?.detail?.productNotFound || ' 未找到商品信息 '}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 relative">
            {contextHolder}
            <div className="container mx-auto px-4 py-8 ">
                {showZoom && (
                    <div className="fixed left-1/2 top-20 inset-0 z-50 w-200 h-200">
                        <div
                            className="absolute w-full h-full bg-cover bg-no-repeat"
                            style={{
                                backgroundImage: `url(${prepareImageForNextJs(selectedImage)})`,
                                backgroundPosition: `${mousePosition.x}% ${mousePosition.y}%`,
                                backgroundSize: '200%',
                                borderRadius: '10px'
                            }}
                        />
                    </div>
                )}

                <div className="flex flex-col lg:flex-row gap-8">
                    {/* 左侧区域：商品图片 + 商品参数 */}
                    <div className="lg:w-1/2 flex flex-col gap-8">
                        {/* 商品图片 */}
                        <div className="bg-white rounded-lg p-6">
                            <div className="flex flex-col md:flex-row gap-4">
                                {/* 左侧缩略图列表 */}
                                <div className="order-2 md:order-1">
                                    <div className="flex md:flex-col gap-3 overflow-x-auto md:overflow-x-visible pb-2 md:pb-0 scrollbar-hide">
                                        {itemData.item_imgs?.filter(img => img?.url)?.slice(0, 6)?.map((img, index) => (
                                            <div
                                                key={index}
                                                className={`relative flex-shrink-0 w-[80px] h-[80px] overflow-hidden rounded cursor-pointer border-2 transition-all duration-200 hover:scale-105 active:scale-95 ${selectedImage === img.url ? 'border-orange-500' : 'border-gray-200'}`}
                                                onMouseOver={() => img.url && setSelectedImage(img.url)}
                                            >
                                                <Image
                                                    src={prepareImageForNextJs(img.url)}
                                                    alt={`${itemData.title || ' 商品图片 '} - 图片 ${index + 1}`}
                                                    fill
                                                    className="object-cover"
                                                    sizes="80px"
                                                />
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* 右侧大图 */}
                                <div className="flex-1 order-1 md:order-2">
                                    <div
                                        ref={imageRef}
                                        className="relative aspect-square w-full overflow-hidden rounded-lg group"
                                        onMouseEnter={() => setShowZoom(true)}
                                        onMouseLeave={() => setShowZoom(false)}
                                        onMouseMove={handleMouseMove}
                                    >
                                        {selectedImage && (
                                            <div className="relative w-full h-full transition-opacity duration-300 ease-in-out">
                                                <Image
                                                    src={prepareImageForNextJs(selectedImage)}
                                                    alt={itemData.title || ' 商品图片 '}
                                                    fill
                                                    className="object-cover transition-transform duration-300"
                                                    priority
                                                    sizes="(max-width: 768px) 100vw, 50vw"
                                                />
                                                {showZoom && (
                                                    <div
                                                        className="absolute w-32 h-32 border-2 border-orange-500 bg-white/30 pointer-events-none"
                                                        style={{
                                                            left: `calc(${mousePosition.x}% - 4rem)`,
                                                            top: `calc(${mousePosition.y}% - 4rem)`,
                                                        }}
                                                    />
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* AI比价模块 */}
                        <PriceParity dict={dict} lng={lng} keyword={itemData?.title || ''} />

                        {/* 参数信息 */}
                        <div className="bg-white rounded-lg p-6 sticky top-30">
                            <h2 className="text-xl font-bold mb-6 flex items-center">
                                 {dict.detail.specifications}
                            </h2>

                            <div className="space-y-4">
                                {itemData?.props && itemData.props.length > 0 && (
                                    <div className="rounded-lg border border-gray-100 overflow-hidden">
                                        <div className="p-4">
                                            <div className="grid grid-cols-2 gap-2">
                                                {itemData.props.map((prop, index) => (
                                                    <div
                                                        key={index}
                                                        className="flex items-start text-sm"
                                                    >
                                                        <span className="text-gray-500 flex-shrink-0 w-20">{prop.name}</span>
                                                        <span title={prop.value} className="text-gray-900 ml-2 line-clamp-2">{prop.value}</span>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* 右侧区域：商品信息 + 商品详情 */}
                    <div className="flex-1 flex flex-col gap-8 w-1/2">
                        {/* 商品信息 */}
                        <div className="bg-white rounded-lg p-6 relative">
                            <div className="space-y-3">
                                <h1 className="text-xl text-gray-900 font-bold">{itemData.title}</h1>

                                <div className="text-gray-500 text-sm flex items-center gap-2">
                                    <i className="i-ant-design:link-outlined w-4 h-4 text-orange-500" />
                                    <Link href={itemData.detail_url?itemData.detail_url:''} target='_blank' className="text-orange-500 transition-colors"> {dict.detail.productLink} </Link>
                                    <button
                                        onClick={() => window.location.reload()}
                                        className="text-orange-500 transition-colors"
                                    >
                                        <i className="i-heroicons-arrow-path w-4 h-4 mr-2" />
                                        <span className='text-sm'> {dict.detail.refresh} </span>
                                    </button>
                                </div>
                                {/* 价格显示区域 */}
                                <div className="flex items-baseline space-x-2">
                                    {(() => {
                                        const localCurrency = localStorage.getItem('selectedCurrency') || 'CNY'; // 默认人民币
                                        const price = currentPrice || itemData.price;
                                        const formatted = formatCurrency(price);
                                        const isCNY = localCurrency === 'CNY';
                                        
                                        // 关闭换算模式 或 是人民币
                                        if (siteConfig?.price_conversion === '0' || isCNY) {
                                            return (
                                                <>
                                                    <span className="text-orange-500 text-xl sm:text-2xl md:text-3xl font-bold whitespace-nowrap">
                                                        {formatted.symbol}{formatted.value}
                                                    </span>
                                                    {itemData.orginal_price > price && (
                                                        <>
                                                            <span className="text-gray-400 text-sm line-through whitespace-nowrap">
                                                                {formatted.symbol}{itemData.orginal_price.toFixed(2)}
                                                            </span>
                                                            <span className="text-orange-500 text-sm">
                                                                ({Math.round((1 - price / itemData.orginal_price) * 100)}% off)
                                                            </span>
                                                        </>
                                                    )}
                                                </>
                                            );
                                        }
                                        
                                        // 开启换算模式且非人民币
                                        return (
                                            <>
                                                <span className="text-orange-500 text-3xl font-bold">¥ {price?.toFixed(2)}</span>
                                                {itemData.orginal_price > price && (
                                                    <>
                                                        <span className="text-gray-400 text-sm line-through">¥ {itemData.orginal_price.toFixed(2)}</span>
                                                        <span className="text-orange-500 text-sm">
                                                            ({Math.round((1 - price / itemData.orginal_price) * 100)}% off)
                                                        </span>
                                                    </>
                                                )}
                                                <span className="text-orange-500 text-sm">≈</span>
                                                <span className="text-orange-500 text-sm sm:text-base md:text-xl whitespace-nowrap">
                                                    {formatted.symbol}{formatted.value}
                                                </span>
                                            </>
                                        );
                                    })()}
                                </div>

                                {/* 批发价展示 */}
                                {itemData.priceRange && itemData.priceRange.length > 0 && (
                                    <div className="mt-4">
                                        <div className="text-gray-900 text-sm mb-3">{dict?.detail?.wholesalePrice || '批发价'}</div>
                                        <div className="flex flex-wrap gap-2">
                                            {itemData.priceRange.map((range, index) => {
                                                const [rangeQuantity, price] = range;
                                                const minQty = parseInt(rangeQuantity);
                                                const nextQuantity = index < itemData.priceRange!.length - 1
                                                    ? itemData.priceRange![index + 1][0]
                                                    : null;

                                                // 构建数量范围显示文本
                                                let quantityText = '';
                                                if (nextQuantity) {
                                                    quantityText = `${rangeQuantity}~${parseInt(nextQuantity) - 1}`;
                                                } else {
                                                    quantityText = `≥${rangeQuantity}`;
                                                }

                                                // 判断当前数量是否在此价格区间内
                                                const isCurrentRange = quantity >= minQty &&
                                                    (nextQuantity ? quantity < parseInt(nextQuantity) : true);

                                                return (
                                                    <div
                                                        key={index}
                                                        className={`px-4 py-2 rounded border text-center min-w-[100px] ${
                                                            isCurrentRange
                                                                ? 'border-orange-500 bg-orange-50 text-orange-600'
                                                                : 'border-gray-200 bg-gray-50 text-gray-600'
                                                        }`}
                                                    >
                                                        <div className="text-xs">{quantityText}</div>
                                                        <div className="font-bold">{formatCurrency(price).formatValue}{dict?.detail?.perPiece || '/件'}</div>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </div>
                                )}

                                {/* 店铺信息和销量 */}
                                <div className="flex items-center justify-between text-sm text-gray-500 ">
                                    <div className="flex items-center space-x-2">
                                        <i className="i-solar:shop-bold-duotone text-[#FF6B00] text-2xl" />
                                        <span>{itemData.seller_info?.shop_name || itemData.nick}</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <i className="i-heroicons-chart-bar w-4 h-4" />
                                        <span> {dict.detail.sales} : {itemData.total_sold}</span>
                                    </div>
                                </div>

                                <div className="space-y-3">
                                    {itemData && parseSkuData(itemData).propertyGroups.map((group, groupIndex) => {
                                        // 计算当前组中每个选项的可用性
                                        const availabilityMap = new Map<string, boolean>();
                                        const skuData = parseSkuData(itemData);

                                        group.values.forEach(value => {
                                            // 检查此值是否在任何有库存的组合中
                                            let available = false;

                                            Object.entries(skuData.skuMap).forEach(([key, stock]) => {
                                                if (stock <= 0) return; // 跳过无库存组合
                                                
                                                const skuValues = key.split('＊');
                                                // 当前值必须存在于SKU组合中
                                                if (!skuValues.includes(value)) return;

                                                // 如果已经选择了其他属性，检查兼容性
                                                let compatible = true;
                                                Object.entries(selectedSku).forEach(([propName, selectedValue]) => {
                                                    // 不检查当前正在遍历的属性组
                                                    if (propName !== group.name && !skuValues.includes(selectedValue)) {
                                                        compatible = false;
                                                    }
                                                });

                                                if (compatible) {
                                                    available = true;
                                                }
                                            });
                                            availabilityMap.set(value, available);
                                            
                                        });

                                        return (
                                            <div key={groupIndex} className="space-y-3">
                                                <div className="text-gray-900">{group.name}</div>
                                                <div className="flex flex-wrap gap-2">
                                                    {group.values.map((value, valueIndex) => {
                                                        const isSelected = selectedSku[group.name] === value;
                                                        const hasImage = !!group.images[value];
                                                        const isAvailable = availabilityMap.get(value) || false;
                                                        const isDisabled = !isAvailable;       

                                                        if (hasImage) {
                                                            return (
                                                                <div
                                                                    key={valueIndex}
                                                                    onClick={() => !isDisabled && handleSkuSelect(group.name, value)}
                                                                    className={`w-16 h-16 relative cursor-pointer rounded-lg overflow-hidden ${isDisabled ? 'opacity-50 cursor-not-allowed' :
                                                                            isSelected ? 'ring-2 ring-orange-500 ring-offset-2' :
                                                                                'hover:ring-2 hover:ring-gray-300 hover:ring-offset-2'
                                                                        }`}
                                                                >
                                                                    <Tooltip placement="bottom" title={<span>{group.values[valueIndex]}</span>} >
                                                                        <Image
                                                                            src={group.images[value].startsWith('//')
                                                                                ? `https:${group.images[value]}`
                                                                                : group.images[value]}
                                                                            alt={value}
                                                                            width={64}
                                                                            height={64}
                                                                            className={`object-cover w-full h-full ${isDisabled ? 'grayscale' : ''}`}
                                                                            unoptimized
                                                                        />
                                                                        {isDisabled && (
                                                                            <div className="absolute inset-0 bg-white/60 flex items-center justify-center text-xs text-red-500 text-center">
                                                                                {dict.detail.outOfStock}
                                                                            </div>
                                                                        )}
                                                                        <span className="sr-only">{value}</span>
                                                                    </Tooltip>
                                                                </div>
                                                            );
                                                        }

                                                        return (
                                                            <button
                                                                key={valueIndex}
                                                                onClick={() => !isDisabled && handleSkuSelect(group.name, value)}
                                                                disabled={isDisabled}
                                                                className={`px-4 py-2 border rounded ${isDisabled ? 'opacity-50 cursor-not-allowed bg-gray-100 border-gray-200 text-gray-400' :
                                                                        isSelected ? 'border-orange-500 bg-orange-50 text-orange-500' :
                                                                            'border-gray-200 hover:border-orange-500'
                                                                    }`}
                                                            >
                                                                {value}
                                                                {isDisabled && <span className="ml-1 text-xs text-red-500">(  {dict.detail.outOfStock} )</span>}
                                                            </button>
                                                        );
                                                    })}
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>

                                <div className="text-gray-600 text-sm">
                                     {dict.detail.shippingFee} : {itemData.location}  {dict.detail.to} {siteConfig?.name}   {dict.detail.warehouse}  {dict.detail.shippingFee} : {formatCurrency(currentExpressFee || 0).formatValue}
                                </div>

                                <div className="flex items-center space-x-4">
                                    <span className="text-gray-900">  {dict.detail.quantity} </span>
                                    <div className="flex items-center border rounded">
                                        <button
                                            type="button"
                                            onClick={() => handleQuantityChange(-1)}
                                            className={`w-8 h-8 flex items-center justify-center border-r ${quantity <= 1 ? 'text-gray-300 cursor-not-allowed' : 'hover:bg-gray-100'
                                                }`}
                                            disabled={quantity <= 1}
                                        >
                                            -
                                        </button>
                                        <input
                                            type="number"
                                            value={quantity}
                                            onChange={async (e) => {
                                                const val = parseInt(e.target.value);
                                                if (!isNaN(val) && val >= 1 && val <= currentStock) {
                                                    setQuantity(val);
                                                    // 更新运费
                                                    await updateEstimateFee(val);
                                                }
                                            }}
                                            className="w-12 text-center border-none focus:outline-none"
                                            min="1"
                                            max={currentStock}
                                        />
                                        <button
                                            type="button"
                                            onClick={() => handleQuantityChange(1)}
                                            className={`w-8 h-8 flex items-center justify-center border-l ${quantity >= currentStock ? 'text-gray-300 cursor-not-allowed' : 'hover:bg-gray-100'
                                                }`}
                                            disabled={quantity >= currentStock}
                                        >
                                            +
                                        </button>
                                    </div>
                                    <span className="text-gray-500 text-sm">
                                        {dict.detail.inventory} : {currentStock}
                                    </span>
                                </div>

                                {/* 批发价起批量提示 */}
                                {itemData.priceRange && itemData.priceRange.length > 0 && (() => {
                                    const wholesaleCheck = checkMinimumWholesaleQuantity(quantity);
                                    const [minQuantityStr] = itemData.priceRange[0];
                                    const minQuantity = parseInt(minQuantityStr);

                                    return (
                                        <div className="text-sm">
                                            {!wholesaleCheck.isValid ? (
                                                <div className="flex items-center space-x-2 text-red-500">
                                                    <i className="i-heroicons-exclamation-triangle w-4 h-4" />
                                                    {/* <span>批发商品起批量为{minQuantity}件，请增加数量</span> */}
                                                    <span>{dict?.detail?.remarkNoMeet?.replace('{minQuantity}', minQuantity)||`批发商品起批量为${minQuantity}件，请增加数量`}</span>
                                                </div>
                                            ) : (
                                                <div className="flex items-center space-x-2 text-green-600">
                                                    <i className="i-heroicons-check-circle w-4 h-4" />
                                                    {/* <span>已满足起批量要求（≥{minQuantity}件）</span> */}
                                                    <span>{dict?.detail?.remarkMeet?.replace('{minQuantity}', minQuantity)||`已满足起批量要求（≥${minQuantity}件）`}</span>
                                                </div>
                                            )}
                                        </div>
                                    );
                                })()}

                                <div className="space-y-2">
                                    <span className="text-gray-900"> {dict.detail.remark} </span>
                                    <textarea
                                        value={remark}
                                        onChange={(e) => setRemark(e.target.value)}
                                        className="w-full border rounded p-2 h-24 resize-none focus:outline-none focus:border-orange-500"
                                        placeholder={dict.detail.remarkHint}
                                    />
                                </div>

                                {/* 一次付款复选框 */}
                                {onePayOrderEnabled && (
                                    <div className="flex items-center space-x-2 mb-4">
                                        <input
                                            type="checkbox"
                                            id="onePayOrder"
                                            checked={isOnePayOrder}
                                            onChange={(e) => setIsOnePayOrder(e.target.checked)}
                                            className="w-4 h-4 text-orange-500 border-gray-300 rounded focus:ring-orange-500"
                                        />
                                        <label htmlFor="onePayOrder" className="text-sm text-gray-700 cursor-pointer">
                                            {dict.detail.onePayOrder}
                                        </label>
                                    </div>
                                )}

                                <div className="flex space-x-4">
                                    <button
                                        className={`flex-1 h-12 rounded transition-colors ${
                                            isAllPropertiesSelected(selectedSku) &&
                                            currentStock > 0 &&
                                            checkMinimumWholesaleQuantity(quantity).isValid
                                                ? 'bg-orange-500 text-white hover:bg-orange-600 active:bg-orange-700 cursor-pointer'
                                                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                            }`}
                                        disabled={
                                            !isAllPropertiesSelected(selectedSku) ||
                                            currentStock <= 0 ||
                                            !checkMinimumWholesaleQuantity(quantity).isValid
                                        }
                                        onClick={handleBuyNow}
                                    >
                                        {dict.detail.buyNow}
                                    </button>
                                    <button
                                        ref={cartBtnRef}
                                        className={`flex-1 h-12 rounded flex items-center justify-center transition-colors ${
                                            isAllPropertiesSelected(selectedSku) &&
                                            currentStock > 0 &&
                                            checkMinimumWholesaleQuantity(quantity).isValid
                                                ? 'bg-orange-50 text-orange-500 hover:bg-orange-100 border border-orange-200'
                                                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                            } ${addingToCart ? 'opacity-50' : ''}`}
                                        disabled={
                                            !isAllPropertiesSelected(selectedSku) ||
                                            currentStock <= 0 ||
                                            addingToCart ||
                                            !checkMinimumWholesaleQuantity(quantity).isValid
                                        }
                                        onClick={handleAddToCart}
                                    >
                                        <i className="i-heroicons-shopping-cart mr-2 w-5 h-5 cart-icon" />
                                        {addingToCart ? dict.detail.adding : dict.detail.addToCart}
                                    </button>
                                </div>

                                <div className="flex items-center space-x-4">
                                    <button
                                        onClick={handleFavorite}
                                        className="flex items-center space-x-1 text-gray-500 hover:text-orange-500 transition-colors"
                                    >
                                        <i className={`${isFavorite!==0 ? 'i-heroicons-heart-solid text-orange-500' : 'i-heroicons-heart'} w-4 h-4`} />
                                        <span>{isFavorite!==0 ? dict.detail.favorited : dict.detail.favorite}</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* 猜你喜欢模块 */}
                        <GuessYouLike dict={dict} lng={lng} />

                        {/* 商品详情 */}
                        <div className="bg-white rounded-lg p-6">
                            <h2 className="text-xl font-bold mb-6 flex items-center">
                                {dict?.detail?.details || '图文详情'}
                            </h2>
                            <div className="prose max-w-none overflow-hidden">

                                <div dangerouslySetInnerHTML={{ __html: itemData?.desc || '' }} />
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    );
}
