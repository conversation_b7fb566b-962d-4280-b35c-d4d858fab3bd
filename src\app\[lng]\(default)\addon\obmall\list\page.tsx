'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { Api } from '@/request/api';
import Loading from '@/components/Loading';
import ProductCard from '@/components/ProductCard';
import Toast from '@/components/Toast';
import Pagination from '@/components/Pagination';
import { getDictionary } from '@/dictionaries';
import type { Locale } from '@/config';
import { normalizeImageUrl } from '@/utils/imageUtils';

// 定义接口类型
interface MallProduct {
  id: number;
  goodsname: string;
  image: string;
  images: string;
  price: string;
  orginal_price?: string;
  sales?: number;
  date_available_text: string;
  date_available: number;
  recommend?: number | string; // 推荐标识字段
  [key: string]: any;
}

interface MallProductListResponse {
  code: number;
  msg: string;
  success: boolean;
  data: {
    data: MallProduct[];
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
  };
  time: number;
}

export default function MallListPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const lng = params.lng as Locale;

  // 获取URL参数
  const categoryId = searchParams.get('category_id');
  const categoryName = searchParams.get('name');

  // 状态管理
  const [dict, setDict] = useState<any>(null);
  const [products, setProducts] = useState<MallProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [addonEnabled, setAddonEnabled] = useState<boolean | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  // 移除排序功能，使用默认排序
  const sortBy = 'default';
  const pageSize = 20;

  // 检查插件状态
  useEffect(() => {
    const checkAddonStatus = async () => {
      try {
        const { isPluginEnabled } = await import('@/utils/plugin');
        const pluginEnabled = await isPluginEnabled('obmall');

        if (pluginEnabled) {
          setAddonEnabled(true);
        } else {
          router.push(`/${lng}`);
          return;
        }
      } catch (error) {
        console.error('Failed to check addon status:', error);
        router.push(`/${lng}`);
        return;
      }
    };

    checkAddonStatus();
  }, [lng, router]);

  // 初始化字典
  useEffect(() => {
    const initDict = async () => {
      try {
        const dictionary = await getDictionary(lng);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };

    if (addonEnabled) {
      initDict();
    }
  }, [lng, addonEnabled]);

  // 加载产品数据
  useEffect(() => {
    const loadProducts = async () => {
      if (!categoryId) {
        Toast.error(dict?.mall?.error?.invalidCategory || '无效的分类ID');
        return;
      }

      try {
        setLoading(true);
        const response: MallProductListResponse = await Api.getMallProductList({
          size: pageSize.toString(),
          sort: sortBy,
          category_id: categoryId,
          page: currentPage
        });

        console.log('Product list response:', response);

        if (response.success) {
          const { data, total, last_page } = response.data;
          setProducts(data || []);
          setTotalProducts(total || 0);
          setTotalPages(last_page || 1);
        } else {
          console.log('Product list response error:', response);
          Toast.error(response.msg || dict?.mall?.error?.loadProducts || '加载商品失败');
          setProducts([]);
        }
      } catch (error) {
        console.error('Failed to load products:', error);
        Toast.error(dict?.mall?.error?.loadProducts || '加载商品失败');
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    if (addonEnabled && dict && categoryId) {
      loadProducts();
    }
  }, [dict, addonEnabled, categoryId, currentPage, sortBy, pageSize]);

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // 返回商城首页
  const handleBackToMall = () => {
    router.push(`/${lng}/addon/obmall`);
  };

  // 如果插件状态未确定或插件未启用，显示加载状态
  if (addonEnabled === null || !addonEnabled) {
    return <Loading />;
  }

  // 如果插件启用但字典还在加载中
  if (loading && !dict) {
    return <Loading />;
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="max-w-[1200px] mx-auto p-4">
        {/* 页面头部 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBackToMall}
                className="flex items-center space-x-2 text-gray-600 hover:text-orange-500 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span>{dict?.mall?.backToMall || '返回商城'}</span>
              </button>
              <div className="text-gray-300">|</div>
              <h1 className="text-2xl font-bold text-gray-800">
                {decodeURIComponent(categoryName || '')} 
              </h1>
            </div>
            <div className="text-sm text-gray-500">
              {dict?.mall?.pagination?.total?.replace('{total}', totalProducts.toString()) || `共 ${totalProducts} 件商品`}
            </div>
          </div>


        </div>

        {/* 产品列表 */}
        {loading ? (
          <Loading />
        ) : products.length > 0 ? (
          <>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 mb-8">
              {products.map((product) => {
                // 处理图片URL - 使用统一的图片URL处理函数
                let imageUrl = product.image;
                if (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('//')) {
                  // 对于相对路径，添加域名前缀
                  if (imageUrl.startsWith('/')) {
                    imageUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'https://api.v6.daigouxt.com'}${imageUrl}`;
                  }
                }
                // 使用工具函数处理协议相对URL
                imageUrl = normalizeImageUrl(imageUrl);

                return (
                  <ProductCard
                    key={product.id}
                    product={{
                      title: product.goodsname,
                      pic_url: imageUrl,
                      price: parseFloat(product.price),
                      promotion_price: product.orginal_price ? parseFloat(product.orginal_price) : undefined,
                      detail_url: `/${lng}/detail/obmall?id=${product.id}`,
                      nick: '',
                      recommend: product.recommend // 传递推荐字段
                    }}
                    platform={'obmall'}
                    dict={dict}
                  />
                );
              })}
            </div>

            {/* 分页组件 */}
            {totalPages > 1 && (
              <div className="flex justify-center">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  pageSize={pageSize}
                  onPageChange={handlePageChange}
                  prevText={dict?.mall?.pagination?.prev || '上一页'}
                  nextText={dict?.mall?.pagination?.next || '下一页'}
                />
              </div>
            )}
          </>
        ) : (
          <div className="bg-white rounded-lg shadow-sm p-12 text-center">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
              </svg>
            </div>
            <div className="text-lg text-gray-600 mb-2">
              {dict?.mall?.empty?.title || '暂无商品'}
            </div>
            <div className="text-sm text-gray-500">
              {dict?.mall?.empty?.description || '该分类下暂时没有商品'}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
