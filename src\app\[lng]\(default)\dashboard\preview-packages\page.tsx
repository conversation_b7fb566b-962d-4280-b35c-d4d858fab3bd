'use client'

import React, { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Select as AntSelect, App } from 'antd'
import Button from '@/components/Button'
import Input from '@/components/Input'
import Select from '@/components/Select'

import ModalComponent from '@/components/Modal'
import Toast from '@/components/Toast'
import Pagination from '@/components/Pagination'
import Loading from '@/components/Loading'
import AntdConfigProvider from '@/components/AntdConfigProvider'
import { Api } from '@/request/api'
import { getDictionary } from '@/dictionaries'
import type { Locale } from '@/config'
import { formatCurrency, refreshCurrencyInfo } from '@/utils/currency'
import { normalizeImageUrl } from '@/utils/imageUtils'
import Link from 'next/link'


const { Option } = AntSelect

interface PreviewPackage {
  id: number
  sn: string
  price: string
  rehearsal_fee: string
  status: number
  status_text: string
  consignee: string
  shippingname: string
  shipping_name: string
  createtime: string
  updatetime: string
  username: string
  actualweight: number
  actualvolume: number
  goods: any[]
  totalmoney: number
  value_added_fee?: string  // 增值费
  service_fee?: string      // 服务费
  server?: string[]         // 选择的附加服务列表
  address: {
    consignee: string
    telephone: string
    mergename: string
    address: string
    zip: string
  }
}

export default function PreviewPackagesPage() {
  const params = useParams()
  const lng = params.lng as Locale
  const { modal } = App.useApp()

  const [dict, setDict] = useState<any>({})
  const [loading, setLoading] = useState(false)
  const [previewPackages, setPreviewPackages] = useState<PreviewPackage[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 15,
    total: 0
  })
  
  // Search conditions
  const [searchForm, setSearchForm] = useState({
    status: undefined as number | undefined,  // 预演包裹状态
    keywords: '', // 关键词：运单编号或预演包裹编号
  })

  useEffect(() => {
    const loadDict = async () => {
      const dictionary = await getDictionary(lng)
      setDict(dictionary)
    }
    loadDict()

    // 刷新货币信息，确保与系统选择的货币一致
    refreshCurrencyInfo()
  }, [lng])

  useEffect(() => {
    if (dict.dashboard) {
      fetchPreviewPackages()
      fetchStatusList()
    }
  }, [dict, pagination.current, pagination.pageSize])

  // Fetch status list
  const fetchStatusList = async () => {
    try {
      const result = await Api.getPreviewPackageStatusList();
      if (result.code === 200) {
        setStatusList(result.data || {});
      }
    } catch (error) {
      console.error(dict?.dashboard?.previewPackages?.fetchStatusFailed || 'Failed to fetch status list:', error);
    }
  }

  const fetchPreviewPackages = async () => {
    setLoading(true)
    try {
      const params: any = {}

      if (searchForm.status !== undefined) {
        params.status = searchForm.status
      }

      if (searchForm.keywords && searchForm.keywords.trim()) {
        params.keywords = searchForm.keywords.trim()
      }

      const res = await Api.getPreviewPackageList(params)
      if (res.success) {
        setPreviewPackages(res.data.data || [])
        setPagination(prev => ({
          ...prev,
          total: res.data.total || 0
        }))
      } else {
        Toast.error(dict?.dashboard?.previewPackages?.fetchListFailed || 'Failed to fetch preview package list')
      }
    } catch (error) {
      Toast.error(dict?.dashboard?.previewPackages?.fetchListFailed || 'Failed to fetch preview package list')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }))
    fetchPreviewPackages()
  }

  const handleReset = () => {
    setSearchForm({
      status: undefined,
      keywords: '',
    })
    setPagination(prev => ({ ...prev, current: 1 }))
    setTimeout(() => {
      fetchPreviewPackages()
    }, 100)
  }

  const getStatusColor = (status: number) => {
    switch (status) {
      case 0: return 'bg-orange-100 text-orange-800' // Pending payment
      case 1: return 'bg-green-100 text-green-800'   // Paid
      case 2: return 'bg-blue-100 text-blue-800'     // Completed
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const [detailModalOpen, setDetailModalOpen] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<PreviewPackage | null>(null)
  const [refundModalOpen, setRefundModalOpen] = useState(false)
  const [cancelModalOpen, setCancelModalOpen] = useState(false)
  const [recordToCancel, setRecordToCancel] = useState<PreviewPackage | null>(null)
  const [refundContent, setRefundContent] = useState('')
  const [statusList, setStatusList] = useState<Record<string, string>>({})
  const [shippingModalOpen, setShippingModalOpen] = useState(false)
  const [shippingTemplates, setShippingTemplates] = useState<any[]>([])
  const [selectedShippingTemplate, setSelectedShippingTemplate] = useState<number | undefined>()

  const handleViewDetail = (record: PreviewPackage) => {
    setSelectedRecord(record)
    setDetailModalOpen(true)
  }

  // Cancel preview package - show modal
  const handleCancelPreview = (record: PreviewPackage) => {
    setRecordToCancel(record)
    setCancelModalOpen(true)
  };

  // Confirm cancel preview package
  const handleConfirmCancel = async () => {
    if (!recordToCancel) return;

    try {
      setLoading(true);
      const result = await Api.cancelPreviewPackage({
        rehearsal_id: recordToCancel.id
      });

      if (result.code === 200 || result.success) {
        Toast.success(dict?.dashboard?.previewPackages?.cancelSuccess || 'Preview package cancelled successfully');
        // Reload data
        fetchPreviewPackages();
      } else {
        Toast.error(result.message || dict?.dashboard?.previewPackages?.cancelFailed || 'Failed to cancel preview package');
      }
    } catch (error) {
      console.error(dict?.dashboard?.previewPackages?.cancelFailed || 'Failed to cancel preview package:', error);
      Toast.error(dict?.dashboard?.previewPackages?.cancelFailed || 'Failed to cancel preview package');
    } finally {
      setLoading(false);
      setCancelModalOpen(false);
      setRecordToCancel(null);
    }
  };

  // Submit freight payment
  const handleFreightPayment = async (record: PreviewPackage) => {
    try {
      setLoading(true);
      const result = await Api.submitFreightPayment({
        id: record.id
      });

      if (result.code === 200 || result.success) {
        Toast.success(dict?.dashboard?.previewPackages?.freightPaymentSuccess || 'Freight payment submitted successfully');
        // Reload data
        fetchPreviewPackages();
      } else {
        Toast.error(result.message || dict?.dashboard?.previewPackages?.freightPaymentFailed || 'Failed to submit freight payment');
      }
    } catch (error) {
      console.error(dict?.dashboard?.previewPackages?.freightPaymentFailed || 'Failed to submit freight payment:', error);
      Toast.error(dict?.dashboard?.previewPackages?.freightPaymentFailed || 'Failed to submit freight payment');
    } finally {
      setLoading(false);
    }
  };

  // Apply for refund
  const handleRefundApplication = (record: PreviewPackage) => {
    setSelectedRecord(record);
    setRefundContent('');
    setRefundModalOpen(true);
  };

  // Submit refund application
  const handleSubmitRefund = async () => {
    if (!selectedRecord || !refundContent.trim()) {
      Toast.error(dict?.dashboard?.previewPackages?.refund?.reasonHint || 'Please fill in the refund reason');
      return;
    }

    try {
      setLoading(true);
      const result = await Api.submitPreviewPackageRefund({
        rehearsal_id: selectedRecord.id,
        content: refundContent.trim()
      });

      if (result.code === 200 || result.success) {
        Toast.success(dict?.dashboard?.previewPackages?.refund?.submitSuccess || 'Refund application submitted successfully');
        setRefundModalOpen(false);
        setRefundContent('');
        // Reload data
        fetchPreviewPackages();
      } else {
        Toast.error(result.message || dict?.dashboard?.previewPackages?.refund?.submitFailed || 'Failed to submit refund application');
      }
    } catch (error) {
      console.error(dict?.dashboard?.previewPackages?.refund?.submitFailed || 'Failed to submit refund application:', error);
      Toast.error(dict?.dashboard?.previewPackages?.refund?.submitFailed || 'Failed to submit refund application');
    } finally {
      setLoading(false);
    }
  };

  // Submit waybill - Convert preview package to normal waybill
  const handleChangeShipping = async (record: PreviewPackage) => {
    try {
      setLoading(true);
      const result = await Api.submitFreightPayment({
        id: record.id
      });

      if (result.code === 200 || result.success) {
        Toast.success(dict?.dashboard?.previewPackages?.waybillGenerateSuccess || 'Waybill generated successfully, redirecting to payment page...');
        // Get generated waybill ID, redirect to normal waybill payment page
        const sendorderId = result.data?.sendorder_id || result.data?.id || result.data;
        if (sendorderId) {
          // Redirect to normal waybill payment page
          window.location.href = `/${lng}/pay?order_id=${sendorderId}&type=package`;
        } else {
          // If no waybill ID returned, reload data to see status changes
          fetchPreviewPackages();
        }
      } else {
        Toast.error(result.message || dict?.dashboard?.previewPackages?.waybillGenerateFailed || 'Failed to generate waybill');
      }
    } catch (error) {
      console.error(dict?.dashboard?.previewPackages?.waybillGenerateFailed || 'Failed to generate waybill:', error);
      Toast.error(dict?.dashboard?.previewPackages?.waybillGenerateFailed || 'Failed to generate waybill');
    } finally {
      setLoading(false);
    }
  };

  // Submit shipping method change
  const handleSubmitShippingChange = async () => {
    if (!selectedRecord || !selectedShippingTemplate) {
      Toast.error(dict?.dashboard?.previewPackages?.shipping?.templateHint || 'Please select shipping template');
      return;
    }

    try {
      setLoading(true);
      const result = await Api.changePreviewPackageShipping({
        template_id: selectedShippingTemplate,
        rehearsal_id: selectedRecord.id
      });

      if (result.code === 200 || result.success) {
        Toast.success(dict?.dashboard?.previewPackages?.shipping?.changeSuccess || 'Shipping method changed successfully');
        setShippingModalOpen(false);
        // Reload data
        fetchPreviewPackages();
      } else {
        Toast.error(result.message || dict?.dashboard?.previewPackages?.shipping?.changeFailed || 'Failed to change shipping method');
      }
    } catch (error) {
      console.error(dict?.dashboard?.previewPackages?.shipping?.changeFailed || 'Failed to change shipping method:', error);
      Toast.error(dict?.dashboard?.previewPackages?.shipping?.changeFailed || 'Failed to change shipping method');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({
      ...prev,
      current: page
    }))
  }

  if (!dict.dashboard) {
    return <Loading />
  }

  return (
    <AntdConfigProvider>
      <App>
        <PreviewPackagesContent lng={lng} dict={dict} />
      </App>
    </AntdConfigProvider>
  )
}

function PreviewPackagesContent({ lng, dict }: { lng: Locale; dict: any }) {
  const { modal } = App.useApp()

  const [loading, setLoading] = useState(false)
  const [previewPackages, setPreviewPackages] = useState<PreviewPackage[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 15,
    total: 0
  })

  // Search conditions
  const [searchForm, setSearchForm] = useState({
    status: undefined as number | undefined,  // 预演包裹状态
    keywords: '', // 关键词：运单编号或预演包裹编号
  })

  const [detailModalOpen, setDetailModalOpen] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<PreviewPackage | null>(null)
  const [refundModalOpen, setRefundModalOpen] = useState(false)
  const [cancelModalOpen, setCancelModalOpen] = useState(false)
  const [recordToCancel, setRecordToCancel] = useState<PreviewPackage | null>(null)
  const [refundContent, setRefundContent] = useState('')
  const [statusList, setStatusList] = useState<Record<string, string>>({})
  const [shippingModalOpen, setShippingModalOpen] = useState(false)
  const [selectedShippingTemplate, setSelectedShippingTemplate] = useState<number | undefined>()

  // 初始化时刷新货币信息
  useEffect(() => {
    refreshCurrencyInfo()
  }, [])

  useEffect(() => {
    if (dict.dashboard) {
      fetchPreviewPackages()
      fetchStatusList()
    }
  }, [dict, pagination.current, pagination.pageSize])

  const fetchPreviewPackages = async () => {
    setLoading(true)
    try {
      const params: any = {}

      if (searchForm.status !== undefined) {
        params.status = searchForm.status
      }

      if (searchForm.keywords && searchForm.keywords.trim()) {
        params.keywords = searchForm.keywords.trim()
      }

      const res = await Api.getPreviewPackageList(params)
      if (res.success) {
        setPreviewPackages(res.data.data || [])
        setPagination(prev => ({
          ...prev,
          total: res.data.total || 0
        }))
      } else {
        Toast.error(dict?.dashboard?.previewPackages?.fetchListFailed || 'Failed to fetch preview package list')
      }
    } catch (error) {
      Toast.error(dict?.dashboard?.previewPackages?.fetchListFailed || 'Failed to fetch preview package list')
    } finally {
      setLoading(false)
    }
  }

  // Get status list
  const fetchStatusList = async () => {
    try {
      const result = await Api.getPreviewPackageStatusList();
      if (result.success) {
        setStatusList(result.data || {});
      }
    } catch (error) {
      console.error(dict?.dashboard?.previewPackages?.fetchStatusFailed || 'Failed to fetch status list:', error);
    }
  }

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }))
    fetchPreviewPackages()
  }

  const handleReset = () => {
    setSearchForm({
      status: undefined,
      keywords: '',
    })
    setPagination(prev => ({ ...prev, current: 1 }))
    setTimeout(() => {
      fetchPreviewPackages()
    }, 100)
  }

  const getStatusColor = (status: number) => {
    switch (status) {
      case 0: return 'bg-orange-100 text-orange-800' // 待付款
      case 1: return 'bg-green-100 text-green-800'   // 已付款
      case 2: return 'bg-blue-100 text-blue-800'     // 已完成
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handleViewDetail = (record: PreviewPackage) => {
    setSelectedRecord(record)
    setDetailModalOpen(true)
  }

  // Cancel preview package - show modal
  const handleCancelPreview = (record: PreviewPackage) => {
    setRecordToCancel(record)
    setCancelModalOpen(true)
  };

  // Confirm cancel preview package
  const handleConfirmCancel = async () => {
    if (!recordToCancel) return;

    try {
      setLoading(true);
      const result = await Api.cancelPreviewPackage({
        rehearsal_id: recordToCancel.id
      });

      if (result.code === 200 || result.success) {
        Toast.success(dict?.dashboard?.previewPackages?.cancelSuccess || 'Preview package cancelled successfully');
        // Reload data
        fetchPreviewPackages();
      } else {
        Toast.error(result.message || dict?.dashboard?.previewPackages?.cancelFailed || 'Failed to cancel preview package');
      }
    } catch (error) {
      console.error(dict?.dashboard?.previewPackages?.cancelFailed || 'Failed to cancel preview package:', error);
      Toast.error(dict?.dashboard?.previewPackages?.cancelFailed || 'Failed to cancel preview package');
    } finally {
      setLoading(false);
      setCancelModalOpen(false);
      setRecordToCancel(null);
    }
  };



  // 支付运单费用 - 跳转到支付页面
  const handleFreightPayment = async (record: PreviewPackage) => {
    try {
      setLoading(true);
      // 获取运单ID，如果有的话直接跳转到支付页面
      if (record.id) {
        // 跳转到运单支付页面
        window.location.href = `/${lng}/pay?order_id=${record.id}&type=packageviewpay`;
      } else {
        // 如果没有运单ID，先生成运单再跳转
        const result = await Api.submitFreightPayment({
          id: record.id
        });

        if (result.success) {
          const id = result.data?.id || result.data;
          if (id) {
            // 跳转到运单支付页面
            window.location.href = `/${lng}/pay?order_id=${id}&type=packageviewpay`;
          } else {
            Toast.error('获取运单信息失败');
          }
        } else {
          Toast.error(result.message || '获取运单信息失败');
        }
      }
    } catch (error) {
      console.error('支付运单失败:', error);
      Toast.error('支付运单失败');
    } finally {
      setLoading(false);
    }
  };

  // 申请退款
  const handleRefundApplication = (record: PreviewPackage) => {
    setSelectedRecord(record);
    setRefundContent('');
    setRefundModalOpen(true);
  };

  // 提交退款申请
  const handleSubmitRefund = async () => {
    if (!selectedRecord || !refundContent.trim()) {
      Toast.error(dict?.dashboard?.previewPackages?.refundReasonRequired || 'Please enter refund reason');
      return;
    }

    try {
      setLoading(true);
      const result = await Api.submitPreviewPackageRefund({
        rehearsal_id: selectedRecord.id,
        content: refundContent.trim()
      });

      if (result.code === 200 || result.success) {
        Toast.success(dict?.dashboard?.previewPackages?.refundSubmitSuccess || 'Refund application submitted successfully');
        setRefundModalOpen(false);
        setRefundContent('');
        // 重新加载数据
        fetchPreviewPackages();
      } else {
        Toast.error(result.message || '退款申请提交失败');
      }
    } catch (error) {
      console.error('退款申请提交失败:', error);
      Toast.error('退款申请提交失败');
    } finally {
      setLoading(false);
    }
  };

  // 提交运单 - 将预演包裹转换为正常运单
  const handleChangeShipping = async (record: PreviewPackage) => {
    try {
      setLoading(true);
      const result = await Api.submitFreightPayment({
        id: record.id
      });

      if (result.code === 200 || result.success) {
        Toast.success('运单生成成功，正在跳转到支付页面...');
        // 获取生成的运单ID，跳转到正常的运单支付页面
        const sendorderId = result.data?.sendorder_id || result.data?.id || result.data;
        if (sendorderId) {
          // 跳转到正常的运单支付页面
          window.location.href = `/${lng}/pay?order_id=${sendorderId}&type=packageview`;
        } else {
          // 如果没有返回运单ID，重新加载数据查看状态变化
          fetchPreviewPackages();
        }
      } else {
        Toast.error(result.message || '运单生成失败');
      }
    } catch (error) {
      console.error('运单生成失败:', error);
      Toast.error('运单生成失败');
    } finally {
      setLoading(false);
    }
  };

  // 提交运费方式更改
  const handleSubmitShippingChange = async () => {
    if (!selectedRecord || !selectedShippingTemplate) {
      Toast.error('请选择运费模板');
      return;
    }

    try {
      setLoading(true);
      const result = await Api.changePreviewPackageShipping({
        template_id: selectedShippingTemplate,
        rehearsal_id: selectedRecord.id
      });

      if (result.code === 200 || result.success) {
        Toast.success('运费方式更改成功');
        setShippingModalOpen(false);
        // 重新加载数据
        fetchPreviewPackages();
      } else {
        Toast.error(result.message || '运费方式更改失败');
      }
    } catch (error) {
      console.error('运费方式更改失败:', error);
      Toast.error('运费方式更改失败');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({
      ...prev,
      current: page
    }))
  }

  if (!dict.dashboard) {
    return <Loading />
  }

  return (
    <div className="p-6">
        <style jsx>{`
          .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        `}</style>
        <div className="mb-6">
          <h1 className="text-2xl font-bold mb-4">{dict?.dashboard?.previewPackages?.title || 'Preview Package Management'}</h1>

          {/* Filter form */}
          <div className="bg-white p-4 rounded-lg shadow-sm mb-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Status filter */}
              <div>
                {/* <label className="block text-sm font-medium text-gray-700 mb-2">
                  {dict?.dashboard?.previewPackages?.statusFilter || 'Status Filter'}
                </label> */}
                <Select
                  value={searchForm.status}
                  onChange={(value) => setSearchForm(prev => ({ ...prev, status: value }))}
                  placeholder={dict?.dashboard?.previewPackages?.allStatus || 'All Status'}
                  className="w-full"
                  allowClear
                >
                  {Object.entries(statusList).map(([key, value]) => (
                    <Option key={key} value={parseInt(key)}>
                      {value}
                    </Option>
                  ))}
                </Select>
              </div>

              {/* Keywords search */}
              <div>
                {/* <label className="block text-sm font-medium text-gray-700 mb-2">
                  {dict?.dashboard?.previewPackages?.keywordsLabel || 'Keywords Search'}
                </label> */}
                <div className="relative">
                  <Input
                    placeholder={dict?.dashboard?.previewPackages?.searchPlaceholder || 'Search keywords'}
                    value={searchForm.keywords}
                    onChange={(e) => setSearchForm(prev => ({ ...prev, keywords: e.target.value }))}
                    className="pl-14"
                    onPressEnter={handleSearch}
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                  </div>
                </div>
              </div>

              {/* Action buttons */}
              <div className="md:col-span-2 flex items-end gap-3">
                <Button
                  type="primary"
                  onClick={handleSearch}
                  className="bg-orange-500 hover:bg-orange-600 px-8"
                >
                  {dict?.dashboard?.previewPackages?.search || 'Search'}
                </Button>
                <Button
                  onClick={handleReset}
                  className="px-8"
                >
                  {dict?.dashboard?.previewPackages?.reset || 'Reset'}
                </Button>
              </div>
            </div>
          </div>

          {/* List header */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
              <div className="grid grid-cols-[1fr_100px_80px_80px_80px_120px] gap-4 text-sm font-medium text-gray-600">
                <div>{dict?.dashboard?.previewPackages?.table?.productDetails || 'Product Details'}</div>
                <div className="text-center">{dict?.dashboard?.previewPackages?.table?.recipient || 'Recipient'}</div>
                <div className="text-center">{dict?.dashboard?.previewPackages?.table?.status || 'Status'}</div>
                <div className="text-center">{dict?.dashboard?.previewPackages?.table?.totalFee || 'Total Fee'}</div>
                <div className="text-center">{dict?.dashboard?.previewPackages?.table?.actions || 'Actions'}</div>
              </div>
            </div>

            {/* 数据列表 */}
            {loading ? (
              <Loading height="300px" />
            ) : (
              <>
                {previewPackages.length > 0 ? (
                  previewPackages.map((record) => (
                    <div key={record.id} className={`border-b border-gray-100 last:border-b-0 ${record.status === -1 ? 'opacity-60' : ''}`}>
                      {/* Preview package info row */}
                      <div className={`px-6 py-4 ${record.status === -1 ? 'bg-gray-100' : 'bg-gray-50'}`}>
                        <div className="flex items-center justify-between">
                          <div className={`flex items-center gap-4 text-sm ${record.status === -1 ? 'text-gray-400' : 'text-gray-600'}`}>
                            <span>{dict?.dashboard?.previewPackages?.packageNumber || 'Preview Package Number'}：{record.sn}</span>
                            <span>{dict?.dashboard?.previewPackages?.createTime || 'Create Time'}：{record.createtime}</span>
                            <span>{dict?.dashboard?.previewPackages?.totalCost || 'Total Cost'}：<span className={`font-medium ${record.status === -1 ? 'text-gray-400' : 'text-red-500'}`}>{formatCurrency(parseFloat(record.rehearsal_fee || '0')).formatValue}</span></span>
                          </div>
                          {/* Remove small buttons, only keep basic info display */}
                        </div>
                      </div>

                      {/* 商品列表 */}
                      {record.goods && record.goods.length > 0 ? (
                        // 有商品数据时显示实际商品信息
                        record.goods.map((item, index) => (
                          <div key={index} className={`px-6 py-4 transition-colors ${record.status === -1 ? 'hover:bg-gray-100' : 'hover:bg-gray-50'}`}>
                            <div className="grid grid-cols-[1fr_100px_80px_80px_80px_120px] gap-4 items-center">
                              {/* 商品详情 */}
                              <div className="flex items-start gap-3">
                                <img
                                  src={normalizeImageUrl(item.goodsimg)}
                                  alt={item.goodsname}
                                  className={`w-16 h-16 object-cover rounded border ${record.status === -1 ? 'grayscale opacity-60' : ''}`}
                                />
                                <div className="flex-1 min-w-0">
                                  <div className={`text-sm font-medium line-clamp-2 mb-1 ${record.status === -1 ? 'text-gray-400' : 'text-gray-900'}`}>
                                    {item.goodsname}
                                  </div>
                                  <div className={`text-xs mb-1 ${record.status === -1 ? 'text-gray-400' : 'text-gray-500'}`}>
                                    {item.skuname || '购买合并：土耳其 • 香草 • 牛角 全套装【4个】'}
                                  </div>
                                  {(() => {
                                    const productNumber = item.goodssn || item.goods_sn || item.sn || item.num_iid;
                                    const hasProductNumber = productNumber && productNumber !== '' && productNumber !== '0';
                                    const hasWeight = item.goodsweight !== undefined && item.goodsweight !== null && item.goodsweight > 0;
                                    const hasVolume = item.goodsvolume !== undefined && item.goodsvolume !== null && item.goodsvolume > 0;

                                    // 只有当至少有一个信息需要显示时才渲染这一行
                                    if (!hasProductNumber && !hasWeight && !hasVolume) {
                                      return null;
                                    }

                                    return (
                                      <div className={`text-xs flex justify-between items-center ${record.status === -1 ? 'text-gray-400' : 'text-gray-400'}`}>
                                        <div>
                                          {hasProductNumber && (
                                            <span>{dict?.dashboard?.previewPackages?.productNumber || 'Product Number'}：{productNumber}</span>
                                          )}
                                        </div>
                                        <div className="flex gap-3">
                                          {hasWeight && (
                                            <span>{item.goodsweight}g</span>
                                          )}
                                          {hasVolume && (
                                            <span>{Math.round(item.goodsvolume)}cm³</span>
                                          )}
                                        </div>
                                      </div>
                                    );
                                  })()}
                                </div>
                              </div>

                              {/* 收件人 */}
                              <div className="text-center text-sm">
                                <div className={record.status === -1 ? 'text-gray-400' : ''}>{record.address?.consignee || record.consignee}</div>
                              </div>

                              {/* 状态 */}
                              <div className="text-center text-sm">
                                <div className={`${record.status === -1 ? 'text-gray-400' : record.status === 0 ? 'text-orange-500' : record.status === 1 ? 'text-green-500' : 'text-blue-500'}`}>
                                  {statusList[record.status] || record.status_text}
                                </div>
                              </div>

                              {/* Total fee */}
                              <div className="text-center">
                                <div className={`text-lg font-bold ${record.status === -1 ? 'text-gray-400' : ''}`}>
                                  {formatCurrency(parseFloat(record.rehearsal_fee || '0')).formatValue}
                                </div>
                                <div className={`text-xs ${record.status === -1 ? 'text-gray-400' : 'text-gray-500'}`}>
                                  {dict?.dashboard?.previewPackages?.previewFee || 'Preview Package Fee'} {formatCurrency(parseFloat(record.rehearsal_fee || '0')).formatValue}
                                </div>
                                {/* 只有当存在增值费且选择了附加服务时才显示 */}
                                {record.value_added_fee && parseFloat(record.value_added_fee) > 0 &&
                                 record.server && Array.isArray(record.server) && record.server.length > 0 && (
                                  <div className={`text-xs ${record.status === -1 ? 'text-gray-400' : 'text-gray-500'}`}>
                                    {dict?.dashboard?.previewPackages?.valueFee || 'Value Fee'} {formatCurrency(parseFloat(record.value_added_fee)).formatValue}
                                  </div>
                                )}
                                {/* 只有当存在服务费时才显示 */}
                                {record.service_fee && parseFloat(record.service_fee) > 0 && (
                                  <div className={`text-xs ${record.status === -1 ? 'text-gray-400' : 'text-gray-500'}`}>
                                    {dict?.dashboard?.previewPackages?.serviceFee || 'Service Fee'} {formatCurrency(parseFloat(record.service_fee)).formatValue}
                                  </div>
                                )}
                              </div>

                              {/* 操作 */}
                              <div className="text-center">
                                <div className="flex flex-col gap-2">
                                  {/* Show different action buttons based on status */}
                                  {record.status === 0 && (
                                    // Status 0 - Pending payment: Show pay and cancel buttons
                                    <div className="flex flex-col gap-1">
                                      <Link href={`/${lng}/pay?order_id=${record.id}&type=packageview`}>
                                        <Button
                                          size="small"
                                          className="bg-orange-500 text-white hover:bg-orange-600 text-xs px-4 py-1 w-full"
                                        >
                                          {dict?.dashboard?.previewPackages?.actions?.pay || 'Pay'}
                                        </Button>
                                      </Link>
                                      <Button
                                        size="small"
                                        className="border border-gray-300 text-gray-600 hover:border-gray-400 text-xs px-4 py-1"
                                        onClick={() => handleCancelPreview(record)}
                                      >
                                        {dict?.dashboard?.previewPackages?.actions?.cancel || 'Cancel'}
                                      </Button>
                                    </div>
                                  )}

                                  {record.status === 1 && (
                                    // Status 1 - Preview fee paid: Show apply refund and pay shipping buttons
                                    <div className="flex flex-col gap-1">
                                      <Button
                                        size="small"
                                        className="bg-red-500 text-white hover:bg-red-600 text-xs px-4 py-1"
                                        onClick={() => handleRefundApplication(record)}
                                      >
                                        {dict?.dashboard?.previewPackages?.actions?.applyRefund || 'Apply Refund'}
                                      </Button>
                                      <Button
                                        size="small"
                                        className="bg-blue-500 text-white hover:bg-blue-600 text-xs px-4 py-1"
                                        onClick={() => handleChangeShipping(record)}
                                      >
                                        {dict?.dashboard?.previewPackages?.actions?.payShipping || 'Pay Shipping'}
                                      </Button>
                                    </div>
                                  )}

                                  {record.status === 2 && (
                                    // Status 2 - Packed: Show submit waybill button
                                    <Button
                                      size="small"
                                      className="bg-blue-500 text-white hover:bg-blue-600 text-xs px-4 py-1"
                                      onClick={() => handleChangeShipping(record)}
                                    >
                                      {dict?.dashboard?.previewPackages?.actions?.submitWaybill || 'Submit Waybill'}
                                    </Button>
                                  )}

                                  {record.status === 3 && (
                                    // Status 3 - Waybill unpaid: Show pay waybill button
                                    <Button
                                      size="small"
                                      className="bg-orange-500 text-white hover:bg-orange-600 text-xs px-4 py-1"
                                      onClick={() => handleFreightPayment(record)}
                                    >
                                      {dict?.dashboard?.previewPackages?.actions?.payWaybill || 'Pay Waybill'}
                                    </Button>
                                  )}

                                  {record.status === 4 && (
                                    // Status 4 - Waybill paid: Only show text, no action buttons (only preview fee supports refund)
                                    <div className="text-xs text-gray-500">
                                      {dict?.dashboard?.previewPackages?.waybillPaid || 'Waybill Paid'}
                                    </div>
                                  )}

                                  {record.status === -1 && (
                                    // 状态-1 - 已取消：仅显示文本，无操作按钮
                                    <div className="text-xs text-gray-500">
                                      {statusList[record.status] || record.status_text || '已取消'}
                                    </div>
                                  )}

                                  {record.status === -2 && (
                                    // 状态-2 - 退款中：仅显示文本，无操作按钮
                                    <div className="text-xs text-gray-500">
                                      {statusList[record.status] || record.status_text || '退款中'}
                                    </div>
                                  )}

                                  {record.status === -3 && (
                                    // 状态-3 - 已退款：仅显示文本，无操作按钮
                                    <div className="text-xs text-gray-500">
                                      {statusList[record.status] || record.status_text || '已退款'}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        // 无商品数据时（如已取消状态）显示默认商品信息
                        <div className={`px-6 py-4 transition-colors ${record.status === -1 ? 'hover:bg-gray-100' : 'hover:bg-gray-50'}`}>
                          <div className="grid grid-cols-[1fr_100px_80px_80px_80px_120px] gap-4 items-center">
                            {/* 商品详情 */}
                            <div className="flex items-start gap-3">
                              <img
                                src="/images/default.jpg"
                                alt="默认商品图片"
                                className={`w-16 h-16 object-cover rounded border ${record.status === -1 ? 'grayscale opacity-60' : ''}`}
                              />
                              <div className="flex-1 min-w-0">
                                <div className={`text-sm font-medium line-clamp-2 mb-1 ${record.status === -1 ? 'text-gray-400' : 'text-gray-900'}`}>
                                  {dict?.dashboard?.previewPackages?.defaultProductInfo || 'Preview Package Product Info'}
                                </div>
                                <div className={`text-xs mb-1 ${record.status === -1 ? 'text-gray-400' : 'text-gray-500'}`}>
                                  {dict?.dashboard?.previewPackages?.cancelled || 'Cancelled'}
                                </div>
                                {/* 默认商品信息不显示商品编号、重量和体积，因为这些是占位符数据 */}
                              </div>
                            </div>

                            {/* 收件人 */}
                            <div className="text-center text-sm">
                              <div className={record.status === -1 ? 'text-gray-400' : ''}>{record.consignee}</div>
                            </div>

                            {/* 状态 */}
                            <div className="text-center text-sm">
                              <div className={`${record.status === -1 ? 'text-gray-400' : record.status === 0 ? 'text-orange-500' : record.status === 1 ? 'text-green-500' : 'text-blue-500'}`}>
                                {statusList[record.status] || record.status_text}
                              </div>
                            </div>

                            {/* 总费用 */}
                            <div className="text-center">
                              <div className={`text-lg font-bold ${record.status === -1 ? 'text-gray-400' : ''}`}>
                                {formatCurrency(parseFloat(record.rehearsal_fee || '0')).formatValue}
                              </div>
                              <div className={`text-xs ${record.status === -1 ? 'text-gray-400' : 'text-gray-500'}`}>
                                {dict?.dashboard?.previewPackages?.previewPackageFee || 'Preview Package Fee'} {formatCurrency(parseFloat(record.rehearsal_fee || '0')).formatValue}
                              </div>
                              {/* 只有当存在增值费且选择了附加服务时才显示 */}
                              {record.value_added_fee && parseFloat(record.value_added_fee) > 0 &&
                               record.server && Array.isArray(record.server) && record.server.length > 0 && (
                                <div className={`text-xs ${record.status === -1 ? 'text-gray-400' : 'text-gray-500'}`}>
                                  {dict?.dashboard?.previewPackages?.valueAddedFee || 'Value Added Fee'} {formatCurrency(parseFloat(record.value_added_fee)).formatValue}
                                </div>
                              )}
                              {/* 只有当存在服务费时才显示 */}
                              {record.service_fee && parseFloat(record.service_fee) > 0 && (
                                <div className={`text-xs ${record.status === -1 ? 'text-gray-400' : 'text-gray-500'}`}>
                                  {dict?.dashboard?.previewPackages?.serviceFee || 'Service Fee'} {formatCurrency(parseFloat(record.service_fee)).formatValue}
                                </div>
                              )}
                            </div>

                            {/* 操作 */}
                            <div className="text-center">
                              <div className="flex flex-col gap-2">
                                {/* 根据状态显示不同的操作按钮 */}
                                {record.status === 0 && (
                                  // 状态0 - 待付款：展示付款、取消按钮
                                  <div className="flex flex-col gap-1">
                                    <Link href={`/${lng}/pay?order_id=${record.id}&type=packageview`}>
                                      <Button
                                        size="small"
                                        className="bg-orange-500 text-white hover:bg-orange-600 text-xs px-4 py-1 w-full"
                                      >
                                        {dict?.dashboard?.previewPackages?.actions?.pay || 'Pay'}
                                      </Button>
                                    </Link>
                                    <Button
                                      size="small"
                                      className="border border-gray-300 text-gray-600 hover:border-gray-400 text-xs px-4 py-1"
                                      onClick={() => handleCancelPreview(record)}
                                    >
                                      {dict?.dashboard?.previewPackages?.actions?.cancel || 'Cancel'}
                                    </Button>
                                  </div>
                                )}

                                {record.status === 1 && (
                                  // 状态1 - 预演费用已付款：展示申请退款和支付运费按钮
                                  <div className="flex flex-col gap-1">
                                    <Button
                                      size="small"
                                      className="bg-red-500 text-white hover:bg-red-600 text-xs px-4 py-1"
                                      onClick={() => handleRefundApplication(record)}
                                    >
                                      {dict?.dashboard?.previewPackages?.actions?.applyRefund || 'Apply Refund'}
                                    </Button>
                                    <Button
                                      size="small"
                                      className="bg-blue-500 text-white hover:bg-blue-600 text-xs px-4 py-1"
                                      onClick={() => handleChangeShipping(record)}
                                    >
                                      {dict?.dashboard?.previewPackages?.actions?.payShipping || 'Pay Shipping'}
                                    </Button>
                                  </div>
                                )}

                                {record.status === 2 && (
                                  // 状态2 - 已打包：展示提交运单按钮
                                  <Button
                                    size="small"
                                    className="bg-blue-500 text-white hover:bg-blue-600 text-xs px-4 py-1"
                                    onClick={() => handleChangeShipping(record)}
                                  >
                                    {dict?.dashboard?.previewPackages?.actions?.submitWaybill || 'Submit Waybill'}
                                  </Button>
                                )}

                                {record.status === 3 && (
                                  // 状态3 - 运单未付款：展示支付运单按钮
                                  <Button
                                    size="small"
                                    className="bg-orange-500 text-white hover:bg-orange-600 text-xs px-4 py-1"
                                    onClick={() => handleFreightPayment(record)}
                                  >
                                    {dict?.dashboard?.previewPackages?.actions?.payWaybill || 'Pay Waybill'}
                                  </Button>
                                )}

                                {record.status === 4 && (
                                  // 状态4 - 运单已付款：仅显示文本，无操作按钮（只有预演费用支持退款）
                                  <div className="text-xs text-gray-500">
                                    {dict?.dashboard?.previewPackages?.status?.waybillPaid || 'Waybill Paid'}
                                  </div>
                                )}

                                {record.status === -1 && (
                                  // 状态-1 - 已取消：仅显示文本，无操作按钮
                                  <div className="text-xs text-gray-500">
                                    {statusList[record.status] || record.status_text || '已取消'}
                                  </div>
                                )}

                                {record.status === -2 && (
                                  // 状态-2 - 退款中：仅显示文本，无操作按钮
                                  <div className="text-xs text-gray-500">
                                    {statusList[record.status] || record.status_text || '退款中'}
                                  </div>
                                )}

                                {record.status === -3 && (
                                  // 状态-3 - 已退款：仅显示文本，无操作按钮
                                  <div className="text-xs text-gray-500">
                                    {statusList[record.status] || record.status_text || '已退款'}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    {dict?.dashboard?.previewPackages?.noData || 'No preview package data'}
                  </div>
                )}

                {/* Pagination */}
                {Math.ceil(pagination.total / pagination.pageSize) > 1 && (
                  <div className="px-6 py-4 border-t border-gray-200">
                    <Pagination
                      currentPage={pagination.current}
                      totalPages={Math.ceil(pagination.total / pagination.pageSize)}
                      onPageChange={handlePageChange}
                      prevText={dict?.dashboard?.previewPackages?.pagination?.prev || 'Previous'}
                      nextText={dict?.dashboard?.previewPackages?.pagination?.next || 'Next'}
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Detail modal */}
        <ModalComponent
          title={dict?.dashboard?.previewPackages?.detail?.title || 'Preview Package Details'}
          open={detailModalOpen}
          onCancel={() => setDetailModalOpen(false)}
          footer={[
            <Button key="close" onClick={() => setDetailModalOpen(false)}>
              {dict?.dashboard?.previewPackages?.detail?.close || 'Close'}
            </Button>
          ]}
          width={800}
        >
          {selectedRecord && (
            <div className="mt-4 max-h-[600px] overflow-y-auto">
              {/* Basic information */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div>
                  <strong>{dict?.dashboard?.previewPackages?.detail?.packageNumber || 'Package Number'}：</strong>{selectedRecord.sn}
                </div>
                <div>
                  <strong>{dict?.dashboard?.previewPackages?.detail?.username || 'Username'}：</strong>{selectedRecord.username || '-'}
                </div>
                <div>
                  <strong>{dict?.dashboard?.previewPackages?.detail?.status || 'Status'}：</strong>
                  <span className={`px-2 py-1 rounded-full text-xs ml-2 ${getStatusColor(selectedRecord.status)}`}>
                    {statusList[selectedRecord.status] || selectedRecord.status_text}
                  </span>
                </div>
                <div>
                  <strong>{dict?.dashboard?.previewPackages?.detail?.shippingMethod || 'Shipping Method'}：</strong>{selectedRecord.shipping_name || selectedRecord.shippingname}
                </div>
                <div>
                  <strong>{dict?.dashboard?.previewPackages?.detail?.previewFee || 'Preview Fee'}：</strong>
                  <span className="text-orange-600 font-medium">
                    {formatCurrency(parseFloat(selectedRecord.rehearsal_fee)).formatValue}
                  </span>
                </div>
                <div>
                  <strong>{dict?.dashboard?.previewPackages?.detail?.totalAmount || 'Total Amount'}：</strong>
                  <span className="font-medium">
                    {formatCurrency(selectedRecord.totalmoney).formatValue}
                  </span>
                </div>
                <div>
                  <strong>{dict?.dashboard?.previewPackages?.detail?.actualWeight || 'Actual Weight'}：</strong>{selectedRecord.actualweight || '-'}g
                </div>
                <div>
                  <strong>{dict?.dashboard?.previewPackages?.detail?.actualVolume || 'Actual Volume'}：</strong>{selectedRecord.actualvolume || '-'}cm³
                </div>
                <div>
                  <strong>{dict?.dashboard?.previewPackages?.detail?.createTime || 'Create Time'}：</strong>{selectedRecord.createtime}
                </div>
                <div>
                  <strong>{dict?.dashboard?.previewPackages?.detail?.updateTime || 'Update Time'}：</strong>{selectedRecord.updatetime}
                </div>
              </div>

              {/* Shipping address information */}
              {selectedRecord.address && (
                <div className="mb-6">
                  <h4 className="font-medium mb-3 text-gray-800">{dict?.dashboard?.previewPackages?.detail?.addressInfo || 'Shipping Address Info'}</h4>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="grid grid-cols-2 gap-3">
                      <div><strong>{dict?.dashboard?.previewPackages?.detail?.recipient || 'Recipient'}：</strong>{selectedRecord.address.consignee}</div>
                      <div><strong>{dict?.dashboard?.previewPackages?.detail?.phone || 'Phone'}：</strong>{selectedRecord.address.telephone}</div>
                      <div><strong>{dict?.dashboard?.previewPackages?.detail?.region || 'Region'}：</strong>{selectedRecord.address.mergename}</div>
                      <div><strong>{dict?.dashboard?.previewPackages?.detail?.postcode || 'Postcode'}：</strong>{selectedRecord.address.zip}</div>
                      <div className="col-span-2">
                        <strong>{dict?.dashboard?.previewPackages?.detail?.detailAddress || 'Detail Address'}：</strong>{selectedRecord.address.address}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Product information */}
              {selectedRecord.goods && selectedRecord.goods.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3 text-gray-800">{dict?.dashboard?.previewPackages?.detail?.productCount?.replace('{count}', selectedRecord.goods.length) || `Products Included (${selectedRecord.goods.length} items)`}</h4>
                  <div className="space-y-3">
                    {selectedRecord.goods.map((item, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-start gap-4">
                          <img
                            src={item.goodsimg.startsWith('//') ? `https:${item.goodsimg}` : item.goodsimg}
                            alt={item.goodsname}
                            className="w-16 h-16 object-cover rounded border"
                          />
                          <div className="flex-1">
                            <div className="font-medium text-sm mb-1">{item.goodsname}</div>
                            <div className="text-xs text-gray-500 mb-2">{item.skuname}</div>
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div><strong>{dict?.dashboard?.previewPackages?.detail?.productNumber || 'Product Number'}：</strong>{item.goodssn}</div>
                              <div><strong>{dict?.dashboard?.previewPackages?.detail?.quantity || 'Quantity'}：</strong>{item.goodsnum}</div>
                              <div><strong>{dict?.dashboard?.previewPackages?.detail?.productPrice || 'Product Price'}：</strong>{formatCurrency(parseFloat(item.goodsprice)).formatValue}</div>
                              <div><strong>{dict?.dashboard?.previewPackages?.detail?.serviceFee || 'Service Fee'}：</strong>{formatCurrency(item.serverfee).formatValue}</div>
                              <div><strong>{dict?.dashboard?.previewPackages?.detail?.weight || 'Weight'}：</strong>{item.goodsweight}g</div>
                              <div><strong>{dict?.dashboard?.previewPackages?.detail?.volume || 'Volume'}：</strong>{item.goodsvolume}cm³</div>
                              <div><strong>{dict?.dashboard?.previewPackages?.detail?.seller || 'Seller'}：</strong>{item.goodsseller}</div>
                              <div><strong>{dict?.dashboard?.previewPackages?.detail?.warehouse || 'Warehouse'}：</strong>{item.warehouse}</div>
                            </div>
                            {item.goodsremark && (
                              <div className="mt-2 text-sm">
                                <strong>{dict?.dashboard?.previewPackages?.detail?.remark || 'Remark'}：</strong>{item.goodsremark}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </ModalComponent>

        {/* 退款申请模态框 */}
        <ModalComponent
          title={dict?.dashboard?.previewPackages?.refund?.title || 'Apply for Refund'}
          open={refundModalOpen}
          onCancel={() => setRefundModalOpen(false)}
          footer={[
            <Button key="cancel" onClick={() => setRefundModalOpen(false)}>
              {dict?.dashboard?.previewPackages?.refund?.cancel || 'Cancel'}
            </Button>,
            <Button
              key="submit"
              type="primary"
              onClick={handleSubmitRefund}
              loading={loading}
              className="bg-orange-500 hover:bg-orange-600"
            >
              {dict?.dashboard?.previewPackages?.refund?.submit || 'Submit Application'}
            </Button>
          ]}
          width={500}
        >
          <div className="mt-4">
            {/* 预演包裹信息 */}
            {selectedRecord && (
              <div className="mb-4">
                <div className="text-sm font-medium mb-2">{dict?.dashboard?.previewPackages?.refund?.packageInfo || 'Preview Package Info'}</div>
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-600 mb-2">
                    <div>{dict?.dashboard?.previewPackages?.refund?.packageNumber || 'Preview Package Number'}：{selectedRecord.sn}</div>
                    <div>{dict?.dashboard?.previewPackages?.refund?.previewFee || 'Preview Fee'}：{formatCurrency(parseFloat(selectedRecord.rehearsal_fee || '0')).formatValue}</div>
                  </div>

                  {/* 商品信息 */}
                  {selectedRecord.goods && selectedRecord.goods.length > 0 && (
                    <div className="border-t pt-3">
                      <div className="text-sm font-medium mb-2">{dict?.dashboard?.previewPackages?.refund?.productInfo || 'Product Information'}</div>
                      {selectedRecord.goods.map((item: any, index: number) => (
                        <div key={index} className="flex items-center mb-3 last:mb-0">
                          <img
                            src={item.goodsimg || item.pic_url || '/images/default.jpg'}
                            alt={item.goodsname || item.title}
                            className="w-12 h-12 rounded object-cover mr-3 flex-shrink-0"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium text-gray-900 truncate">
                              {item.goodsname || item.title}
                            </div>
                            <div className="text-xs text-gray-500 truncate">
                              {item.skuname && `${dict?.dashboard?.previewPackages?.detail?.spec || 'Specification'}：${item.skuname}`}
                              {item.goodsnum && ` ${dict?.dashboard?.previewPackages?.detail?.quantity || 'Quantity'}：${item.goodsnum}`}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">{dict?.dashboard?.previewPackages?.refund?.reasonRequired || 'Refund Reason'} <span className="text-red-500">*</span></label>
              <textarea
                value={refundContent}
                onChange={(e) => setRefundContent(e.target.value)}
                placeholder={dict?.dashboard?.previewPackages?.refund?.reasonPlaceholder || 'Please explain the refund reason in detail...'}
                rows={4}
                maxLength={500}
                className="w-full border rounded p-3 resize-none focus:outline-none focus:border-orange-500"
              />
              <div className="text-right text-gray-400 text-sm mt-1">
                {refundContent.length}/500
              </div>
            </div>
          </div>
        </ModalComponent>

        {/* 更改运费方式模态框 */}
        <ModalComponent
          title={dict?.dashboard?.previewPackages?.shipping?.title || 'Change Shipping Method'}
          open={shippingModalOpen}
          onCancel={() => setShippingModalOpen(false)}
          footer={[
            <Button key="cancel" onClick={() => setShippingModalOpen(false)}>
              {dict?.dashboard?.previewPackages?.shipping?.cancel || 'Cancel'}
            </Button>,
            <Button
              key="submit"
              type="primary"
              onClick={handleSubmitShippingChange}
              loading={loading}
              className="bg-orange-500 hover:bg-orange-600"
            >
              {dict?.dashboard?.previewPackages?.shipping?.confirm || 'Confirm Change'}
            </Button>
          ]}
          width={500}
        >
          <div className="mt-4">
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">{dict?.dashboard?.previewPackages?.shipping?.templateRequired || 'Select Shipping Template'} <span className="text-red-500">*</span></label>
              <Select
                value={selectedShippingTemplate}
                onChange={setSelectedShippingTemplate}
                placeholder={dict?.dashboard?.previewPackages?.shipping?.templatePlaceholder || 'Please select shipping template'}
                className="w-full"
              >
                {/* TODO: Load shipping template options dynamically */}
                <Option value={1}>{dict?.dashboard?.previewPackages?.shipping?.standardTemplate || 'Standard Shipping Template'}</Option>
                <Option value={2}>{dict?.dashboard?.previewPackages?.shipping?.fastTemplate || 'Fast Shipping Template'}</Option>
                <Option value={3}>{dict?.dashboard?.previewPackages?.shipping?.economyTemplate || 'Economy Shipping Template'}</Option>
              </Select>
            </div>
            {selectedRecord && (
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-600">
                  <div>{dict?.dashboard?.previewPackages?.shipping?.packageNumber || 'Preview Package Number'}：{selectedRecord.sn}</div>
                  <div>{dict?.dashboard?.previewPackages?.shipping?.currentMethod || 'Current Shipping Method'}：{selectedRecord.shipping_name || selectedRecord.shippingname}</div>
                </div>
              </div>
            )}
          </div>
        </ModalComponent>

        {/* 取消预演包裹确认模态框 */}
        <ModalComponent
          title={dict?.dashboard?.previewPackages?.confirmCancel || 'Confirm Cancel'}
          open={cancelModalOpen}
          onOk={handleConfirmCancel}
          onCancel={() => {
            setCancelModalOpen(false)
            setRecordToCancel(null)
          }}
          okText={dict?.dashboard?.previewPackages?.confirm || 'Confirm'}
          cancelText={dict?.dashboard?.previewPackages?.cancel || 'Cancel'}
          centered
        >
          <p>
            {recordToCancel && (
              dict?.dashboard?.previewPackages?.confirmCancelContent?.replace('{sn}', recordToCancel.sn) ||
              `Are you sure you want to cancel preview package ${recordToCancel.sn}? This action cannot be undone.`
            )}
          </p>
        </ModalComponent>
      </div>
  )
}
