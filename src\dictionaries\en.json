{"title": "Onebuy - Global Shopping Platform", "description": "Onebuy - Global Shopping Platform", "common": {"retry": "Retry", "manualEntry": "Manual Entry", "pageNotFound": "Page Not Found", "errorMessage": "Error Message", "viewDetails": "View Details", "goBack": "Go Back"}, "nav": {"home": "Home", "about": "About", "transport": "Transport", "alliance": "Alliance", "shipping": "Shipping Estimate", "help": "Help Center", "contact": "Contact Us", "more": "More", "login": "<PERSON><PERSON>", "register": "Register", "selfservice": "Manual Order", "sizecomparison": "Size Guide", "dashboard": "Dashboard", "orders": "Orders", "warehouse": "Warehouse", "packages": "Packages", "previewPackages": "Preview Packages", "logout": "Logout", "backToHome": "Back to Home"}, "confirm": {"server": {"unpack": "Unpack", "pack": "Pack", "photo": "Photo", "insurance": "Insurance"}, "onepayorder": {"title": "One-Time Payment Order Confirmation", "couponNotice": "One-time payment orders can only use coupons with 'All' usage scope", "shippingAddress": "Shipping Address", "shippingMethod": "Shipping Method", "productSummary": "Product Summary", "packageServices": "Package Additional Services", "productTotal": "Product Total", "packageSummary": "Package Summary", "packageTotal": "Package Total", "grandTotal": "Grand Total", "submitOrder": "Submit One-Time Payment Order", "coupon": {"title": "Available Coupons", "noCoupons": "No available coupons", "selectCoupon": "Select Coupon", "loading": "Loading...", "fetchingCoupons": "Fetching coupons", "validUntil": "Valid until", "noExpiry": "No expiry"}, "summary": {"productSummary": "Product Summary", "productAmount": "Product Amount", "shippingToChina": "Shipping to China", "orderAdditionalService": "Order Additional Service Fee", "productServiceFee": "Product Service Fee", "couponDiscount": "Coupon Discount", "productPayable": "Product Payable Amount", "packageAdditionalServices": "Package Additional Services", "packageSummary": "Package Summary", "discountRate": "Discount Rate", "deliveryFee": "Delivery Fee", "customsFee": "Customs Fee", "transportServiceFee": "Transport Service Fee", "fuelFee": "Fuel Fee", "internationalShipping": "International Shipping", "packageServiceFee": "Package Service Fee", "packageAdditionalServiceFee": "Package Additional Service Fee", "packagePayable": "Package Payable Amount", "saved": "Saved", "total": "Total", "serviceTerms": "Onebuy Service Terms", "returnPolicy": "Return & Exchange Policy", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "faq": "FAQ", "agreeTerms": "I have read and agree to the terms", "submitting": "Submitting...", "submit": "Submit", "notice": "Notice", "noticeText": "After submitting the order and payment, we will purchase and ship for you. Please wait patiently. After shipping, you can track the package through the international tracking number in the package.", "loading": "Loading data..."}, "validation": {"selectAddressAndShipping": "Please select shipping address and shipping method", "cartInfoMissing": "Cart information is missing", "agreeTermsFirst": "Please read and agree to the terms first", "fillPhotoRemarks": "Please fill in photo remarks"}, "messages": {"orderSubmitSuccess": "Order submitted successfully", "orderSubmitFailed": "Order submission failed", "orderSubmitRetry": "Order submission failed, please try again"}, "services": {"vacuum": "Vacuum Packaging", "photo": "Photo Service", "insurance": "Insurance", "insuranceDesc": "Product Total * 2%"}, "coupons": {"availableCoupons": "Available Coupons", "viewAll": "View All", "noCoupons": "No Coupons", "noCouponsDesc": "No available coupons"}, "productInfo": {"shippingToChina": "Shipping to China", "specifications": "Specifications", "additionalServices": "Additional Services", "packageInfo": "Package Information"}, "address": {"shippingAddress": "Shipping Address", "addNewAddress": "Add New Address", "default": "<PERSON><PERSON><PERSON>", "noAddresses": "No addresses yet", "noAddressesFound": "No addresses found"}, "shipping": {"shippingMethod": "Shipping Method", "shippingFee": "Shipping Fee", "deliveryFee": "Delivery Fee", "shippingNotice": "Shipping Notice", "actualShippingNotice": "Actual shipping fee is subject to final settlement. We will notify you promptly if there are any differences after warehouse re-measurement", "volumeWeightFormula": "Volume Weight = Length(cm) × Width(cm) × Height(cm) ÷ Volume Weight", "chargeableWeightFormula": "Chargeable Weight = max (Actual Weight, Volume Weight)", "securityCheckNotice": "All packages are shipped after security inspection"}}, "order": {"productInfo": "Product Information", "noProducts": "No product information", "returnToCart": "Please return to cart and select products again", "invalidData": "Invalid product data", "refreshPage": "Please refresh the page or select products again", "officialStore": "Official Store", "coupons": {"fetchFail": "Failed to load coupons", "list": "Coupon List", "applySuccess": "Coupon applied successfully", "activateFail": "Failed to activate coupon!"}, "errors": {"missingInfo": "Missing order information", "submitFail": "Failed to submit order", "missingAddressAndShipping": "Please select shipping address and shipping method", "missingAddress": "Please select shipping address", "previewSubmitSuccess": "Preview package submitted successfully", "previewSubmitFail": "Failed to submit preview package!", "previewSubmitError": "Error submitting preview package", "fetchServerListFail": "Failed to fetch additional services"}, "summary": {"title": "Order Summary", "serviceFee": "Service charge", "retailPrice": "Retail Price", "discount": "Discount", "shippingFee": "Shipping Fee", "coupon": "Coupon", "extraService": "Extra Service", "serverfee": "Order service fee", "total": "Total", "saved": "You Saved", "excludeShipping": "Excluding international shipping", "returnPolicy": "《Return Policy》", "terms": "《Terms of Service》", "privacy": "《Privacy Policy》", "faq": "《FAQ》", "agreement": "I agree to the terms", "submit": "Submit", "internationalFee": "International Shipping Fee", "photoCount": "Photo Quantity", "photoUnit": "Photos", "remark": "Remarks", "remarkHint": "Enter remarks", "totalFee": "Total Fee", "savedAmount": "You Saved", "includeShipping": "Includes Shipping", "includeService": "Inc. Services", "previewPackageFee": "Preview Package Fee", "includePreviewPackageFee": "Include Preview Package Fee", "includeAdditionalServiceFee": "Include Additional Service Fees", "submitPreviewPackage": "Submit Preview Package", "tips": {"title": "Tips", "content": "After submitting and payment, please wait for seller to ship. We'll notify you when items arrive at warehouse."}}, "addition": {"service": "Additional Services", "photoCount": "Photo Quantity", "photo": "Photos", "remark": "Remarks", "remarkPlaceholder": "Please enter remarks"}, "transport": {"title": "International Shipping", "fetchError": "Failed to load shipping methods", "loadError": "Error loading shipping options", "addressFirst": "Please select address first", "estimate": "Estimated Delivery", "restriction": "Restrictions", "feature": "route characteristic", "noAvailable": "No available shipping methods", "firstWeight": "First Weight", "continueWeight": "Additional Weight", "weightRange": "Weight Range"}}, "sendorder": {"sendorderTitle": "Waybill Summary", "discount_rate": "discount rate", "saved": "Saved"}}, "home": {"banner": {"title": "Global Access to Chinese Products at Affordable Prices", "subtitle": "One-Stop Cross-Border Shopping Solution"}, "all": "All", "hotProducts": "Hot Products", "emptyList": "List is empty", "shoppingTime": "Shopping Time", "services": {"title": "One-Stop Shopping Service", "noData": "No services available at the moment", "items": {"easyOrder": {"title": "Easy Ordering", "description": "No need for accounts on major e-commerce platforms. Place orders easily on our platform in seconds and experience Chinese products."}, "warehouse": {"title": "China Warehouse Procurement", "description": "Choose your desired products, and we'll help you procure them to our warehouse in China, ensuring product safety."}, "inspection": {"title": "Inspection Service", "description": "Once your products arrive at the Onebuy warehouse, you can request our professional service team to take photos of your items, ensuring what you see online matches reality."}, "shipping": {"title": "International Shipping", "description": "After receiving your products at our warehouse, we can ship your packages to any country worldwide using fast and reliable logistics services."}}}, "footer": {"about": {"title": "Onebuy", "description": "Onebuy is your trusted global shopping partner. We offer professional overseas purchasing and shipping services, allowing you to shop globally without leaving your home. Excellent service, quality assurance, making cross-border shopping easy and simple."}, "service": {"title": "Customer Service", "items": {"help": "Help Center", "faq": "FAQ", "contact": "Contact Us"}}, "guide": {"title": "Shopping Guide", "items": {"taobao": "Taobao Purchase", "tutorial": "Beginner's Guide", "checklist": "Shopping Checklist"}}, "payment": {"title": "Payment Methods", "methods": ["Visa", "MC", "AMEX", "PP", "UnionPay"]}, "copyright": "Copyright © 2020-2024 Onebuy.com All Rights Reserved"}, "error": {"title": "Optimizing your experience", "description": "Detected need to refresh data, redirecting to optimization page...", "autoRedirect": "If the page doesn't redirect automatically, please click the button below", "goToOptimize": "Go to optimization page"}}, "footer": {"learn": "Learn", "examples": "Examples", "goToNextJs": "Go to nextjs.org →", "paymentMethods": "Payment Methods", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "cookiePolicy": "<PERSON><PERSON>", "disclaimer": "Disclaimer", "copyright": "Copyright © 2020-2024 Onebuy.com All Rights Reserved", "description": "Onebuy is your trusted global shopping partner. We offer professional overseas purchasing and shipping services, allowing you to shop globally without leaving your home. Excellent service, quality assurance, making cross-border shopping easy and simple."}, "languageSwitcher": {"selectLanguage": "Select language", "selectCurrency": "Select currency"}, "about": {"title": "About Next.js Internationalization", "solution": "Next.js App Router Internationalization Solution", "features": {"dynamicRoutes": "Using App Router dynamic route parameters [lng] to handle different languages", "middleware": "Using middleware to intercept requests, automatically detect user language and redirect", "jsonStorage": "Storing translation content in JSON files for different languages", "clientComponent": "Implementing language switching functionality with client components", "serverComponent": "Loading translation content through server components"}}, "help": {"title": "Help Center", "subtitle": "Comprehensive shopping guides and frequently asked questions", "views": "Views", "search": {"placeholder": "Search help content...", "button": "Search", "noResults": "No relevant content found", "resultsCount": "Found {count} relevant results"}, "categories": {"title": "Help Categories", "viewAll": "View All", "articles": "articles"}, "articles": {"title": "Related Articles", "readMore": "Read More", "backToCategories": "Back to Categories", "lastUpdated": "Last Updated", "relatedArticles": "Related Articles"}, "loading": "Loading...", "error": "Article content does not exist or failed to load", "empty": "No content available"}, "search": {"foundProducts": "Found {query} related products, about {count} items", "searchResults": "Search Results", "productSource": "Product Source:", "all": "All", "taobao": "Taobao", "1688": "1688", "jd": "JD", "micro": "Micro", "wechat": "WeChat", "sortBy": "Sort by:", "default": "<PERSON><PERSON><PERSON>", "sales": "Sales", "price": "Price", "sold": "{count} sold", "placeholder": "Enter product name/link", "searchButton": "Search", "hotSearch": "Hot Search:", "hotWords": ["Phone", "Dresses", "Sneakers"], "noResults": {"title": "No Products Found", "description": "No related products found, please try other keywords or manual entry"}, "timeout": {"title": "Loading Timeout", "description": "Network connection is slow, please try again later or try manual entry"}, "error": {"title": "Loading Failed", "description": "An error occurred while fetching product information, please retry or try manual entry"}}, "mall": {"title": "Mall Showcase", "subtitle": "Selected products, quality guaranteed", "products": "products", "more": "More", "backToMall": "Back to Mall", "categories": {"title": "Categories", "all": "All Categories"}, "sort": {"title": "Sort by", "default": "<PERSON><PERSON><PERSON>", "sales": "Sales", "credit": "Credit", "priceAsc": "Price: Low to High", "priceDesc": "Price: High to Low"}, "product": {"viewDetail": "View Details", "addToCart": "Add to Cart", "price": "Price", "sales": "Sales", "credit": "Credit"}, "pagination": {"prev": "Previous", "next": "Next", "total": "Total {total} products"}, "loading": "Loading...", "error": {"loadCategories": "Failed to load categories", "loadProducts": "Failed to load products", "loadFloors": "Failed to load floors", "invalidCategory": "Invalid category ID", "retry": "Retry"}, "empty": {"title": "No Products", "description": "No products available in this category", "noProducts": "No products", "floorEmpty": "No products in this floor", "noFloors": "No floor data", "tryLater": "Please try again later"}}, "login": {"title": "<PERSON><PERSON>", "orText": "or", "emailLabel": "Email", "emailPlaceholder": "Enter your email", "passwordLabel": "Password", "passwordPlaceholder": "Enter your password", "forgotPassword": "Forgot password?", "rememberMe": "Remember me", "loginButton": "Sign in", "noAccount": "There is no account yet?", "goRegister": "Go Register", "socialLoginText": "Or continue with", "invalidEmail": "Please enter a valid email address", "loginFailed": "<PERSON><PERSON> failed, please check your account and password", "facebookLogin": "Continue with Facebook", "googleLogin": "Continue with Google", "login": "Be logging in ...", "emailRequired": "Please enter your email", "codePlaceholder": "Email verification code", "codeRequired": "Please enter email verification code", "sendCode": "Send Code", "sending": "Sending", "codeSent": "Verification code sent", "sendCodeError": "Failed to send verification code, please try again later"}, "register": {"title": "Register", "emailRequired": "Please enter your email", "invalidEmail": "Please enter a valid email address", "sendCodeError": "Failed to send verification code, please try again later", "passwordRule": "Password must be 6-16 characters long and contain uppercase, lowercase letters and numbers", "passwordMismatch": "Passwords do not match", "agreeTermsRequired": "Please agree to the Terms of Service and Privacy Policy", "registerError": "Registration failed, please try again later", "backToHome": "Back to Home", "emailPlaceholder": "Email", "passwordPlaceholder": "Password", "confirmPasswordPlaceholder": "Confirm Password", "invitationCodePlaceholder": "Invitation Code (optional)", "agreeToTerms": "I have read and agree to", "userAgreement": "User Agreement", "declaration": "Declaration", "registerButton": "Register", "haveAccount": "Already have an account?", "loginLink": "<PERSON><PERSON>", "enterVerificationCode": "Please enter verification code", "cancel": "Cancel", "confirm": "Confirm", "verificationCodeSent": "Verification code has been sent to your email", "resend": "Resend", "agreeToTermsText": "I have read and agree to", "and": "and", "sendCodeFailed": "Failed to send verification code, please try again later", "registerFailed": "Registration failed, please try again later", "mobileRequired": "Please enter mobile number", "invalidMobile": "Please enter a valid mobile number", "mobilePlaceholder": "Mobile Number", "mobileCodePlaceholder": "Enter mobile verification code", "sendMobileCode": "Send Mobile Code", "mobileCodeSent": "Mobile verification code sent", "sendMobileCodeFailed": "Failed to send mobile verification code, please try again later", "selectCountryCode": "Select Country/Region", "verificationCodeSentMobile": "Verification code has been sent to your mobile"}, "forgot": {"title": "Forgot Password", "enterEmail": "Please enter your email, we will send a verification code", "enterCode": "Please enter verification code", "enterPassword": "Please enter new password", "emailPlaceholder": "Email", "codePlaceholder": "Verification Code", "passwordPlaceholder": "New Password", "confirmPasswordPlaceholder": "Confirm New Password", "sendCodeButton": "Send Code", "verifyButton": "Verify", "resetButton": "Reset Password", "backToVerify": "Back to Verify", "backToEmail": "Back to Email", "rememberPassword": "Remember password?", "loginLink": "<PERSON><PERSON>", "emailRequired": "Please enter email", "invalidEmail": "Please enter a valid email address", "sendCodeError": "Failed to send verification code, please try again later", "codeRequired": "Please enter verification code", "codeError": "Verification code error, please re-enter", "codeVerifyError": "Verification code verification failed, please try again later", "passwordRequired": "Please enter new password", "passwordRule": "Password must be 6-16 characters long and contain uppercase, lowercase letters and numbers", "passwordMismatch": "Passwords do not match", "resetError": "Reset password failed, please try again later", "emailSent": "Email sent", "emailSendError": "Email send failed", "countdownText": "seconds before resend", "countdownReturn": "seconds before return"}, "dashboard": {"home": {"profile": {"title": "Profile", "description": "Here shows your personal information, including avatar, nickname and ID", "signin": "Check In", "signinSuccessMsg": "Check in successful!", "signinErrorMsg": "Check in failed", "loading": "Loading...", "alreadySigned": "Already checked in today", "signingIn": "Checking in..."}, "anno": {"title": "Announcement", "empty": "empty"}, "menu": {"profile": "Member Center", "cart": "Shopping Cart", "favorites": "Favorites"}, "balance": {"title": "Balance", "description": "Here is your account balance, you can recharge and withdraw"}, "vip": {"title": "VIP", "description": "Check out the latest activities!"}, "history": {"title": "My History", "empty": "No history yet", "clearAll": "Clear History", "confirmClear": "Confirm to clear", "confirm": "Confirm", "cancel": "cancel", "clearWarning": "Are you sure you want to clear all the history? This operation cannot be restored"}, "signin": {"title": "Sign In"}}, "orders": {"status": {"pending": "Pending Payment", "paid": "Paid", "send": "Send", "processing": "Processing", "shipped": "Shipped", "completed": "Completed", "cancelled": "Cancelled"}, "steps": {"order": "Order", "payment": "Payment", "inspection": "Inspect", "shipping": "Ship", "receipt": "Receive"}, "list": {"searchPlaceholder": "Shop Name / Order Number / Product", "startDate": "Start Date", "endDate": "End Date", "searchButton": "Search", "orderMsg": "Order Message", "message": "Message", "inputHint": "Enter your message", "confirmBtn": "Confirm", "cancelBtn": "Cancel", "msgSuccess": "Message Success", "msgFail": "Message Fail", "sendMessage": "Send a message", "noMessages": "No messages yet", "photographyRequirements": "Photography requirements", "status": {"all": "All", "pendingReview": "Pending Review", "waitingPayment": "Waiting Payment", "paid": "Paid", "shipped": "Shipped", "delivered": "Delivered", "invalid": "Invalid"}, "item": {"cancel": "Cancel", "confirmCancel": "Confirm Cancel", "confirm": "Confirm", "cancelPrompt": "Are you sure to cancel this order?", "cancelSuccess": "Order cancelled", "cancelFail": "Cancel failed", "shop": "Shop", "orderNo": "Order Number", "createTime": "Created", "payment": "Payment", "awaitingSupplement": "Pending payment", "detail": "Order Details"}, "goodsName": "Product Name", "totalPrice": "Total Price", "quantity": "Quantity", "operation": "Operation", "freight": "Freight", "logistics": "logistics", "unitPrice": "Unit Price", "price": "Price"}, "detail": {"title": "Order Info", "orderNo": "Order No", "orderType": "Order Type", "selfOrder": "Self-Operated", "normalOrder": "Normal Order", "sellerName": "<PERSON><PERSON>", "expressCompany": "Express company", "expressNo": "Express number ", "orderStatus": "Status", "orderTime": "Order Time", "payMethod": "Payment", "onlinePay": "Online Pay", "goodsList": "Items", "goodsImg": "Image", "goodsInfo": "Product Info", "price": "Unit Price", "quantity": "Qty", "status": "Status", "subtotal": "Subtotal", "remark": "Note", "goodsAmount": "Amount", "serviceFee": "Service Fee", "domesticFee": "Domestic Shipping", "totalPay": "Total", "backToList": "Back to List"}}, "account": {"changeMobile": {"title": "Account", "newMobile": "New Mobile Number", "verificationCode": "Verification Code", "sendCode": "Send Code", "countdown": "{countdown}s"}, "changeEmail": {"title": "Change Email", "newEmail": "New Email", "verificationCode": "Verification Code", "sendCode": "Send Code", "countdown": "{countdown}s"}, "title": "Profile", "security": "Account Security", "saveSuccess": "Saved successfully", "saveFail": "Save failed", "formError": "Please complete all fields", "pwdMismatch": "Passwords do not match", "updateSuccess": "Updated successfully", "updateFail": "Update failed", "phoneHint": "Enter new phone number", "codeSent": "Verification code sent", "sendFail": "Failed to send", "emailHint": "Enter new email", "avatarSuccess": "Avatar uploaded", "avatarTypeError": "Please select image (jpg, png, etc.)", "avatarSizeError": "Image size exceeds 5MB", "avatarSaveFail": "Failed to save avatar", "uploadFail": "Upload failed", "avatarUploadFail": "Avatar upload failed", "uploadTitle": "Upload Avatar", "username": "Username", "gender": "Gender", "male": "Male", "female": "Female", "email": "Email", "modifyEmail": "Change Email", "phone": "Phone", "modifyPhone": "Change Phone", "birthday": "Birthday", "reset": "Reset", "save": "Save", "changePwd": "Change Password", "pwdTips": "Stronger passwords make your account more secure. We recommend changing your password regularly. Password must contain letters and numbers, with length over 6 characters.", "oldPwd": "Current Password", "newPwd": "New Password", "confirmPwd": "Confirm Password", "confirm": "Confirm", "cancel": "Cancel", "payPassword": {"title": "Payment Password", "tips": "After setting a payment password, you need to verify the password when making payments to enhance account security.", "set": "Set Payment Password", "change": "Change Payment Password", "notSet": "Not Set", "alreadySet": "Already Set", "sendCode": "Send Email Code", "createTitle": "Set Payment Password", "changeTitle": "Change Payment Password", "newPassword": "New Payment Password", "confirmPassword": "Confirm Payment Password", "currentPassword": "Current Payment Password", "passwordPlaceholder": "Please enter 6-digit password", "confirmPlaceholder": "Please enter password again", "currentPlaceholder": "Please enter current password", "verifyCodePlaceholder": "Please enter verification code", "verifyCodeRequired": "Please enter verification code", "noEmail": "Please bind email first", "passwordMismatch": "The two passwords entered do not match", "passwordFormat": "Payment password must be 6 digits", "setSuccess": "Payment password set successfully", "changeSuccess": "Payment password changed successfully", "setFail": "Failed to set payment password", "changeFail": "Failed to change payment password", "validateFail": "Payment password verification failed", "required": "Please enter payment password"}}, "sidebar": {"message": "Messages", "address": "Address", "referral": "Referral Program", "question": "consult"}, "wallet": {"title": "Balance", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "records": "Transactions", "all": "All", "income": "Income", "expense": "Expense", "time": "Time", "category": "Category", "inOut": "In/Out", "remaining": "Remaining", "transNo": "Trans No", "remark": "Note", "noRecords": "No records", "points": "Points", "pointUnit": "Pts", "pointExchange": "Redeem Coupons", "pointLog": "Points Log", "pointType": "Points Type", "shopPoints": "Shopping Pts", "checkinReward": "Check-in Reward", "pointConvert": "Redeem", "reviewReward": "Review Reward", "eventReward": "Event Reward", "coupon": "Coupon", "alipayDeposit": "<PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "orderRefund": "Order Refund", "orderPayment": "Order Payment", "depositFailed": "Deposit Failed", "networkError": "Network Error, Please Try Again", "getRecordsFailed": "Failed to get transaction records", "getFail": "Failed to get coupons", "inputTip": "Enter coupon code", "activeOk": "Activated successfully", "activeFail": "Activation failed", "canUse": "Available", "used": "Used", "expired": "Expired", "inputTitle": "Coupon code", "activeBtn": "Activate", "loading": "Loading", "noData": "No coupons", "validDate": "Valid until", "noExpiry": "None", "redeemNow": "Redeem Now", "pointsNeeded": "Points Required", "redeemTitle": "Points Redemption", "redeemSuccess": "Redeemed Successfully", "redeemFail": "Redemption Failed", "noPtsRecords": "No Points Records", "transactionRecords": "Transaction Records", "withdrawalRecords": "Withdrawal Records", "remittanceRecords": "Remittance Records", "recharge": {"title": "Recharge", "prompt": "Please enter a valid recharge amount", "fail": "Recharge failed", "success": "Recharge request submitted. Please complete the payment on the payment page", "failMsg": "Recharge failed. Please try again later", "confirm": "Confirm Recharge", "tip": "Enter or select a recharge amount", "quick": "Quick Selection"}}, "cart": {"steps": {"select": "Select", "confirm": "Confirm", "payProduct": "Pay Items", "inspect": "Inspect", "submit": "Submit", "submitPreview": "Submit Preview", "payShipping": "Ship", "receive": "Deliver"}, "empty": "Your shopping cart is currently empty", "actions": {"goShopping": "Go Shopping", "deleteSelected": "Delete Selected", "clearCart": "Clear Cart", "submit": "Submit", "selectAll": "Select All"}, "summary": {"title": "Order Summary", "tip": "Final price will be confirmed on the next page", "retailPrice": "Retail Price", "total": "Estimated Total", "excludeShipping": "Excluding international shipping"}, "notifications": {"quantityUpdateFail": "Failed to update quantity, please try again", "remarkCleared": "Remark cleared", "remarkUpdated": "Remark updated", "remarkUpdateFail": "Failed to update remark, please try again", "itemDeleted": "Item deleted", "deleteFail": "Failed to delete item, please try again", "noSelection": "Please select items to delete first", "batchDeleted": "Selected items deleted", "fetchFail": "Failed to load cart data", "batchDeleteFail": "Failed to delete items, please try again", "alreadyEmpty": "Cart is already empty", "cleared": "<PERSON><PERSON> cleared", "clearFail": "Failed to clear cart, please try again", "selectItems": "Please select items first", "1688QuantityNotAllowed": "1688 products do not allow quantity modification"}, "table": {"productInfo": "Product Info", "unitPrice": "Unit Price", "quantity": "Quantity", "subtotal": "Subtotal", "action": "Action", "remarks": "Remarks", "enterRemarks": "Please enter remarks", "addRemarks": "Add remarks"}, "hint": {"title": "Tips", "content": "This price excludes international shipping", "viewShipping": "view shipping details"}, "guessYouLike": "You May Also Like", "refresh": "Refresh", "emptyList": "List is empty", "pagination": {"prev": "Previous", "next": "Next"}}, "favorites": {"title": "My Favorites", "soldOut": "Sold Out", "emptyList": "No Favorites Yet", "clearAll": "Clear All", "confirmClear": "Confirm Clear", "confirm": "Confirm", "cancel": "Cancel", "clearWarning": "Are you sure to clear all favorites? This cannot be undone"}, "warehouse": {"title": "My Warehouse", "searchPlaceholder": "Search Items", "searchButton": "Search", "fetchError": "Failed to load data", "selectPrompt": "Please select items to submit", "itemInfo": "Item Info", "itemNumber": "Number", "pieces": "Pcs", "quantityWeight": "Qty/Weight", "totalPrice": "Total Price", "location": "Location", "unassigned": "Unassigned", "shippingInfo": "Shipping Info", "noData": "N/A", "noTracking": "No Tracking", "storageTime": "Storage Date", "status": "Status", "selectedCount": "Selected", "unit": "items", "emptyData": "No warehouse data", "selectAll": "Select All", "invertSelect": "Invert Selection", "clearAll": "Clear All", "submitShipping": "Submit Shipment", "total": "total", "items": "items"}, "packages": {"all": "All", "placeholder": "Package No", "search": "Search", "cancelOk": "Cancel success", "cancelFail": "Cancel failed", "receiveOk": "Received success", "receiveFail": "Received failed", "msgOk": "Message sent", "msgFail": "Message failed", "noMessages": "No messages yet", "confirmCancel": "Confirm cancel", "confirmReceipt": "Confirm receipt", "confirmReceiptMsg": "Are you sure you want to confirm receipt?", "ok": "OK", "cancel": "Cancel", "cancelAsk": "Confirm to cancel?", "orderMsg": "Package message", "submit": "Submit", "msgHint": "Enter message", "packageNo": "Package No", "createDay": "Create date", "shipFee": "Shipping fee", "extraFee": "Extra service", "additionalFee": "Extra service", "confirmGet": "Confirm receive", "viewPhoto": "View Photos", "sendorderPhoto": "Package picture", "noPhoto": "There are currently no pictures available", "leaveMsg": "Message", "cancelOrder": "Cancel order", "payment": "Payment", "table": {"productInfo": "Product Info", "weightVolume": "Weight/Volume", "price": "Price", "status": "Status", "actions": "Actions", "logistics": "View logistics"}, "YQTrackTitle": "Package logistics", "comment": "Comment", "commentTitle": "Package Comment", "commentContent": "Comment Content", "commentStar": "Rating", "commentImage": "Image", "commentSubmit": "Submit Comment", "commentSuccess": "Comment submitted successfully", "commentFail": "Failed to submit comment", "commentPlaceholder": "Please enter comment content", "commentRequired": "Please enter comment content", "commentStarRequired": "Please select a rating"}, "previewPackages": {"title": "Preview Package Management", "searchPlaceholder": "Search keywords", "keywordsLabel": "Keywords Search", "search": "Search", "reset": "Reset", "statusFilter": "Status Filter", "allStatus": "All Status", "noData": "No preview package data", "confirmCancel": "Confirm Cancel", "confirmCancelContent": "Are you sure to cancel preview package {sn}? This action cannot be undone.", "confirm": "Confirm Cancel", "cancel": "Cancel", "cancelSuccess": "Preview package cancelled successfully", "cancelFailed": "Failed to cancel preview package", "paymentSuccess": "Waybill generated successfully, redirecting to payment page...", "paymentFailed": "Failed to generate waybill", "refundReasonRequired": "Please enter refund reason", "refundSubmitSuccess": "Refund application submitted successfully", "refundSubmitFailed": "Failed to submit refund application", "shippingChangeSuccess": "Shipping method changed successfully", "shippingChangeFailed": "Failed to change shipping method", "selectShippingTemplate": "Please select shipping template", "getWaybillInfoFailed": "Failed to get waybill info", "payWaybillFailed": "Failed to pay waybill", "productNumber": "Product Number", "defaultProductInfo": "Preview Package Product Info", "cancelled": "Cancelled", "previewPackageFee": "Preview Package Fee", "valueAddedFee": "Value Added Fee", "serviceFee": "Service Fee", "table": {"productDetails": "Product Details", "weight": "Weight", "volume": "Volume", "recipient": "Recipient", "status": "Status", "totalFee": "Total Fee", "actions": "Actions"}, "packageInfo": "Preview Package Info", "packageNumber": "Preview Package Number", "createTime": "Create Time", "totalCost": "Total Cost", "previewFee": "Preview Package Fee", "valueFee": "Value Fee", "defaultProduct": "Preview Package Product Info", "waybillPaid": "Waybill Paid", "actions": {"pay": "Pay", "cancel": "Cancel", "applyRefund": "Apply Refund", "payShipping": "Pay Shipping", "submitWaybill": "Submit Waybill", "payWaybill": "Pay Waybill"}, "status": {"pendingPayment": "Pending Payment", "paid": "Paid", "packed": "Packed", "waybillUnpaid": "Waybill Unpaid", "waybillPaid": "Waybill Paid", "cancelled": "Cancelled", "refunding": "Refunding", "refunded": "Refunded"}, "confirmCancelBtn": "Confirm Cancel", "cancelBtn": "Cancel", "fetchListFailed": "Failed to fetch preview package list", "fetchStatusFailed": "Failed to fetch status list", "freightPaymentSuccess": "Freight payment submitted successfully", "freightPaymentFailed": "Failed to submit freight payment", "waybillGenerateSuccess": "Waybill generated successfully, redirecting to payment page...", "waybillGenerateFailed": "Failed to generate waybill", "pagination": {"prev": "Previous", "next": "Next"}, "detail": {"title": "Preview Package Details", "close": "Close", "basicInfo": "Basic Information", "packageNumber": "Package Number", "username": "Username", "status": "Status", "shippingMethod": "Shipping Method", "previewFee": "Preview Fee", "totalAmount": "Total Amount", "actualWeight": "Actual Weight", "actualVolume": "Actual Volume", "createTime": "Create Time", "updateTime": "Update Time", "addressInfo": "Shipping Address Info", "recipient": "Recipient", "phone": "Phone", "region": "Region", "postcode": "Postcode", "detailAddress": "Detail Address", "productInfo": "Product Information", "productCount": "Products Included ({count} items)", "productNumber": "Product Number", "quantity": "Quantity", "productPrice": "Product Price", "serviceFee": "Service Fee", "weight": "Weight", "volume": "Volume", "seller": "<PERSON><PERSON>", "warehouse": "Warehouse", "remark": "Remark", "spec": "Specification"}, "refund": {"title": "Apply for Refund", "cancel": "Cancel", "submit": "Submit Application", "packageInfo": "Preview Package Info", "packageNumber": "Preview Package Number", "previewFee": "Preview Fee", "productInfo": "Product Information", "reasonLabel": "Refund Reason", "reasonRequired": "Refund Reason *", "reasonPlaceholder": "Please explain the refund reason in detail...", "reasonHint": "Please fill in the refund reason", "submitSuccess": "Refund application submitted successfully", "submitFailed": "Failed to submit refund application"}, "shipping": {"title": "Change Shipping Method", "cancel": "Cancel", "confirm": "Confirm Change", "templateLabel": "Select Shipping Template", "templateRequired": "Select Shipping Template *", "templatePlaceholder": "Please select shipping template", "templateHint": "Please select shipping template", "standardTemplate": "Standard Shipping Template", "fastTemplate": "Fast Shipping Template", "economyTemplate": "Economy Shipping Template", "packageNumber": "Preview Package Number", "currentMethod": "Current Shipping Method", "changeSuccess": "Shipping method changed successfully", "changeFailed": "Failed to change shipping method"}}, "message": {"all": "All", "unread": "Unread", "read": "Read", "getFail": "Failed to get messages", "markFail": "<PERSON> as read failed", "markBtn": "<PERSON> as read"}, "question": {"title": "My Feedback", "submitBtn": "Submit <PERSON>", "listFail": "Failed to get feedback", "submitSuccess": "Submitted successfully", "submitFail": "Submission failed", "submitRetry": "Failed, please retry", "reply": "Reply", "cancel": "Cancel", "submit": "Submit", "content": "<PERSON><PERSON><PERSON>", "contentHint": "Enter feedback", "contentPlaceholder": "Describe your issue or suggestion..."}, "address": {"title": "Shipping Address", "text": "Address", "addNew": "Add New Address", "empty": "No Address Yet", "noAddressesFound": "No addresses found", "deleteSuccess": "Deleted Successfully", "deleteFail": "Failed to Delete", "default": "<PERSON><PERSON><PERSON>", "edit": "Edit", "delete": "Delete", "addTitle": "Add Address", "editTitle": "Edit Address", "receiver": "Recipient", "receiverHint": "Enter recipient name", "phone": "Phone", "phoneHint": "Enter phone number", "phoneError": "Please enter valid phone", "country": "Country", "countryHint": "Select country", "region": "Region", "regionHint": "Select region", "detail": "Address", "detailHint": "Enter detailed address", "detailMinLength": "Please enter a detailed address of no less than 5 digits", "postcode": "Postcode", "postcodeHint": "Enter postcode", "postcodeError": "Please enter the correct postal code", "setDefault": "Set as default", "cancel": "Cancel", "confirm": "Confirm", "countryFail": "Failed to load countries", "regionFail": "Failed to load regions", "updateSuccess": "Updated successfully", "addSuccess": "Added successfully", "addFail": "Failed to add", "updateFail": "Failed to update", "confirmDelete": "Confirm Delete", "confirmDeleteContent": "Are you sure you want to delete this address?"}, "referral": {"emailError": "Please enter valid email", "sendSuccess": "Invitation sent", "sendFail": "Send failed, please retry", "resendSuccess": "Resent to", "copySuccess": "Link copied", "copyFail": "Co<PERSON> failed", "recordTitle": "Commission Records", "recordId": "Commission ID", "subordinateUsers": "Subordinate Users", "shippingFee": "Shipping Fee", "commission": "Commission", "time": "Time", "noRecords": "No records", "pendingStatus": "Pending", "inviteEmail": "<PERSON><PERSON><PERSON>", "inviteTime": "Invite Time", "action": "Action", "resend": "Resend", "noPending": "No pending users", "registeredStatus": "Registered", "userEmail": "User Email", "regTime": "Register Time", "spendAmount": "Amount", "noRegistered": "No registered users", "totalCommission": "Total Commission", "copyLink": "Copy Link", "sendInvite": "Send Invite", "emailHint": "Enter friend's email", "sendBtn": "Send"}, "cash": {"title": "Withdrawal Records", "applyBtn": "Apply for Withdrawal", "time": "Application Time", "amount": "Amount", "fee": "Fee", "bank": "Bank Name", "cardNumber": "Card Number", "name": "Payee", "status": "Status", "noRecords": "No withdrawal records", "deleted": "Deleted", "reviewFailed": "Review Failed", "pending": "Pending", "approved": "Approved", "paidSuccess": "Payment Success", "paidFailed": "Payment Failed", "unknown": "Unknown Status"}, "remittance": {"title": "Remittance", "startDate": "Start Date", "endDate": "End Date", "searchBtn": "Search", "remitTime": "Remittance Time", "senderName": "Sender Name", "paymentMethod": "Payment Method", "bankName": "Bank Name", "amount": "Amount", "status": "Status", "processTime": "Process Time", "noRecords": "No Records"}, "addcash": {"feeRate": "Withdrawal Fee Rate", "serviceFee": "Service Fee", "feeRange": "Fee Charge Range", "receiverHint": "Enter payee name", "receiverLabel": "Payee", "accountLabel": "Account Number", "accountHint": "Enter bank card number", "accountRequired": "Bank card number required", "bankLabel": "Bank Institution", "bankHint": "Enter receiving bank", "selectBank": "Select Bank", "amountLabel": "Amount", "amountHint": "Enter transfer amount", "amountMultiple": "Amount must be multiples of 100", "totalAmount": "Total Payment", "receiveAmount": "Amount Received", "emailVerify": "Email Verification", "email": "Email", "verificationCode": "Verification Code", "sendCode": "Send Code", "confirm": "Confirm", "cancel": "Cancel", "networkError": "Network error, please try again", "submitSuccess": "<PERSON><PERSON><PERSON> submitted", "submitFailed": "<PERSON><PERSON><PERSON> failed"}, "onepayorders": {"title": "One-Time Payment Orders", "search": "Search by order number...", "searchPlaceholder": "Enter keywords to search...", "searchButton": "Search", "all": "All", "noOrders": "No one-time payment orders found", "noOrdersDescription": "You don't have any one-time payment orders yet", "viewDetails": "View Details", "cancel": "Cancel", "delete": "Delete", "confirmCancel": "Are you sure you want to cancel this one-time payment order?", "confirmDelete": "Are you sure you want to delete this one-time payment order? This action cannot be undone.", "cancelSuccess": "Order cancelled successfully", "deleteSuccess": "Order deleted successfully", "fetchError": "Failed to fetch orders", "orderNumber": "Order Number", "createTime": "Create Time", "shippingToChina": "Shipping to China", "orderTotal": "Order Total", "table": {"productInfo": "Product Info", "unitPrice": "Unit Price", "quantity": "Quantity", "message": "Message", "logisticsName": "Logistics Name", "logistics": "Logistics", "amountServiceFee": "Amount/Service Fee", "status": "Status/Actions"}, "product": {"group": "Group", "productAmount": "Product Amount", "internationalShipping": "International Shipping", "valueAddedFee": "Value-added Fee", "serviceFee": "Service Fee"}, "buttons": {"message": "Message", "dhlLogistics": "DHL Logistics", "pay": "Pay", "cancel": "Cancel", "generating": "Generating..."}, "orderMessage": "Order Message", "confirmBtn": "Confirm", "cancelBtn": "Cancel", "messageRequired": "Please enter a message", "messageSuccess": "Message sent successfully", "messageFailed": "Failed to send message", "inputHint": "Please enter your message...", "sendMessage": "Send Message", "noMessages": "No messages yet", "admin": "Admin", "statusLabels": {"all": "All", "pendingPayment": "Pending Payment", "paid": "Paid", "pendingShipment": "Pending Shipment", "shipped": "Shipped", "received": "Received", "invalid": "Invalid", "refundSuccess": "Refund Success"}, "detail": {"title": "One-Time Payment Order Details", "back": "Back", "orderInfo": "Order Information", "orderNumber": "Order Number", "status": "Status", "createdTime": "Created Time", "updatedTime": "Updated Time", "currency": "<PERSON><PERSON><PERSON><PERSON>", "totalAmount": "Total Amount", "shopTotal": "Shop Total", "otherFees": "Other Fees", "subOrder": "Sub Order", "seller": "<PERSON><PERSON>", "subOrderTotal": "Sub Order Total", "product": "Product", "price": "Price", "quantity": "Quantity", "total": "Total", "confirmReceipt": "Confirm Receipt", "confirmReceiptMsg": "Are you sure you want to confirm receipt of this order?", "receiptSuccess": "Receipt confirmed successfully"}}}, "detail": {"specifications": "Specifications", "size": "Size", "color": "Color", "productLink": "Product Link", "refresh": "Refresh", "outOfStock": "Out of Stock", "shippingFee": "Shipping Fee", "to": "To", "warehouse": "Warehouse", "quantity": "Quantity", "inventory": "Inventory", "remark": "Remarks", "remarkHint": "Enter remarks", "buyNow": "Buy Now", "adding": "Adding...", "addToCart": "Add to Cart", "favorited": "Favorited", "favorite": "Favorite", "details": "Details", "wholesalePrice": "Wholesale Price", "perPiece": "/piece", "addToCartSuccess": "Successfully added to cart!", "quantityNotMeet": "Quantity does not meet minimum requirement", "authError": "Authentication error, please refresh and try again", "emptyUrl": "Product URL cannot be empty", "productNotFound": "Product information not found", "image": "image", "sales ": "Sales", "loadFailed": "Product details loading timeout or failed.", "solution1": "You can", "refreshPage": "refresh the page", "solution2": "retry, or go to", "manualOrder": "Manual Order", "emptyProductId": "Product ID cannot be empty", "fetchProductFailed": "Failed to fetch product details", "loginRequired": "Please login to continue", "favoriteRemoved": "Removed from favorites", "favoriteAdded": "Added to favorites", "operationFailed": "Operation failed, please try again", "addToCartFailed": "Failed to add to cart, please try again", "buyFailed": "Purchase failed, please try again", "checkingFavoriteStatus": "Checking favorite status - start", "userNotLoggedIn": "User not logged in, setting favorite status to 0", "addHistoryFailed": "Failed to add browsing history", "noStockSku": "No SKU with stock found", "autoSelectedSku": "Auto-selected SKU", "favoriteOperationFailed": "Favorite operation failed", "originalApiResponse": "Original API response", "tryParseSkuData": "Trying to parse SKU data", "remarkNoMeet": "The minimum quantity for wholesale goods is {minQuantity} pieces. Please increase the quantity", "remarkMeet": "The minimum batch quantity requirement (≥{minQuantity} piece) has been met.", "tieredPrice": "Tiered Pricing", "unit": "Unit", "buyFailedError": "Purchase failed", "onePayOrder": "One-time Payment", "priceComparison": {"title": "Price Comparison", "refresh": "Refresh", "noProducts": "No comparison products available", "loading": "Loading comparison data...", "error": "Failed to load comparison data", "platforms": {"1688": "1688", "taobao": "Taobao", "jd": "JD", "micro": "Micro"}}}, "transport": {"steps": {"submit": "Submit Package", "description": "After receiving your package, you can select items to pack and ship in 'My Warehouse'.", "action": "Submit Package >", "payment": "Pay Shipping", "paymentDescription": "Your international package will be automatically cancelled if the weight exceeds the limit. Please pay promptly.", "paymentAction": "Pay International Shipping >"}, "guideTitle": "Forwarding Guide", "guideDesc": "Quickly learn the forwarding process for hassle-free overseas shopping. We provide professional forwarding services to solve your international logistics challenges.", "copyBtn": "Copy Onebuy Address", "addressTitle": "Shipping Address", "receiver": "Receiver: XXX-3-nodeanddeno", "phone": "Phone: 4008208820", "address": "Address: XXXX Road, Xinyu City, Jiangxi Province", "packageTitle": "Package Info", "packageNameHint": "Enter package name", "trackingNumberHint": "Enter tracking number", "deletePackage": "Delete Package", "addMore": "Add More", "agreement": "《Forwarding Agreement》", "submitBtn": "Submit", "agreeAndAccept": "Agree And Accept", "error": "Submission failed", "authError": "Please log in first", "success": "Submission successful", "copySuccess": "<PERSON><PERSON>d successfully", "copyFailed": "Co<PERSON> failed", "fillPackageInfo": "Please fill in package information", "fillComplete": "Please fill in completely", "agreeTerms": "Please agree to the forwarding service agreement", "receiverLabel": "Recipient:", "phoneLabel": "Phone:", "addressLabel": "Address:"}, "pay": {"online": "Online Payment", "offline": "Offline Payment", "title": "Order Payment", "titleCZ": "Recharge Payment", "payPreviewPackage": "Pay Preview Package", "orderNo": "Order Number", "amount": "Payment Amount", "actually": "Actually paid", "currencyFee": "Commission", "selectMethod": "Select Payment Method", "payNow": "Pay Now", "fail": "Payment Failed", "getLinkFail": "Failed to Get Payment Link", "success": "Payment Successful", "selectMethodHint": "Please Select Payment Method", "goUse": "go with continue", "orderNotExist": "Order Not Exist", "getOrderFail": "Failed to Get Order Info", "validity": "Validity", "coupon": "Coupon", "useNow": "Use Now", "discount": "Off", "cancel": "Cancel", "invalidAmount": "Payment amount cannot be less than 0", "couponFullDiscount": "Coupon has fully covered the order amount", "availableCoupons": "Available Coupons", "unavailableCoupons": "Unavailable Coupons", "used": "Used", "expired": "Expired", "noAvailableCoupons": "No Available Coupons", "noAvailableCouponsDesc": "You currently have no available coupons", "noUnavailableCoupons": "No Unavailable Coupons", "noUnavailableCouponsDesc": "You have no used or expired coupons", "viewAll": "View All", "collapse": "Collapse", "noExpiry": "None", "payPasswordVerify": {"title": "Payment Password Verification", "placeholder": "Please enter payment password", "confirm": "Confirm Payment", "cancel": "Cancel", "required": "Please enter payment password", "verifyFail": "Payment password verification failed", "tips": "For your account security, please enter payment password"}, "setPasswordReminder": {"title": "Set Payment Password", "tips": "For your account security, please set a payment password before making payments.", "goToSet": "Go to Set", "cancel": "Cancel"}, "offlinePay": {"title": "Offline Payment", "rechargeNotice": "Offline Recharge Notice", "verifyInfo": "Please verify remittance info and upload proof", "notTransferred1": "If you haven't transferred", "notTransferred2": "If completed, skip to step 2", "notTransferred3": "Get payee info for bank transfer", "notTransferred4": "Double-check spelling", "region": "Region", "paymentMethod": "Payment Method", "payeeInfo": "Payee Info", "payeeName": "Payee Name", "payeeAccount": "Payee Account", "description": "Description", "transferred1": "If already transferred", "remitterName": "Remitter Name", "remitterNameHint": "Enter remitter name", "remitterBank": "Remitter Bank", "remitterBankHint": "Enter remitter bank", "voucherNo": "Voucher Number", "voucherNoHint": "If provided by bank", "voucherImage": "Voucher Image", "voucherImageHint": "Upload remittance proof", "voucherPreview": "Voucher Preview", "currency": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Amount", "submitBtn": "I've transferred, submit info", "fetchError": "Failed to get payment methods", "fetchAreaError": "Failed to get area list", "submitSuccess": "Submitted successfully!", "submitFailed": "Submission failed!", "imageTypeError": "Select image (jpg, png, etc.)", "imageSizeError": "Max 5MB per image", "uploadError": "Upload failed, please retry!", "uploadSuccess": "Upload success!", "uploadImage": "Upload Image"}}, "order": {"progress": {"order": "Order", "check": "Inspect", "submit": "Submit", "receive": "Receive"}}, "estimate": {"bannerTitle": "Global Shipping Made Easy", "bannerDesc": "Fast, secure and reliable international shipping services to deliver your packages worldwide. Accurate freight calculation and transparent pricing for the best delivery experience.", "title": "Shipping Estimate", "destination": "Destination", "selectDestination": "Select Destination", "province": "Province/State", "selectProvince": "Select Province/State", "goodsType": "Goods Type", "selectGoodsType": "Select Goods Type", "weight": "Weight", "inputWeight": "Enter Weight", "length": "Length", "inputLength": "Enter Length", "width": "<PERSON><PERSON><PERSON>", "inputWidth": "<PERSON><PERSON>", "height": "Height", "inputHeight": "Enter Height", "calculateBtn": "Calculate", "resultTitle": "Shipping Estimate", "deliveryTime": "Estimated Delivery", "baseWeight": "Base Weight", "additionalWeight": "Additional Weight", "feeDetails": "<PERSON><PERSON>", "shippingFee": "Shipping Fee", "tax": "Tax", "serviceFee": "Service Fee", "fuelFee": "Fuel Surcharge", "restrictions": "Restrictions", "disclaimer": "*Estimate is for reference only. Actual cost may vary due to cargo characteristics, seasonal changes, fuel surcharge, etc.", "noShippingMethods": "No Available Methods", "feature": "Feature", "ban": "Ban", "noShippingTips": "No suitable shipping methods found based on your input. Please adjust item details or contact customer service."}, "sharepromotion": {"mainTitle": "Earn Commission", "subTitle": "Start your earning journey as our affiliate partner", "ruleTitle": "Rules", "joinNow": "Join Now", "stepsTitle": "Steps", "step1": "Register as affiliate", "step2": "Share product links", "step3": "Earn commission", "register": "Register", "share": "Share", "qualificationTitle": "Requirements", "qualification1": "1. Must be 18+ years old and comply with platform rules and laws.", "commissionTitle": "Commission Rules", "commission1": "1. Earn up to 15% commission based on your level.", "commission2": "2. Commission is paid monthly when reaching ¥100 minimum.", "methodTitle": "Methods", "method1": "1. Promote via social media, websites etc. No false advertising allowed.", "upgradeTitle": "Upgrade", "upgrade1": "1. Reach targets to upgrade for higher commission rates.", "penaltyTitle": "Penalties", "penalty1": "1. Violations may lead to downgrade or permanent ban."}, "promotion": {"invitedUsers": "Invited Users", "activatedUsers": "Activated Users", "activeUsers": "Active Users", "commissionBalance": "Commission Balance", "frozenAmount": "Frozen Amount", "estimatedCommission": "Estimated Commission", "myLevel": "My Level", "dreamPromoter": "Dream Promoter", "commissionRate": "Commission Rate", "experience": "Experience", "upgradeCondition": "Upgrade Condition", "lv1Title": "LV1 Dream Promoter", "lv2Title": "LV2 Junior Promoter", "lv3Title": "LV3 Intermediate Promoter", "lv4Title": "LV4 Super Promoter", "lv1Rate": "2%", "lv2Rate": "3.5%", "lv3Rate": "5%", "lv4Rate": "6.5%", "lv1Condition": "Start from 0", "lv2Condition": "Start from 1000", "lv3Condition": "Start from 5000", "lv4Condition": "Start from 10000", "promotionMethod": "Promotion Method", "promotionMethodDesc": "By scanning QR code or clicking promotion link, or filling in your promotion code during registration, they will automatically become your promoted users", "downloadQR": "Download QR Code", "copyLink": "Copy Link", "promoteProducts": "Promote Products", "withdraw": "Withdraw", "balanceTab": "Commission Balance", "frozenTab": "Frozen Amount", "recordsTab": "Settlement Records", "accumulatedTab": "Points List", "experienceTab": "Experience Details", "inviteTab": "Invite List", "helpTab": "Get Help"}, "referralpro": {"title": "Promotion Agreement & Rules", "totalInviteUsers": "Total Invited Users", "activatedUsers": "Activated Users", "activeUsers": "Active Users", "activeUsersTooltip": "Statistics of users who completed orders and payments on the platform in the current natural month, with no less than 10 orders", "commissionBalance": "Commission Balance", "frozenBalance": "Frozen Balance", "frozenBalanceTooltip": "After the users you invited complete the receipt of the shipment, the corresponding commission will first enter the frozen amount waiting for settlement, and will be automatically transferred to the commission balance after settlement", "settlementDate": "System monthly settlement date is X", "estimatedCommission": "Estimated Commission", "estimatedCommissionTooltip": "Statistics of commission amounts corresponding to shipments that have been paid but not yet received by users you invited", "totalWithdrawal": "Total Withdrawal", "myLevel": "My Level", "bonusRatio": "Bonus Ratio", "experienceValue": "Experience Value", "startingFrom": "Starting from", "noLevelInfo": "No level information available", "levelDataLoading": "Level data loading, please refresh the page later", "promotionMethod": "Promotion Method", "promotionMethodDesc": "By scanning QR code or clicking promotion link, or filling in your promotion code during registration, they will automatically become your promoted users", "downloadInviteQR": "Download Invite QR Code", "copyInviteLink": "Copy Invite Link", "copyInviteCode": "Copy Invite Code", "commissionAmountTab": "Commission Amount", "frozenAmountTab": "Frozen Amount", "settlementRecordsTab": "Settlement Records", "pointsListTab": "Points List", "experienceDetailsTab": "Experience Details", "activeUsersTab": "Active Users", "inviteListTab": "Invite List", "goInviteTab": "Go Invite", "typeFilter": "Type", "allTypes": "All", "timeFilter": "Time", "settlementTimeFilter": "Settlement Time", "startDate": "Start Date", "endDate": "End Date", "startTime": "Start Time", "endTime": "End Time", "search": "Search", "reset": "Reset", "loading": "Loading...", "id": "ID", "type": "Type", "beforeAmount": "Before Amount", "changeAmount": "Change Amount", "afterAmount": "After Amount", "time": "Time", "remark": "Remark", "noCommissionRecords": "No commission records", "commissionRecordsDesc": "Commission change records will be displayed after successful promotion", "noFrozenRecords": "No frozen amount records", "frozenRecordsDesc": "Frozen amount change records will be displayed here", "settlementAmount": "Settlement Amount", "settlementPeriod": "Settlement Period", "createTime": "Create Time", "noSettlementRecords": "No settlement records", "settlementRecordsDesc": "Commission settlement records will be displayed here", "source": "Source", "noExperienceRecords": "No experience records", "experienceRecordsDesc": "Complete promotion tasks to earn experience points", "inviteUsername": "<PERSON><PERSON><PERSON>", "email": "Email", "status": "Status", "inviteTime": "Invite Time", "activated": "Activated", "registered": "Registered", "unregistered": "Unregistered", "noInviteRecords": "No invite records", "productAmount": "Product Amount", "orderCount": "Order Count", "shippingAmount": "Shipping Amount", "shippingCount": "Shipping Count", "registerTime": "Register Time", "activeMonth": "Active Month", "noActiveUsers": "No active users", "activeUsersDesc": "Users who completed more than 10 orders this month will be displayed here", "sendInviteEmail": "Send In<PERSON>te <PERSON>", "enterFriendEmail": "Enter friend's email", "sendInvite": "Send In<PERSON>te <PERSON>", "inviteEmailDesc": "Invite friends to register via email, you will receive promotion rewards after successful registration.", "inviteMethodsDesc": "You can also invite friends through the following methods:", "sharePromotionLink": "Share promotion link", "sharePromotionQR": "Share promotion QR code", "shareInviteCode": "Share invite code", "beforePoints": "Before Points", "changePoints": "Change Points", "afterPoints": "After Points", "noPointsRecords": "No points records", "pointsRecordsDesc": "Points change records will be displayed here", "pointsTypes": {"registration": "Registration Gift", "inviterReward": "In<PERSON><PERSON>", "inviteeGift": "Invitee Gift", "delivery": "Delivery", "sharing": "Sharing", "signin": "Sign-in", "adminOperation": "Admin Operation", "promotionPoints": "Promotion Points", "upgrade": "Upgrade", "couponExchange": "Coupon Exchange"}, "pagination": {"showing": "Showing", "to": "to", "of": "of", "records": "records", "page": "Page", "totalPages": "of", "pages": "pages", "previous": "Previous", "next": "Next"}, "withdrawModal": {"title": "Withdraw", "description": "Commission balance will be withdrawn to your account", "withdrawAmount": "Withdraw Amount", "availableBalance": "Available Balance", "enterAmount": "Please enter withdraw amount", "verificationEmail": "Verification Email", "verificationMoney": "Can't be greater than the withdrawable balance!", "enterEmail": "Please enter verification email", "verificationCode": "Verification Code", "enterCode": "Please enter verification code", "sendCode": "Send Code", "cancel": "Cancel", "confirm": "Confirm"}, "messages": {"copyLinkSuccess": "<PERSON><PERSON> Successfully", "copyLinkFail": "Copy failed, please copy manually", "qrDownloadSuccess": "QR code downloaded successfully", "qrNotAvailable": "QR code not available", "inviteCodeCopySuccess": "Invite code {code} copied successfully", "invalidEmail": "Please enter a valid email address", "inviteEmailSent": "Invite email sent successfully", "sendFailed": "Send failed", "sendFailedRetry": "Send failed, please try again later", "noWithdrawBalance": "Current commission balance is 0, cannot withdraw", "verificationCodeSent": "Verification code has been sent to your email", "sendCodeFailed": "Failed to send verification code, please try again later", "enterValidEmail": "Please enter a valid email address", "enterVerificationCode": "Please enter email verification code", "enterValidAmount": "Please enter a valid withdraw amount", "withdrawSubmitted": "Withdraw application submitted, please wait for review", "withdrawFailed": "Withdraw application failed, please try again later", "dataLoadFailed": "Failed to load data, please try again later", "tabDataLoadFailed": "Failed to load tab data"}}, "sizecomparison": {"size": "Size", "womensShirtChart": "Women's Clothing Size Chart", "womensShirtTitle": "Women's <PERSON><PERSON>ze Comparison Chart", "international": "International", "bust": "Bust", "waistline": "Waist", "Shirt": "Shirt", "shoulder": "Shoulder Width", "height": "Height", "womensShirtTip1": "Size definition: 'Size' refers to a person's height in cm, which is the basis for designing and selecting clothing length;", "womensShirtTip2": "'Type' refers to a person's bust and waist measurements in cm, which is the basis for designing and selecting clothing width.", "womensShirtTip3": "Body type classification: Based on the difference between bust and waist measurements, body types are divided into four categories: Y (Slim), A (Standard), B (Full), C (Obesity).", "dressChart": "Dress <PERSON><PERSON>on Chart", "dress": "Dress", "dressTip1": "Asian and European sizes: Human body structures share some similarities but have significant dimensional differences across regions. Asian and European sizes refer to different sizing systems, each with further subdivisions. Countries establish their own prototypes and standardized sizing charts based on local body measurements.", "womensPantsChart": "Women's Pants Size Chart", "pants": "<PERSON>ts", "feet": "Feet", "foot": "Foot Length", "model": "Model", "hip": "Hip", "womensPantsTip1": "How to measure side length: Measure from the waist to the ankle.", "womensPantsTip2": "How to measure waist: Measure the horizontal circumference at the belly button (om).", "womensPantsTip3": "Standard waist calculation: Waist = Height/2 - 19cm (e.g., standard waist for 160cm height = 160cm/2 - 19 = 61cm).", "mensChart": "Men's Clothing Size Chart", "mensShirtChart": "Men's Shirt Size Chart", "length": "Length", "clothing": "Clothing", "mensShirtTip1": "This is only a standard size reference. Actual sizes may vary significantly due to individual body differences. Please try on clothes for proper fitting.", "mensSuitChart": "Men's Suit Size Chart", "spec": "Specification", "version": "Cut", "sleeve": "Sleeve <PERSON>", "overweight": "Full", "standard": "Standard", "mensSuitTip1": "Men's suits are categorized into Slim Fit, Regular Fit, and Full Fit based on body type.", "mensPantsChart": "Men's Pants Size Chart", "mensPants": "Men's Pants", "inch": "Inch", "clothRuler": "<PERSON><PERSON>", "clothingSize": "Clothing Si<PERSON>", "kidsClothingChart": "Children's Clothing Size Chart", "babyClothingChart": "Baby Clothing Size Chart", "babyClothingTip1": "Baby's delicate skin requires special care. The main difference between children's and adult clothing lies in materials and dyeing techniques. Check fabric composition to ensure no skin irritation.", "chineseKidsChart": "Chinese Children's Clothing Size Chart", "yearOld": "Years Old", "months": "Months", "age": "Age", "chineseKidsTip1": "Meaning of product standards on tags: Class A, B, C indicate formaldehyde content levels - Class A for infants, Class B for direct skin contact, Class C for non-direct skin contact.", "womensLingerieChart": "Women's Lingerie Size Chart", "braChart": "Bra Size Chart", "china": "China", "europe": "Europe", "cup": "Cup", "bustMeasureTip1": "Bust measurement method: Measure around the fullest part of your bust while wearing a non-padded bra.", "bustMeasureTip2": "Cup size is determined by the difference between bust and underbust measurements.", "underwearChart": "Underwear Size Chart", "waistMeasureTip1": "Waist measurement method: Measure around your natural waistline, keeping the tape comfortably loose.", "waistMeasureTip2": "Hip measurement method: Measure around the fullest part of your hips.", "shoeChart": "Shoe Size Chart", "womensShoeChart": "Women's Shoe Size Chart", "usa": "USA", "uk": "UK", "japan": "Japan", "shoeTip1": "Women's shoe sizes may vary by brand. This chart is for reference only.", "mensShoeChart": "Men's Shoe Size Chart", "shoeTip2": "Men's shoe sizes may vary by brand. This chart is for reference only.", "footMeasureTip": "Foot measurement method: Stand on paper and trace your foot. Measure length from heel to toe in centimeters.", "measureGuide": "Body Measurement Guide", "guide1": "1. Bust Measurement Method", "guideDesc1": "Stand straight with arms at sides. Place tape at nipple line (over bust for women) and below shoulder blades. Me<PERSON>ure relaxed, inhaled, and exhaled bust. Avoid shrugging or bending.", "guide2": "2. Waist Measurement Method", "guideDesc2": "Pant waist: Measure around seam. Natural waist: Measure around the narrowest part.", "guide3": "3. Hip Measurement Method", "guideDesc3": "Pant hip: Measure widest part below waist. Natural hip: Measure fullest part.", "guide4": "4. Weight Measurement", "guideDesc4": "Wear undershirt and shorts, stand steadily on scale.", "guide5": "5. <PERSON><PERSON>ure<PERSON> (Both Legs)", "guideDesc5": "Stand straight with weight evenly distributed. Measure around thickest part of calf.", "guide6": "6. Pant Length Measurement", "guideDesc6": "Measure from waist to hem. Casual/jeans length excludes cuff allowance.", "guide7": "7. Net Pant Length Measurement", "guideDesc7": "Measure from waist to actual hem. Standard men's length reaches shoe welt.", "guide8": "8. Thigh Measurement (Both Legs)", "guideDesc8": "Stand naturally with feet 15cm apart. Measure around thickest part of thigh."}, "selfservice": {"manualOrder": "Manual Order Service", "shippingFee": "Seller to Onebuy Warehouse Shipping Fee", "serviceDesc": "We offer professional overseas purchasing and forwarding services, covering major e-commerce platforms in China. From product purchase, customs clearance, warehousing to delivery, we provide one-stop solutions for your cross-border shopping needs. With high-quality services and quality guarantees, we make cross-border shopping easy and simple.", "productLink": "Product Link", "productLinkHint": "Enter product link", "linkRule": "Link must start with https://", "productName": "Product Name", "productNameHint": "Enter product name", "specs": "Specifications", "specsHint": "Enter specifications", "specsDetail": "Enter details (color, size, etc.)", "productImage": "Product Image", "uploadHint": "Upload product image", "uploading": "Uploading...", "uploadTip": "Click or drag to upload", "imageRule": "Supports JPG/PNG/WebP, max 5MB per image", "remark": "Remarks", "remarkHint": "Enter description", "price": "Price", "priceHint": "Enter price", "quantity": "Quantity", "quantityHint": "Enter quantity", "inputShipping": "Enter shipping fee", "addProduct": "Add Product", "addedProduct": "Added Product", "delete": "Delete", "noImage": "No Image", "linkCol": "Link", "priceCol": "Price", "shippingCol": "Shipping", "subtotal": "Subtotal", "total": "Total", "acceptTerms": "I have read and accept", "serviceAgreement": "《Purchasing Service Agreement》", "clearConfirm": "Confirm Clear", "clearMessage": "Clear all added products?", "confirm": "Confirm", "cancel": "Cancel", "cleared": "All cleared", "clearList": "Clear List", "submitCart": "Submit Cart", "disclaimer": "Disclaimer", "discllaimer": "Disclaimer", "disclaimerDetail": "All the items available for purchase on Onebuy are retrieved from third-party purchasing platforms and are not directly sold by Onebuy. Therefore, regarding the above-mentioned products and pricing issues, all responsibilities shall be borne by the corresponding sellers on the third-party platform. Onebuy shall not bear any related, incidental or joint liability", "feeDescription": "description of fees", "feeNote": "Product price is original, excluding tax/shipping", "warehouseFee": "The shipping fee for the seller to ship to the Onebuy warehouse", "priceAlert": "If there is any change in the price of the goods, we will contact you", "imageFormat": "Only JPG/PNG/WEBP formats", "sizeLimit": "Max 5MB per image", "uploadFailed": "Upload failed", "fillRequired": "Please fill required fields", "addSuccess": "Product added", "agreeFirst": "Please agree to the agreement", "minProduct": "Add at least one product", "cartSuccess": "Added to cart successfully", "cartFailed": "Failed to add to cart", "submitRetry": "Submit failed, please retry", "step1Title": "Submit Product Info", "step1Desc": "Fill product details", "step2Title": "Make Payment", "step2Desc": "Confirm payment", "step3Title": "Await Shipping", "step3Desc": "Processing"}, "error": {"systemError": "System encountered some issues", "dontWorry": "Don't worry, we're preparing a solution for you...", "autoRedirect": "Page will automatically redirect to the fix page in 3 seconds", "goToFix": "Go to fix page immediately", "retryPage": "Retry current page", "whatIsThis": "What is this problem?", "reasons": ["Browser cache data conflict", "Login status out of sync", "Need to refresh page data"]}, "cache": {"optimizing": "Clearing cache...", "steps": {"clearing": {"title": "Clearing Cache", "description": "Clearing browser cache data, please wait..."}, "refreshing": {"title": "Refreshing Data", "description": "Reloading latest data, almost done..."}, "complete": {"title": "Optimization Complete", "description": "Cache clearing complete, redirecting to target page"}}}, "console": {"parseError": "Failed to parse siteData", "apiRequest": "Making API request", "urlParseError": "Error parsing URL", "uploadFailed": "Upload failed", "fileProcessError": "Error processing file", "copyFailed": "Co<PERSON> failed", "extractFromCnProps": "Extracting properties from cn_props_list", "configInitComplete": "Configuration initialization complete, ready to display page", "configInitFailed": "Configuration initialization failed"}, "previewPackage": {"title": "Preview Package", "description": "Preview package functionality description", "shippingInfo": {"previewOnly": "For preview only, no need to select shipping method"}}, "productDetail": {"size": "Size", "colorCategory": "Color"}, "errors": {"fetchHotCategoriesFailed": "Failed to fetch hot categories", "fetchProductsFailed": "Failed to fetch products", "fetchShoppingTimeFailed": "Failed to fetch shopping time"}}