import { useState, useEffect, forwardRef, useImperativeHandle } from "react";
import { Api } from "@/request/api";
import { Card, Empty, Skeleton } from "antd";
import { formatCurrency } from "@/utils/currency";
import dayjs from "dayjs";
import Button from "@/components/Button";
import { useRouter, useParams } from "next/navigation";
import type { Locale } from "@/config";
import { getDictionary } from "@/dictionaries";

interface CashRecord {
  id: number;
  user_id: number;
  money: string;
  bankname: string;
  bankcard: string;
  realname: string;
  remark: string | null;
  taxfee: string;
  status: number;
  paycode: string | null;
  errorcode: string | null;
  createtime: number;
  checktime: number;
  paytime: number;
  refusetime: number;
  status_text: string;
}

interface CashListResponse {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  data: CashRecord[];
}
// 定义ref的类型
export interface WithdrawContentRef {
  refresh: () => void;
}
const WithdrawContent = forwardRef<WithdrawContentRef>((props, ref) => {
  const [loading, setLoading] = useState(true);
  const [cashList, setCashList] = useState<CashListResponse | null>(null);
  const router = useRouter();
  const params = useParams();
  const lng = (params.lng as Locale) || "zh-cn";
  const [dict, setDict] = useState<any>(null); // 添加字典状态
  // 暴露refresh方法给父组件
  useImperativeHandle(ref, () => ({
    refresh: fetchCashList,
  }));
  // 异步获取字典数据
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error("Failed to load dictionary:", error);
      }
    };

    fetchDictionary();
  }, [lng]);

  const fetchCashList = async () => {
    try {
      setLoading(true);
      const response = await Api.getCashList();
      if (response.success) {
        setCashList(response.data);
      }
    } catch (error) {
      console.error("获取提现记录失败:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCashList();
  }, []);

  // const getStatusText = (status: number) => {
  //   switch (status) {
  //     case -2:
  //       return dict?.dashboard?.cash?.deleted;
  //     case -1:
  //       return dict?.dashboard?.cash?.reviewFailed;
  //     case 0:
  //       return dict?.dashboard?.cash?.pending;
  //     case 1:
  //       return dict?.dashboard?.cash?.approved;
  //     case 2:
  //       return dict?.dashboard?.cash?.paidSuccess;
  //     case 3:
  //       return dict?.dashboard?.cash?.paidFailed;
  //     default:
  //       return dict?.dashboard?.cash?.unknown;
  //   }
  // };

  const getStatusClass = (status: number) => {
    switch (status) {
      case -2:
        return "bg-gray-50 text-gray-600";
      case -1:
        return "bg-red-50 text-red-600";
      case 0:
        return "bg-blue-50 text-blue-600";
      case 1:
        return "bg-green-50 text-purple-600";
      case 2:
        return "bg-purple-50 text-green-600";
      case 3:
        return "bg-red-50 text-red-600";
      default:
        return "bg-gray-50 text-gray-600";
    }
  };

  return (
    <div>
      <div>
        <div className="flex items-center justify-between mb-5">
          <h3 className="text-lg font-medium m-0">
            {dict?.dashboard?.cash?.title}
          </h3>
        </div>
        {/* <div className="flex items-center justify-between mb-6">
            <h1 className="text-xl font-medium m-0">{dict?.dashboard?.cash?.title}</h1>
            <Button 
              type="primary"
              onClick={() => router.push('/dashboard/addcash')}
              className="bg-orange-500 hover:bg-orange-600"
            >
              {dict?.dashboard?.cash?.applyBtn}
            </Button>
          </div> */}

        {loading ? (
          <Skeleton active paragraph={{ rows: 5 }} />
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full border-collapse">
              <thead>
                <tr className="border-b text-gray-600 bg-gray-50">
                  <th className="py-3 px-4 text-left font-medium text-sm">
                    {dict?.dashboard?.cash?.time}
                  </th>
                  <th className="py-3 px-4 text-left font-medium text-sm">
                    {dict?.dashboard?.cash?.amount}
                  </th>
                  <th className="py-3 px-4 text-left font-medium text-sm">
                    {dict?.dashboard?.cash?.fee}
                  </th>
                  <th className="py-3 px-4 text-left font-medium text-sm">
                    {dict?.dashboard?.cash?.bank}
                  </th>
                  <th className="py-3 px-4 text-left font-medium text-sm">
                    {dict?.dashboard?.cash?.cardNumber}
                  </th>
                  <th className="py-3 px-4 text-left font-medium text-sm">
                    {dict?.dashboard?.cash?.name}
                  </th>
                  <th className="py-3 px-4 text-left font-medium text-sm">
                    {dict?.dashboard?.cash?.status}
                  </th>
                </tr>
              </thead>
              <tbody>
                {cashList?.data && cashList.data.length > 0 ? (
                  cashList.data.map((record) => (
                    <tr key={record.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4 text-gray-700">
                        {typeof record.createtime === "number"
                          ? dayjs(record.createtime * 1000).format(
                              "YYYY-MM-DD HH:mm"
                            )
                          : record.createtime}
                      </td>
                      <td className="py-3 px-4">
                        <span className="font-medium text-gray-900">
                          {formatCurrency(Number(record.money)).formatValue}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {formatCurrency(Number(record.taxfee)).formatValue}
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {record.bankname}
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {record.bankcard}
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {record.realname}
                      </td>
                      <td className="py-3 px-4">
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${getStatusClass(
                            record.status
                          )}`}
                        >
                          {record.status_text}
                        </span>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="py-8 text-center text-gray-500">
                      <Empty description={dict?.dashboard?.cash?.noRecords} />
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
});
export default WithdrawContent;
