import Image from 'next/image'
import ButtonComponent from '@/components/Button'
import { useRouter } from 'next/navigation'
import { Api } from '@/request/api'
import ModalComponent from '@/components/Modal'
import { useState } from 'react'
import Link from 'next/link'
import Toast from '@/components/Toast'
import { OrderStatus } from '@/types'
import { Tag, Input,Modal,Button } from 'antd'
import { formatCurrency } from '@/utils/currency'
import { MessageOutlined,FileImageOutlined } from '@ant-design/icons'
import { prepareImageForNextJs } from '@/utils/imageUtils'

export default function OrderItem({ data, onRefresh, lng, dict }: { data: any, onRefresh?: () => void, lng: string, dict: any }) {
    const router = useRouter()
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [isMessageModalOpen, setIsMessageModalOpen] = useState(false)
    const [message, setMessage] = useState('')
    const [currentSendorderId, setCurrentSendorderId] = useState<number | null>(null)
    const [messageLoading, setMessageLoading] = useState(false)
    const [messageList, setMessageList] = useState([])
    const infoStr = localStorage.getItem('info');
    const userInfo: any = infoStr ? JSON.parse(infoStr)?.data?.userinfo : null;
    const [isImgModalOpen, setIsImgModalOpen] = useState(false);
    const [photoExplain, setPhotoExplain] = useState([]);
    const [serviceImgList, setServiceImgList] = useState([]);
    const [imageError, setImageError] = useState<{[key: string]: boolean}>({});
    
    const handleCancel = () => {
        setIsModalOpen(true)
    }

    const handleMessage = async (order_goods_id: number) => {
        setCurrentSendorderId(order_goods_id)
        const res = await Api.OrderMessage(order_goods_id)
        if (res.success) {
            setIsMessageModalOpen(true)
            // setMessage(res.data[0].content)
            // 按时间正序排列留言（最早的在上面，最新的在下面）
            const sortedMessages = res.data.sort((a: any, b: any) => {
                return new Date(a.createtime).getTime() - new Date(b.createtime).getTime()
            })
            setMessageList(sortedMessages)
        }
    }

    const handleMessageSubmit = async () => {
        if (!message.trim()) {
            Toast.error(dict.dashboard.orders.list.inputHint)
            return
        }
        if (!currentSendorderId) {
            Toast.error('未选择商品')
            return
        }
        setMessageLoading(true)
        try {
            const res = await Api.createOrderReply({
                order_goods_id: currentSendorderId,
                content: message.trim()
            })
            if (res.success) {
                Toast.success(dict.dashboard.orders.list.msgSuccess)
                onRefresh?.()
                // 重新获取留言列表以保持时间排序
                if (currentSendorderId) {
                    const messageRes = await Api.OrderMessage(currentSendorderId)
                    if (messageRes.success) {
                        const sortedMessages = messageRes.data.sort((a: any, b: any) => {
                            return new Date(a.createtime).getTime() - new Date(b.createtime).getTime()
                        })
                        setMessageList(sortedMessages)
                    }
                }
                setIsMessageModalOpen(false)
                setMessage('')
                setCurrentSendorderId(null)
            } else {
                Toast.error(res.msg || dict.dashboard.orders.list.msgFail)
            }
        } catch (error) {
            Toast.error(dict.dashboard.orders.list.msgFail)
        }
        setMessageLoading(false)
    }

    const handleConfirm = async () => {
        try {
            const res = await Api.cancelOrder({
                order_id: data.id,
            })
            if (res.success) {
                Toast.success(dict.dashboard.orders.list.item.cancelSuccess)
                onRefresh?.()
            } else {
                Toast.error(res.msg || dict.dashboard.orders.list.item.cancelFail)
            }
        } catch (error) {
            Toast.error(dict.dashboard.orders.list.item.cancelFail)
        }
        setIsModalOpen(false)
    }

    const handleImgOk = () => {
        setIsImgModalOpen(false);
    };
    const setActiveItem = async (item:any) =>{
        let remark: any = [];
        item.service.forEach((item: any) => {
            if (item.name_code === 'photo') {
                try {
                    remark = JSON.parse(item.remark);
                } catch (error) {
                    remark = []; 
                }
            }
        });
        setPhotoExplain(remark)
        const res = await  Api.getGoodServiceImg(item.id)
        if(res.success){
            console.log(res.data,'data')
            setServiceImgList(res.data)
            setIsImgModalOpen(true)
        }else{
            Toast.error(res.message || '获取图片失败')
        }
    }
    const getmallid = (url: string) => {
   
        url = decodeURIComponent(url);
        console.log(url)
        const match = url.match(/tid=([^&]+)/);
        if (match && match[1]) {
          return match[1];
        }
        return '';
    };
    let colorMap: Record<number, string> = {
        [OrderStatus.waitingPayment]: 'gold',
        [OrderStatus.paid]: 'green',
        [OrderStatus.shipped]: 'orange',
        [OrderStatus.invalid]: 'red',
        [OrderStatus.pendingReview]: 'blue',
        [OrderStatus.delivered]: 'green',
    }

    // 检查订单实际状态的函数
    const getActualOrderStatus = () => {
        if (!data || !data.goods || data.goods.length === 0) {
            return { statusId: data?.status_id || 0, statusText: '', color: 'default' };
        }

        // 检查是否所有商品都是无效单
        const allInvalid = data.goods.every((item: any) =>
            item.status_text === '无效单' || item.status_id === 100
        );

        if (allInvalid) {
            return {
                statusId: OrderStatus.invalid,
                statusText: dict.dashboard.orders.list.status.invalid || '无效单',
                color: 'red'
            };
        }

        // 如果不是所有商品都无效，使用原始订单状态
        return {
            statusId: data.status_id,
            statusText: dict.dashboard.orders.list.status[OrderStatus[data.status_id]] || '',
            color: colorMap[data.status_id] || 'default'
        };
    };

    return (
        <>
            <ModalComponent
                title={dict.dashboard.orders.list.item.confirmCancel}
                open={isModalOpen}
                onOk={handleConfirm}
                onCancel={() => setIsModalOpen(false)}
                okText={dict.dashboard.orders.list.item.confirm}
                centered
                cancelText={dict.dashboard.orders.list.item.cancel}
            >
                <p>{dict.dashboard.orders.list.item.cancelPrompt}</p>
            </ModalComponent>

            <ModalComponent
                title={dict.dashboard.orders.list.orderMsg}
                open={isMessageModalOpen}
                onOk={handleMessageSubmit}
                onCancel={() => {
                    setIsMessageModalOpen(false)
                    setMessage('')
                }}
                okText={dict.dashboard.orders.list.confirmBtn}
                centered
                cancelText={dict.dashboard.orders.list.cancelBtn}
                confirmLoading={messageLoading}
                footer={null}
            >
                {/* <Input.TextArea 
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder={dict.dashboard.orders.list.inputHint}
                    rows={4}
                /> */}
                <div>
                    {/* 留言列表区域 */}
                    <div className="p-6 max-h-[60vh] overflow-y-auto">
                        <div className="space-y-6" id="messageList">
                            {messageList.length === 0 ? (
                                <div className="text-center text-gray-500 py-8">
                                    {dict?.dashboard?.orders?.list?.noMessages || '暂无留言'}
                                </div>
                            ) : (
                                messageList.map((remark: any, index: number) => (
                                <div key={index}>
                                    {remark.admin_id == 0 ? (
                                        <div className="flex gap-4 justify-end">
                                            <div className="w-80 flex px-4 py-2 border border-gray-200 rounded-lg">
                                                <div className="flex-1">
                                                    <div className="flex items-center justify-between mb-2">
                                                        <span className="text-sm text-gray-500">{remark.createtime}</span>
                                                    </div>
                                                    <p className="text-gray-700 text-sm">{remark.content}</p>
                                                </div>
                                                {remark?.userinfo?.avatar ? (
                                                    <div className="w-10 h-10 rounded-full flex">
                                                        <img src={remark?.userinfo?.avatar} alt="User avatar" className="rounded-full" />
                                                    </div>
                                                ) : (
                                                    <div
                                                        className="rounded-full overflow-hidden relative bg-orange-500 flex items-center justify-center text-white text-2xl font-semibold w-10 h-10"
                                                        id="avatarContainer"
                                                    >
                                                        <div className="absolute inset-0 flex items-center justify-center" id="avatarLetter">
                                                            {remark?.userinfo?.username?.charAt(0).toUpperCase()}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="flex gap-4 w-80 px-4 py-2 border border-gray-200 rounded-lg">
                                            {remark?.userinfo?.avatar ? (
                                                <div className="w-10 h-10 rounded-full flex">
                                                    <img src={remark?.userinfo?.avatar} alt="User avatar" className="rounded-full" />
                                                </div>
                                            ) : (
                                                <div
                                                    className="rounded-full overflow-hidden relative bg-orange-500 flex items-center justify-center text-white text-2xl font-semibold w-10 h-10"
                                                    id="avatarContainer"
                                                >
                                                    <div className="absolute inset-0 flex items-center justify-center" id="avatarLetter">
                                                        {remark?.userinfo?.username?.charAt(0).toUpperCase()}
                                                    </div>
                                                </div>
                                            )}
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between mb-2">
                                                    <div>
                                                        <span className="font-medium text-gray-900">{remark?.userinfo?.username}</span>
                                                    </div>
                                                    <span className="text-sm text-gray-500">{remark.createtime}</span>
                                                </div>
                                                <p className="text-gray-700 text-sm">{remark.content}</p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                                ))
                            )}
                        </div>
                    </div>
                    {/* 发送留言区域  */}
                    <div className="px-6 py-4 border-t bg-gray-50 rounded-lg">
                        <div className="flex gap-4">
                            <textarea
                                id="messageInput"
                                value={message}
                                onChange={(e) => setMessage(e.target.value)}
                                className="flex-1 h-20 p-3 border border-gray-200 rounded-lg resize-none focus:outline-none focus:border-orange-500 text-sm"
                                placeholder={dict.dashboard.orders.list.inputHint}
                            ></textarea>
                            <button
                                onClick={handleMessageSubmit}
                                className="px-6 h-10 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors whitespace-nowrap self-end"
                            >
                               {dict.dashboard.orders.list.sendMessage}
                            </button>
                        </div>
                    </div>
                </div>
            </ModalComponent>

            <div className="flex flex-col border border-gray-200 rounded-lg mb-4">
                {/* 订单头部信息 */}
                <div className="flex justify-between items-center p-4 border-b border-gray-200">
                    <div className="flex items-center gap-2">
                        <Link href={data.sellerurl} target="_blank"><span className="text-sm">{dict.dashboard.orders.list.item.shop}： {data.goodsseller} </span></Link>

                        <span className="text-sm "> {dict.dashboard.orders.list.item.orderNo} : {data.ordersn}</span>
                        <span className="text-sm text-gray-400"> {dict.dashboard.orders.list.item.createTime} : {data.createtime}</span>
                        <span className="text-sm  mr-4"> <Tag bordered={false} color={getActualOrderStatus().color}>{getActualOrderStatus().statusText}</Tag> </span>
                    </div>

                    <div className="flex items-center">
                        <div className="flex gap-2 justify-center items-center">
                            <ButtonComponent style={{
                                display: data.status_id === OrderStatus['waitingPayment'] && getActualOrderStatus().statusId !== OrderStatus.invalid ? 'block' : 'none'
                            }}
                                onClick={() => router.push(`/pay?order_id=${data.id}`)}
                                size='small'
                                color={process.env.NEXT_PUBLIC_BASE_COLOR as any} variant="solid">
                                {dict.dashboard.orders.list.item.payment}
                            </ButtonComponent>

                            <ButtonComponent onClick={handleCancel}
                                size='small'
                                style={{
                                    display: (data.status_id === OrderStatus['waitingPayment'] || data.status_id === OrderStatus['pendingReview']) && getActualOrderStatus().statusId !== OrderStatus.invalid ? 'block' : 'none'
                                }} variant="link" color="default">
                                {dict.dashboard.orders.list.item.cancel}
                            </ButtonComponent>
                        </div>
                    </div>
                </div>

                {/* 商品列表 */}
                {data.goods.map((item: any) => (
                    <div key={item.id} className="grid grid-cols-[400px_140px_120px_120px_140px_120px] gap-4 p-4 text-sm items-start">
                        {/* 商品名称列 */}
                        <div className="flex items-start">
                            <div className="mr-3">
                            <Image
                                src={imageError[item.id] ? '/images/default.jpg' : prepareImageForNextJs(item.goodsimg)}
                                alt={item.goodsname}
                                width={80}
                                height={80}
                                className="object-cover rounded-md"
                                onError={() => setImageError(prev => ({...prev, [item.id]: true}))}
                                style={{
                                    objectFit: 'cover',
                                    objectPosition: 'center',
                                    width: '80px',
                                    height: '80px',
                                    overflow: 'hidden',
                                }}
                            />
                            </div>
                            <div className="flex flex-col flex-1">
                                {item.goodsseller === 'Buy Yourself' || item.goodsseller === 'OneBuy' || item.goodsseller === 'By yourself' ? (
                                    <h3 className="font-medium mb-2 line-clamp-2 text-left">{item.goodsname}</h3>
                                ) : (
                                    <Link href={`/${lng}/detail/${item.goodssite}?${item.goodssite=='obmall'?'id='+getmallid(item.goodsurl):'url='+item.goodsurl}`}  target="_blank">
                                        <h3 className="font-medium mb-2 line-clamp-2 text-left">{item.goodsname}</h3>
                                    </Link>
                                )}
                                {item.skuname && (
                                    <div className="text-sm text-gray-500 mb-2 line-clamp-2 text-left">
                                        {item.skuname}
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* 商品总价列 */}
                        <div className="flex items-center justify-center flex-col">
                            <span className="text-2xl font-bold text-[#1D2129]">
                             {formatCurrency(Number(item.totalmoney)).formatValue}
                            </span>
                            <span className="text-center text-[#86909C]">
                               {dict.dashboard.orders.list.unitPrice}： {formatCurrency(Number(item.goodsprice)).formatValue}
                            </span>
                        </div>

                        {/* 数量列 */}
                        <div className="flex items-center justify-center">
                            <span className="text-gray-500 text-center">
                                x{item.goodsnum}
                            </span>
                        </div>

                        {/* 物流列 */}
                        <div className="flex items-center justify-center">
                           { data.status_id === OrderStatus['shipped'] && item.express ?( <span className="text-gray-500 text-center" >
                                 {item.express}
                                 <div>({item.expressname})</div>
                            </span>):'-'}
                        </div>

                        {/* 商品操作列 */}
                        <div className="flex flex-col items-center justify-center text-gray-500">
                            <div 
                                style={{display:item.service.some((item:any)=>item.name_code=='photo')?'block':'none'}} 
                                className="text-orange-500 cursor-pointer  mb-1" 
                                onClick={() => 
                                    // setIsImgModalOpen(true)
                                    setActiveItem(item)
                                }
                            >
                                <FileImageOutlined />
                            </div>
                            <div className="text-orange-500 cursor-pointer text-center mb-1" onClick={() => router.push(`/dashboard/orders/detail?orders_id=${data.id}`)}>
                                {dict.dashboard.orders.list.item.detail}
                            </div>
                            <div
                                className="text-orange-500 cursor-pointer flex items-center justify-center mb-2"
                                onClick={() => handleMessage(item.id)}
                            >
                                <MessageOutlined className="mr-1" />
                                {dict.dashboard.orders.list.message}
                            </div>
                            {/* 补款 */}
                            {/* <ButtonComponent style={{
                                display: item.status_id === OrderStatus['awaitingSupplement'] ? 'block' : 'none'}}
                                onClick={() => router.push(`/pay?order_id=${item.id}&type=GR`)}
                                size='small'
                                color={process.env.NEXT_PUBLIC_BASE_COLOR as any} variant="solid">
                                {dict.dashboard.orders.list.item.awaitingSupplement}
                            </ButtonComponent> */}
                        </div>

                        {/* 运费列 */}
                        <div className="flex items-center justify-center">
                            <span className="text-center">{formatCurrency(Number(data.totalmoney || '0')).formatValue}</span>
                        </div>
                    </div>
                ))}
            </div>

            <Modal
                title=""
                centered
                cancelText=''
                closable={false}
                open={isImgModalOpen}
                onOk={handleImgOk}
                footer={[
                    <Button key="submit" type="primary" onClick={handleImgOk}>
                       {dict.dashboard.orders.list.item.confirm}
                    </Button>]}>
                    <div>
                        <div>{dict.dashboard.orders.list.photographyRequirements}</div>
                        <div className='flex gap-2'>
                             {photoExplain && photoExplain.length && photoExplain.map((item: any, index: number) => (
                                     <span>{index+1}: {item}</span>
                            ))}
                        </div>
                        <div className='max-h-[600px] flex gap-2 flex-wrap overflow-auto m-t-2 justify-center '>
                            {serviceImgList.length ? serviceImgList.map((item: any, index: number) => (
                                    <img src={item.image} alt=""  className='max-h-[400px] max-w-[400px] object-contain' />
                            )):(<div className='text-gray-500 text-center'>暂无图片数据</div>)}
                        </div>
                    </div>
             
            </Modal>
        </>
    )
}
