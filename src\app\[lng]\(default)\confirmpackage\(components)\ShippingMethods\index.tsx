'use client'
import React, { useState, useEffect } from 'react'
import { Spin, Empty, message } from 'antd'
import { Api } from '@/request/api'
import { formatCurrency } from '@/utils/currency'
import Image from 'next/image'

// 内部定义格式化价格的函数
const formatPrice = (price: number): string => {
  return Number(price).toFixed(2);
};

interface ShippingMethodFee {
  totalfee: number;
  firstfee: string;
  continuefee: number;
  sendfee: string;
  customsfee: string;
  serverfee: string;
  fuelfee: string;
  freemoney: string;
}

interface ShippingMethod {
  template_id: number;
  fee: ShippingMethodFee;
  weigh: number;
  firstweight: number;
  continueweight: number;
  continuefee: string;
  firstfee: string;
  ban: string;
  cycle: string;
  feature: string;
  minweight: number;
  maxweight: number;
  logo: string;
  customsfee: string;
  name: string;
  type: string[];
  insurance_rate: string;
}

interface ShippingMethodsProps {
  addressId: number | null;
  weight: number;
  volume: number;
  goodstype: number[] | string;
  stotalmoney: number;
  onSelect?: (method: ShippingMethod) => void;
  dict:any,
}

export default function ShippingMethods({ 
  addressId,
  weight = 0,
  volume = 0,
  goodstype = [],
  stotalmoney = 0,
  onSelect ,
  dict
}: ShippingMethodsProps) {
  const [loading, setLoading] = useState(false);
  const [methods, setMethods] = useState<ShippingMethod[]>([]);
  const [selectedMethod, setSelectedMethod] = useState<number | null>(null);

  useEffect(() => {
    if (addressId) {
     setTimeout(async () => { 
      await fetchShippingMethods();
     }, 500);
    }
  }, [addressId, weight, volume, goodstype, stotalmoney]);


  const fetchShippingMethods = async () => {
    
    if (!addressId) return;
    
    setLoading(true);
    try {
      const response = await Api.getEstimates({
        area_id: addressId,
        weight: weight,
        volume: volume,
        goodstype: goodstype,
        stotalmoney: stotalmoney,
        clienttype: 'pc'
      });
      
      if (response.success) {
        setMethods(response.data);
        // 默认选中第一个方式
        if (response.data.length > 0) {
          const firstMethod = response.data[0];
          setSelectedMethod(firstMethod.template_id);
          
          onSelect && onSelect(firstMethod);
        }
      } else {
        message.error(dict?.confirm?.order?.transport?.fetchError);
      }
    } catch (error) {
      console.error('获取国际运输方式错误:', error);
      message.error(dict?.confirm?.order?.transport?.loadError);
    } finally {
      setLoading(false);
    }
  };

  const handleMethodChange = (e: any) => {
    const methodId = e.target.value;
    setSelectedMethod(methodId);
    const selectedMethodObject = methods.find(method => method.template_id === methodId);
    if (selectedMethodObject) {
      onSelect && onSelect(selectedMethodObject);
    }
  };

  if (!addressId) {
    return (
      <div className="bg-white p-6 rounded-lg">
        <Empty description={dict?.confirm?.order?.transport?.addressFirst} />
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg">
      <Spin spinning={loading}>
        {methods.length > 0 ? (
          <div className="space-y-4">
            {methods.map(method => (
              <div
                key={method.template_id}
                className={`border rounded-lg p-4 hover:border-[#FF6000] transition-all cursor-pointer relative ${
                  selectedMethod === method.template_id ? 'border-[#FF6000] bg-orange-50' : 'border-gray-200'
                }`}
                onClick={() => handleMethodChange({ target: { value: method.template_id } })}
              >
                <div className="flex gap-4">
                  {/* 运输方式图片 */}
                  {method.logo && (
                    <div className="flex-shrink-0 flex items-center">
                      <Image
                        src={method.logo.startsWith('//') ? `https:${method.logo}` : method.logo}
                        alt={method.name}
                        width={60}
                        height={60}
                        className="rounded-lg object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                        }}
                      />
                    </div>
                  )}

                  <div className="flex-1">
                    <div className="font-medium text-base">{method.name}</div>
                    <div className="text-sm text-gray-500 mt-1">
                      {dict?.confirm?.order?.transport?.estimate}: {method.cycle}
                    </div>

                    {/* 费用信息 */}
                    <div className="flex gap-4 mt-2 text-sm">
                      <span className="text-gray-600">
                        {dict?.confirm?.order?.transport?.firstWeight}: <span className="font-medium text-[#FF6000]">{formatCurrency(parseFloat(method.firstfee)).formatValue}</span>/{method.firstweight}g
                      </span>
                      <span className="text-gray-600">
                        {dict?.confirm?.order?.transport?.continueWeight}: <span className="font-medium text-[#FF6000]">{formatCurrency(parseFloat(method.continuefee)).formatValue}</span>/{method.continueweight}g
                      </span>
                    </div>

                    {method.ban && (
                      <div className="text-xs text-gray-400 mt-2">{dict?.confirm?.order?.transport?.restriction}: {method.ban}</div>
                    )}

                    {/* 线路特点 */}
                    {method.feature && (
                      <div className="mt-2">
                        <div className="text-xs text-gray-600">
                          <span className="font-medium">{dict?.confirm?.order?.transport?.feature}:</span>
                        </div>
                        <div className="text-xs text-gray-500 mt-1 leading-relaxed">
                          {method.feature}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                <div className="absolute top-4 right-4 text-lg font-bold text-[#FF6000]">
                  {formatCurrency(method.fee.totalfee).formatValue}
                </div>
              </div>
            ))}
          </div>
        ) : (
          !loading && <Empty description={dict?.confirm?.order?.transport?.noAvailable}/>
        )}
      </Spin>
    </div>
  );
} 