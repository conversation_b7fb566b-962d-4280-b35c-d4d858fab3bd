'use client'

import React, { useState, useCallback, useEffect } from 'react';
import { Modal, Skeleton, Empty, Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import {Api, AddressItem} from '@/request/api';
import AddressCard from '@/app/[lng]/(default)/dashboard/account/components/AddressCard';
import AddressModal from '@/components/AddressModal';
import Toast from '@/components/Toast';
import { useParams } from 'next/navigation';
import { getDictionary } from "@/dictionaries";

export default function AddressPage() {
  const [addresses, setAddresses] = useState<AddressItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddressModal, setShowAddressModal] = useState(false);
  const [currentAddress, setCurrentAddress] = useState<AddressItem | null>(null);
  const { lng } = useParams();
  const [dict, setDict] = useState<any>(null);

  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };
    fetchDictionary();
  }, [lng]);

  useEffect(() => {
    fetchAddresses();
  }, []);

  const fetchAddresses = async () => {
    try {
      const response = await Api.getAddressList();
      setAddresses(response.data);
    } catch (error) {
      console.error('获取地址列表失败', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      const response = await Api.deleteAddress(id);
      if (response.success) {
        Toast.success(dict?.dashboard?.address?.deleteSuccess || '删除成功');
        fetchAddresses(); // 刷新列表
      } else {
        Toast.error(response.msg || dict?.dashboard?.address?.deleteFail || '删除失败');
      }
    } catch (error) {
      Toast.error(dict?.dashboard?.address?.deleteFail || '删除失败');
    }
  };

  const confirmDelete = (id: number) => {
    Modal.confirm({
      title: dict?.dashboard?.address?.confirmDelete || '确认删除',
      content: dict?.dashboard?.address?.confirmDeleteContent || '确定要删除这个地址吗？',
      okText: dict?.dashboard?.address?.confirm || '确定',
      cancelText: dict?.dashboard?.address?.cancel || '取消',
      onOk: () => handleDelete(id)
    });
  };

  const handleEdit = useCallback((id: number) => {
    const address = addresses.find(addr => addr.id === id);
    if (address) {
      setCurrentAddress(address);
      setShowAddressModal(true);
    }
  }, [addresses]);

  const handleAddAddress = () => {
    setCurrentAddress(null);
    setShowAddressModal(true);
  };

  const formatAddressDetail = (address: AddressItem) => {
    return `${address.address}${address.door_number ? ` #${address.door_number}` : ''}`;
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-center flex-1">
          {dict?.dashboard?.address?.title || '地址管理'}
        </h1>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddAddress}
          style={{ backgroundColor: '#FF6000', borderColor: '#FF6000' }}
        >
          {dict?.dashboard?.address?.addNew || '添加新地址'}
        </Button>
      </div>

      {loading ? (
        <div className="grid grid-cols-1 gap-4">
          {[...Array(3)].map((_, i) => (
            <Skeleton key={i} active paragraph={{ rows: 2 }} />
          ))}
        </div>
      ) : addresses.length > 0 ? (
        <div className="grid grid-cols-1 gap-4 animate-fade-in">
          {addresses.map((address) => (
            <AddressCard
              key={address.id}
              recipient={address.consignee}
              phone={address.telephone}
              addressDetail={formatAddressDetail(address)}
              isDefaultAddress={address.isdefault === 1}
              onEdit={() => handleEdit(address.id)}
              onDelete={() => confirmDelete(address.id)}
              dict={dict}
            />
          ))}
        </div>
      ) : (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <span className="text-gray-500">
              {dict?.dashboard?.address?.empty || '还没有收货地址，点击"添加新地址"开始添加'}
            </span>
          }
        />
      )}

      {showAddressModal && (
        <AddressModal
          open={showAddressModal}
          onCancel={() => {
            setShowAddressModal(false);
            setCurrentAddress(null);
          }}
          onSuccess={fetchAddresses}
          currentAddress={currentAddress}
          dict={dict}
        />
      )}
    </div>
  );
}