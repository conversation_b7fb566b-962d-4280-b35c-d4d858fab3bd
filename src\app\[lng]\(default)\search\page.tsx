import { getDictionary } from "@/dictionaries";
import type { Locale } from "@/config";
import SearchResults from "./(components)/SearchResults";
import { SearchPageProps } from "./types";


export default async function SearchPage({
  params,
  searchParams,
}: SearchPageProps) {
 
    const [resolvedParams, resolvedSearchParams] = await Promise.all([
      Promise.resolve(params),
      Promise.resolve(searchParams),
    ]);

    // 验证语言参数
    if (!resolvedParams?.lng) {
      throw new Error("Language parameter is required");
    }

    const lng = resolvedParams.lng as Locale;
    const query = resolvedSearchParams?.q?.toString() || "";
    const currentPage = Number(resolvedSearchParams?.page) || 1;
    const pageSize = 20;
    const imgid = resolvedSearchParams?.imgid?.toString() || "";
    const initialPlatform = resolvedSearchParams?.type?.toString() || resolvedSearchParams?.platform?.toString() || "";

    // 获取字典数据
    const dict = await getDictionary(lng);
   
    return (
      <SearchResults
          query={query}
          currentPage={currentPage}
          pageSize={pageSize}
          imgid={imgid}
          dict={dict}
          lng={lng}
          initialPlatform={initialPlatform}
          />
    );

}
