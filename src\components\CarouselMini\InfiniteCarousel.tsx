'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';





interface CarouselItem {
  image: string;
  title?: string;
  link?: string;
  linkurl?: string;
  target?: string;
  description?: string;
}

interface InfiniteCarouselProps {
  carouselItems: CarouselItem[];
}

export default function InfiniteCarousel({ carouselItems }: InfiniteCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(carouselItems.length);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const carouselRef = useRef<HTMLDivElement>(null);
  const timerRef = useRef<NodeJS.Timeout | undefined>(undefined);

  // 创建无限循环的数组，前后各复制一组
  const items = [...carouselItems, ...carouselItems, ...carouselItems];
  const itemWidth = 33.33; // 每个项目占33.33%的宽度

  // 预加载所有图片
  useEffect(() => {
    if (typeof window !== 'undefined') {
      carouselItems.forEach(item => {
        const img = new window.Image();
        img.src = item.image;
      });
    }
  }, []);

  // 处理过渡结束
  const handleTransitionEnd = () => {
    setIsTransitioning(false);
    // 检查是否需要重置位置
    if (currentIndex >= items.length - carouselItems.length) {
      setCurrentIndex(carouselItems.length);
    } else if (currentIndex < carouselItems.length) {
      setCurrentIndex(items.length - carouselItems.length * 2);
    }
  };

  // 自动轮播
  useEffect(() => {
    if (!isPaused && !isTransitioning) {
      timerRef.current = setInterval(() => {
        setIsTransitioning(true);
        setCurrentIndex((prev: number) => prev + 1);
      }, 3000);
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isPaused, isTransitioning]);

  const handlePrev = () => {
    if (!isTransitioning) {
      setIsTransitioning(true);
      setCurrentIndex((prev: number) => prev - 1);
    }
  };

  const handleNext = () => {
    if (!isTransitioning) {
      setIsTransitioning(true);
      setCurrentIndex((prev: number) => prev + 1);
    }
  };

  const handleDotClick = (index: number) => {
    if (!isTransitioning) {
      setIsTransitioning(true);
      setCurrentIndex(index + carouselItems.length);
    }
  };

  return (
    <div 
      className="relative w-full overflow-hidden group"
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
    >
      <div 
        ref={carouselRef}
        className={`flex ${isTransitioning ? 'transition-transform duration-500 ease-in-out' : ''}`}
        style={{
          transform: `translateX(-${currentIndex * itemWidth}%)`,
        }}
        onTransitionEnd={handleTransitionEnd}
      >
        {items.map((item, index) => (
          <div 
            key={`${item.title}-${index}`}
            className="flex-shrink-0 relative px-3"
            style={{ width: `${itemWidth}%` }}
            onClick={()=>{
              window.open(item.linkurl, item.target || '_blank');
            }}
          >
            <div className="relative h-[210px] rounded-2xl overflow-hidden shadow-lg">
              <Image
                src={item.image}
                alt={item.title || ''}
                fill
                priority
                className="object-cover"
                sizes="33vw"
                onError={(e) => {
                  console.error('Image load error:', e);
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent" />
              <div className="absolute inset-x-0 bottom-0 p-6 text-white">
                <h2 className="text-2xl font-bold mb-2 line-clamp-1">{item.title}</h2>
                <p className="text-base text-white/90 line-clamp-2" dangerouslySetInnerHTML={{ __html: item.description || '' }}></p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 导航按钮 */}
      <button
        className="absolute left-6 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-3 rounded-full shadow-lg transition-all duration-200 opacity-0 group-hover:opacity-100 z-10"
        onClick={handlePrev}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      <button
        className="absolute right-6 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-3 rounded-full shadow-lg transition-all duration-200 opacity-0 group-hover:opacity-100 z-10"
        onClick={handleNext}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </button>

      {/* 指示器 */}
      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 z-10">
        {carouselItems.map((_: CarouselItem, index: number) => (
          <button
            key={index}
            className={`w-2 h-2 rounded-full transition-all duration-200 ${
              index === currentIndex % carouselItems.length ? 'bg-white w-4' : 'bg-white/50'
            }`}
            onClick={() => handleDotClick(index)}
          />
        ))}
      </div>
    </div>
  );
} 