export interface HotProduct {
  id: number;
  keyword: string;
  goodssite: 'taobao' | '1688' | 'jd';
  createtime: number;
  langcode: string;
  weight: number;
}

export interface HotProductResponse {
  success: boolean;
  data: HotProduct[];
}

export interface ShoppingTimeProduct {
  id: number;
  order_id: number;
  user_id: number;
  goodssn: string;
  goodsurl: string;
  goodsname: string;
  cn_goodsname: string;
  goodsweight: number;
  goodsvolume: number;
  goodsprice: string;
  oldgoodsprice: string;
  sendprice: string;
  buyprice: string;
  serverfee: number;
  serverdiscount: number;
  goodsnum: number;
  goodsimg: string;
  goodsseller: string;
  sellerurl: string;
  goodssite: '1688' | 'taobao' | 'jd' | 'micro';
  goodsremark: string;
  goodstype: string;
  status_id: number;
  status_text: string;
  sku_id: string;
  skuname: string;
  cn_skuname: string | null;
  currencycode: string;
  createtime: string;
  updatetime: string;
  recommend?: number | string; // 推荐标识字段
  user: {
    username: string;
    nickname: string;
  };
}

export interface ShoppingTimeResponse {
  code: number;
  msg: string;
  data: ShoppingTimeProduct[];
}