'use client';

import style from './index.module.css'
import { useParams } from 'next/navigation';
import { getDictionary } from "@/dictionaries";
import React, { useState, useEffect } from 'react'
export default function SizeComparisonPage() {
      const { lng } = useParams();
      const [dict, setDict] = useState<any>(null); // 添加字典状态
       // 异步获取字典数据
      useEffect(() => {
        const fetchDictionary = async () => {
          try {
            const dictionary = await getDictionary(lng as string);
            setDict(dictionary);
          } catch (error) {
            console.error('Failed to load dictionary:', error);
          }
        };
    
        fetchDictionary();
      }, [lng]);
    return (
        <div className="w-full">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="bg-white rounded-lg shadow-sm" id="t_container">
                    {/* 女士尺码表标题 */}
                    <div className="border-b pb-4 mb-6">
                        <h1 className="text-2xl font-bold text-center sm:text-left text-gray-800">
                               {dict?.sizecomparison?.womensShirtChart}
                        </h1>
                    </div>
                    
                    {/* 女士衬衫尺码表 */}
                    <div className={style.area}>
                        <span className={style.title}>
                         {dict?.sizecomparison?.womensShirtTitle}
                        </span>
                        
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white border-collapse">
                                <tbody>
                                    <tr className="bg-gray-100">
                                        <td className="py-3 px-4 border font-bold text-gray-700">{dict?.sizecomparison?.Shirt} {dict?.sizecomparison?.size}</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">S</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">M</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">L</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XL</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XXL</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XXXL</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.international}{dict?.sizecomparison?.size}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">36</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">37</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">38</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">39</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">40</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">41</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.bust} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">79-82</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">83-86</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">87-90</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">91-94</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">95-98</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">99-103</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.waistline} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">62-66</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">67-70</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">71-74</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">75-78</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">79-82</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">83-86</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.shoulder} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">37</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">38</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">39</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">40</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">41</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">42</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.height} / {dict?.sizecomparison?.bust}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">155 / 82A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">160 / 86A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">165 / 90A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">170 / 94A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">172 / 98A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">175 / 102A</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div className={style.desc}>
                            <p>{dict?.sizecomparison?.womensShirtTip1}</p>
                            <p>{dict?.sizecomparison?.womensShirtTip2}</p>
                            <p>{dict?.sizecomparison?.womensShirtTip3}</p>
                        </div>
                    </div>
                    
                    {/* 连衣裙尺码表 */}
                    <div className={style.area}>
                        <h2 className={style.title}>
                           {dict?.sizecomparison?.dressChart}
                        </h2>
                        
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white border-collapse">
                                <tbody>
                                    <tr className="bg-gray-100">
                                        <td className="py-3 px-4 border font-bold text-gray-700">{dict?.sizecomparison?.Shirt}{dict?.sizecomparison?.size}</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">S</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">M</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">L</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XL</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XXL</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.Shirt}{dict?.sizecomparison?.size}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">36</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">37</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">38</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">39</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">40</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.bust} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">79-82</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">83-86</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">87-90</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">91-94</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">95-98</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.waistline} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">62-66</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">67-70</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">71-74</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">75-78</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">79-82</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.shoulder} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">37</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">38</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">39</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">40</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">41</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.height} / {dict?.sizecomparison?.bust}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">155 / 82A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">160 / 86A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">165 / 90A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">170 / 94A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">172 / 98A</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div className={style.desc}>
                            <p>{dict?.sizecomparison?.dressTip1}</p>
                        </div>
                    </div>
                    
                    {/* 女士裤子尺码表 */}
                    <div className={style.area}>
                        <h2 className={style.title}>
                           {dict?.sizecomparison?.womensPantsChart}
                        </h2>
                        
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white border-collapse">
                                <tbody>
                                    <tr className="bg-gray-100">
                                        <td className="py-3 px-4 border font-bold text-gray-700">{dict?.sizecomparison?.pants}</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">S</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">M</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">L</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XL</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.pants}{dict?.sizecomparison?.size} ( {dict?.sizecomparison?.feet})</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">25-26</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">27-28</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">29-30</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">31-32</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.international}{dict?.sizecomparison?.model}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">155/62A - 159/64A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">160/66A - 164/68A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">165/70A - 169/72A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">170/74A - 170/76A</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.hip} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">85-87.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">90-92.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">95-97.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">100-102.5</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.waistline} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">62-64.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">67-69.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">72-74.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">77-79.5</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div className={style.desc}>
                            <p>{dict?.sizecomparison?.womensPantsTip1}</p>
                            <p>{dict?.sizecomparison?.womensPantsTip2}</p>
                            <p>{dict?.sizecomparison?.womensPantsTip3}</p>
                          
                        </div>
                    </div>
                    
                    {/* 男士尺码表标题 */}
                    <div className="border-t border-b py-4 my-8">
                        <h1 className="text-2xl font-bold text-center sm:text-left text-gray-800">
                          {dict?.sizecomparison?.mensChart}
                        </h1>
                    </div>
                    
                    {/* 男士衬衫尺码表 */}
                    <div className={style.area}>
                        <h2 className={style.title}>
                            {dict?.sizecomparison?.mensShirtChart}
                        </h2>
                        
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white border-collapse">
                                <tbody>
                                    <tr className="bg-gray-100">
                                        <td className="py-3 px-4 border font-bold text-gray-700"> {dict?.sizecomparison?.Shirt} {dict?.sizecomparison?.size}</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XS</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">S</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">M</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">L</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XL</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XXL</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XXXL</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.clothing } {dict?.sizecomparison?.size}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">37</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">38</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">39</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">40</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">41</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">42</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">43</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.international}{dict?.sizecomparison?.model}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">160/80A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">165/84A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">170/88A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">175/92A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">180/96A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">180/100A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">185/104A</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.shoulder} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">42-43</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">44-45</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">46-47</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">47-48</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">49-50</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">51-52</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">53-54</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.bust} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">98-101</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">102-105</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">106-109</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">110-113</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">114-117</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">118-121</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">122-125</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.length} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">72</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">74</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">76</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">78</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">80</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">82</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">83</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.height} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">160</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">165</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">170</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">175</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">180</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">185</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">190</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div className={style.desc}>
                            <p>{dict?.sizecomparison?.mensShirtTip1}</p>
                        </div>
                    </div>
                    
                    {/* 男士西装尺码表 */}
                    <div className={style.area}>
                        <h2 className={style.title}>
                           {dict?.sizecomparison?.mensSuitChart}
                        </h2>
                        
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white border-collapse">
                                <tbody>
                                    <tr className="bg-gray-100">
                                        <td className="py-3 px-4 border font-bold text-gray-700"> {dict?.sizecomparison?.size}</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700"> {dict?.sizecomparison?.spec}</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">{dict?.sizecomparison?.version}</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">{dict?.sizecomparison?.length}</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">{dict?.sizecomparison?.bust}</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">{dict?.sizecomparison?.shoulder}</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">{dict?.sizecomparison?.sleeve}</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">2R48</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">165 / 96C</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.overweight}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">70</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">106</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">44.7</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">60</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">2R50</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">170 / 100C</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.overweight}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">72</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">110</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">45.9</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">61.5</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">2R52</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">175 / 104C</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.overweight}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">74</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">114</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">47.1</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">63</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">2R54</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">180 / 108C</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.overweight}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">76</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">118</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">48.3</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">64.5</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">2R56</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">185 / 112C</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.overweight}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">78</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">122</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">49.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">66</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">4R46</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">165 / 92B</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.standard}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">70</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">102</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">43.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">60</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">4R48</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">170 / 96B</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.standard}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">72</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">106</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">44.7</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">61.5</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">4R50</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">175 / 100B</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.standard}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">74</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">110</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">45.9</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">63</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">4R54</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">185 / 108B</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.standard}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">78</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">118</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">48.3</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">66</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div className={style.desc}>
                            <p>{dict?.sizecomparison?.mensShirtTip1}</p>
                        </div>
                    </div>
                    
                    {/* 男士裤子尺码表 */}
                    <div className={style.area}>
                        <h2 className={style.title}>
                            {dict?.sizecomparison?.mensPantsChart}
                        </h2>
                        
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white border-collapse">
                                <tbody>
                                    <tr className="bg-gray-100">
                                        <td className="py-3 px-4 border font-bold text-gray-700"> {dict?.sizecomparison?.mensPants}</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">S</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">M</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">L</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700"> {dict?.sizecomparison?.clothing}{dict?.sizecomparison?.size}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">170/72A-170-74A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">170/76A-175/80A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">175/82A-175/84A</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.size}/{dict?.sizecomparison?.inch}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">29-30</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">31-32</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">33-34</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.hip} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">97.5-100</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">102.5-105</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">107.5-110</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.waistline}  / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">73.7-76.2</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">78.7-81.3</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">83.8-110</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.waistline}  / {dict?.sizecomparison?.clothRuler} </td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">2 {dict?.sizecomparison?.feet} 2 {dict?.sizecomparison?.inch} - 2 {dict?.sizecomparison?.feet} 3 {dict?.sizecomparison?.inch}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">2 {dict?.sizecomparison?.feet} 4 {dict?.sizecomparison?.inch}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">2 {dict?.sizecomparison?.feet} 5 {dict?.sizecomparison?.inch} - 2 {dict?.sizecomparison?.feet} 6 {dict?.sizecomparison?.inch}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div className="mt-6 overflow-x-auto">
                            <table className="min-w-full bg-white border-collapse">
                                <tbody>
                                    <tr className="bg-gray-100">
                                        <td className="py-3 px-4 border font-bold text-gray-700"> {dict?.sizecomparison?.mensPants}</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XL</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XXL</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XXXL</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.clothing}{dict?.sizecomparison?.size}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">180/86A-180/90A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">185/92A-185/94B</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">190/98B-195/102B</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.size}/{dict?.sizecomparison?.inch}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">35-36</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">37-38</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">40-42</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.hip} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">112.5-100</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">117.5-120</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">122.5-130</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">waistline / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">89-91.4</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">93.3-96.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">101.6-106.6</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.waistline}  / {dict?.sizecomparison?.clothRuler}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">2 {dict?.sizecomparison?.feet}  6 {dict?.sizecomparison?.inch} - 2 {dict?.sizecomparison?.feet}  7 {dict?.sizecomparison?.inch}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">2 {dict?.sizecomparison?.feet}  8 {dict?.sizecomparison?.inch} - 2 {dict?.sizecomparison?.feet}  9 {dict?.sizecomparison?.inch}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">3 {dict?.sizecomparison?.feet}  1 {dict?.sizecomparison?.inch} - 3 {dict?.sizecomparison?.feet}  2 {dict?.sizecomparison?.inch}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    {/* 儿童尺码表标题 */}
                    <div className="border-t border-b py-4 my-8">
                        <h1 className="text-2xl font-bold text-center sm:text-left text-gray-800">
                         {dict?.sizecomparison?.kidsClothingChart}
                        </h1>
                    </div>
                    
                    {/* 婴儿服装尺码表 */}
                    <div className={style.area}>
                        <h2 className={style.title}>
                           {dict?.sizecomparison?.babyClothingChart}
                        </h2>
                        
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white border-collapse">
                                <tbody>
                                    <tr className="bg-gray-100">
                                        <td className="py-3 px-4 border font-bold text-gray-700">{dict?.sizecomparison?.size} / cm</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">59</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">66</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">73</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">80</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">90</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.age} </td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">0-3{dict?.sizecomparison?.months}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">3-6{dict?.sizecomparison?.months}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">6-9{dict?.sizecomparison?.months}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">9-12{dict?.sizecomparison?.months}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">1-1.5{dict?.sizecomparison?.yearOld}</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.height}/ cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">59</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">66</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">73</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">80</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">90</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.bust} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">40</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">44</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">47</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">50</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">50</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.waistline} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">38</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">40</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">42</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">44</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">46</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div className={style.desc}>
                            <p>{dict?.sizecomparison?.babyClothingTip1}</p>
                        </div>
                    </div>
                    
                    {/* 中国儿童服装尺码表 */}
                    <div className={style.area}>
                        <h2 className={style.title}>
                            {dict?.sizecomparison?.chineseKidsChart}
                        </h2>
                        
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white border-collapse">
                                <tbody>
                                    <tr className="bg-gray-100">
                                        <td className="py-3 px-4 border font-bold text-gray-700">{dict?.sizecomparison?.size} / cm</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">100</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">110</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">120</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">130</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.age}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">1.5{dict?.sizecomparison?.yearOld}-3{dict?.sizecomparison?.yearOld}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">3{dict?.sizecomparison?.yearOld}-6{dict?.sizecomparison?.yearOld}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">6{dict?.sizecomparison?.yearOld}-8{dict?.sizecomparison?.yearOld}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">8{dict?.sizecomparison?.yearOld}-10{dict?.sizecomparison?.yearOld}</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.height} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">100</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">110</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">120</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">130</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.bust} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">54</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">58</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">62</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">66</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.waistline} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">52</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">54</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">56</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">58</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div className={style.desc}>
                            <p>{dict?.sizecomparison?.chineseKidsTip1}</p>
                        </div>
                    </div>
                    
                    {/* 女性内衣尺码表标题 */}
                    <div className="border-t border-b py-4 my-8">
                        <h1 className="text-2xl font-bold text-center sm:text-left text-gray-800">
                          {dict?.sizecomparison?.womensLingerieChart}
                        </h1>
                    </div>
                    
                    {/* 文胸尺码表 */}
                    <div className={style.area}>
                        <h2 className={style.title}>
                             {dict?.sizecomparison?.braChart}
                        </h2>
                        
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white border-collapse">
                                <tbody>
                                    <tr className="bg-gray-100">
                                        <td className="py-3 px-4 border font-bold text-gray-700" rowSpan={2}>{dict?.sizecomparison?.china} / cm</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700" colSpan={8}>{dict?.sizecomparison?.europe} {dict?.sizecomparison?.size} </td>
                                    </tr>
                                    <tr className="bg-gray-100">
                                        <td className="py-3 px-4 border font-bold text-gray-700">65</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">70</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">75</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">80</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">85</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">90</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">95</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">100</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.bust} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">76-78</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">81-83</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">86-88</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">91-93</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">96-98</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">101-103</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">106-108</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">111-113</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.cup} A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">65A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">70A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">75A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">80A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">85A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">90A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">95A</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">100A</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.cup} B</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">65B</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">70B</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">75B</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">80B</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">85B</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">90B</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">95B</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">100B</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.cup} C</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">65C</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">70C</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">75C</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">80C</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">85C</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">90C</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">95C</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">100C</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.cup} D</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">65D</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">70D</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">75D</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">80D</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">85D</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">90D</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">95D</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">100D</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div className={style.desc}>
                            <p>{dict?.sizecomparison?.bustMeasureTip1}</p>
                            <p>{dict?.sizecomparison?.bustMeasureTip2}</p>
                        </div>
                    </div>
                    
                    {/* 内裤尺码表 */}
                    <div className={style.area}>
                        <h2 className={style.title}>
                            {dict?.sizecomparison?.underwearChart}
                        </h2>
                        
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white border-collapse">
                                <tbody>
                                    <tr className="bg-gray-100">
                                        <td className="py-3 px-4 border font-bold text-gray-700">{dict?.sizecomparison?.international} {dict?.sizecomparison?.size} </td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XS</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">S</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">M</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">L</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XL</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">XXL</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.dress}{dict?.sizecomparison?.size}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">0-2</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">4-6</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">8-10</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">12-14</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">16-18</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">20</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.waistline} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">61-63</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">66-68</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">71-76</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">78-83</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">86-93</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">96-103</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.hip} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">86-89</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">91-94</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">96-99</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">101-104</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">106-109</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">111-116</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div className={style.desc}>
                            <p>{dict?.sizecomparison?.waistMeasureTip1}</p>
                           <p>{dict?.sizecomparison?.waistMeasureTip2}</p>
                        </div>
                    </div>
                    
                    {/* 鞋子尺码表标题 */}
                    <div className="border-t border-b py-4 my-8">
                        <h1 className="text-2xl font-bold text-center sm:text-left text-gray-800">
                            {dict?.sizecomparison?.shoeChart}
                        </h1>
                    </div>
                    
                    {/* 女鞋尺码表 */}
                    <div className={style.area}>
                        <h2 className={style.title}>
                              {dict?.sizecomparison?.womensShoeChart}
                        </h2>
                        
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white border-collapse">
                                <tbody>
                                    <tr className="bg-gray-100">
                                        <td className="py-3 px-4 border font-bold text-gray-700"> {dict?.sizecomparison?.europe}</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">35</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">36</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">37</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">38</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">39</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">40</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">41</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">42</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.usa}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">5.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">6.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">7.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">8.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">9</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">9.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">10</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.uk}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">3</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">3.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">4</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">6</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">6.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">7</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">7.5</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.japan} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">22</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">22.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">23.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">24.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">25</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">25.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">26</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">26.5</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.china}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">35</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">36</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">37</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">38</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">39</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">40</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">41</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">42</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div className={style.desc}>
                            <p>{dict?.sizecomparison?.shoeTip1}</p>
                        </div>
                    </div>
                    
                    {/* 男鞋尺码表 */}
                    <div className={style.area}>
                        <h2 className={style.title}>
                       {dict?.sizecomparison?.mensShoeChart}
                        </h2>
                        
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white border-collapse">
                                <tbody>
                                    <tr className="bg-gray-100">
                                        <td className="py-3 px-4 border font-bold text-gray-700">{dict?.sizecomparison?.europe}</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">39</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">40</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">41</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">42</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">43</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">44</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">45</td>
                                        <td className="py-3 px-4 border font-bold text-gray-700">46</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.usa}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">6.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">7.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">8</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">8.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">9.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">10.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">11.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">12</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.uk}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">6</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">6.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">7</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">8</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">9</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">10</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">11</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">11.5</td>
                                    </tr>
                                    <tr className="bg-white">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.japan} / cm</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">25</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">25.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">26</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">26.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">27.5</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">28</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">29</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">29.5</td>
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="py-2 px-4 border text-sm text-gray-700">{dict?.sizecomparison?.china}</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">39</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">40</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">41</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">42</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">43</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">44</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">45</td>
                                        <td className="py-2 px-4 border text-sm text-gray-700">46</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div className={style.desc}>
                            <p>{dict?.sizecomparison?.shoeTip2}</p>
                            <p>{dict?.sizecomparison?.footMeasureTip}</p>
                        </div>
                    </div>
                    
                    {/* 測量指南 */}
                    <div className="border-t pt-4 mt-8">
                        <h1 className="text-2xl font-bold text-center sm:text-left text-gray-800 mb-6">
                           {dict?.sizecomparison?.measureGuide}
                        </h1>
                        
                        <div className="p-6 bg-gray-50 rounded-lg shadow-sm">
                            <div className="space-y-4 text-gray-700">
                                <div>
                                    <p className="font-semibold">{dict?.sizecomparison?.guide1}</p>
                                    <p className="mt-1">{dict?.sizecomparison?.guideDesc1}</p>
                                </div>
                                
                                <div>
                                   <p className="font-semibold">{dict?.sizecomparison?.guide2}</p>
                                    <p className="mt-1">{dict?.sizecomparison?.guideDesc2}</p>
                                </div>
                                
                                <div>
                                      <p className="font-semibold">{dict?.sizecomparison?.guide3}</p>
                                    <p className="mt-1">{dict?.sizecomparison?.guideDesc3}</p>
                                </div>
                                
                                <div>
                                      <p className="font-semibold">{dict?.sizecomparison?.guide4}</p>
                                    <p className="mt-1">{dict?.sizecomparison?.guideDesc4}</p>
                                </div>
                                
                                <div>
                                    <p className="font-semibold">{dict?.sizecomparison?.guide5}</p>
                                    <p className="mt-1">{dict?.sizecomparison?.guideDesc5}</p>
                                </div>
                                
                                <div>
                                     <p className="font-semibold">{dict?.sizecomparison?.guide6}</p>
                                    <p className="mt-1">{dict?.sizecomparison?.guideDesc6}</p>
                                </div>
                                
                                <div>
                                      <p className="font-semibold">{dict?.sizecomparison?.guide7}</p>
                                    <p className="mt-1">{dict?.sizecomparison?.guideDesc7}</p>
                                </div>
                                
                                <div>
                                     <p className="font-semibold">{dict?.sizecomparison?.guide8}</p>
                                    <p className="mt-1">{dict?.sizecomparison?.guideDesc8}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}