"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Api } from "@/request/api";
import { Select, Input, message, Form } from "antd";
import Button from "@/components/Button";
import Toast from "@/components/Toast";
import Modal from "@/components/Modal";
import type { Locale } from "@/config";
import { useParams } from "next/navigation";
import { getDictionary } from "@/dictionaries";
import ToastHelper from "@/utils/toastHelper";
import ModalComponent from "@/components/Modal";

interface BankItem {
  id: number;
  name: string;
  code: string;
  createtime: number;
  updatetime: number;
  status: string;
}

export default function WithdrawModal({ onOk, ...props }: any) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [bankList, setBankList] = useState<BankItem[]>([]);
  const [payAmount, setPayAmount] = useState(0);
  const [form] = Form.useForm();
  const [config, setConfig] = useState<any>({});

  const params = useParams();
  const lng = (params.lng as Locale) || "zh-cn";
  const [dict, setDict] = useState<any>(null);
  const [emailModalOpen, setEmailModalOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [emailCode, setEmailCode] = useState("");
  const [emailCountdown, setEmailCountdown] = useState(0);
  const emailTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 获取URL参数
  const withdrawalType = searchParams.get("type"); // 'commission' 表示佣金提现
  const prefilledAmount = searchParams.get("amount"); // 预填充金额

  useEffect(() => {
    return () => {
      if (emailTimerRef.current) {
        clearInterval(emailTimerRef.current);
      }
    };
  }, []);
  useEffect(() => {
    const loadDict = async () => {
      const dictionary = await getDictionary(lng);
      setDict(dictionary);
    };
    loadDict();
  }, [lng]);

  useEffect(() => {
    let config = getConfig();
    setConfig(config);
  }, []);

  // 安全地获取图片搜索配置，避免SSR时localStorage不可用的问题
  const getConfig = () => {
    if (typeof window === "undefined") {
      return 0; // 服务器端默认返回0
    }
    try {
      const siteData = localStorage.getItem("siteData");
      if (!siteData) return 0;
      const parsedData = JSON.parse(siteData);
      return parsedData;
    } catch (error) {
      console.error("解析siteData失败:", error);
      return 0;
    }
  };

  const calculatePayAmount = (amount: number) => {
    if (!amount) return 0;

    const feeRate = Number(config.withdraw_exp1);
    const serviceFee = Number(config.withdraw_exp2);
    const minFee = Number(config.withdraw_min);
    const maxFee = Number(config.withdraw_max);
    const calcFee = Math.min(
      Math.max(amount * feeRate, minFee) + serviceFee,
      maxFee
    );
    console.log("calculatePayAmount", amount, calcFee);
    const fee = amount + calcFee;

    return fee;
  };

  // 监听表单金额变化
  const handleAmountChange = (value: string) => {
    const amount = parseFloat(value) || 0;
    setPayAmount(calculatePayAmount(amount));
  };

  const fetchBankList = async () => {
    setLoading(true);
    try {
      const res = await Api.getBankList();
      if (res.success) {
        setBankList(res.data);
      } else {
        Toast.error(res.msg || ToastHelper.getLocalizedMessage('fetch_bank_list_failed', lng as string));
      }
    } catch (error) {
      console.error("获取银行列表失败:", error);
      ToastHelper.showNetworkError(lng as string);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBankList();
  }, []);

  // 预填充佣金提现金额
  useEffect(() => {
    if (withdrawalType === "commission" && prefilledAmount) {
      const amount = parseFloat(prefilledAmount);
      if (amount > 0) {
        form.setFieldsValue({ amount: amount.toString() });
        setPayAmount(calculatePayAmount(amount));
      }
    }
  }, [withdrawalType, prefilledAmount, form, config]);

  const beforeSubmit = async () => {
    if (config.withdraw_captcha === "email") {
      setEmailModalOpen(true);
      return;
    }
    handleSubmit();
  };
  const handleSubmit = async (value: any = {}) => {
    setLoading(true);
    const values = await form.validateFields();
    try {
      let data = {
        transfer_account: values.receiver,
        transfer_card: values.account,
        transfer_bank_selected: values.bank,
        transfer_amount: parseFloat(values.amount),
        // 添加提现类型标识
        withdrawal_type: withdrawalType || "balance",
        ...value,
      };
      const res = await Api.addCash(data);
      console.log("提现成功:", res);
      if (res.success) {
        if(res.data?.code && res.data.code !== 200){
          Toast.error(res.msg || res.data?.msg || "提现申请失败");
          return
        }
        const successMessage =
          withdrawalType === "commission"
            ? "佣金提现申请已提交，请等待审核"
            : "提现申请已提交";
        Toast.success(successMessage);
        form.resetFields();
        setPayAmount(0);
        // router.push(`/${lng}/dashboard/cash`);
        handleSubmitOk();
      } else {
        Toast.error(res.msg || res.data?.msg || "提现申请失败");
      }
    } catch (error) {
      console.error("提现失败:", error);
      Toast.error("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };
  const handleEmailModal = () => {
    handleSubmit({ email: email, captcha: emailCode });
  };
  const startEmailCountdown = () => {
    setEmailCountdown(60);
    emailTimerRef.current = setInterval(() => {
      setEmailCountdown((prev) => {
        if (prev <= 1) {
          if (emailTimerRef.current) {
            clearInterval(emailTimerRef.current);
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };
  const handleSendEmailCode = async () => {
    if (!email) {
      return Toast.error(dict?.dashboard?.account?.emailHint);
    }
    try {
      const res = await Api.sendEmailCode({
        email: email,
        event: "withdraw",
      });
      if (res?.success) {
        Toast.success(dict?.dashboard?.account?.codeSent);
        startEmailCountdown();
      } else {
        Toast.error(res?.msg || dict?.dashboard?.account?.sendFail);
      }
    } catch (e) {
      console.error(e);
    }
  };
  const handleSubmitOk = () => {
    setEmailModalOpen(false);
    console.log(emailModalOpen, "emailModalOpen");
    if (onOk) {
      onOk();
    }
  };
  return (
    <div>
      <ModalComponent
        title={dict?.dashboard?.wallet?.withdraw}
        centered
        closable
        width={500}
        {...props}
        footer={[]}
      >
        <div>
          <div>
            {/* 显示提现类型提示 */}
            {withdrawalType === "commission" && (
              <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="text-orange-800 font-medium text-sm">
                  💰 佣金提现申请
                </div>
                <div className="text-orange-600 text-xs mt-1">
                  您正在申请提现推广佣金余额，请填写以下信息完成申请
                </div>
              </div>
            )}

            <div className="text-center mb-4 text-gray-500 text-sm">
              {dict?.dashboard?.addcash?.feeRate}: {config.withdraw_exp1 * 100}%
              {dict?.dashboard?.addcash?.serviceFee}: {config.withdraw_exp2}{" "}
              {dict?.dashboard?.addcash?.feeRange}: {config.withdraw_min} ~{" "}
              {config.withdraw_max}
            </div>

            <Form
              form={form}
              onFinish={beforeSubmit}
              layout="vertical"
              className="max-w-xl mx-auto"
            >
              <Form.Item
                label={dict?.dashboard?.addcash?.receiverLabel}
                name="receiver"
                rules={[
                  {
                    required: true,
                    message: dict?.dashboard?.addcash?.receiverHint,
                  },
                ]}
              >
                <Input placeholder={dict?.dashboard?.addcash?.receiverHint} />
              </Form.Item>

              <Form.Item
                label={dict?.dashboard?.addcash?.accountLabel}
                name="account"
                rules={[
                  {
                    required: true,
                    message: dict?.dashboard?.addcash?.accountHint,
                  },
                ]}
              >
                <Input placeholder={dict?.dashboard?.addcash?.accountHint} />
              </Form.Item>

              <Form.Item
                label={dict?.dashboard?.addcash?.bankLabel}
                name="bank"
                rules={[
                  {
                    required: true,
                    message: dict?.dashboard?.addcash?.bankHint,
                  },
                ]}
              >
                <Select
                  placeholder={dict?.dashboard?.addcash?.selectBank}
                  loading={loading}
                  options={bankList.map((bank) => ({
                    label: bank.name,
                    value: bank.code,
                  }))}
                />
              </Form.Item>

              <Form.Item
                label={dict?.dashboard?.addcash?.amountLabel}
                name="amount"
                rules={[
                  {
                    required: true,
                    message: dict?.dashboard?.addcash?.amountHint,
                  },
                  {
                    validator: (_, value) => {
                      if (!value) return Promise.resolve();
                      const num = parseFloat(value);

                      // 佣金提现的特殊验证
                      if (withdrawalType === "commission" && prefilledAmount) {
                        const maxAmount = parseFloat(prefilledAmount);
                        if (num > maxAmount) {
                          return Promise.reject(
                            `提现金额不能超过可用佣金余额 ${maxAmount.toFixed(
                              2
                            )}`
                          );
                        }
                      }

                      if (num % 100 !== 0) {
                        return Promise.reject(
                          dict?.dashboard?.addcash?.amountMultiple
                        );
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input
                  placeholder={dict?.dashboard?.addcash?.amountLabel}
                  type="number"
                  onChange={(e) => handleAmountChange(e.target.value)}
                />
                {withdrawalType === "commission" && prefilledAmount && (
                  <div className="text-xs text-gray-500 mt-1">
                    可用佣金余额: USD ${parseFloat(prefilledAmount).toFixed(2)}
                  </div>
                )}
              </Form.Item>

              <div className="text-right mt-4">
                <span className="mr-2">
                  {dict?.dashboard?.addcash?.totalAmount}:{" "}
                  {payAmount.toFixed(2)}
                </span>
              </div>
              <Button
                type="primary"
                htmlType="submit"
                className="w-full text-lg! h-12! bg-orange-500 text-white text-lg font-medium rounded-md hover:bg-orange-600 transition-colors mt-4"
              >
                {dict?.dashboard?.addcash?.receiveAmount}
              </Button>
            </Form>
          </div>
        </div>
      </ModalComponent>
      {/* 邮箱验证Modal */}
      <Modal
        title={dict?.dashboard?.addcash?.emailVerify}
        open={emailModalOpen}
        onCancel={() => setEmailModalOpen(false)}
        onOk={handleEmailModal}
        centered
        okText={dict?.dashboard?.addcash?.confirm}
        cancelText={dict?.dashboard?.addcash?.cancel}
      >
        <Input
          placeholder={dict?.dashboard?.addcash?.email}
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          style={{ marginBottom: 12 }}
        />
        <div style={{ display: "flex" }}>
          <Input
            placeholder={dict?.dashboard?.addcash?.verificationCode}
            value={emailCode}
            onChange={(e) => setEmailCode(e.target.value)}
            style={{ marginRight: 12, flex: 1 }}
          />
          <Button
            type="primary"
            disabled={emailCountdown > 0}
            onClick={handleSendEmailCode}
          >
            {emailCountdown > 0
              ? `${emailCountdown}s`
              : dict?.dashboard?.addcash?.sendCode}
          </Button>
        </div>
      </Modal>
    </div>
  );
}
