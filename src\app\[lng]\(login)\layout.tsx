import { getDictionary } from "@/dictionaries";
import type { Locale } from "@/config";
import { getConfigList } from "@/request/server";
import CacheValidator from "@/components/CacheValidator";
import ConfigLoadingWrapper from "@/components/ConfigLoadingWrapper";

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ lng: Locale }>;
}>) {
  const { lng } = await params;
  const dict = await getDictionary(lng);
  const configList = await getConfigList();

  return (
    <>
      <CacheValidator />
      <ConfigLoadingWrapper currentLng={lng}>
        <main>{children}</main>
      </ConfigLoadingWrapper>
    </>
  );
}
