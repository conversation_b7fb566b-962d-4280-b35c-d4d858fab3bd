'use client'

import React, { useState } from 'react'
import PhotoViewer from './PhotoViewer'

// 模拟运单数据
const mockOrderData = {
    id: 123,
    service: [
        { id: 1, name: 'pack', title: '打包' },
        { id: 2, name: 'photo', title: '拍照' },
        { id: 3, name: 'insurance', title: '保险' }
    ]
}

// 模拟字典数据
const mockDict = {
    confirm: {
        server: {
            pack: '打包',
            photo: '拍照',
            insurance: '保险'
        }
    },
    dashboard: {
        packages: {
            noPhoto: '暂无照片'
        }
    }
}

// 模拟照片数据
const mockPhotos = [
    {
        id: 1,
        image: 'https://via.placeholder.com/800x600/FF6B6B/FFFFFF?text=运单照片+1'
    },
    {
        id: 2,
        image: 'https://via.placeholder.com/800x600/4ECDC4/FFFFFF?text=运单照片+2'
    }
]

// 模拟API调用
const mockApi = {
    sendorderPhoto: async (id: number) => {
        console.log('模拟API调用 - 运单ID:', id)
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // 50% 概率返回照片，50% 概率返回空
        const hasPhotos = Math.random() > 0.5
        
        return {
            success: true,
            data: hasPhotos ? mockPhotos : [],
            msg: hasPhotos ? '获取成功' : '暂无照片'
        }
    }
}

interface PhotoItem {
    id: number;
    image: string;
}

export default function PhotoClickTest() {
    const [photoList, setPhotoList] = useState<PhotoItem[]>([])
    const [photoIsModal, setPhotoIsModal] = useState(false)
    const [photoViewerModal, setPhotoViewerModal] = useState(false)
    const [selectedPhotoIndex, setSelectedPhotoIndex] = useState(0)
    const [loading, setLoading] = useState(false)

    const handleViewPhoto = async () => {
        console.log('点击拍照服务，运单ID:', mockOrderData.id)
        console.log('运单服务数据:', mockOrderData.service)
        
        setLoading(true)
        
        try {
            const res = await mockApi.sendorderPhoto(mockOrderData.id)
            if (res.success) {
                if (res.data.length > 0) {
                    setPhotoList(res.data)
                    setPhotoIsModal(true)
                } else {
                    setPhotoList([])
                    alert(mockDict?.dashboard?.packages?.noPhoto || '暂无照片')
                }
            } else {
                alert(res.msg)
            }
        } catch (error) {
            console.error('获取照片失败:', error)
            alert('获取照片失败')
        } finally {
            setLoading(false)
        }
    }

    const handlePhotoClick = (index: number) => {
        setSelectedPhotoIndex(index)
        setPhotoViewerModal(true)
        setPhotoIsModal(false)
    }

    const handleClosePhotoViewer = () => {
        setPhotoViewerModal(false)
        setPhotoIsModal(true)
    }

    return (
        <div className="p-6">
            <h2 className="text-2xl font-bold mb-6">运单拍照功能测试</h2>
            
            <div className="mb-6">
                <p className="text-gray-600 mb-4">
                    这是运单服务标签的测试页面，点击"拍照"标签测试照片查看功能：
                </p>
            </div>

            {/* 运单服务标签 */}
            <div className="bg-white p-4 rounded-lg border mb-6">
                <h3 className="text-lg font-semibold mb-3">运单 #{mockOrderData.id}</h3>
                <div className='flex align-center'>
                    {mockOrderData.service.map((item: any) => {
                        const serviceName = item.name || item.name_code
                        const isPhotoService = serviceName === 'photo'
                        
                        return (
                            <p 
                                key={item.id} 
                                className={`inline-flex items-center mr-2 px-2.5 py-1 bg-orange-50/50 text-orange-500 rounded-full text-xs font-medium border border-orange-100 ${
                                    isPhotoService ? 'cursor-pointer hover:bg-orange-100 transition-colors' : ''
                                }`}
                                onClick={isPhotoService ? handleViewPhoto : undefined}
                            >
                                {isPhotoService && <i className="fas fa-camera mr-1"></i>}
                                {loading && isPhotoService && <i className="fas fa-spinner fa-spin mr-1"></i>}
                                {(mockDict?.confirm.server as any)?.[serviceName] || item.title || serviceName}
                            </p>
                        )
                    })}
                </div>
            </div>

            <div className="text-sm text-gray-500 mb-4">
                点击"拍照"标签可以查看运单照片（随机返回有照片或无照片的结果）
            </div>

            {/* 照片列表模态框 */}
            {photoIsModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold">运单照片</h3>
                            <button 
                                onClick={() => setPhotoIsModal(false)}
                                className="text-gray-500 hover:text-gray-700"
                            >
                                <i className="fas fa-times"></i>
                            </button>
                        </div>
                        <div className="grid grid-cols-2 gap-4 mb-6 overflow-y-auto max-h-[500px]">
                            {photoList.map((item: any, index: number) => (
                                <img 
                                    src={item.image}  
                                    key={item.id}  
                                    className="w-full h-36 object-contain rounded-lg cursor-pointer hover:opacity-80 transition-opacity" 
                                    onClick={() => handlePhotoClick(index)}
                                    alt={`运单照片 ${index + 1}`}
                                />
                            ))}
                        </div>
                    </div>
                </div>
            )}

            {/* 照片查看器 */}
            <PhotoViewer
                photos={photoList}
                isOpen={photoViewerModal}
                onClose={handleClosePhotoViewer}
                initialIndex={selectedPhotoIndex}
            />
        </div>
    )
}
