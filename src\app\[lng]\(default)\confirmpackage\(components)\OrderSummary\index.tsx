'use client';

import React, { useEffect, useState, useCallback } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Checkbox, Modal, Input, App, InputNumber } from 'antd';
import { Api } from '@/request/api';
import message from '@/components/CustomMessage';
import Button from '@/components/Button';
import { formatCurrency } from '@/utils/currency';
interface Product {
  id: number;
  goodsimg: string;
  goodsname: string;
  skuname: string;
  goodsprice: string;
  goodsnum: number;
  [key: string]: any;
}
interface FeeInfo {
  freightdiscount:number,
  freight:number,
  oldfreight:number
}
export default function OrderSummary({ 
  couponIsOpen, 
  dict, 
  products = [],
  localkey,
  totalProductPrice = 0,
  shippingFee = 0,
  order_goods_ids = [],
  type = 'sendorder',
  shippingFeeInfo
}: { 
  couponIsOpen: boolean, 
  dict: any,
  products: Product[],
  localkey: string,
  totalProductPrice: number,
  shippingFee?: number,
  order_goods_ids?: number[],
  type: string,
  shippingFeeInfo:FeeInfo
}) {
  console.log('products', products);
  const searchParams = useSearchParams();
  const router = useRouter();
  // const [isCouponModalOpen, setIsCouponModalOpen] = useState(false);
  const [couponCode, setCouponCode] = useState('');
  const [couponList, setCouponList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedCouponId, setSelectedCouponId] = useState<number | null>(null);
  const [selectedCoupon, setSelectedCoupon] = useState<CouponItem | null>(null);
  const [serverList, setServerList] = useState<any>({});
  const [selectedServices, setSelectedServices] = useState<Record<string, boolean>>({});
  const [photoCount, setPhotoCount] = useState<number>(1);
  const [photoRemarks, setPhotoRemarks] = useState<string[]>(['']);
  const [submitting, setSubmitting] = useState(false);
  const [config, setConfig] = useState({ sendneedcheck: 1 });

  // 从localStorage恢复服务选择状态
  const loadServiceStateFromStorage = useCallback(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedState = localStorage.getItem('packageServiceState');
        if (savedState) {
          const parsedState = JSON.parse(savedState);
          console.log('恢复预演包裹附加服务选择状态:', parsedState);
          setSelectedServices(parsedState.selectedServices || {});
          setPhotoCount(parsedState.photoCount || 1);
          setPhotoRemarks(parsedState.photoRemarks || ['']);
          return parsedState;
        }
      } catch (error) {
        console.error('恢复预演包裹附加服务状态失败:', error);
      }
    }
    return null;
  }, []);

  // 保存服务选择状态到localStorage
  const saveServiceStateToStorage = useCallback((services: Record<string, boolean>, count: number, remarks: string[]) => {
    if (typeof window !== 'undefined') {
      try {
        const stateToSave = {
          selectedServices: services,
          photoCount: count,
          photoRemarks: remarks
        };
        localStorage.setItem('packageServiceState', JSON.stringify(stateToSave));
        console.log('保存预演包裹附加服务选择状态:', stateToSave);
      } catch (error) {
        console.error('保存预演包裹附加服务状态失败:', error);
      }
    }
  }, []);

  // 清理保存的服务状态
  const clearServiceState = useCallback(() => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem('packageServiceState');
        console.log('已清理预演包裹附加服务选择状态');
      } catch (error) {
        console.error('清理预演包裹附加服务状态失败:', error);
      }
    }
  }, []);
  // 订单服务费开关状态 - confirmpackage页面始终为true
  const [orderServerFeeEnabled, setOrderServerFeeEnabled] = useState(true);
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
  const serverFee =products.length ? products.reduce((sum, item) => sum + item.serverfee, 0): 0
  interface CouponItem {
    id: number;
    money: string;
    status: string;
    status_text: string;
    endtime_text: string | number;
    type_text: string;
    usetype_text: string;
    coupon_rule: {
      name: string;
      code: string;
    };
    
  }
  // 获取站点配置信息
  const getSiteConfig = () => {
    try {
      if (typeof window !== 'undefined') {
        const siteData = localStorage.getItem('siteData');
        if (siteData) {
          const config = JSON.parse(siteData);
          return config;
        }
      }
    } catch (error) {
      console.error('Failed to parse siteData:', error);
    }
    return null;
  };

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const res = await Api.getConfigList();
        if(res.success){
          setConfig({sendneedcheck: Number(res.data.site.sendneedcheck)})
        }
      } catch (error) {
        console.error('Failed to load config:', error);
      }
    };
    fetchConfig();
  }, []);

  // 初始化订单服务费开关状态 - confirmpackage页面始终启用服务费
  useEffect(() => {
    // confirmpackage页面始终启用订单服务费，不受配置影响
    setOrderServerFeeEnabled(true);
  }, []);

  // 确保货币信息在组件挂载时正确初始化
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // 检查是否有货币符号和汇率，如果没有则尝试从选择的货币设置默认值
      const currencySymbol = localStorage.getItem('currencySymbol');
      const exchangeRate = localStorage.getItem('currentExchangeRate');
      const selectedCurrency = localStorage.getItem('selectedCurrency');

      if (!currencySymbol && selectedCurrency) {
        localStorage.setItem('currencySymbol', selectedCurrency);
      }
      if (!exchangeRate || exchangeRate === 'NaN') {
        localStorage.setItem('currentExchangeRate', '1');
      }
    }
  }, []);
  // 获取 cart_ids
  const cart_ids = searchParams.get('cart_ids');
  const cart_ids_list = cart_ids ? cart_ids.split(',') : [];

  // 不再计算商品总价，而是直接从父组件接收
  // 移除 subtotal 计算
  
  const discount = 0.00;
  // 使用传入的国际运输费用
  const freight = shippingFee;
  const insurance = 0.00;
  const coupon = selectedCoupon ? parseFloat(selectedCoupon.money) : 0.00;

  // 计算附加服务费用
  const serviceFee = Object.entries(serverList).reduce((total, [key, service]: [string, any]) => {
    if (key === 'photo') {
      return total + (selectedServices[key] ? parseFloat(service.value) * photoCount : 0);
    }else if(key == 'insurance'){
      return total + (selectedServices[key] ? parseFloat(service.value) * totalProductPrice : 0);
    }
    return total + (selectedServices[key] ? parseFloat(service.value) : 0);
  }, 0);

  // 总价只包括运费和附加服务费用（不包括商品价格）- 根据开关决定是否包含订单服务费
  const serverFeeToAdd = orderServerFeeEnabled ? serverFee : 0;
  const total = serviceFee + freight + insurance + serverFeeToAdd;
  const payableTotal = Math.max(0, total - coupon - discount);

  // 获取优惠券列表
  const fetchCouponList = async () => {
    try {
      setLoading(true)
      const { data } = await Api.getCouponList()
      // console.log('优惠券列表', data.data)
      setCouponList(data.data || [])
    } catch (error) {
      message.error('获取优惠券列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 打开弹窗时获取优惠券列表
  // useEffect(() => {
  //   if (isCouponModalOpen) {
  //     fetchCouponList()
  //   }
  // }, [isCouponModalOpen])

  const handleActivateCoupon = async () => {
    const res = await Api.couponActivate({ code: couponCode })
    if (res.success) {
      message.success('优惠券使用成功')
      fetchCouponList()
    } else {
      message.error('优惠券激活失败！')
    }

  }

  // 选择优惠券
  // const handleSelectCoupon = (coupon: CouponItem) => {
  //   setSelectedCouponId(selectedCouponId === coupon.id ? null : coupon.id);
  //   setSelectedCoupon(selectedCouponId === coupon.id ? null : coupon);
  //   setIsCouponModalOpen(false);
  //   message.success('优惠券使用成功');
  // }

  const handleSubmitOrder = async () => {
    // 首先验证条款同意状态
    const agreementCheckbox = document.getElementById('agreement') as HTMLInputElement;
    if (!agreementCheckbox || !agreementCheckbox.checked) {
      message.error(dict?.confirm?.order?.summary?.agreeTermsFirst || '请先阅读并同意服务条款');
      return;
    }

    setSubmitting(true);
    // 校验照片备注
    // if (selectedServices['photo'] && photoCount > 0) {
    //   const emptyRemarks = photoRemarks.some(remark => !remark.trim());
    //   if (emptyRemarks) {
    //     message.error('请填写所有照片的备注信息');
    //     setSubmitting(false);
    //     return;
    //   }
    // }

    // 处理服务数据
    const server: any = {};
    
    // 处理照片服务
    if (selectedServices['photo'] && photoCount > 0) {
      server.photo = {};
      // 为每个商品ID创建照片服务配置
      order_goods_ids.forEach(id => {
        server.photo[id] = {
          num: photoCount,
          remark: JSON.stringify(photoRemarks)
        };
      });
    }

    // 处理其他服务
    Object.entries(selectedServices).forEach(([key, selected]) => {
      if (selected && key !== 'photo') {
        if (!server[key]) {
          server[key] = [];
        }
        // 添加所有商品ID到服务中
        order_goods_ids.forEach(id => {
          server[key].push(Number(id));
        });
      }
    });

    try {
      const addressId = searchParams.get('address_id');
      const templateId = searchParams.get('template_id');
      
      if (!addressId || !templateId) {
        message.error('请选择收货地址和运输方式');
        setSubmitting(false);
        return;
      }
      
      const params = {
        template_id: Number(templateId),
        address_id: Number(addressId),
        order_goods_ids: isTp5?order_goods_ids: order_goods_ids.join(','),
        server
      }
      const res = await Api.toPackage(params);
      if (res.success) {
        // 订单提交成功，清理保存的附加服务状态
        clearServiceState();

        message.success('订单提交成功');
        let goUrl = `/pay?order_id=${res.data?.id|| res.data}&type=package`
        if(config.sendneedcheck){
          goUrl ='/dashboard/packages'
        }
        router.push(goUrl);
        window.localStorage.removeItem(localkey);
      } else {
        message.error('提交订单失败!');
        setSubmitting(false);
      }
    } catch (error) {
      console.error('提交订单出错', error);
      message.error('提交订单出错');
      setSubmitting(false);
    }
  }

  // 获取附加服务列表
  const fetchServerList = async () => {
    try {
      const res = await Api.getServerList('sendorder')
      if (res.success) {
        setServerList(res.data)
      }
    } catch (error) {
      message.error('获取附加服务失败')
    }
  }

  useEffect(() => {
    fetchServerList()
    // 对于新的提交包裹页面，清理之前保存的状态，确保默认不选中任何服务
    // 只有在用户明确选择后才保存状态
    if (typeof window !== 'undefined') {
      // 检查是否是从包裹列表页面跳转过来的新提交
      const isNewSubmission = !sessionStorage.getItem('confirmpackage_visited');
      if (isNewSubmission) {
        // 清理之前的状态，确保默认不选中
        localStorage.removeItem('packageServiceState');
        sessionStorage.setItem('confirmpackage_visited', 'true');
        console.log('新的提交包裹页面，已清理附加服务状态');
      } else {
        // 恢复之前保存的状态（用户在当前会话中的选择）
        loadServiceStateFromStorage();
      }
    }

    // 页面卸载时清理sessionStorage标记，以便下次访问时能正确识别为新提交
    return () => {
      if (typeof window !== 'undefined') {
        sessionStorage.removeItem('confirmpackage_visited');
      }
    };
  }, [loadServiceStateFromStorage])

  // 处理服务选择
  const handleServiceSelect = (key: string) => {
    if (key === 'photo') {
      const newSelectedServices = {
        ...selectedServices,
        [key]: !selectedServices[key]
      };
      setSelectedServices(newSelectedServices);

      let newPhotoCount = photoCount;
      let newPhotoRemarks = photoRemarks;

      if (!selectedServices[key]) {
        newPhotoCount = 1;
        newPhotoRemarks = [''];
        setPhotoCount(newPhotoCount);
        setPhotoRemarks(newPhotoRemarks);
      } else {
        newPhotoCount = 0;
        newPhotoRemarks = [''];
        setPhotoCount(newPhotoCount);
        setPhotoRemarks(newPhotoRemarks);
      }

      // 保存状态
      saveServiceStateToStorage(newSelectedServices, newPhotoCount, newPhotoRemarks);
    } else {
      const newSelectedServices = {
        ...selectedServices,
        [key]: !selectedServices[key]
      };
      setSelectedServices(newSelectedServices);

      // 保存状态
      saveServiceStateToStorage(newSelectedServices, photoCount, photoRemarks);
    }
  };

  const handlePhotoCountChange = (count: number) => {
    setPhotoCount(count);
    setPhotoRemarks(prev => {
      const newRemarks = [...prev];
      if (count > prev.length) {
        // 如果新增照片，添加空备注
        newRemarks.push(...Array(count - prev.length).fill(''));
      } else {
        // 如果减少照片，截取数组
        newRemarks.length = count;
      }

      // 保存状态
      saveServiceStateToStorage(selectedServices, count, newRemarks);

      return newRemarks;
    });
  };

  const handlePhotoRemarkChange = (index: number, remark: string) => {
    const newRemarks = [...photoRemarks];
    newRemarks[index] = remark;
    setPhotoRemarks(newRemarks);

    // 保存状态
    saveServiceStateToStorage(selectedServices, photoCount, newRemarks);
  };

  return (
    <div className="bg-white p-4 rounded-lg sticky top-40">
      <h2 className="text-lg font-medium mb-4">{dict.confirm.sendorder.sendorderTitle}</h2>

      <div className="space-y-3">
        {/* 删除零售价显示 */}
        
        {shippingFeeInfo?.freightdiscount  !== 100 && (
          <div className="flex justify-between items-center text-[var(--base-color)]">
            <span>{dict.confirm.sendorder.discount_rate}</span>
            <span>{shippingFeeInfo?.freightdiscount}%</span>
          </div>
        )}
        <div className="flex justify-between items-center">
          <span>{dict.confirm.order.summary.internationalFee}</span>
          <span>{formatCurrency(freight).formatValue}</span>
        </div>
        {orderServerFeeEnabled && (
          <div className="flex justify-between items-center">
            <span>{dict.confirm.order.summary.serviceFee}</span>
            <span>{formatCurrency(serverFee).formatValue}</span>
          </div>
        )}
        {coupon > 0 && (
          <div className="flex justify-between items-center text-[var(--base-color)]">
            <span>{dict.confirm.order.summary.coupon}</span>
            <span>- {formatCurrency(coupon).formatValue}</span>
          </div>
        )}

        {/* 附加服务列表 */}
        <div className="py-2 border-t border-gray-100">
          <div className="text-sm font-medium mb-2">{dict.confirm.order.summary.extraService}</div>
          <div className="space-y-2">
            {Object.entries(serverList).map(([key, service]: [string, any]) => (
              <div 
                key={key} 
                className="flex justify-between items-center text-sm cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors"
                onClick={() => handleServiceSelect(key)}
              >
                <div className="flex items-center gap-2">
                  <Checkbox 
                    checked={selectedServices[key] || false}
                  />
                  <span>{dict.confirm.server[key]}</span>
                </div>
                <span>{formatCurrency(parseFloat(service.name =='insurance' ? (service.value * totalProductPrice) : service.value)).formatValue}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="mt-4 pt-4 border-t border-gray-100">
        <div className="flex justify-between items-center">
          <div className="text-base font-medium">{dict.confirm.order.summary.totalFee}</div>
          <div className="text-xl font-bold">{formatCurrency(total).formatValue}</div>
        </div>
        {
          shippingFeeInfo?.freightdiscount != 100 && 
          <div className="flex justify-between items-center">
          <div className='text-sm text-orange-400'>
            {dict.confirm.sendorder.saved}
          </div>
          <div className="text-sm text-orange-400">{formatCurrency(shippingFeeInfo?.oldfreight - shippingFeeInfo?.freight).formatValue}</div>
          </div>
        }
        <div className="text-xs text-gray-400 mt-1">
          <span>{dict.confirm.order.summary.includeShipping}: {formatCurrency(freight).formatValue}</span>
        </div>
        <div className="text-xs text-gray-400">
          <span>{dict.confirm.order.summary.includeService}: {formatCurrency(serviceFee).formatValue}</span>
        </div>
      </div>

      {/* 服务协议与链接 */}
      <div className="flex flex-wrap gap-2 mt-3 text-xs text-gray-500">
        <a href="#" className="text-gray-500">{dict.confirm.order.summary.returnPolicy}</a>
        <a href="#" className="text-gray-500">{dict.confirm.order.summary.terms}</a>
        <a href="#" className="text-gray-500">{dict.confirm.order.summary.privacy}</a>
        <a href="#" className="text-gray-500">{dict.confirm.order.summary.faq}</a>
      </div>
      {/* 提交订单区域 */}
      <div className="mt-4">
        <div className="flex items-center mb-3">
          <Checkbox id="agreement" className="mr-2">
            <span className="text-sm">{dict.confirm.order.summary.agreement}</span>
          </Checkbox>
        </div>
        <Button
          className="w-full mb-5 bg-[var(--base-color)] text-white py-3 rounded-md font-medium hover:bg-[var(--base-color-hover)] transition-colors disabled:opacity-70"
          onClick={handleSubmitOrder}
          loading={submitting}
          size="large"
          variant="solid"
          color='primary'
        >
        {dict.confirm.order.summary.submit}
        </Button>
        <div className="bg-gray-100 p-3 rounded-md mb-4">
          <div className="text-sm font-medium">{dict.confirm.order.summary.tips.title}</div>
          <div className="text-xs text-gray-500">{dict.confirm.order.summary.tips.content}</div>
        </div>
      </div>
    </div>
  )
}