'use client';

import { useState, useEffect } from 'react';
import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import type { Locale } from '@/config';
import { getDictionary } from '@/dictionaries';
import { md5 } from 'js-md5';
import { Api } from '@/request/api';
import Cookies from 'js-cookie';
import { EyeOutlined, EyeInvisibleOutlined } from "@ant-design/icons";
import  BackgroundImg  from '../(component)/BackgroundImg';
import Toast from '@/components/Toast';
import Modal from '@/components/Modal';
import { Input, Button } from 'antd';
import ToastHelper from '@/utils/toastHelper';
export default function LoginPage({
  params,
}: {
  params: Promise<{ lng: Locale }>;
}) {
  // 在Next.js 14中，需要使用React.use()来解包params
  const resolvedParams = React.use(params);
  const { lng } = resolvedParams;

  return (
    <ClientLoginPage lng={lng} />
  );
}

function ClientLoginPage({ lng }: { lng: Locale }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showPwd, setShowPwd] = useState(false);
  const [error, setError] = useState('');
  const [dict, setDict] = useState<any>({});
  const [config, setConfig] = useState<any>({logo:'',name:'', mail_verify_type: '0'});
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [sendingCode, setSendingCode] = useState(false);

  useEffect(() => {
    async function loadDictionary() {
      try {
        const dictionary = await getDictionary(lng);
        setDict(dictionary);
      } catch (err) {
        console.error('加载字典失败', err);
      }
    }

    loadDictionary();
  }, [lng, searchParams]);

  useEffect(()=>{
    const getConfig = async () => {
      try {
          const res =  await Api.getConfigList()
          if (res?.success && res?.data?.site) {
            let data =  res.data.site
            setConfig({
              logo: data?.logo || '',
              name: data?.name || '',
              mail_verify_type: data?.mail_verify_type || '0'
            })
          }
        } catch (e) {
          console.error('Failed to load config:', e)
        }
    }
    getConfig()
  },[])


  // 发送邮箱验证码
  const handleSendCode = async () => {
    if (!email) {
      setError(dict?.login?.emailRequired || '请输入邮箱');
      return;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setError(dict?.login?.invalidEmail || '请输入有效的邮箱地址');
      return;
    }

    setSendingCode(true);
    setError('');

    try {
      const response = await Api.sendEmailCode({
        email,
        event: 'login' // 登录时使用login事件类型
      });

      if (response?.success) {
        startCountdown();
        Toast.success(dict?.login?.codeSent || '验证码已发送');
      } else {
        setError(response?.msg || dict?.login?.sendCodeError || '发送验证码失败，请稍后重试');
      }
    } catch (err) {
      console.error('Failed to send verification code', err);
      setError(err instanceof Error ? err.message : dict?.login?.sendCodeError || '发送验证码失败，请稍后重试');
    } finally {
      setSendingCode(false);
    }
  };

  // 倒计时功能
  const startCountdown = () => {
    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // 验证码验证并登录
  const handleVerifyAndLogin = async () => {
    if (!verificationCode) {
      setError(dict?.login?.codeRequired || '请输入邮箱验证码');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // 构建登录参数
      const loginParams: any = {
        account: email,
        password: md5(password),
        is_remember: rememberMe ? 1 : 0,
        captcha: verificationCode
      };

      const response = await Api.login(loginParams);

      if (response?.success && response?.data?.userinfo?.token) {
        localStorage.setItem('info', JSON.stringify(response));

        // 根据后端类型设置正确的token cookie名称
        const tokenName = process.env.NEXT_PUBLIC_BACKEND_TYPE === '6' ? 'access_token' : 'token';
        Cookies.set(tokenName, response.data.userinfo.token, { expires: 30, path: '/' });

        // 处理未登录时添加购物车商品
        handleLoginSuccess();

        // 关闭弹窗
        setShowVerificationModal(false);

        // 获取callback参数，如果存在则跳转到原始页面，否则跳转到首页
        const callback = searchParams.get('callback');

        if (callback) {
          // 确保callback是一个有效的内部路径
          if (callback.startsWith('/')) {
            router.push(callback);
          } else {
            router.push(`/${lng}`);
          }
        } else {
          router.push(`/${lng}`);
        }
      } else {
        // 更详细的错误处理
        const errorMessage = response?.msg || dict?.login?.loginFailed || '登录失败，请检查账号密码';
        setError(errorMessage);
        console.error('Login failed:', response);
      }
    } catch (err) {
      console.error('Login failed:', err);
      setError(err instanceof Error ? err.message : dict?.login?.loginFailed || '登录失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // 验证邮箱格式
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
    if (!emailRegex.test(email)) {
      setError(dict?.login?.invalidEmail || '请输入有效的邮箱地址');
      setLoading(false);
      return;
    }

    // 暂时注释掉验证码校验 - 如果需要邮箱验证码但还没有验证码，先显示验证码弹窗
    // if (config.mail_verify_type === '2' && !verificationCode) {
    //   setLoading(false);
    //   setShowVerificationModal(true);
    //   handleSendCode(); // 自动发送验证码
    //   return;
    // }

    // 构建登录参数
    const loginParams: any = {
      account: email,
      password: md5(password),
      is_remember: rememberMe ? 1 : 0
    };

    // 暂时注释掉验证码校验 - 如果需要验证码，添加到参数中
    // if (config.mail_verify_type === '2') {
    //   loginParams.captcha = verificationCode;
    // }

    const response = await Api.login(loginParams);

    if (response?.success && response?.data?.userinfo?.token) {
      localStorage.setItem('info', JSON.stringify(response));

      // 根据后端类型设置正确的token cookie名称
      const tokenName = process.env.NEXT_PUBLIC_BACKEND_TYPE === '6' ? 'access_token' : 'token';
      Cookies.set(tokenName, response.data.userinfo.token, { expires: 30, path: '/' });

      // 处理未登录时添加购物车商品
      handleLoginSuccess()
      // 获取callback参数，如果存在则跳转到原始页面，否则跳转到首页
      const callback = searchParams.get('callback');

      if (callback) {
        // 确保callback是一个有效的内部路径
        if (callback.startsWith('/')) {
          router.push(callback);
        } else {
          router.push(`/${lng}`);
        }
      } else {
        router.push(`/${lng}`);
      }
    } else {
      // 检查是否需要邮箱验证 - 检查两种可能的响应结构
      const errorCode = response?.code || response?.data?.code;
      if (errorCode === 15024) {
        // 需要邮箱验证，显示验证码弹窗
        setShowVerificationModal(true);
        handleSendCode(); // 自动发送验证码
      } else {
        // 更详细的错误处理
        const errorMessage = response?.msg || response?.data?.msg || dict?.login?.loginFailed || '登录失败，请检查账号密码';
        setError(errorMessage);
        console.error('Login failed:', response);
      }
    }
    setLoading(false);

  };
  // 在用户登录成功后的回调中
  const handleLoginSuccess = async () => {
      // 检查本地是否有存储的购物车数据
      const localCartItemsStr = localStorage.getItem('localCartItems');
      const localCartItems = localCartItemsStr ? JSON.parse(localCartItemsStr) : [];

      if (localCartItems.length > 0) {
          const results = [];
          const failedItems = [];

          // 逐个添加商品，记录成功和失败的情况
          for (const item of localCartItems) {
              try {
                  const result = await Api.addCart(item);
                  if (result.success) {
                      results.push({ success: true, item });
                  } else {
                      results.push({ success: false, item, error: result.msg || ToastHelper.getLocalizedMessage('add_cart_failed', lng) });
                      failedItems.push(item);
                  }
              } catch (error) {
                  console.error('添加商品到购物车失败:', error);
                  results.push({ success: false, item, error: (error as Error).message || ToastHelper.getLocalizedMessage('network_error', lng) });
                  failedItems.push(item);
              }
          }

          const successCount = results.filter(r => r.success).length;
          const failedCount = failedItems.length;

          // 如果有失败的商品，保留在本地存储中
          if (failedItems.length > 0) {
              localStorage.setItem('localCartItems', JSON.stringify(failedItems));
          } else {
              // 全部成功，清空本地存储
              localStorage.removeItem('localCartItems');
          }

          // 触发购物车更新事件，通知所有相关组件刷新数据
          if (typeof window !== 'undefined') {
              window.dispatchEvent(new CustomEvent('cartUpdated'));
              window.dispatchEvent(new CustomEvent('loginSuccess'));
          }

          // 给用户反馈
          if (successCount > 0 && failedCount === 0) {
              Toast.success(`${successCount}件商品已加入购物车`);
          } else if (successCount > 0 && failedCount > 0) {
              Toast.warning(`${successCount}件商品已加入购物车，${failedCount}件商品添加失败`);
          } else if (failedCount > 0) {
              Toast.error('商品加入购物车失败，请稍后重试');
          }
      }
  };
  const handleThirdLogin = async (name: string) => { 
    let data = {
        state: generateRandomState(),
        jump_url: window.location.origin+ "/"+lng+ '/thirdLogin',
    }
    let loginObj : any = {
      'facebook': Api.facebookLogin,
      'google': Api.googleLogin
    }
    const response = await loginObj[name](data)
    if (response?.success) {
      window.location.href = response.data.url;
    } else {
      Toast.error(response?.msg || "登录失败");
    }
  };
  function generateRandomState() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 16; i++) {
      const randomIndex = Math.floor(Math.random() * chars.length);
      result += chars[randomIndex];
    }
  return result;
}
  // 切换密码可见性逻辑
  const toggleEvent = () => {
    setShowPwd(!showPwd);
  };
  const handleRemember = (e: any) =>{
    setRememberMe(e.target.checked)
    console.log('Remember Me:', e.target.checked);
  }

  return (
    <div className="min-h-screen flex">
      {/* 左侧图片部分 */}
      <div className="hidden lg:block lg:w-1/2 relative">
        <Link href={`/${lng}`} className="absolute top-12 left-12 text-white z-10 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          {dict?.nav?.backToHome || '返回首页'}
        </Link>
        <div className="absolute inset-0 bg-white">
          <BackgroundImg/>
        </div>
      </div>

      {/* 右侧登录表单部分 */}
      <div className="w-full lg:w-1/2 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
         {config?.logo ? (<img src={config?.logo} alt="Logo" className="w-[130px] h-[40px] mx-auto" />):<h1 className="text-4xl font-bold text-[#FF6B00] text-center">onebuy</h1>}
          <div className="text-center">
            <h2 className="mt-6 text-2xl font-bold text-gray-900">{dict?.login?.title || '账号登录'}</h2>
          </div>

          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded">
                {error}
              </div>
            )}

            <div className="space-y-4">
              <div>
                <div className="relative">
                  <input
                    id="email"
                    name="email"
                    type="text"
                    autoComplete="username"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="appearance-none rounded-md relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#FF6B00] focus:border-[#FF6B00] focus:z-10 sm:text-sm"
                    placeholder={dict?.login?.emailPlaceholder || '账号'}
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                  </div>
                </div>
              </div>

              <div>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                   type={showPwd ? "text" : "password"}
                    autoComplete="current-password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="appearance-none rounded-md relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#FF6B00] focus:border-[#FF6B00] focus:z-10 sm:text-sm"
                    placeholder={dict?.login?.passwordPlaceholder || '密码'}
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button type="button" className="text-gray-400 hover:text-gray-500 focus:outline-none" onClick={() => toggleEvent()}>
                      {showPwd ? <EyeInvisibleOutlined  className='text-[18px] text-[#6B7280]' /> :  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                        <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                      </svg>}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => handleRemember(e)}
                  className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                  {dict?.login?.rememberMe || '保持登录'}
                </label>
              </div>
              <div className="text-sm">
                <Link
                  href={(() => {
                    const callback = searchParams.get('callback');
                    return callback ? `/${lng}/forgot?callback=${encodeURIComponent(callback)}` : `/${lng}/forgot`;
                  })()}
                  className="font-medium text-[#FF6B00] hover:text-[#E55A00]"
                >
                  {dict?.login?.forgotPassword || '忘记密码'}
                </Link>
              </div>
            </div>

            <div >
              <button
                type="submit"
                disabled={loading}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-[#FF6B00] hover:bg-[#E55A00] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B00]"
              >
                
                {loading ?'':dict?.login?.loginButton || '登录'}
                {loading ? (
                <span className=" inset-y-0 left-0 flex items-center pl-3">
                  <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </span>
                ) : null}
              </button>
            </div>

            <div className="text-center text-sm">
              <span className="text-gray-500">{dict?.login?.noAccount || '没有账户'}</span>{' '}
              <Link
                href={(() => {
                  const callback = searchParams.get('callback');
                  return callback ? `/${lng}/register?callback=${encodeURIComponent(callback)}` : `/${lng}/register`;
                })()}
                className="font-medium text-[#FF6B00] hover:text-[#E55A00]"
              >
               {dict?.login?.goRegister || '去注册'}
              </Link>
            </div>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">{dict?.login?.orText || '或'}</span>
              </div>
            </div>

            <div className="mt-6 space-y-3">
              <button
                type="button"
                className="w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                onClick={() => handleThirdLogin('facebook')}
              >
                <svg className="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="#4267B2">
                  <path d="M12 2.04C6.5 2.04 2 6.53 2 12.06C2 17.06 5.66 21.21 10.44 21.96V14.96H7.9V12.06H10.44V9.85C10.44 7.34 11.93 5.96 14.22 5.96C15.31 5.96 16.45 6.15 16.45 6.15V8.62H15.19C13.95 8.62 13.56 9.39 13.56 10.18V12.06H16.34L15.89 14.96H13.56V21.96C15.9 21.58 18.03 20.39 19.6 18.57C21.17 16.76 22.02 14.43 22 12.06C22 6.53 17.5 2.04 12 2.04Z" />
                </svg>
                {dict?.login?.facebookLogin || '使用Facebook登录'}
              </button>
              <button
                type="button"
                className="w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                 onClick={() => handleThirdLogin('google')}
              >
                <svg className="h-5 w-5 mr-2" viewBox="0 0 24 24">
                  <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4" />
                  <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853" />
                  <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05" />
                  <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335" />
                </svg>
                {dict?.login?.googleLogin || '使用Google登录'}
              </button>
            </div>
          </div>

          {/* 邮箱验证码弹窗 */}
          <Modal
            title={dict?.login?.codePlaceholder || '邮箱验证码'}
            open={showVerificationModal}
            onCancel={() => {
              setShowVerificationModal(false);
              setVerificationCode('');
              setError('');
            }}
            footer={[
              <Button
                key="cancel"
                onClick={() => {
                  setShowVerificationModal(false);
                  setVerificationCode('');
                  setError('');
                }}
              >
                {dict?.register?.cancel || '取消'}
              </Button>,
              <Button
                key="submit"
                type="primary"
                onClick={handleVerifyAndLogin}
                loading={loading}
              >
                {dict?.login?.loginButton || '登录'}
              </Button>
            ]}
            maskClosable={false}
          >
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded mb-4">
                {error}
              </div>
            )}
            <p className="text-sm text-gray-500 mb-4">
              {dict?.register?.verificationCodeSent || '验证码已发送至您的邮箱'}：{email}
            </p>
            <div className="flex space-x-2">
              <Input
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                placeholder={dict?.login?.codePlaceholder || '邮箱验证码'}
                style={{ flex: 1 }}
                onPressEnter={handleVerifyAndLogin}
              />
              <Button
                type="primary"
                disabled={countdown > 0 || sendingCode}
                onClick={handleSendCode}
                loading={sendingCode}
              >
                {countdown > 0 ? `${countdown}s` : (dict?.login?.sendCode || '发送验证码')}
              </Button>
            </div>
          </Modal>
        </div>
      </div>
    </div>
  );
}