'use client';

import { Tabs, TabsProps } from 'antd';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import React, { ReactNode } from 'react';
import styles from './index.module.css';

interface CustomTabsProps extends TabsProps {
  children?: ReactNode;
  variant?: 'default' | 'orange'; // 新增 variant 属性
}

export default function TabsComponent({ children, variant = 'default', className, ...props }: CustomTabsProps) {
  const tabsClassName = variant === 'orange'
    ? `${styles.orangeTabs} ${className || ''}`.trim()
    : className;

  return (
    <AntdConfigProvider>
      <Tabs {...props} className={tabsClassName}>
        {children&&children}
      </Tabs>
    </AntdConfigProvider>
  );
}