'use client';

import { useEffect, useState, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { locales, localeLabels } from '@/config';
import SearchBar from '@/components/Search';
import { UpperCaseFirst } from '@/utils';
import { Api } from '@/request/api';
import { useCartStore } from '@/store/cartStore';
import { Avatar } from 'antd';
import Cookies from 'js-cookie';
import { isUserLoggedIn } from '@/request/index';
import { getDictionary } from '@/dictionaries';
import type { Locale } from '@/config';
import { prepareImageForNextJs } from '@/utils/imageUtils';

// 静态 Header 组件
const StaticHeader = ({ isScrolled = false, dict = {}, name = '', isScrolledTransparent = false }: { isScrolled: boolean, dict?: any, name?: string, isScrolledTransparent: boolean }) => {
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [navList, setNavList] = useState<any[]>([]);
  const [topOffset, setTopOffset] = useState(0);
  const topImageRef = useRef<HTMLImageElement>(null);
  // 使用 zustand 状态管理获取购物车数量
  const cartStore = useCartStore();
  const { count: cartCount = 0, fetchCartCount } = cartStore || { count: 0, fetchCartCount: () => Promise.resolve() };

  // 从路径中提取语言代码
  const pathSegments = pathname.split('/');
  const currentLocale = pathSegments[1] || 'zh-cn';

  // 动态字典状态
  const [dynamicDict, setDynamicDict] = useState(dict);

  const [currency, setCurrency] = useState('CNY');
  const [showMenu, setShowMenu] = useState(false);
  const [showMoreMenu, setShowMoreMenu] = useState(false);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isConfirm, setIsConfirm] = useState(true);
  const menuRef = useRef<HTMLDivElement>(null);
  const moreMenuRef = useRef<HTMLDivElement>(null);
  const [languageList, setLanguageList] = useState<any[]>([]);
  const [currencyList, setCurrencyList] = useState<any[]>([]);
  const [exchangeRates, setExchangeRates] = useState<Record<string, number>>({});
  const [headAd, setHeadAd] = useState<any>({});
  const [configList, setConfigList] = useState<any>({});

  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'

  const updateTopOffset = () => {
    if (topImageRef.current) {
      setTopOffset(topImageRef.current.offsetHeight);
      document.documentElement.style.setProperty('--banner-top', `${topImageRef.current.offsetHeight}px`);
    }
  };

  useEffect(() => {
    // 组件挂载时立即执行一次
    setTimeout(() => {
      updateTopOffset();
    }, 500);

    // 监听 DOMContentLoaded 和 load 事件
    window.addEventListener('load', updateTopOffset);
    window.addEventListener('resize', updateTopOffset);
    window.addEventListener('scroll', updateTopOffset);

    return () => {
      window.removeEventListener('resize', updateTopOffset);
      window.removeEventListener('load', updateTopOffset);
      window.removeEventListener('scroll', updateTopOffset);
    };
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
      }
      if (moreMenuRef.current && !moreMenuRef.current.contains(event.target as Node)) {
        setShowMoreMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 动态更新字典数据
  useEffect(() => {
    const updateDictionary = async () => {
      try {
        // 确保currentLocale是有效的语言代码
        const validLocale = locales.includes(currentLocale) ? currentLocale as Locale : 'zh-cn';
        const newDict = await getDictionary(validLocale);
        setDynamicDict(newDict);
      } catch (error) {
        console.error('Failed to load dictionary in Header:', error);
        // 如果加载失败，保持使用传入的dict
        setDynamicDict(dict);
      }
    };

    // 添加延迟确保路径已经更新
    const timer = setTimeout(updateDictionary, 100);
    return () => clearTimeout(timer);
  }, [currentLocale, dict, pathname]);

  // 获取配置数据
  useEffect(() => {
    const fetchConfigList = async () => {
      try {
        const response = isTp5 ? await Api.getConfigList() : await Api.getConfigList();
        if (response?.success || response) {
          const config = response?.data || response;
          setConfigList(config);
        }
      } catch (error) {
        console.error('Failed to load config in Header:', error);
        // 使用默认配置
        setConfigList({
          site: { name: 'onebuy', logo: null },
          nav_list: [],
          language_list: [],
          currency_list: []
        });
      }
    };

    fetchConfigList();
  }, [isTp5]);

  useEffect(() => {
    const fetchNavList = async () => {
      const response = isTp5? await Api.getNavList({ type: 'top' }):{data: configList.nav_list || [], success: true}
      if (response.success) {
        // const urlMap: Record<string, string> = {
        //   '/index/member/warehouse/index.html': '/transport',
        //   '/index/page/estimate.html': '/estimate',
        //   '/index/page/help.html': '/help',
        //   '/index/page/selfservice.html': '/selfservice',
        //   '/index/page/sizecomparison.html': '/sizecomparison',
        //   '/index/page/followus.html': '/sharepromotion'
        // }
        //  urlMap[item.linkurl]
        const menuItems = response.data.map((item: any) => ({
          label: item.name,
          href: item.linkurl || '#',
          path: item.path,
          target: item.target
        }));
        setNavList(menuItems);
      }
    };

    const fetchLanguageList = async () => {
      const response = isTp5? await Api.getLanguageList(): {data: configList.language_list || [], success: true}
      console.log('Language List Response:', configList);
      if (response.success) {
        setLanguageList(response.data);
      }
    };

    const fetchCurrencyList = async () => {
      const response = isTp5? await Api.getCurrencyList():{data: configList.currency_list || [], success: true}
      if (response.success) {
        // 检查response.data的类型并相应处理
        if (Array.isArray(response.data)) {
          // 如果是数组格式，直接使用
          setCurrencyList(response.data);

          // 仍然提取汇率值用于兼容性
          const rates: Record<string, number> = {};
          response.data.forEach((item: any) => {
            if (item.code && item.value) {
              rates[item.code] = parseFloat(item.value);
            }
          });
          setExchangeRates(rates);

        } else if (typeof response.data === 'object' && response.data !== null) {
          // 如果是对象格式，转换为数组
          const currencyArray = Object.entries(response.data).map(([code, currData]: [string, any]) => ({
            code,
            ...currData
          }));
          setCurrencyList(currencyArray);

          // 提取汇率值用于兼容性
          const rates: Record<string, number> = {};
          Object.entries(response.data).forEach(([code, currData]: [string, any]) => {
            if (currData.value) {
              rates[code] = parseFloat(currData.value);
            }
          });
          setExchangeRates(rates);
        }

        // 直接保存完整的货币数据到本地存储
        if (typeof window !== 'undefined') {
          localStorage.setItem('currencyData', JSON.stringify(response.data));
        }
      }
    };

    // 只有在configList加载完成后才执行这些请求
    if (Object.keys(configList).length > 0) {
      fetchNavList();
      fetchLanguageList();
      fetchCurrencyList();
    }
  }, [configList, isTp5]);

  useEffect(() => {
    if(!isUserLoggedIn()){
       fetchCartCount();
    }
    // 改为直接从API获取用户信息，不再从localStorage获取
    const fetchUserData = async () => {
      setIsLoading(true);
      try {
        const response = await Api.getUserInfo();
        if (response?.success && response?.data) {
          setUserInfo(response.data);
          // 登录状态下获取购物车数量
          fetchCartCount();
        } else {
          setUserInfo(null);
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        setUserInfo(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
    // 从cookie中获取isConfirm的值
    const isConfirmCookie = Cookies.get('isConfirm');
    setIsConfirm(!!isConfirmCookie);
  }, [fetchCartCount]);

  // 监听购物车更新和登录成功事件
  useEffect(() => {
    // 购物车更新事件处理函数
    const handleCartUpdate = () => {
      fetchCartCount();
    };

    // 登录成功事件处理函数
    const handleLoginSuccess = async () => {
      // 重新获取用户信息
      try {
        const response = await Api.getUserInfo();
        if (response?.success && response?.data) {
          setUserInfo(response.data);
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
      // 重新获取购物车数量
      fetchCartCount();
    };

    // 添加自定义事件监听器
    window.addEventListener('cartUpdated', handleCartUpdate);
    window.addEventListener('loginSuccess', handleLoginSuccess);

    // 设置定期检查购物车更新的计时器（每分钟检查一次）
    const intervalId = setInterval(fetchCartCount, 60000);

    // 组件卸载时清理事件监听器和计时器
    return () => {
      window.removeEventListener('cartUpdated', handleCartUpdate);
      window.removeEventListener('loginSuccess', handleLoginSuccess);
      clearInterval(intervalId);
    };
  }, [fetchCartCount]);

  // 从本地存储和cookie中获取用户选择的货币和语言
  useEffect(() => {
    // 安全地检查localStorage，避免SSR时不可用的问题
    if (typeof window === 'undefined') return;

    // 获取保存的货币和语言设置
    const localCurrency = localStorage.getItem('selectedCurrency');
    const cookieCurrency = Cookies.get('currency');

    // 设置货币
    if (localCurrency) {
      setCurrency(localCurrency);
    } else if (cookieCurrency) {
      setCurrency(cookieCurrency);
    }

    // 尝试从localStorage获取汇率信息
    const localExchangeRates = localStorage.getItem('exchangeRates');
    if (localExchangeRates) {
      try {
        const rates = JSON.parse(localExchangeRates);
        setExchangeRates(rates);
      } catch (e) {
        console.error('解析汇率信息失败:', e);
      }
    }
  }, []);
  useEffect(() => {
    // head_01 PC端首页顶部广告
    async function  getHead(){
      let head = { 
        mark: "head_01",
        num: 1,
      }
      const res = await Api.getAdList(head);
      if (res.success) {
        setHeadAd(res.data || {});
      } 
    }
    getHead()
  }, []);


  const handleMenuToggle = () => {
    setShowMenu(!showMenu);
  };

  const handleMoreMenuToggle = () => {
    setShowMoreMenu(!showMoreMenu);
  };

  const handleLanguageChange = (lang: string) => {
    setShowMenu(false);

    // 保存用户选择的语言到cookie和localStorage
    Cookies.set('selectedLanguage', lang, { expires: 30, path: '/' });
    if (typeof window !== 'undefined') {
      localStorage.setItem('selectedLanguage', lang);
    }

    // 获取当前路径和查询参数
    const pathSegments = pathname.split('/');

    // 如果当前路径已经包含语言前缀，则替换它
    if (locales.includes(pathSegments[1])) {
      pathSegments[1] = lang;
    } else {
      // 否则在路径前添加语言前缀
      pathSegments.unshift(lang);
    }

    // 构建新路径，保留所有查询参数
    const newPath = pathSegments.join('/');
    const params = new URLSearchParams(searchParams.toString());
    const finalPath = params.toString() ? `${newPath}?${params.toString()}` : newPath;
    router.push(finalPath);
  };

  const handleCurrencyChange = (curr: string) => {
    setCurrency(curr);

    // 保存用户选择的货币到cookie和localStorage
    Cookies.set('currency', curr, { expires: 30, path: '/' });
    if (typeof window !== 'undefined') {
      localStorage.setItem('selectedCurrency', curr);
    }

    // 从本地存储中获取完整的货币数据
    let currencyDataStr = null;
    if (typeof window !== 'undefined') {
      currencyDataStr = localStorage.getItem('currencyData');
    }
    let selectedCurrencyData = null;
    
    if (currencyDataStr) {
      try {
        const currencyData = JSON.parse(currencyDataStr);
        if (Array.isArray(currencyData)) {
          // 如果是数组格式
          selectedCurrencyData = currencyData.find((item: any) => item.code === curr);
        } else if (typeof currencyData === 'object' && currencyData !== null) {
          // 如果是对象格式
          selectedCurrencyData = currencyData[curr];
        }
      } catch (e) {
        console.error('解析货币数据失败:', e);
      }
    }
    
    // 如果从本地存储找到了数据
    if (selectedCurrencyData && typeof window !== 'undefined') {
      // 保存当前货币的汇率
      if (selectedCurrencyData.value) {
        localStorage.setItem('currentExchangeRate', selectedCurrencyData.value.toString());
      } else if (selectedCurrencyData.rate) {
        localStorage.setItem('currentExchangeRate', selectedCurrencyData.rate.toString());
      }

      // 保存当前货币的符号
      if (selectedCurrencyData.symbol_left) {
        localStorage.setItem('currencySymbol', selectedCurrencyData.symbol_left);
      } else if (selectedCurrencyData.symbol) {
        localStorage.setItem('currencySymbol', selectedCurrencyData.symbol);
      } else {
        // 如果没有符号，使用货币代码
        localStorage.setItem('currencySymbol', curr);
      }

      // 保存当前完整的货币数据
      localStorage.setItem('currentCurrencyData', JSON.stringify(selectedCurrencyData));
    } else if (currencyList.length > 0 && typeof window !== 'undefined') {
      // 如果本地存储没有，但是state中有当前货币列表，从中找出
      const selectedCurrency = currencyList.find((item: any) => item.code === curr);
      if (selectedCurrency) {
        // 保存汇率
        if (selectedCurrency.value) {
          localStorage.setItem('currentExchangeRate', selectedCurrency.value.toString());
        } else if (selectedCurrency.rate) {
          localStorage.setItem('currentExchangeRate', selectedCurrency.rate.toString());
        } else {
          localStorage.setItem('currentExchangeRate', '1');
        }

        // 保存符号
        if (selectedCurrency.symbol_left) {
          localStorage.setItem('currencySymbol', selectedCurrency.symbol_left);
        } else if (selectedCurrency.symbol) {
          localStorage.setItem('currencySymbol', selectedCurrency.symbol);
        } else {
          localStorage.setItem('currencySymbol', curr);
        }

        localStorage.setItem('currentCurrencyData', JSON.stringify(selectedCurrency));
      }
    } else if (exchangeRates[curr] && typeof window !== 'undefined') {
      // 最后的兼容性处理
      localStorage.setItem('currentExchangeRate', exchangeRates[curr].toString());
      localStorage.setItem('currencySymbol', curr);
    } else if (typeof window !== 'undefined') {
      // 最终的默认值处理
      localStorage.setItem('currentExchangeRate', '1');
      localStorage.setItem('currencySymbol', curr);
    }
    
    setShowMenu(false);
    // 刷新页面以应用新货币设置
    window.location.reload();
  };

  const visibleItems = navList.slice(0, 4);
  const dropdownItems = navList.slice(4);

  const isHome = pathname === `/${currentLocale}` || pathname === `/${currentLocale}/`;

  const computedClassName = () => {
    if (isHome && !isScrolledTransparent) {
      return 'fixed bg-transparent backdrop-blur-sm bg-black/20 dark:bg-black/20 text-white dark:text-white top-banner';
    }
    return "sticky top-0 bg-white dark:bg-black text-black dark:text-white shadow-md";
  }

  const logout = async () => {
    try {
      await Api.logout();

      // 清理本地存储的用户信息
      if (typeof window !== 'undefined') {
        localStorage.removeItem('info');
        // 根据后端类型清理对应的token cookie
        const tokenName = process.env.NEXT_PUBLIC_BACKEND_TYPE === '6' ? 'access_token' : 'token';
        Cookies.remove(tokenName, { path: '/' });
      }

      // 重置用户状态
      setUserInfo(null);
      // 重置购物车数量
      fetchCartCount();

      // 跳转到登录页面
      const loginUrl = `/${currentLocale}/login`;
      router.push(loginUrl);

    } catch (error) {
      console.error('退出登录失败:', error);
      // 即使API调用失败，也要清理本地状态并跳转
      setUserInfo(null);
      if (typeof window !== 'undefined') {
        localStorage.removeItem('info');
        const tokenName = process.env.NEXT_PUBLIC_BACKEND_TYPE === '6' ? 'access_token' : 'token';
        Cookies.remove(tokenName, { path: '/' });
      }

      const loginUrl = `/${currentLocale}/login`;
      router.push(loginUrl);
    }
  }

  // 菜单项翻译映射
  const menuTranslations: Record<string, string> = {
    'home': dynamicDict?.nav?.home || '首页',
    'transport': dynamicDict?.nav?.transport || '转运',
    'sharepromotion': dynamicDict?.nav?.alliance || '推广联盟',
    'estimate': dynamicDict?.nav?.shipping || '运费估算',
    'help': dynamicDict?.nav?.help || '帮助中心',
    'selfservice': dynamicDict?.nav?.selfservice || '手工填单',
    'sizecomparison': dynamicDict?.nav?.sizecomparison || '尺码对照'
  };

  return (
    <>
      <div style={{ display: isConfirm ? 'none' : 'block' }} className="w-full">
      {headAd?.data?.length && headAd.data.map((item:any, index:number) => (
        <div key={index}>
          {item.lang && Array.isArray(item.lang) && item.lang.map((langItem: any, langIndex: number) => (
            <div key={langIndex}>
                <Image
                  ref={topImageRef}
                  src={prepareImageForNextJs(langItem.imageurl)}
                  alt="Onebuy"
                  width={2880}
                  height={160}
                  style={{
                      width: `100%`,
                      height: `90px`,
                    }}
                  className="object-cover"
                  priority
                  draggable="false"
                  onLoad={updateTopOffset}
                />
            </div>
          ))}
        </div>
      ))}
    
        <div
          onClick={() => {
            Cookies.set('isConfirm', 'true', { expires: 7, path: '/' });
            setIsConfirm(true)
          }}
          className="absolute text-white top-0 right-0 w-10 h-10 flex items-center justify-center cursor-pointer hover:bg-black/10"
        >
          <span className="text-lg font-medium"> × </span>
        </div>
      </div>
      <header
        className={`z-50 w-full ${computedClassName()}`}
        style={{
          top: !isScrolled && !isConfirm ? `${topOffset}px` : 0,
        }}
      >
        {/* <div className="w-full">
          <Image
            src="/images/home-tips2.png"
            alt="Onebuy"
            width={2880}
            height={80}
            priority
            draggable="false"
          />
        </div> */}
        <div className="container mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center">
            {/* todo logo */}
            <Link href={`/${currentLocale}`}>
              {configList?.site?.logo ? (
                <img src={configList.site.logo} alt="Logo" className="w-[130px] h-[40px] mr-2" />
              ) : (
                <h1 className="text-3xl font-bold text-[var(--base-color)] mr-2">onebuy</h1>
              )}
            {/* <span className="text-[var(--base-color)] text-2xl font-bold">{name}</span> */}
            
            </Link>
            <nav className="ml-8 hidden md:flex items-center">
              {visibleItems.map((item) => {
                // 检查是否为当前路径
                const isCurrentPath = item.href === '/' 
                  ? (pathname === `/${currentLocale}` || pathname === `/${currentLocale}/`) 
                  : pathname.includes(`/${currentLocale}${item.href}`);
                
                return item.href && item.href !== '#' ? (
                  <Link
                    href={item.href}
                    key={item.label}
                    target={item.target}
                    className={`px-3 py-2 min-w-[50px] text-center truncate ${
                      isCurrentPath
                        ? 'text-[var(--base-color)] font-medium'
                        : 'hover:text-[var(--base-color)]'
                    }`}
                  >
                    {menuTranslations[item.path] || item.label}
                  </Link>
                ) : (
                  <span
                    key={item.label}
                    className={`px-3 py-2 min-w-[50px] text-center truncate cursor-default ${
                      isCurrentPath
                        ? 'text-[var(--base-color)] font-medium'
                        : 'text-gray-400'
                    }`}
                  >
                    {menuTranslations[item.path] || item.label}
                  </span>
                );
              })}
              {dropdownItems.length > 0 && (
                <div ref={moreMenuRef} className="relative min-w-[100px]">
                  <button
                    onClick={handleMoreMenuToggle}
                    className={`px-3 py-2 hover:text-[var(--base-color)]  flex items-center`}
                  >
                    {dynamicDict?.nav?.more || '更多'}
                    <span className={`ml-1 transition-transform ${showMoreMenu ? 'rotate-180' : ''}`}>
                      <i className="fas fa-chevron-down"></i>
                    </span>
                  </button>
                  {showMoreMenu && (
                    <div className="absolute top-full left-0 bg-white dark:bg-gray-800 rounded-lg shadow-lg w-[200px] z-50 mt-2 overflow-hidden transform transition-all duration-200 ease-in-out origin-top">
                      <div className="py-1">
                        {dropdownItems.map((item, index) => {
                          // 检查是否为当前路径
                          const isCurrentPath = item.href === '/' 
                            ? (pathname === `/${currentLocale}` || pathname === `/${currentLocale}/`) 
                            : pathname.includes(`/${currentLocale}${item.href}`);
                          
                          return (
                            <div key={item.label}>
                              {item.href && item.href !== '#' ? (
                                <Link
                                  href={item.href}
                                  target={item.target}
                                  className={`block px-4 py-2.5 ${isCurrentPath
                                    ? 'text-[var(--base-color)] font-medium bg-gray-50 dark:bg-gray-700/50'
                                    : 'text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                                  } duration-200 relative group`}
                                >
                                  {menuTranslations[item.path] || item.label}
                                  <span className={`absolute left-0 top-1/2 -translate-y-1/2 w-1 ${
                                    isCurrentPath ? 'h-full' : 'h-0 group-hover:h-full'
                                  } bg-[var(--base-color)] transition-all duration-200`}></span>
                                </Link>
                              ) : (
                                <span
                                  className={`block px-4 py-2.5 cursor-default ${isCurrentPath
                                    ? 'text-[var(--base-color)] font-medium bg-gray-50 dark:bg-gray-700/50'
                                    : 'text-gray-400'
                                  } duration-200 relative`}
                                >
                                  {menuTranslations[item.path] || item.label}
                                </span>
                              )}
                              {index < dropdownItems.length - 1 && (
                                <div className="mx-4 border-t border-gray-100 dark:border-gray-700"></div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </nav>
          </div>
          <div className="flex items-center gap-4">
            {
              <div className={`mr-2 transition-all duration-600 ${isScrolled ? 'opacity-100 visible' : 'opacity-0 invisible'}`}>
                <SearchBar topNav dict={dict} />
              </div>
            }
            <span
              ref={menuRef}
              className={`relative flex items-center hover:text-[var(--base-color)] justify-between duration-300 cursor-pointer min-w-[150px]`}
              onClick={handleMenuToggle}
            >
              <Image
                src="/globe.svg"
                alt={dynamicDict?.languageSwitcher?.selectLanguage || " 语言 "}
                width={20}
                height={20}
                className="mr-1"
                priority
              />
              {currentLocale} / {currency}
              <span className={`ml-1 transition-transform duration-300 ${showMenu ? 'rotate-180' : ''}`}>
                <i className="fas fa-chevron-down"></i>
              </span>
              {showMenu && (
                <div className="absolute top-full right-0 bg-white dark:bg-gray-800 rounded-lg shadow-lg w-[250px] z-50 mt-2 p-4">
                  <h3 className="text-sm mb-2 text-[var(--base-color)]">{dynamicDict?.languageSwitcher?.selectLanguage || " 选择语言 "}</h3>
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    {(
                      languageList.map((item) => (
                        <button
                          key={item.code}
                          onClick={() => handleLanguageChange(item.code)}
                          className={`px-4 py-1.5 rounded text-sm text-center ${currentLocale === item.code
                            ? 'border border-[var(--base-color)] text-[var(--base-color)]'
                            : 'border border-gray-200 text-gray-600 hover:border-gray-300'
                            }`}
                        >
                          {item.name}
                        </button>
                      ))
                    )}
                  </div>
                  <h3 className="text-sm mb-2 text-[var(--base-color)]">{dynamicDict?.languageSwitcher?.selectCurrency || " 选择货币 "}</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {
                      currencyList.map((item) => (
                        <button
                          key={item.code}
                          onClick={() => handleCurrencyChange(item.code)}
                          className={`px-4 py-1.5 rounded text-sm text-center ${currency === item.code
                            ? 'border border-[var(--base-color)] text-[var(--base-color)]'
                            : 'border border-gray-200 text-gray-600 hover:border-gray-300'
                            }`}
                        >
                          {item.code} {item.rate ? `(${item.rate})` : ''}
                        </button>
                      ))
                    }
                  </div>
                </div>
              )}
            </span>
            <span className={`relative hover:text-[var(--base-color)]  duration-300 cursor-pointer cart-icon-wrapper flex flex-wrap items-center`} onClick={() => {
              router.push(`/${currentLocale}/dashboard/cart`);
            }}>
              <i className="fas fa-shopping-cart text-xl mr-2"></i>
              <span className="absolute -top-2 -right-2 bg-[var(--base-color)] text-white rounded-full h-5 w-5 flex items-center justify-center text-xs">{cartCount}</span>
            </span>

            {isLoading ? (
              <div className="w-[35px] h-[35px] " />
            ) : !userInfo ? (
              <>
                <Link href={`/${currentLocale}/login`} className="hover:text-[var(--base-color)] duration-300 cursor-pointer min-w-[40px] text-center truncate">
                  {dynamicDict?.nav?.login || ' 登录 '}
                </Link>
                <Link href={`/${currentLocale}/register`} className="bg-[var(--base-color)] text-white px-4 py-1 rounded-md hover:bg-[#E55A00] duration-300 min-w-[70px] text-center truncate">
                  {dynamicDict?.nav?.register || ' 注册 '}
                </Link>
              </>
            ) : ( 
              <div className="relative group">
                <span className="flex items-center hover:text-[var(--base-color)] duration-300 cursor-pointer">
                  {userInfo?.avatar ? (
                    <Avatar size={35} src={<Image src={prepareImageForNextJs(userInfo.avatar)} alt="avatar" width={200} height={200} />} />
                  ) : (
                    <Avatar style={{ backgroundColor: process.env.NEXT_PUBLIC_BASE_COLOR, color: 'white' }} size={35}>
                      {UpperCaseFirst(userInfo?.nickname?.slice(0,1))}
                    </Avatar>
                  )}
                </span>
                <div className="absolute top-full right-0 bg-white dark:bg-gray-800 rounded-lg shadow-lg w-[120px] z-50 mt-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                  <div className="py-2">
                    <Link href={`/${currentLocale}/dashboard/home`} className="flex items-center text-sm px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 hover:font-bold hover:text-[var(--base-color)] transition-all duration-300">
                      <i className="fas fa-user mr-3 w-4 text-center"></i>
                      <span>{dynamicDict?.nav?.dashboard || '会员中心'}</span>
                    </Link>
                    <Link href={`/${currentLocale}/dashboard/orders`} className="flex items-center text-sm px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 hover:font-bold hover:text-[var(--base-color)] transition-all duration-300">
                      <i className="fas fa-shopping-bag mr-3 w-4 text-center"></i>
                      <span>{dynamicDict?.nav?.orders || '我的订单'}</span>
                    </Link>
                    <Link href={`/${currentLocale}/dashboard/warehouse`} className="flex items-center text-sm px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 hover:font-bold hover:text-[var(--base-color)] transition-all duration-300">
                      <i className="fas fa-warehouse mr-3 w-4 text-center"></i>
                      <span>{dynamicDict?.nav?.warehouse || '我的仓库'}</span>
                    </Link>
                    <Link href={`/${currentLocale}/dashboard/packages`} className="flex items-center text-sm px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 hover:font-bold hover:text-[var(--base-color)] transition-all duration-300">
                      <i className="fas fa-box mr-3 w-4 text-center"></i>
                      <span>{dynamicDict?.nav?.packages || '我的包裹'}</span>
                    </Link>
                    <button onClick={logout} className="flex items-center text-sm px-4 py-2 w-full text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 hover:font-bold hover:text-[var(--base-color)] transition-all duration-300">
                      <i className="fas fa-sign-out-alt mr-3 w-4 text-center"></i>
                      <span>{dynamicDict?.nav?.logout || '退出账号'}</span>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </header>
    </>
  );
};

// 导出带滚动逻辑的 Header 组件
export default function Header({ dict = {} }: { dict?: any }) {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isScrolledTransparent, setIsScrolledTransparent] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const pathname = usePathname();
  const pathSegments = pathname.split('/');
  const currentLocale = pathSegments[1] || 'zh-cn';

  // 简化的 useMount 钩子，替代 useIsClient
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 简化的 useScroll 钩子
  useEffect(() => {
    if (!isMounted) return;

    const handleScroll = () => {
        setIsScrolled(window.scrollY > 10);
        setIsScrolledTransparent(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);

    // 初始化检查
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isMounted, pathname, currentLocale]);


  // 服务端渲染时使用未滚动状态
  if (!isMounted) {
    return <StaticHeader isScrolled={false} dict={dict} name="" isScrolledTransparent={isScrolledTransparent}/>;
  }

  return <StaticHeader isScrolled={isScrolled} dict={dict} name="" isScrolledTransparent={isScrolledTransparent}/>;
}
