/* 橙色底白色文字的标签页样式 */
.orangeTabs .ant-tabs-nav {
  background: linear-gradient(to right, #f97316, #ea580c);
  padding: 8px 16px;
  border-radius: 8px 8px 0 0;
  margin-bottom: 0;
}

.orangeTabs .ant-tabs-nav::before {
  display: none; /* 隐藏默认的底部边框 */
}

.orangeTabs .ant-tabs-nav-wrap {
  padding: 0;
}

.orangeTabs .ant-tabs-tab {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8) !important;
  padding: 12px 20px;
  font-size: 16px;
  border-radius: 8px 8px 0 0;
  margin-right: 4px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-bottom: none;
  transition: all 0.3s ease;
}

.orangeTabs .ant-tabs-tab:hover {
  color: white !important;
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.orangeTabs .ant-tabs-tab-active {
  color: #f97316 !important;
  font-weight: 600;
  background: white !important;
  border-color: white !important;
  position: relative;
  z-index: 1;
}

.orangeTabs .ant-tabs-ink-bar {
  display: none; /* 隐藏默认的下划线，使用自定义样式 */
}

.orangeTabs .ant-tabs-content-holder {
  padding: 0;
  background: white;
}

.orangeTabs .ant-tabs-extra-content {
  margin-left: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .orangeTabs .ant-tabs-tab {
    padding: 8px 16px;
    font-size: 14px;
  }
  
  .orangeTabs .ant-tabs-nav {
    padding: 6px 12px;
  }
  
  .orangeTabs .ant-tabs-extra-content {
    margin-left: 12px;
  }
}
