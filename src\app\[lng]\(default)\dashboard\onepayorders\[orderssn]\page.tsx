'use client'

import React, { useState, useEffect } from 'react'
import { Card, Descriptions, Table, Modal, Spin } from 'antd'
import { ArrowLeftOutlined, StopOutlined, CheckOutlined } from '@ant-design/icons'
import { Api } from '@/request/api'
import { formatCurrency } from '@/utils/currency'
import { useParams, useRouter } from 'next/navigation'
import { getDictionary } from '@/dictionaries'
import Image from 'next/image'
import Button from '@/components/Button'
import Loading from '@/components/Loading'
import AntdConfigProvider from '@/components/AntdConfigProvider'
import message from '@/components/CustomMessage'

interface OnePayOrderDetail {
  id: number;
  orderssn: string;
  user_id: number;
  isonepay: number;
  status_id: number;
  status_text: string;
  currencycode: string;
  createtime: string;
  updatetime: string;
  totalmoney: number;
  shop_totalmoney: number;
  otherfee: number;
  order: Array<{
    id: number;
    ordersn: string;
    goodsseller: string;
    sellerurl: string;
    totalmoney: number;
    goods: Array<{
      id: number;
      goodsname: string;
      goodsimg: string;
      goodsprice: string;
      goodsnum: number;
      skuname: string;
      totalmoney: string;
    }>;
  }>;
}

export default function OnePayOrderDetailPage() {
  const [orderDetail, setOrderDetail] = useState<OnePayOrderDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [dict, setDict] = useState<any>(null);
  const { lng, orderssn } = useParams();
  const router = useRouter();

  // Load dictionary
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };
    fetchDictionary();
  }, [lng]);

  // Fetch order detail
  const fetchOrderDetail = async () => {
    try {
      setLoading(true);
      const response = await Api.getOnePayOrderDetail({ orderssn: orderssn as string });
      if (response.success) {
        setOrderDetail(response.data);
      } else {
        message.error('Failed to fetch order details');
      }
    } catch (error) {
      console.error('Error fetching order details:', error);
      message.error('Failed to fetch order details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (orderssn) {
      fetchOrderDetail();
    }
  }, [orderssn]);

  // Handle cancel order
  const handleCancelOrder = () => {
    Modal.confirm({
      title: 'Cancel Order',
      content: 'Are you sure you want to cancel this one-time payment order?',
      okText: 'Yes, Cancel',
      cancelText: 'No',
      okType: 'danger',
      onOk: async () => {
        try {
          if (!orderDetail) return;
          const response = await Api.cancelOnePayOrder({ orders_id: orderDetail.id });
          if (response.success) {
            message.success('Order cancelled successfully');
            fetchOrderDetail(); // 刷新订单详情以显示最新状态
          } else {
            message.error(response.msg || 'Failed to cancel order');
          }
        } catch (error) {
          message.error('Failed to cancel order');
        }
      }
    });
  };

  // Handle confirm receipt
  const handleConfirmReceipt = () => {
    if (!orderDetail) return;
    
    Modal.confirm({
      title: 'Confirm Receipt',
      content: 'Are you sure you want to confirm receipt of this order?',
      okText: 'Yes, Confirm',
      cancelText: 'No',
      onOk: async () => {
        try {
          const response = await Api.confirmOnePayOrderReceipt({ 
            orders_id: orderDetail.id.toString() 
          });
          if (response.success) {
            message.success('Receipt confirmed successfully');
            fetchOrderDetail();
          } else {
            message.error(response.msg || 'Failed to confirm receipt');
          }
        } catch (error) {
          message.error('Failed to confirm receipt');
        }
      }
    });
  };

  // Get status color
  const getStatusColor = (statusId: number) => {
    switch (statusId) {
      case 0: return 'orange'; // Pending
      case 1: return 'green';  // Paid
      case 2: return 'blue';   // Processing
      case 3: return 'purple'; // Shipped
      case 4: return 'green';  // Completed
      case -1: return 'red';   // Cancelled
      default: return 'default';
    }
  };

  // Product table columns
  const productColumns = [
    {
      title: 'Product',
      dataIndex: 'goodsname',
      key: 'goodsname',
      render: (text: string, record: any) => (
        <div className="flex items-center space-x-3">
          <div className="w-16 h-16 relative">
            <Image
              src={record.goodsimg.startsWith('http') ? record.goodsimg : `https:${record.goodsimg}`}
              alt={text}
              fill
              className="object-cover rounded"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = '/images/default.jpg';
              }}
            />
          </div>
          <div>
            <div className="font-medium text-sm line-clamp-2">{text}</div>
            {record.skuname && (
              <div className="text-xs text-gray-500 mt-1">{record.skuname}</div>
            )}
          </div>
        </div>
      ),
    },
    {
      title: 'Price',
      dataIndex: 'goodsprice',
      key: 'goodsprice',
      render: (price: string) => formatCurrency(parseFloat(price)).formatValue,
    },
    {
      title: 'Quantity',
      dataIndex: 'goodsnum',
      key: 'goodsnum',
    },
    {
      title: 'Total',
      dataIndex: 'totalmoney',
      key: 'totalmoney',
      render: (total: string) => (
        <span className="font-medium text-[#FF6000]">
          {formatCurrency(parseFloat(total)).formatValue}
        </span>
      ),
    },
  ];

  if (!dict) {
    return <div className="flex justify-center items-center h-64"><Spin size="large" /></div>;
  }

  if (loading) {
    return <Loading height="400px" />;
  }

  if (!orderDetail) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Order Not Found</h2>
          <p className="text-gray-500 mb-4">The requested one-time payment order could not be found.</p>
          <Button onClick={() => router.back()}>Go Back</Button>
        </div>
      </div>
    );
  }

  return (
    <AntdConfigProvider>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => router.back()}
          >
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Order #{orderDetail.orderssn}</h1>
            <p className="text-gray-500">One-time Payment Order Details</p>
          </div>
        </div>
        
        <div className="flex space-x-2">
          {orderDetail.status_id === 0 && (
            <Button 
              type="default" 
              danger 
              icon={<StopOutlined />}
              onClick={handleCancelOrder}
            >
              Cancel Order
            </Button>
          )}
          
          {orderDetail.status_id === 3 && (
            <Button 
              type="primary" 
              icon={<CheckOutlined />}
              onClick={handleConfirmReceipt}
              className="bg-[#FF6000]"
            >
              Confirm Receipt
            </Button>
          )}
        </div>
      </div>

      {/* Order Information */}
      <Card title="Order Information" className="mb-6">
        <Descriptions column={2}>
          <Descriptions.Item label="Order Number">{orderDetail.orderssn}</Descriptions.Item>
          <Descriptions.Item label="Status">
            <span className={`text-${getStatusColor(orderDetail.status_id)}-600 font-medium`}>
              {orderDetail.status_text}
            </span>
          </Descriptions.Item>
          <Descriptions.Item label="Created Time">{orderDetail.createtime}</Descriptions.Item>
          <Descriptions.Item label="Updated Time">{orderDetail.updatetime}</Descriptions.Item>
          <Descriptions.Item label="Currency">{orderDetail.currencycode}</Descriptions.Item>
          <Descriptions.Item label="Total Amount">
            <span className="text-xl font-bold text-[#FF6000]">
              {formatCurrency(orderDetail.totalmoney).formatValue}
            </span>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* Sub Orders */}
      {orderDetail.order && orderDetail.order.map((subOrder, index) => (
        <Card 
          key={subOrder.id}
          title={`Sub Order #${subOrder.ordersn} - ${subOrder.goodsseller}`}
          className="mb-6"
        >
          <div className="mb-4">
            <p className="text-sm text-gray-500">
              Seller: <a href={subOrder.sellerurl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                {subOrder.goodsseller}
              </a>
            </p>
            <p className="text-sm text-gray-500">
              Sub Order Total: <span className="font-medium text-[#FF6000]">
                {formatCurrency(subOrder.totalmoney).formatValue}
              </span>
            </p>
          </div>
          
          <Table
            columns={productColumns}
            dataSource={subOrder.goods}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </Card>
      ))}
      </div>
    </AntdConfigProvider>
  );
}
