'use client';

import { useState, useEffect, useRef } from 'react';
import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter ,useSearchParams} from 'next/navigation';
import type { Locale } from '@/config';
import { request } from '@/request';
import { getDictionary } from '@/dictionaries';
import {Api} from '@/request/api';
import { md5 } from 'js-md5';
import Modal from '@/components/Modal';
import { Input, Button, Select } from 'antd';
import Cookies from 'js-cookie';
import Toast from '@/components/Toast';
import  BackgroundImg  from '../(component)/BackgroundImg';
import ToastHelper from '@/utils/toastHelper';

export default function LoginPage({
  params,
}: {
  params: Promise<{ lng: Locale }>;
}) {
  // 在Next.js 14中，需要使用React.use()来解包params
  const resolvedParams = React.use(params);
  const { lng } = resolvedParams;

  return (
    <ClientLoginPage lng={lng} />
  );
}

function ClientLoginPage({ lng }: { lng: Locale }) {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [invitationCode, setInvitationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [dict, setDict] = useState<any>({});
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [config, setConfig] = useState<any>({user_register_captcha:'',logo:'',register_statement_id:'',website_statement_id:""});
  const [countdown, setCountdown] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const searchParams = useSearchParams();

  // 手机验证相关状态
  const [mobile, setMobile] = useState('');
  const [selectedMobileCode, setSelectedMobileCode] = useState('86');
  const [selectedMobileCodeDisplay, setSelectedMobileCodeDisplay] = useState('86');
  const [mobileCodeList, setMobileCodeList] = useState<any[]>([]);
  const [mobileVerificationCode, setMobileVerificationCode] = useState('');
  const [showMobileVerificationModal, setShowMobileVerificationModal] = useState(false);
  const [mobileCountdown, setMobileCountdown] = useState(0);
  const mobileTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 获取invite参数（邀请码）
  useEffect(() => {
    const invite = searchParams.get('invite');
      if (invite) {
        setInvitationCode(invite);
      }
  }, [searchParams]);

  useEffect(() => {
    async function loadDictionary() {
      try {
        const dictionary = await getDictionary(lng);
        setDict(dictionary);
      } catch (err) {
        console.error('Failed to load dictionary', err);
      }
    }

    loadDictionary();
  }, [lng]);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (mobileTimerRef.current) {
        clearInterval(mobileTimerRef.current);
      }
    };
  }, []);

  useEffect(()=>{
    const getConfig = async () => {
      try {
          const res =  await Api.getConfigList()
          if (res?.success && res.data) {
            let site = res.data.site
            setConfig({
              logo:site.logo,
              user_register_captcha:site.user_register_captcha,
              register_statement_id:site.register_agreement,
              website_statement_id:site.website_statement,
              name:site.name
            })

            // 获取区号列表
            const mobileAreaList = res.data.mobile_area_list || [];
            setMobileCodeList(mobileAreaList);
            if (mobileAreaList.length > 0) {
              setSelectedMobileCode(mobileAreaList[0].code);
              setSelectedMobileCodeDisplay(mobileAreaList[0].code);
            }
          }
        } catch (e) {
          console.error(e)
        }
    }
    getConfig()
  },[])
 
  const startCountdown = () => {
    setCountdown(60);
    timerRef.current = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          if (timerRef.current) {
            clearInterval(timerRef.current);
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // 添加邮箱验证函数
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
    if (!email) {
      setError(dict?.register?.emailRequired || '请输入邮箱');
      return false;
    }
    if (!emailRegex.test(email)) {
      setError(dict?.register?.invalidEmail || '请输入有效的邮箱地址');
      return false;
    }
    return true;
  };

  // 添加手机号验证函数
  const validateMobile = (mobile: string): boolean => {
    const mobileRegex = /^[0-9]{6,15}$/;
    if (!mobile) {
      setError(dict?.register?.mobileRequired || '请输入手机号');
      return false;
    }
    if (!mobileRegex.test(mobile)) {
      setError(dict?.register?.invalidMobile || '请输入有效的手机号');
      return false;
    }
    return true;
  };

  // 手机验证码倒计时
  const startMobileCountdown = () => {
    setMobileCountdown(60);
    mobileTimerRef.current = setInterval(() => {
      setMobileCountdown((prev) => {
        if (prev <= 1) {
          if (mobileTimerRef.current) {
            clearInterval(mobileTimerRef.current);
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleSendCode = async () => {
    if (!validateEmail(email)) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await Api.sendEmailCode({
        email,
        event: 'register'
      });
      if (response?.success) {
        setShowVerificationModal(true);
        startCountdown();
      } else {
        Toast.error(response?.msg ||response?.data|| dict?.register?.sendCodeFailed || '发送验证码失败，请稍后重试');
        setError(response?.msg ||response?.data|| dict?.register?.sendCodeFailed || '发送验证码失败，请稍后重试');
        setShowVerificationModal(false);
      }
    } catch (err) {
      console.error('Failed to send verification code', err);
      setError(err instanceof Error ? err.message : dict?.register?.sendCodeFailed || '发送验证码失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 发送手机验证码
  const handleSendMobileCode = async () => {
    if (!validateMobile(mobile)) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await Api.sendMobileCode({
        mobile,
        mobilecode: selectedMobileCode,
        event: 'register'
      });
      if (response?.success) {
        setShowMobileVerificationModal(true);
        startMobileCountdown();
        Toast.success(dict?.register?.mobileCodeSent || '手机验证码已发送');
      } else {
        Toast.error(response?.msg || response?.data || dict?.register?.sendMobileCodeFailed || '发送手机验证码失败，请稍后重试');
        setError(response?.msg || response?.data || dict?.register?.sendMobileCodeFailed || '发送手机验证码失败，请稍后重试');
        setShowMobileVerificationModal(false);
      }
    } catch (err) {
      console.error('Failed to send mobile verification code', err);
      setError(err instanceof Error ? err.message : dict?.register?.sendMobileCodeFailed || '发送手机验证码失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newEmail = e.target.value;
    setEmail(newEmail);
    const usernameFromEmail = newEmail.split('@')[0];
    setUsername(usernameFromEmail);
  };

  // 在用户登录成功后的回调中
  const handleLoginSuccess = async () => {
    // 检查本地是否有存储的购物车数据
    const localCartItemsStr = localStorage.getItem('localCartItems');
    const localCartItems = localCartItemsStr ? JSON.parse(localCartItemsStr) : [];

    if (localCartItems.length > 0) {
      const results = [];
      const failedItems = [];

      // 逐个添加商品，记录成功和失败的情况
      for (const item of localCartItems) {
        try {
          const result = await Api.addCart(item);
          if (result.success) {
            results.push({ success: true, item });
          } else {
            results.push({ success: false, item, error: result.msg || ToastHelper.getLocalizedMessage('add_cart_failed', lng) });
            failedItems.push(item);
          }
        } catch (error) {
          console.error('添加商品到购物车失败:', error);
          results.push({ success: false, item, error: (error as Error).message || ToastHelper.getLocalizedMessage('network_error', lng) });
          failedItems.push(item);
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failedCount = failedItems.length;

      // 如果有失败的商品，保留在本地存储中
      if (failedItems.length > 0) {
        localStorage.setItem('localCartItems', JSON.stringify(failedItems));
      } else {
        // 全部成功，清空本地存储
        localStorage.removeItem('localCartItems');
      }

      // 触发购物车更新事件，通知所有相关组件刷新数据
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('cartUpdated'));
        window.dispatchEvent(new CustomEvent('loginSuccess'));
      }

      // 给用户反馈
      if (successCount > 0 && failedCount === 0) {
        Toast.success(`${successCount}件商品已加入购物车`);
      } else if (successCount > 0 && failedCount > 0) {
        Toast.warning(`${successCount}件商品已加入购物车，${failedCount}件商品添加失败`);
      } else if (failedCount > 0) {
        Toast.error('商品加入购物车失败，请稍后重试');
      }
    }
  };

  // 自动登录函数
  const performAutoLogin = async () => {
    try {
      // 确定登录账号
      let loginAccount = email;
      if (config.user_register_captcha === "mobile" && !email) {
        loginAccount = `${mobile}@mobile.user`;
      }

      const loginResponse = await Api.login({
        account: loginAccount,
        password: md5(password)
      });

      if (loginResponse.success) {
        // 设置用户信息到localStorage
        localStorage.setItem('info', JSON.stringify(loginResponse));

        // 根据后端类型设置正确的token cookie名称
        const tokenName = process.env.NEXT_PUBLIC_BACKEND_TYPE === '6' ? 'access_token' : 'token';
        Cookies.set(tokenName, loginResponse.data.userinfo.token, { expires: 30, path: '/' });

        // 处理未登录时添加购物车商品
        await handleLoginSuccess();

        // 获取callback参数，如果存在则跳转到原始页面，否则跳转到会员中心
        const callback = searchParams.get('callback');

        if (callback) {
          // 确保callback是一个有效的内部路径
          if (callback.startsWith('/')) {
            router.push(callback);
          } else {
            router.push(`/${lng}/dashboard/home`);
          }
        } else {
          // 注册成功后默认跳转到会员中心
          router.push(`/${lng}/dashboard/home`);
        }
        return true;
      } else {
        console.error('自动登录失败:', loginResponse);
        return false;
      }
    } catch (err) {
      console.error('自动登录失败:', err);
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // 根据配置验证不同的字段
    if (config.user_register_captcha === "mobile") {
      // 手机验证模式
      if (!validateMobile(mobile)) {
        return;
      }
      // 如果需要手机验证码但还没有验证码，先发送验证码
      if (!mobileVerificationCode) {
        handleSendMobileCode();
        return;
      }
    } else {
      // 邮箱验证模式（默认）
      if (!validateEmail(email)) {
        return;
      }
      // 如果配置要求邮箱验证码但还没有验证码，先发送验证码
      if (config.user_register_captcha === "email" && !verificationCode) {
        handleSendCode();
        return;
      }
    }

    // 密码长度为6-16位，必须包含大小写字母和数字
    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[\x21-\x7E]{6,16}$/;
    if (!regex.test(password)) {
      setError(dict?.register?.passwordRule || '密码长度为6-16位，必须包含大小写字母和数字');
      return;
    }

    if (password !== confirmPassword) {
      setError(dict?.register?.passwordMismatch || '两次输入的密码不一致');
      return;
    }

    if (!agreeToTerms) {
      setError(dict?.register?.agreeTermsRequired || '请同意用户协议和隐私政策');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // 构建注册参数
      let finalEmail = email;
      let finalUsername = username;

      // 在手机验证模式下，如果没有邮箱，生成一个默认邮箱
      if (config.user_register_captcha === "mobile" && !email) {
        finalEmail = `${mobile}@mobile.user`;
        finalUsername = mobile;
      }

      const registerParams: any = {
        email: finalEmail,
        username: finalUsername,
        password: md5(password),
        inviter: invitationCode || undefined,
        clienttype: 'pc'
      };

      // 根据配置添加验证码字段
      if (config.user_register_captcha === "email") {
        registerParams.captcha = verificationCode;
      } else if (config.user_register_captcha === "mobile") {
        registerParams.mobile = mobile;
        registerParams.mobilecode = selectedMobileCode;
        registerParams.captcha = mobileVerificationCode;
      }

      const response = await Api.register(registerParams);

      if (response.success) {
        // 注册成功后自动登录
        const autoLoginSuccess = await performAutoLogin();
        if (!autoLoginSuccess) {
          // 如果自动登录失败，则跳转到登录页面
          const callback = searchParams.get('callback');
          const loginUrl = callback ? `/${lng}/login?callback=${encodeURIComponent(callback)}` : `/${lng}/login`;
          router.push(loginUrl);
        }
      } else {
        // 如果是需要验证码的错误，显示验证码窗口
        if (response.code === 15020) {
          setShowVerificationModal(true);
          handleSendCode(); // 自动发送验证码
        } else {
          // 其他错误显示错误消息
          setError(response.data || dict?.register?.registerFailed || '注册失败，请稍后重试');
        }
      }
    } catch (err) {
      console.error('Registration failed', err);
      setError(err instanceof Error ? err.message : dict?.register?.registerFailed || '注册失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleVerify = async () => {
    if (!verificationCode) {
      setError(dict?.register?.enterVerificationCode || '请输入验证码');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // 构建注册参数
      const registerParams: any = {
        email,
        username,
        password: md5(password),
        inviter: invitationCode || undefined,
        clienttype: 'pc',
        captcha: verificationCode
      };

      const response = await Api.register(registerParams);
      
      if (response.success) {
        // 注册成功后自动登录
        const autoLoginSuccess = await performAutoLogin();
        if (!autoLoginSuccess) {
          // 如果自动登录失败，则跳转到登录页面
          const callback = searchParams.get('callback');
          const loginUrl = callback ? `/${lng}/login?callback=${encodeURIComponent(callback)}` : `/${lng}/login`;
          router.push(loginUrl);
        }
      } else {
        setError(response.msg || dict?.register?.registerFailed || '注册失败，请稍后重试');
      }
    } catch (err) {
      console.error('Registration failed', err);
      setError(err instanceof Error ? err.message : dict?.register?.registerFailed || '注册失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 手机验证码确认处理函数
  const handleMobileVerify = async () => {
    if (!mobileVerificationCode) {
      setError(dict?.register?.enterVerificationCode || '请输入验证码');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // 构建注册参数
      let finalEmail = email;
      let finalUsername = username;

      // 如果没有邮箱，生成一个默认邮箱
      if (!email) {
        finalEmail = `${mobile}@mobile.user`;
        finalUsername = mobile;
      }

      const registerParams: any = {
        email: finalEmail,
        username: finalUsername,
        password: md5(password),
        inviter: invitationCode || undefined,
        clienttype: 'pc',
        mobile: mobile,
        mobilecode: selectedMobileCode,
        captcha: mobileVerificationCode
      };

      const response = await Api.register(registerParams);

      if (response.success) {
        // 注册成功后自动登录
        const autoLoginSuccess = await performAutoLogin();
        if (!autoLoginSuccess) {
          // 如果自动登录失败，则跳转到登录页面
          const callback = searchParams.get('callback');
          const loginUrl = callback ? `/${lng}/login?callback=${encodeURIComponent(callback)}` : `/${lng}/login`;
          router.push(loginUrl);
        }
      } else {
        setError(response.msg || dict?.register?.registerFailed || '注册失败，请稍后重试');
      }
    } catch (err) {
      console.error('Registration failed', err);
      setError(err instanceof Error ? err.message : dict?.register?.registerFailed || '注册失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* 左侧图片部分 */}
      <div className="hidden lg:block lg:w-1/2 relative">
        <Link href={`/${lng}`} className="absolute top-12 left-12 text-white z-10 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          {dict?.nav?.backToHome || '返回首页'}
        </Link>
        <div className="absolute inset-0 bg-white">
            <BackgroundImg/>
        </div>
      </div>

      {/* 右侧注册表单部分 */}
      <div className="w-full lg:w-1/2 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
            {config?.logo ? (<img src={config?.logo} alt="Logo" className="w-[130px] h-[40px] mx-auto" />):<h1 className="text-4xl font-bold text-[#FF6B00] text-center">onebuy</h1>}
          <div className="text-center">
            <h2 className="mt-6 text-2xl font-bold text-gray-900">{dict?.register?.title || '注册'}</h2>
          </div>

          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded">
                {error}
              </div>
            )}

            <div className="space-y-4">
              {/* 邮箱字段 - 始终显示，但在手机验证模式下不是必填 */}
              <div>
                <div className="relative">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required={config.user_register_captcha !== "mobile"}
                    value={email}
                    onChange={handleEmailChange}
                    className="appearance-none rounded-md relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#FF6B00] focus:border-[#FF6B00] focus:z-10 sm:text-sm"
                    placeholder={dict?.register?.emailPlaceholder || '邮箱'}
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* 手机号字段 - 仅在手机验证模式下显示 */}
              {config.user_register_captcha === "mobile" && (
                <div>
                  <div className="flex space-x-2">
                    <Select
                      value={selectedMobileCodeDisplay}
                      onChange={(value, option: any) => {
                        const selectedItem = mobileCodeList.find(item => item.name === value || item.code === value);
                        if (selectedItem) {
                          setSelectedMobileCode(selectedItem.code);
                          setSelectedMobileCodeDisplay(selectedItem.code);
                        }
                      }}
                      className="w-24"
                      placeholder={dict?.register?.selectCountryCode || '选择国家/地区'}
                    >
                      {mobileCodeList.map(item => (
                        <Select.Option key={item.id} value={item.code}>
                          +{item.code}
                        </Select.Option>
                      ))}
                    </Select>
                    <div className="relative flex-1">
                      <input
                        id="mobile"
                        name="mobile"
                        type="tel"
                        autoComplete="tel"
                        required
                        value={mobile}
                        onChange={(e) => setMobile(e.target.value)}
                        className="appearance-none rounded-md relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#FF6B00] focus:border-[#FF6B00] focus:z-10 sm:text-sm"
                        placeholder={dict?.register?.mobilePlaceholder || '手机号'}
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    autoComplete="new-password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="appearance-none rounded-md relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#FF6B00] focus:border-[#FF6B00] focus:z-10 sm:text-sm"
                    placeholder={dict?.register?.passwordPlaceholder || '密码'}
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button 
                      type="button" 
                      onClick={() => setShowPassword(!showPassword)}
                      className="text-gray-400 hover:text-gray-500 focus:outline-none"
                    >
                      {showPassword ? (
                        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                          <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                        </svg>
                      ) : (
                        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
              </div>

              <div>
                <div className="relative">
                  <input
                    id="confirm-password"
                    name="confirm-password"
                    type={showConfirmPassword ? "text" : "password"}
                    autoComplete="new-password"
                    required
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="appearance-none rounded-md relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#FF6B00] focus:border-[#FF6B00] focus:z-10 sm:text-sm"
                    placeholder={dict?.register?.confirmPasswordPlaceholder || '确认密码'}
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button 
                      type="button" 
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="text-gray-400 hover:text-gray-500 focus:outline-none"
                    >
                      {showConfirmPassword ? (
                        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                          <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                        </svg>
                      ) : (
                        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
              </div>

              <div>
                <div className="relative">
                  <input
                    id="invitation-code"
                    name="invitation-code"
                    type="text"
                    value={invitationCode}
                    onChange={(e) => setInvitationCode(e.target.value)}
                    className="appearance-none rounded-md relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#FF6B00] focus:border-[#FF6B00] focus:z-10 sm:text-sm"
                    placeholder={dict?.register?.invitationCodePlaceholder || '输入邀请码（可选）'}
                  />
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <input
                id="agree-terms"
                name="agree-terms"
                type="checkbox"
                checked={agreeToTerms}
                onChange={(e) => setAgreeToTerms(e.target.checked)}
                className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300 rounded"
              />
              <label htmlFor="agree-terms" className="ml-2 block text-sm text-gray-900">
                {dict?.register?.agreeToTermsText || '我已阅读并同意'}{' '}
                <Link href={`/${lng}/information?id=${config.register_agreement}`} target='_blank' className="font-medium text-[#FF6B00] hover:text-[#E55A00]">
                  {config.name} {dict?.register?.userAgreement}
                </Link>
                {' '}{dict?.register?.and || '和'}{' '}
                <Link href={`/${lng}/information?id=${config.website_statement}`} target='_blank' className="font-medium text-[#FF6B00] hover:text-[#E55A00]">
                  {config.name} {dict?.register?.declaration}
                </Link>
              </label>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-[#FF6B00] hover:bg-[#E55A00] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B00]"
              >
                {loading ? (
                  <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </span>
                ) : null}
                {dict?.register?.registerButton || '注册'}
              </button>
            </div>

            <div className="text-center text-sm">
              <span className="text-gray-500">{dict?.register?.haveAccount || '已有账户？'}</span>{' '}
              <Link
                href={(() => {
                  const callback = searchParams.get('callback');
                  return callback ? `/${lng}/login?callback=${encodeURIComponent(callback)}` : `/${lng}/login`;
                })()}
                className="font-medium text-[#FF6B00] hover:text-[#E55A00]"
              >
                {dict?.register?.loginLink}
              </Link>
            </div>
          </form>

          {/* 邮箱验证码模态框 */}
          <Modal
            title= {dict?.register?.enterVerificationCode}
            open={showVerificationModal}
            onCancel={() => setShowVerificationModal(false)}
            footer={[
              <Button
                key="cancel"
                onClick={() => setShowVerificationModal(false)}
              >
               {dict?.register?.cancel}
              </Button>,
              <Button
                key="submit"
                type="primary"
                onClick={handleVerify}
                loading={loading}
              >
                {dict?.register?.confirm}
              </Button>
            ]}
            maskClosable={false}
          >
            <p className="text-sm text-gray-500 mb-4">
              {dict?.register?.verificationCodeSent}：{email}
            </p>
            <div className="flex space-x-2">
              <Input
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                placeholder={dict?.register?.enterVerificationCode}
                style={{ flex: 1 }}
              />
              <Button
                type="primary"
                disabled={countdown > 0}
                onClick={handleSendCode}
              >
                {countdown > 0 ? `${countdown}s` :dict?.register?.resend}
              </Button>
            </div>
          </Modal>

          {/* 手机验证码模态框 */}
          <Modal
            title={dict?.register?.enterVerificationCode}
            open={showMobileVerificationModal}
            onCancel={() => setShowMobileVerificationModal(false)}
            footer={[
              <Button
                key="cancel"
                onClick={() => setShowMobileVerificationModal(false)}
              >
                {dict?.register?.cancel}
              </Button>,
              <Button
                key="submit"
                type="primary"
                onClick={handleMobileVerify}
                loading={loading}
              >
                {dict?.register?.confirm}
              </Button>
            ]}
            maskClosable={false}
          >
            <p className="text-sm text-gray-500 mb-4">
              {dict?.register?.verificationCodeSentMobile}：+{selectedMobileCode} {mobile}
            </p>
            <div className="flex space-x-2">
              <Input
                value={mobileVerificationCode}
                onChange={(e) => setMobileVerificationCode(e.target.value)}
                placeholder={dict?.register?.mobileCodePlaceholder}
                style={{ flex: 1 }}
              />
              <Button
                type="primary"
                disabled={mobileCountdown > 0}
                onClick={handleSendMobileCode}
              >
                {mobileCountdown > 0 ? `${mobileCountdown}s` : dict?.register?.resend}
              </Button>
            </div>
          </Modal>
        </div>
      </div>
    </div>
  );
} 