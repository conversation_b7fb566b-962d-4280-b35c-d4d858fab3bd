'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import ButtonComponent from '@/components/Button';
import { Modal, Upload, Form, Input, InputNumber, Steps, Alert, Checkbox, Button, List, Space, Divider } from 'antd';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { UploadOutlined, InfoCircleOutlined, PlusOutlined, DeleteOutlined, LoadingOutlined } from '@ant-design/icons';
import { Api } from '@/request/api';
import Toast from '@/components/Toast';
import styles from './styles.module.css';
import { getDictionary } from '@/dictionaries';


export default function SharePromotionPage() {
  const params = useParams();
  const lng = params.lng as string;
  const searchParams = useSearchParams();
  const [form] = Form.useForm();
  const [totalPrice, setTotalPrice] = useState<number>(0);
  const [agreedToTerms, setAgreedToTerms] = useState<boolean>(false);
  const [productsList, setProductsList] = useState<any[]>([]);
  const [isFormChanged, setIsFormChanged] = useState<boolean>(false);
  const router = useRouter();
  const [uploading, setUploading] = useState<boolean>(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
  const [dict, setDict] = useState<any>(null); // 添加字典状态
  const [siteConfig, setSiteConfig] = useState<any>(null);

  useEffect(() => {
    const loadConfig = async () => {
      const config = await getConfig();
      console.log(config,'siteConfig')
      setSiteConfig(config);
    };
    loadConfig();
  }, []);

  // 获取字典数据
  useEffect(() => {
    const loadDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      } finally {
      }
    };
    loadDictionary();
  }, [lng]);

  // 从查询参数中获取URL并设置到表单
  useEffect(() => {
    const urlFromQuery = searchParams.get('url');
    if (urlFromQuery) {
      form.setFieldsValue({ productLink: decodeURIComponent(urlFromQuery) });
    }
  }, [searchParams, form]);

  // 计算总价
  useEffect(() => {
    let total = 0;
    productsList.forEach(product => {
      total += (product.price * product.quantity) + product.shippingFee;
    });
    setTotalPrice(total);
  }, [productsList]);

  // 获取站点配置信息
  const getConfig = async () => {
    try {
      if (typeof window !== "undefined") {
        const siteData = localStorage.getItem("siteData");
        if (siteData) {
          try {
            const config = JSON.parse(siteData);
            return config; 
          } catch (parseError) {
            console.error("Failed to parse siteData:", parseError);
          }
        }
      }
      const res = await Api.getConfigList();
      return res.data.site || null; 
      
    } catch (error) {
      console.error("Failed to get configuration:", error);
      return null; 
    }
  };

  // 当表单值变化时设置表单已更改状态
  const handleValuesChange = () => {
    setIsFormChanged(true);
  };
  const beforeUpload = (file: File) => {
    const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];
    // 检查文件类型
    if (!ALLOWED_TYPES.includes(file.type)) {
      Toast.error(dict?.selfservice?.imageFormat);
      return Upload.LIST_IGNORE; // 阻止文件进入列表
    }

    // 检查文件大小
    if (file.size > MAX_FILE_SIZE) {
      Toast.error(dict?.selfservice?.sizeLimit);
      return Upload.LIST_IGNORE; // 阻止文件进入列表
    }
    return true; // 允许上传
  };
  // 自定义上传处理
  const customUploadRequest = async (options: any) => {
    const { file, onSuccess, onError } = options;
    setUploading(true);

    try {
      // 将文件转换为Base64格式
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = async (e) => {
        const base64String = e.target?.result as string;
        if (base64String) {
          // 使用uploadImage方法上传Base64格式的图片
          const response = isTp5? await Api.uploadImage(base64String): await Api.uploadImage(file);
          if (response.success || response.code === 1) {
            // 处理成功响应
            const imageUrl = response.data?.items?.item?.name || response.data?.url || '';
            const fileInfo = {
              uid: file.uid,
              name: file.name,
              status: 'done',
              url: imageUrl,
              response: response // 保存完整的响应数据
            };
            setFileList([fileInfo]);
            form.setFieldsValue({ productImage: [fileInfo] });
            console.log('图片上传成功，设置fileInfo:', fileInfo);
            onSuccess({ ...response, url: imageUrl }, file);
          } else {
            // 处理失败响应
            onError(new Error(response.msg || '上传失败'));
            Toast.error(response.msg || dict?.selfservice?.uploadFailed);
          }
        }
      };
    } catch (error) {
      console.error('上传错误:', error);
      onError(error);
      Toast.error(dict?.selfservice?.uploadFailed);
    } finally {
      setUploading(false);
    }
  };

  // 添加商品到列表
  const addProductToList = async () => {
    try {
      // 验证表单
      await form.validateFields();
      const values = form.getFieldsValue();

      // 必填字段验证
      const requiredFields = ['productLink', 'productName', 'specification', 'productImage', 'price', 'quantity', 'shippingFee'];
      const missingFields = requiredFields.filter(field => {
        if (field === 'productImage') {
          return !values[field] || values[field].length === 0;
        }
        return !values[field] && values[field] !== 0;
      });

      if (missingFields.length > 0) {
        const fieldNames = {
          productLink: dict?.selfservice?.productLink,
          productName: dict?.selfservice?.productName,
          specification: dict?.selfservice?.specs,
          productImage: dict?.selfservice?.productImage,
          price: dict?.selfservice?.price,
          quantity: dict?.selfservice?.quantity,
          shippingFee: dict?.selfservice?.shippingCol
        };
        
        const missingFieldNames = missingFields.map(field => fieldNames[field as keyof typeof fieldNames]);
        Toast.error(`${dict?.selfservice?.fillRequired}: ${missingFieldNames.join(', ')}`);
        return;
      }
      
      // 处理图片URL - 修复图片获取逻辑
      let productImage = '';
      if (values.productImage && values.productImage.length > 0) {
        const imageFile = values.productImage[0];
        // 优先使用上传成功后的URL
        productImage = imageFile.url ||
                      imageFile.response?.url ||
                      imageFile.response?.data?.url ||
                      imageFile.response?.data?.items?.item?.name ||
                      '';
      }

      // 如果还是没有图片URL，检查fileList状态
      if (!productImage && fileList.length > 0) {
        const fileItem = fileList[0];
        productImage = fileItem.url ||
                      fileItem.response?.url ||
                      fileItem.response?.data?.url ||
                      fileItem.response?.data?.items?.item?.name ||
                      '';
      }

      // 调试信息
      console.log('图片URL获取结果:', {
        productImage,
        formValues: values.productImage,
        fileList,
        fileListLength: fileList.length
      });

      // 创建新商品对象
      const newProduct = {
        ...values,
        productImage,
        id: Date.now() // 临时ID，用于列表渲染和操作
      };

      // 添加到列表
      setProductsList([...productsList, newProduct]);
      Toast.success(dict?.selfservice?.addSuccess);

      // 重置表单
      form.resetFields();
      setFileList([]); // 清空文件列表
      setIsFormChanged(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 删除商品
  const handleDeleteItem = (index: number) => {
        const newList = [...productsList];
        newList.splice(index, 1);
        setProductsList(newList);
  };

  // 提交所有商品到购物车
  const handleSubmitAll = async () => {
    try {
      if (!agreedToTerms) {
        Toast.error(dict?.selfservice?.agreeFirst);
        return;
      }

      if (productsList.length === 0) {
        Toast.error(dict?.selfservice?.minProduct);
        return;
      }

      // 处理每个商品，准备批量添加参数，只保留必要的字段
      const cartItems = productsList.map(product => ({
        goodsurl: product.productLink,
        goodsname: product.productName,
        goodsimg: product.productImage || '/assets/img/default/buyfromchina.jpg',
        goodsprice: product.price,
        sendprice: product.shippingFee || 0, // 如果运费为null则默认为0
        goodsseller: 'By yourself', // 设置默认店铺名
        goodsnum: product.quantity,
        goodsremark: product.description || '',
        sellerurl: '/',
        goodssite: 'taobao',
        goodsweight: 0,
        sku_id: '',
        spare_sku_id: '',
        clienttype: 'pc'
      }));

      // 调试信息
      console.log('准备提交的商品数据:', cartItems);

      // 根据后端版本选择不同的提交方式
      let response;
      if (isTp5) {
        // TP5版本：使用list字段包含JSON字符串
        response = await Api.batchAddCart({
          list: JSON.stringify(cartItems)
        });
      } else {
        // TP6版本：直接传递数组或使用不同的参数格式
        // 由于API定义显示batchAddCart期望单个商品对象，我们需要逐个添加
        const results = [];
        for (const item of cartItems) {
          try {
            const result = await Api.addCart({
              ...item,
              isspec: 0,
              goodstype: 0,
              mall_goods_id: 0,
              flash_sale_id: '',
              originalprice: item.goodsprice,
              spare_sku_name: '',
              quickbuy: 0,
              secret_key: '',
              cid: '',
              checked: true
            });
            results.push(result);
          } catch (error) {
            console.error('添加商品失败:', error);
            results.push({ success: false, error });
          }
        }
        // 检查是否所有商品都添加成功
        const allSuccess = results.every(r => r.success);
        response = { success: allSuccess, results };
      }
      
      if (response.success) {
        setProductsList([]);
        form.resetFields();
        setFileList([]); // 清空文件列表
        Toast.success(dict?.selfservice?.cartSuccess);
        // 跳转到购物车页面
        router.push(`/${lng}/dashboard/cart`);
      } else {
        Toast.error(response.msg ||dict?.selfservice?.cartFailed);
      }
    } catch (error) {
      Toast.error(dict?.selfservice?.submitRetry);
      console.error(error);
    }
  };

  const steps = [
    {
      title: dict?.selfservice?.step1Title,
      description: dict?.selfservice?.step1Desc
    },
    {
      title:dict?.selfservice?.step2Title,
      description: dict?.selfservice?.step2Desc
    },
    {
      title: dict?.selfservice?.step3Title,
      description: dict?.selfservice?.step3Desc
    }
  ];
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部横幅区域 */}
      <div className="w-full min-h-[400px] bg-cover bg-center py-16 relative flex justify-center" 
        style={{
          backgroundImage: 'url(/images/selfservice.jpg)',
        }}>
        {/* 橙色遮罩 */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#f97316cc] to-[#ea580ccc] opacity-90"></div>
        
        <div className="container mx-auto px-4 lg:px-8 relative z-10 flex flex-col lg:flex-row justify-between items-center gap-10">
          <div className="mx-auto lg:w-1/2 text-white">  
            <h1 className="text-4xl font-bold mb-6 md:text-5xl sm:text-3xl text-center text-shadow-xl text-shadow-color-[#8f3b00cc]">{dict?.selfservice?.manualOrder}</h1>
            <p className="text-lg opacity-90 mb-8 leading-relaxed text-shadow-xl text-shadow-color-[#8f3b00cc]">
              {dict?.selfservice?.serviceDesc}
            </p>
          </div>
        </div>
      </div>

      {/* 步骤条 */}
      <div className="w-full border-b bg-white">
        <div className={styles.stepsContainer}>
          <Steps
            current={0}
            items={steps}
          />
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* 左侧：表单 */}
            <div className="flex-1">
              <div className={`bg-white rounded-xl p-8 shadow-sm ${styles.formRequiredStyles}`}>
                <Form
                  form={form}
                  layout="vertical"
                  initialValues={{
                    quantity: 1,
                    depositAmount: 0,
                    shippingFee: 0
                  }}
                  requiredMark={false}
                  className="max-w-full"
                  onValuesChange={handleValuesChange}
                >
                  <Form.Item
                    label={<span className="font-normal">{dict?.selfservice?.productLink}</span>}
                    name="productLink"
                    rules={[
                      { required: true, message: dict?.selfservice?.productLinkHint },
                      {
                        pattern: /^https:\/\/.+/,
                        message: dict?.selfservice?.linkRule 
                      }
                    ]}
                    className="mb-6"
                    required
                  >
                    <Input placeholder={dict?.selfservice?.linkRule } size="large" />
                  </Form.Item>

                  <Form.Item
                    label={<span className="font-normal">{dict?.selfservice?.productName}</span>}
                    name="productName"
                    rules={[{ required: true, message: dict?.selfservice?.productNameHint  }]}
                    className="mb-6"
                    required
                  >
                    <Input placeholder={dict?.selfservice?.productNameHint } size="large" />
                  </Form.Item>

                  <Form.Item
                    label={<span className="font-normal">{dict?.selfservice?.specs}</span>}
                    name="specification"
                    rules={[{ required: true, message: dict?.selfservice?.specsHint  }]}
                    className="mb-6"
                    required
                  >
                    <Input placeholder={dict?.selfservice?.specsDetail} size="large" />
                  </Form.Item>

                  <Form.Item
                    label={<span className="font-normal">{dict?.selfservice?.productImage}</span>}
                    name="productImage"
                    rules={[{ required: true, message: dict?.selfservice?.uploadHint}]}
                    className="mb-6"
                    required
                    valuePropName="fileList"
                    getValueFromEvent={(e) => {
                      if (Array.isArray(e)) {
                        return e;
                      }
                      return e?.fileList || [];
                    }}
                  >
                    <Upload.Dragger
                      listType="picture"
                      maxCount={1}
                      beforeUpload={beforeUpload}
                      customRequest={customUploadRequest}
                      className="bg-gray-50"
                      fileList={fileList}
                      onChange={({ fileList }) => setFileList(fileList)}
                    >
                      {uploading ? (
                        <div className="py-8 px-4 text-center">
                          <LoadingOutlined style={{ fontSize: 24 }} spin />
                          <p className="text-gray-600 font-medium mt-2">{dict?.selfservice?.uploading}</p>
                        </div>
                      ) : (
                        <div className="py-8 px-4 text-center">
                          <svg className="mx-auto mb-2" width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 12V19M12 12L15 15M12 12L9 15M20 16.7428C21.2215 15.734 22 14.2079 22 12.5C22 9.46243 19.5376 7 16.5 7C16.2815 7 16.0771 6.886 15.9661 6.69774C14.6621 4.48484 12.2544 3 9.5 3C5.35786 3 2 6.35786 2 10.5C2 12.5661 2.83545 14.4371 4.18695 15.7935" stroke="#6B7280" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                          <p className="text-gray-600 font-medium">{dict?.selfservice?.uploadTip}</p>
                          <p className="text-gray-400 text-sm mt-1">{dict?.selfservice?.imageRule}</p>
                        </div>
                      )}
                    </Upload.Dragger>
                  </Form.Item>

                  <Form.Item
                    label={<span className="font-normal">{dict?.selfservice?.remark}</span>}
                    name="description"
                    className="mb-6"
                  >
                    <Input.TextArea 
                      rows={4} 
                      placeholder={dict?.selfservice?.remarkHint}
                      size="large"
                    />
                  </Form.Item>

                  <Form.Item
                    label={<span className="font-normal">{dict?.selfservice?.price}</span>}
                    name="price"
                    rules={[{ required: true, message:dict?.selfservice?.priceHint }]}
                    className="mb-6"
                    required
                  >
                    <InputNumber
                      min={0}
                      addonAfter="CNY"
                      style={{ width: '100%' }}
                      size="large"
                      placeholder="0"
                    />
                  </Form.Item>

                  <Form.Item
                    label={<span className="font-normal">{dict?.selfservice?.quantity}</span>}
                    name="quantity"
                    rules={[{ required: true, message: dict?.selfservice?.quantityHint }]}
                    className="mb-6"
                    required
                  >
                    <div className="flex items-center">
                      <button 
                        type="button" 
                        className="border border-gray-300 w-12 h-[48px] flex items-center justify-center rounded-l-md bg-white hover:bg-gray-50"
                        onClick={() => {
                          const currentValue = form.getFieldValue('quantity') || 1;
                          if (currentValue > 1) {
                            const newValue = currentValue - 1;
                            form.setFieldsValue({ quantity: newValue });
                            setIsFormChanged(true);
                          }
                        }}
                      >
                        <span className="text-xl text-gray-500">−</span>
                      </button>
                      <Form.Item noStyle name="quantity">
                        <InputNumber
                          min={1}
                          controls={false}
                          className="flex-1 rounded-none"
                          style={{ width: '100%', textAlign: 'center' }}
                          size="large"
                          placeholder={dict?.selfservice?.quantityHint}
                        />
                      </Form.Item>
                      <button 
                        type="button" 
                        className="border border-gray-300 w-12 h-[48px] flex items-center justify-center rounded-r-md bg-white hover:bg-gray-50"
                        onClick={() => {
                          const currentValue = form.getFieldValue('quantity') || 1;
                          const newValue = currentValue + 1;
                          form.setFieldsValue({ quantity: newValue });
                          setIsFormChanged(true);
                        }}
                      >
                        <span className="text-lg text-gray-500">+</span>
                      </button>
                    </div>
                  </Form.Item>

                  <Form.Item
                    label={<span className="font-normal"> {dict?.selfservice?.shippingFee.replace('Onebuy', siteConfig.name)}</span>}
                    name="shippingFee"
                    rules={[{ required: true, message: dict?.selfservice?.inputShipping }]}
                    className="mb-6"
                    required
                  >
                    <InputNumber
                      min={0}
                      addonAfter="CNY"
                      style={{ width: '100%' }}
                      size="large"
                      placeholder="0"
                    />
                  </Form.Item>

                  <Form.Item className="mb-6">
                    <div className="flex justify-center gap-6">
                      <ButtonComponent
                        onClick={addProductToList}
                        size="large"
                        className="min-w-[120px] h-[44px] rounded font-normal bg-orange-500 hover:bg-orange-600 border-orange-500 hover:border-orange-600 text-white"
                      >
                       {dict?.selfservice?.addProduct}
                      </ButtonComponent>
                    </div>
                  </Form.Item>
                </Form>

                {/* 商品列表 */}
                {productsList.length > 0 && (
                  <div className="mt-10">
                    <Divider orientation="left">{dict?.selfservice?.addedProduct} ({productsList.length})</Divider>
                    
                    <List
                      itemLayout="vertical"
                      dataSource={productsList}
                      renderItem={(item, index) => (
                        <List.Item
                          key={item.id}
                          actions={[
                            <Button 
                              key="delete" 
                              type="text" 
                              danger 
                              icon={<DeleteOutlined />} 
                              onClick={() => handleDeleteItem(index)}
                            >
                              {dict?.selfservice?.delete} 
                            </Button>
                          ]}
                          extra={
                            item.productImage ? (
                              <img
                                width={120}
                                height={120}
                                alt={item.productName}
                                src={item.productImage}
                                style={{ objectFit: 'cover', borderRadius: '8px' }}
                                onError={(e) => {
                                  // 图片加载失败时显示默认图片
                                  e.currentTarget.src = '/assets/img/default/buyfromchina.jpg';
                                }}
                              />
                            ) : (
                              <div
                                style={{
                                  width: 120,
                                  height: 120,
                                  backgroundColor: '#f5f5f5',
                                  borderRadius: '8px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  color: '#999'
                                }}
                              >
                                {dict?.selfservice?.noImage} 
                              </div>
                            )
                          }
                        >
                          <List.Item.Meta
                            title={item.productName}
                            description={item.specification}
                          />
                          <div className="flex flex-col gap-1 mt-2">
                            <div className="text-gray-500">{dict?.selfservice?.linkCol} : {item.productLink}</div>
                            <div className="text-gray-500">{dict?.selfservice?.priceCol} : {item.price} CNY × {item.quantity} = {item.price * item.quantity} CNY</div>
                            <div className="text-gray-500">{dict?.selfservice?.shippingCol} : {item.shippingFee} CNY</div>
                            <div className="font-medium">{dict?.selfservice?.subtotal} : {(item.price * item.quantity) + item.shippingFee} CNY</div>
                          </div>
                        </List.Item>
                      )}
                    />
                    
                    {/* 总计部分 */}
                    <div className="bg-gray-50 px-4 py-3 rounded-none mt-6 mb-6 border-t border-b border-gray-200">
                      <div className="flex justify-between items-center">
                        <div className="text-gray-700 text-base">{dict?.selfservice?.total} </div>
                        <div className="flex items-baseline">
                          <span className="text-2xl font-medium text-orange-500">{totalPrice.toFixed(2)}</span>
                          <span className="text-gray-500 text-sm ml-1">CNY</span>
                        </div>
                      </div>
                    </div>

                    {/* 协议勾选 */}
                    <Form.Item className="mb-6">
                      <Checkbox 
                        checked={agreedToTerms} 
                        onChange={(e) => setAgreedToTerms(e.target.checked)}
                        className="text-gray-600"
                      >
                        <span className="text-gray-600 text-sm">
                          {/* todo 获取id */}

                          {dict?.selfservice?.acceptTerms}  <a href={`/information?id=9`} className="text-orange-500 hover:text-orange-600" target='_blank'>{dict?.selfservice?.serviceAgreement} </a>
                        </span>
                      </Checkbox>
                    </Form.Item>

                    <Form.Item className="mb-0">
                      <div className="flex justify-center gap-6">
                        <ButtonComponent 
                          onClick={() => {
                            Modal.confirm({
                              title: dict?.selfservice?.clearConfirm,
                              content: dict?.selfservice?.clearMessage,
                              okText: dict?.selfservice?.confirm,
                              cancelText: dict?.selfservice?.cancel,
                              onOk: () => {
                                setProductsList([]);
                                form.resetFields();
                                Toast.success(dict?.selfservice?.cleared);
                              }
                            });
                          }}
                          size="large"
                          className="min-w-[120px] h-[44px] rounded border-gray-300 font-normal text-gray-600"
                        >
                         {dict?.selfservice?.clearList}
                        </ButtonComponent>
                        <ButtonComponent 
                          type="primary" 
                          onClick={handleSubmitAll}
                          size="large"
                          className="min-w-[120px] h-[44px] rounded font-normal bg-orange-500 hover:bg-orange-600 border-orange-500 hover:border-orange-600"
                        >
                        {dict?.selfservice?.submitCart}
                        </ButtonComponent>
                      </div>
                    </Form.Item>
                  </div>
                )}
              </div>
            </div>

            {/* 右侧：免责声明和费用说明 */}
            <div className="lg:w-[320px]">
              <div className="sticky top-6 space-y-4">
                {/* 免责声明 */}
                <div className="bg-orange-50 p-6 rounded-xl shadow-sm border border-orange-100">
                  <div className="flex items-start mb-4">
                    <InfoCircleOutlined className="mt-1 mr-3 text-orange-500 text-xl" />
                    <div className="font-semibold text-orange-500 text-lg">{dict?.selfservice?.discllaimer}</div>
                  </div>
                  <div className="text-gray-600 text-base leading-relaxed">
                    {dict?.selfservice?.disclaimerDetail}
                  </div>
                </div>

                {/* 费用说明 */}
                <div className="bg-orange-50 p-6 rounded-xl shadow-sm border border-orange-100">
                  <div className="flex items-start mb-4">
                    <svg className="w-5 h-5 mt-1 mr-3 text-orange-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <div className="font-semibold text-orange-500 text-lg"> {dict?.selfservice?.feeDescription}</div>
                  </div>
                  <ul className="text-gray-600 text-base leading-relaxed space-y-2">
                    <li className="flex items-start">
                      <span className="text-orange-500 mr-2">•</span>
                       {dict?.selfservice?.feeNote}
                    </li>
                    <li className="flex items-start">
                      <span className="text-orange-500 mr-2">•</span>
                     {dict?.selfservice?.warehouseFee}
                    </li>
                    <li className="flex items-start">
                      <span className="text-orange-500 mr-2">•</span>
                       {dict?.selfservice?.priceAlert}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
