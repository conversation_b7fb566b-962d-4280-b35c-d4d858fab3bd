'use client';

import { useMemo, useCallback } from 'react';
import Image from 'next/image';
import useEmblaCarousel from 'embla-carousel-react';
import Autoplay from 'embla-carousel-autoplay';
import { ShoppingTimeProduct } from "@/types/product";
import { prepareImageForNextJs } from '@/utils/imageUtils';
import { formatCurrency } from '@/utils/currency';
import styles from './ShoppingTimeCarousel.module.css';

interface ShoppingTimeCarouselProps {
  products: ShoppingTimeProduct[];
  dict: any;
}

interface UserGroup {
  user: {
    username: string;
    nickname: string;
  };
  products: ShoppingTimeProduct[];
  latestTime: string;
  totalItems: number;
}

export default function SeamlessEmblaCarousel({ products, dict }: ShoppingTimeCarouselProps) {
  // 按用户nickname进行分组聚合商品数据
  const userGroups = useMemo(() => {
    const groupMap = new Map<string, UserGroup>();

    products.forEach(product => {
      // 优先使用nickname作为分组键，如果没有nickname则使用username，最后使用user_id
      const groupKey = product.user?.nickname || product.user?.username || `user_${product.user_id}` || 'unknown';

      if (groupMap.has(groupKey)) {
        const group = groupMap.get(groupKey)!;
        group.products.push(product);
        group.totalItems += product.goodsnum;
        // 更新最新时间
        if (new Date(product.createtime) > new Date(group.latestTime)) {
          group.latestTime = product.createtime;
        }
      } else {
        // 创建新的用户组
        groupMap.set(groupKey, {
          user: product.user,
          products: [product],
          latestTime: product.createtime,
          totalItems: product.goodsnum
        });
      }
    });

    // 按最新购买时间排序，最新的在前面
    return Array.from(groupMap.values()).sort((a, b) =>
      new Date(b.latestTime).getTime() - new Date(a.latestTime).getTime()
    );
  }, [products]);

  // 将用户组展开为连续的商品流，并创建无缝循环所需的重复内容
  const continuousProducts = useMemo(() => {
    const baseProducts: Array<{
      product: ShoppingTimeProduct;
      user: UserGroup['user'];
      isFirstInGroup: boolean;
      totalProductsInGroup: number;
      indexInGroup: number;
      isClone?: boolean;
      cloneId?: string;
    }> = [];

    userGroups.forEach((userGroup) => {
      // 每个用户最多显示3个商品
      const productsToShow = userGroup.products.slice(0, 3);

      productsToShow.forEach((product, index) => {
        baseProducts.push({
          product,
          user: userGroup.user,
          isFirstInGroup: index === 0,
          totalProductsInGroup: userGroup.products.length,
          indexInGroup: index
        });
      });
    });

    // 如果商品数量少于10个，复制内容以确保无缝循环
    if (baseProducts.length < 10) {
      const clonedProducts = [...baseProducts, ...baseProducts, ...baseProducts].map((item, index) => ({
        ...item,
        isClone: index >= baseProducts.length,
        cloneId: `clone-${index}`
      }));
      return clonedProducts;
    }

    return baseProducts;
  }, [userGroups]);

  // Embla Carousel 配置 - 实现真正的无缝无限轮播
  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop: true,
      align: 'start',
      skipSnaps: false,
      dragFree: false, // 暂时禁用自由拖拽，专注于无缝循环
      containScroll: false, // 禁用容器滚动限制
      slidesToScroll: 1, // 每次滚动一个商品
      duration: 15, // 更快的过渡速度
    },
    [
      Autoplay({
        delay: 2500, // 2.5秒自动播放
        stopOnInteraction: false, // 交互后继续自动播放
        stopOnMouseEnter: true, // 鼠标悬停时暂停
        stopOnFocusIn: true, // 获得焦点时暂停
        playOnInit: true, // 初始化后立即开始播放
      })
    ]
  );

  // 导航按钮处理
  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  // 渲染单个商品卡片，每个都是288px宽度，实现无缝衔接
  const renderProductCard = (item: {
    product: ShoppingTimeProduct;
    user: UserGroup['user'];
    isFirstInGroup: boolean;
    totalProductsInGroup: number;
    indexInGroup: number;
    isClone?: boolean;
    cloneId?: string;
  }, index: number) => {
    const { product, user, isFirstInGroup, totalProductsInGroup, indexInGroup, isClone, cloneId } = item;
    const titleLength = product.goodsname?.length || 0;
    const titleLengthClass = titleLength <= 10 ? 'short' : titleLength <= 20 ? 'medium' : 'long';
    const detailUrl = product.goodsurl
      ? `/detail/${product.goodssite || 'taobao'}/?url=${encodeURIComponent(product.goodsurl)}`
      : '#';

    return (
      <div
        key={isClone ? `${cloneId}-${product.id || product.goodsimg || index}` : `product-${product.id || product.goodsimg || index}`}
        className="embla__slide flex-shrink-0"
        style={{ flex: '0 0 288px', width: '288px', marginRight: '16px' }} // 固定宽度实现无缝衔接
      >
        <div className="relative h-full">
          {/* 用户信息标签 - 增强版本，更明显的分组关系 */}
          {isFirstInGroup && (
            <div className="absolute -top-6 left-0 right-0 z-20 flex justify-center">
              <div className="bg-gradient-to-r from-orange-400 to-orange-600 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-lg border-2 border-white">
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 bg-white/30 rounded-full flex items-center justify-center text-xs font-bold">
                    {(user.nickname || user.username || 'U').charAt(0).toUpperCase()}
                  </div>
                  <span className="truncate max-w-32 text-sm font-semibold">
                    {user.nickname || user.username}
                  </span>
                  <span className="text-xs bg-white/20 px-2 py-0.5 rounded-full">
                    {totalProductsInGroup > 3 ? `${Math.min(3, totalProductsInGroup)}+` : totalProductsInGroup}件
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* 同一用户商品的视觉连接 - 使用底部边框色彩 */}
          <div className={`absolute bottom-0 left-0 right-0 h-1 ${
            isFirstInGroup ? 'bg-gradient-to-r from-orange-400 to-orange-500' :
            indexInGroup === 1 ? 'bg-gradient-to-r from-orange-500 to-orange-400' :
            'bg-gradient-to-r from-orange-400 to-orange-300'
          } z-10`}></div>

          {/* 商品卡片 - 与ProductCard完全一致的样式 */}
          <a
            href={detailUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="custom-card flex flex-col h-full relative block bg-white rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-200 border border-gray-100"
            data-title-length={titleLengthClass}
            style={{ marginTop: isFirstInGroup ? '24px' : '0' }} // 为更大的用户标签留出空间
          >
            {/* 商品图片 - 与ProductCard保持一致的正方形比例 */}
            <div className="relative aspect-square bg-white">
              <Image
                src={prepareImageForNextJs(product.goodsimg)}
                alt={product.goodsname || '商品图片'}
                fill
                sizes="288px"
                className="object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/images/default.jpg';
                }}
              />
              {/* 推荐标签 - 与ProductCard保持一致的样式 */}
              {product.recommend === 1 && (
                <div style={{
                  position: 'absolute',
                  top: '0',
                  right: '0',
                  zIndex: 49,
                  width: '50px',
                  height: '50px',
                  overflow: 'hidden'
                }}>
                  {/* 三角形背景 */}
                  <div style={{
                    position: 'absolute',
                    top: '0',
                    right: '0',
                    width: '0',
                    height: '0',
                    borderTop: `50px solid var(--base-color, #ff6b6b)`,
                    borderLeft: '50px solid transparent'
                  }}></div>
                  {/* 文字 */}
                  <div style={{
                    position: 'absolute',
                    top: '8px',
                    right: '8px',
                    color: 'white',
                    fontSize: '10px',
                    fontWeight: '700',
                    textShadow: '0 1px 2px rgba(0,0,0,0.4)',
                    letterSpacing: '0.5px',
                    transform: 'rotate(45deg)',
                    transformOrigin: 'center',
                    width: '30px',
                    textAlign: 'center'
                  }}>
                    推荐
                  </div>
                </div>
              )}
            </div>
            {/* 商品信息 - 与ProductCard保持一致的样式，但移除标题变色效果 */}
            <div className="p-3 flex-1 flex flex-col">
              <h3 className="text-md text-gray-800 line-clamp-2 mb-2 custom-h-48px">
                {product.goodsname}
              </h3>
              <div className="flex items-center justify-between mt-auto">
                <span className="text-[#FF6B00] font-medium">
                  <span className='text-sm sm:text-base md:text-lg lg:text-xl font-bold'>
                    {formatCurrency(Number(product.goodsprice)).formatValue}
                  </span>
                </span>
              </div>
            </div>
          </a>
        </div>
      </div>
    );
  };

  return (
    <div className="relative w-full group px-4">
      {continuousProducts.length > 0 ? (
        <>
          <div className="embla overflow-hidden pt-8" ref={emblaRef}>
            <div className="embla__container flex" style={{ gap: '0px' }}>
              {continuousProducts.map((item, index) => renderProductCard(item, index))}
            </div>
          </div>

          {/* 导航按钮 - 仅在hover时显示 */}
          <button
            className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-gray-700 hover:text-orange-600 p-3 rounded-full shadow-lg transition-all duration-300 z-30 border border-gray-200 opacity-0 group-hover:opacity-100"
            onClick={scrollPrev}
            aria-label="上一个"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-gray-700 hover:text-orange-600 p-3 rounded-full shadow-lg transition-all duration-300 z-30 border border-gray-200 opacity-0 group-hover:opacity-100"
            onClick={scrollNext}
            aria-label="下一个"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      ) : (
        <div className="text-center text-gray-500 py-12">
          {dict?.home?.emptyList || '列表为空'}
        </div>
      )}
    </div>
  );
}
