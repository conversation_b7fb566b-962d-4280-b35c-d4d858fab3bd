'use client';

import { useRouter, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Api } from '@/request/api';

interface HotWordsProps {
  hotSearchLabel: string;
}

export default function HotWords({ hotSearchLabel }: HotWordsProps) {
  const router = useRouter();
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];
  const [hotWords, setHotWords] = useState<string[]>([]);

  const handleHotWordClick = (word: string) => {
    router.push(`/${currentLocale}/search?q=${encodeURIComponent(word)}&page=1&type=taobao`);
  };

  useEffect(() => {
    const fetchHotWords = async () => {
      const hotWords = await Api.getHotRecommend(6);
      if (hotWords.success) {
        setHotWords((hotWords.data.data || hotWords.data).map((item: any) => item.keyword));
      }
    };
    fetchHotWords();
  }, []);

  return (
    <div className="mt-4 flex flex-wrap items-center text-sm">
      <span className="text-gray-300 mr-4">{hotSearchLabel}</span>
      <div className="flex flex-wrap gap-2">
        {hotWords.map((word: string, index: number) => (
          <span 
            key={index} 
            className="bg-white bg-opacity-20 px-3 py-1 rounded-full hover:bg-opacity-30 transition-colors duration-300 cursor-pointer"
            onClick={() => handleHotWordClick(word)}
          >
            {word}
          </span>
        ))}
      </div>
    </div>
  );
} 