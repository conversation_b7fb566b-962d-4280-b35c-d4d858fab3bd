import { useState, useEffect } from "react";
import { Form, DatePicker, Button } from "antd";
import { Api } from "@/request/api";
import { Empty } from "antd";
import type { Locale } from "@/config";
import { useParams } from "next/navigation";
import AntdConfigProvider from "@/components/AntdConfigProvider";
import Loading from "@/components/Loading";
import dayjs from "dayjs";
import { formatCurrency } from "@/utils/currency";

import { getDictionary } from "@/dictionaries";
interface OrderData {
  id: string;
  accountname: string;
  bankname: string;
  createtime: string;
  payname: string;
  money: number;
  status_text: string;
  handletime: number | string;
  remark: string;
  currencycode: string;
}

interface OrderListParams {
  starttime?: string;
  endtime?: string;
}

export default function RemitRecord() {
  const params = useParams();
  const lng = (params.lng as Locale) || "zh-cn";
  const [orders, setOrders] = useState<OrderData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchLoading, setSearchLoading] = useState<boolean>(false);
  const [dict, setDict] = useState<any>(null); // 添加字典状态
  const [form] = Form.useForm();
  const { RangePicker } = DatePicker;

  const fetchOrders = async (params: OrderListParams = {}) => {
    let param: {
      starttime: string | undefined;
      endtime: string | undefined;
    } = {
      starttime: params.starttime,
      endtime: params.endtime,
    };
    setLoading(true);
    const response = await Api.getOfflineRecord(param);
    if (response.success) {
      setOrders(response.data || []);
    }
    setLoading(false);
    setSearchLoading(false);
  };

  useEffect(() => {
    handleSearch();
  }, [lng]);

  // 异步获取字典数据
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error("Failed to load dictionary:", error);
      }
    };

    fetchDictionary();
  }, [lng]);
  const handleSearch = () => {
    const values = form.getFieldsValue();
    const [starttime, endtime] = values.dateRange || [null, null];

    fetchOrders({
      starttime: starttime ? starttime.format("YYYY-MM-DD") : undefined,
      endtime: endtime ? endtime.format("YYYY-MM-DD") : undefined,
    });
  };

  return (
    <AntdConfigProvider>
      <div className="">
        <div className="flex items-center justify-between mb-5">
          <h3 className="text-lg font-medium m-0">
            {dict?.dashboard?.remittance?.title}
          </h3>
          <div className="flex gap-4">
            <Form
              form={form}
              layout="inline"
              onFinish={() => {
                setSearchLoading(true);
                handleSearch();
              }}
              initialValues={{
                dateRange: undefined,
              }}
            >
              <Form.Item name="dateRange">
                <RangePicker
                  className="w-64 h-8"
                  placeholder={["-/-/-", "-/-/-"]}
                  size="large"
                  onChange={handleSearch}
                />
              </Form.Item>
              {/* <Form.Item className="mb-2">
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={searchLoading}
                  size="large"
                >
                  {dict?.dashboard?.remittance?.searchBtn}
                </Button>
              </Form.Item> */}
            </Form>
          </div>
        </div>

        {loading ? (
          <Loading height="300px" />
        ) : (
          <div>
            <div className="overflow-x-auto">
              <table className="min-w-full border-collapse">
                <thead>
                  <tr className="border-b text-gray-600 bg-gray-50">
                    <th className="py-3 px-4 text-left font-medium text-sm">
                      {dict?.dashboard?.remittance?.remitTime}
                    </th>
                    <th className="py-3 px-4 text-left font-medium text-sm">
                      {dict?.dashboard?.remittance?.senderName}
                    </th>
                    <th className="py-3 px-4 text-left font-medium text-sm">
                      {dict?.dashboard?.remittance?.paymentMethod}
                    </th>
                    <th className="py-3 px-4 text-left font-medium text-sm">
                      {dict?.dashboard?.remittance?.bankName}
                    </th>
                    <th className="py-3 px-4 text-left font-medium text-sm">
                      {dict?.dashboard?.remittance?.amount}
                    </th>
                    <th className="py-3 px-4 text-left font-medium text-sm">
                      {dict?.dashboard?.remittance?.status}
                    </th>
                    <th className="py-3 px-4 text-left font-medium text-sm">
                      {dict?.dashboard?.remittance?.processTime}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {orders && orders.length > 0 ? (
                    orders.map((record) => (
                      <tr key={record.id} className="border-b hover:bg-gray-50">
                        <td className="py-3 px-4 text-gray-700">
                          {typeof record.createtime === "number"
                            ? dayjs(record.createtime * 1000).format(
                                "YYYY-MM-DD HH:mm"
                              )
                            : record.createtime}
                        </td>
                        <td className="py-3 px-4 text-gray-600">
                          {record.accountname}
                        </td>
                        <td className="py-3 px-4 text-gray-600">
                          {record.payname}
                        </td>
                        <td className="py-3 px-4 text-gray-600">
                          {record.bankname}
                        </td>
                        <td className="py-3 px-4">
                          <span className="font-medium text-gray-900">
                            {/* {
                              formatCurrency(Number(record.money))
                                .formatValue
                            } */}
                            {record.currencycode} {record.money}
                          </span>
                        </td>

                        <td className="py-3 px-4 text-gray-600">
                          {record.status_text}
                        </td>
                        <td className="py-3 px-4 text-gray-700">
                          {record.handletime &&
                          typeof record.handletime === "number"
                            ? dayjs(record.handletime * 1000).format(
                                "YYYY-MM-DD HH:mm"
                              )
                            : record.handletime || "-"}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan={7}
                        className="py-8 text-center text-gray-500"
                      >
                        <Empty
                          description={dict?.dashboard?.remittance?.noRecords}
                        />
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </AntdConfigProvider>
  );
}
