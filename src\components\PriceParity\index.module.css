.priceParityTabs .ant-tabs-tab {
  font-weight: 500;
}

.priceParityTabs .ant-tabs-tab-active {
  color: #f97316;
}

.priceParityTabs .ant-tabs-ink-bar {
  background-color: #f97316;
}

.priceParityTabs .ant-tabs-content-holder {
  padding-top: 16px;
}

.productGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

@media (min-width: 640px) {
  .productGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 14px;
  }
}

@media (min-width: 768px) {
  .productGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }
}

@media (min-width: 1024px) {
  .productGrid {
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
  }
}

.emptyState {
  grid-column: 1 / -1;
  text-align: center;
  padding: 32px 0;
}
