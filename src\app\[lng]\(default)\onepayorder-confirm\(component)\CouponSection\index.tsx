'use client';

import React, { useEffect, useState } from 'react';
import { Api } from '@/request/api';
import { formatCurrency } from '@/utils/currency';

interface CouponItem {
  id: number;
  money: string;
  status: string | number; // 优惠券状态：1=未使用, 2=已使用, 3=已过期
  status_text: string;
  endtime: number; // 过期时间戳
  endtime_text: string | number;
  type_text: string;
  usetype_text: string;
  usetype: number; // Usage scope: 1=全部, 2=订单, 3=运单, 4=充值
  getway_text: string; // 获取方式：券码兑换、积分兑换等
  coupon_rule: {
    name: string;
    code: string;
  };
}

interface CouponSectionProps {
  dict: any;
}

export default function CouponSection({ dict }: CouponSectionProps) {
  const [couponList, setCouponList] = useState<CouponItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCouponId, setSelectedCouponId] = useState<number | null>(null);

  // Fetch coupon list with "All" scope filter and exclude expired coupons
  const fetchCouponList = async () => {
    try {
      setLoading(true);
      // 使用API参数过滤：只获取未使用的优惠券 (status = 1)
      const response = await Api.getCouponList({ status: 1 });

      if (response && response.success && response.data) {
        // 前端再过滤：只显示"全部"用途的优惠券 (usetype = 1)
        const allScopeCoupons = (response.data.data || []).filter((coupon: CouponItem) => {
          return coupon.usetype === 1; // 只显示"全部"用途的优惠券
        });

        console.log('Filtered All scope available coupons for one-time payment:', allScopeCoupons);
        setCouponList(allScopeCoupons);

        // 默认选择第一张优惠券
        if (allScopeCoupons.length > 0) {
          const firstCoupon = allScopeCoupons[0];
          setSelectedCouponId(firstCoupon.id);

          // 存储到 localStorage 并触发事件
          if (typeof window !== 'undefined') {
            localStorage.setItem('selectedOnePayCoupon', JSON.stringify(firstCoupon));
            window.dispatchEvent(new CustomEvent('onePayCouponChanged', {
              detail: firstCoupon
            }));
          }
        }
      } else {
        console.error('Failed to fetch coupon list:', response);
        setCouponList([]);
      }
    } catch (error) {
      console.error('Error fetching coupon list:', error);
      setCouponList([]);
    } finally {
      setLoading(false);
    }
  };

  // Initialize coupon list
  useEffect(() => {
    fetchCouponList();
  }, []);

  // Handle coupon selection
  const handleSelectCoupon = (couponId: number) => {
    const newSelectedId = selectedCouponId === couponId ? null : couponId;
    setSelectedCouponId(newSelectedId);

    // Store selected coupon in global state or localStorage for OnePayOrderSummary to access
    const selectedCoupon = couponList.find(c => c.id === newSelectedId);
    if (typeof window !== 'undefined') {
      if (selectedCoupon) {
        localStorage.setItem('selectedOnePayCoupon', JSON.stringify(selectedCoupon));
      } else {
        localStorage.removeItem('selectedOnePayCoupon');
      }
      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('onePayCouponChanged', {
        detail: selectedCoupon
      }));
    }
  };

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-base font-medium text-gray-900">{dict?.confirm?.onepayorder?.coupons?.availableCoupons || 'Available Coupons'}</h3>
        <button className="text-orange-500 text-sm hover:text-orange-600">
          {dict?.confirm?.onepayorder?.coupons?.viewAll || 'View All'}
        </button>
      </div>

      {loading ? (
        <div className="bg-gray-200 text-gray-600 px-6 py-4 rounded-lg w-48 animate-pulse">
          <div className="text-xl font-bold">{dict?.confirm?.onepayorder?.coupon?.loading || 'Loading...'}</div>
          <div className="text-sm">{dict?.confirm?.onepayorder?.coupon?.fetchingCoupons || 'Fetching coupons'}</div>
        </div>
      ) : couponList.length > 0 ? (
        <div className="flex flex-wrap gap-4">
          {couponList.map((coupon) => (
            <div
              key={coupon.id}
              className={`
                relative rounded-lg transition-all duration-300 ease-in-out
                hover:shadow-md cursor-pointer w-48 px-6 py-4 border-2
                ${selectedCouponId === coupon.id
                  ? 'bg-white border-[#FF6000] shadow-md text-gray-900'
                  : 'bg-white border-gray-200 hover:border-gray-300 text-gray-900'
                }
              `}
              onClick={() => handleSelectCoupon(coupon.id)}
            >
              {/* 右上角获取方式标签 */}
              <div className={`absolute top-2 right-2 px-2 py-1 rounded text-xs ${
                selectedCouponId === coupon.id
                  ? 'bg-[#FF6000] text-white'
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {coupon.getway_text}
              </div>

              <div className={`text-xl font-bold ${
                selectedCouponId === coupon.id ? 'text-[#FF6000]' : 'text-gray-900'
              }`}>
                {formatCurrency(parseFloat(coupon.money)).formatValue}
              </div>
              <div className="text-sm text-gray-600">{coupon.type_text}</div>
              <div className="text-sm mt-1 text-gray-500">
                {dict?.confirm?.onepayorder?.coupon?.validUntil || 'Valid until'}：{coupon.endtime !== 0 ? coupon.endtime_text : (dict?.confirm?.onepayorder?.coupon?.noExpiry || 'No expiry')}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-gray-300 text-gray-600 px-6 py-4 rounded-lg w-48">
          <div className="text-xl font-bold">{dict?.confirm?.onepayorder?.coupons?.noCoupons || 'No Coupons'}</div>
          <div className="text-sm">{dict?.confirm?.onepayorder?.coupons?.noCouponsDesc || 'No available coupons'}</div>
        </div>
      )}
    </div>
  );
}
