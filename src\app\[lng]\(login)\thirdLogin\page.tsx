"use client";

import { useState, useEffect } from "react";
import React from "react";
import { useRouter, useSearchParams, useParams } from "next/navigation";
import type { Locale } from "@/config";
import { getDictionary } from "@/dictionaries";
import { Api } from "@/request/api";
import Toast from "@/components/Toast";
import Cookies from "js-cookie";
import { info } from "console";
import ToastHelper from "@/utils/toastHelper";
export default function ThirdLoginPage() {
  const router = useRouter();
  const params = useParams();
  const lng = (params.lng as Locale) || "zh-cn";
  const [dict, setDict] = useState<any>(null); // 添加字典状态
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  useEffect(() => {
    async function loadDictionary() {
      try {
        const dictionary = await getDictionary(lng);
        setDict(dictionary);
      } catch (err) {
        console.error("加载字典失败", err);
      }
    }

    loadDictionary();
  }, [lng, searchParams]);

  useEffect(() => {
    console.log("token", token);

    const handleLogin = async () => {
      if (!token) {
        Toast.error("登录失败：缺少 token");
        router.push(`/login`);
        return;
      }
      // Base64解密token
      let decodedToken;
      try {
        decodedToken = atob(token); // 使用浏览器内置的atob函数解码Base64
        console.log("Decoded token:", decodedToken);
      } catch (error) {
        console.error("Token解码失败:", error);
        Toast.error("登录失败：无效的token格式");
        router.push(`/login`);
        return;
      }
      // 和正常登录一样存储token
      localStorage.setItem(
        "info",
        JSON.stringify({
          data: { userinfo: { token: decodedToken } }, // 按照之前的结构存
        })
      );
      try {
        let response = await Api.getUserInfo();
        console.log("第三方登录：response", response);
        let data = {
          data: { userinfo: { token: decodedToken, ...response.data } },
        };
        console.log("第三方登录：data", data);
        localStorage.setItem("info", JSON.stringify(data));

        // 根据后端类型设置正确的token cookie名称
        const tokenName =
          process.env.NEXT_PUBLIC_BACKEND_TYPE === "6"
            ? "access_token"
            : "token";

        Cookies.set(tokenName, decodedToken, {
          expires: 30,
          path: "/",
        });

        // 处理未登录时添加购物车商品
        handleLoginSuccess();

        // 获取callback参数，如果存在则跳转到原始页面，否则跳转到首页
        const callback = searchParams.get("callback");

        if (callback) {
          // 确保callback是一个有效的内部路径
          if (callback.startsWith("/")) {
            router.push(callback);
          } else {
            router.push(`/${lng}`);
          }
        } else {
          router.push(`/${lng}`);
        }
      } catch (error) {
        console.error("登录处理失败:", error);
        Toast.error("登录失败，请重试");
        router.push(`/${lng}`);
      }
    };

    handleLogin();
  }, [token, lng, router, searchParams]);
  // 在用户登录成功后的回调中
  const handleLoginSuccess = async () => {
    // 检查本地是否有存储的购物车数据
    const localCartItemsStr = localStorage.getItem("localCartItems");
    const localCartItems = localCartItemsStr
      ? JSON.parse(localCartItemsStr)
      : [];

    if (localCartItems.length > 0) {
      const results = [];
      const failedItems = [];

      // 逐个添加商品，记录成功和失败的情况
      for (const item of localCartItems) {
        try {
          const result = await Api.addCart(item);
          if (result.success) {
            results.push({ success: true, item });
          } else {
            results.push({ success: false, item, error: result.msg || ToastHelper.getLocalizedMessage('add_cart_failed', lng) });
            failedItems.push(item);
          }
        } catch (error) {
          console.error('添加商品到购物车失败:', error);
          results.push({ success: false, item, error: (error as Error).message || ToastHelper.getLocalizedMessage('network_error', lng) });
          failedItems.push(item);
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failedCount = failedItems.length;

      // 如果有失败的商品，保留在本地存储中
      if (failedItems.length > 0) {
        localStorage.setItem('localCartItems', JSON.stringify(failedItems));
      } else {
        // 全部成功，清空本地存储
        localStorage.removeItem('localCartItems');
      }

      // 触发购物车更新事件，通知所有相关组件刷新数据
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('cartUpdated'));
        window.dispatchEvent(new CustomEvent('loginSuccess'));
      }

      // 给用户反馈
      if (successCount > 0 && failedCount === 0) {
        Toast.success(`${successCount}件商品已加入购物车`);
      } else if (successCount > 0 && failedCount > 0) {
        Toast.warning(`${successCount}件商品已加入购物车，${failedCount}件商品添加失败`);
      } else if (failedCount > 0) {
        Toast.error('商品加入购物车失败，请稍后重试');
      }
    }
  };

  return (
    <div className="min-h-screen flex m-y-30">
      <div className="w-full text-center">{dict?.login?.login || '登录中..'}</div>
    </div>
  );
}
