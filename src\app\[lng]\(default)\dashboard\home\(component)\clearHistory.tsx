'use client'

import { Api } from '@/request/api'
import { useState } from 'react'
import { message } from 'antd'
import ModalComponent from '@/components/Modal'
import ButtonComponent from '@/components/Button'
import { useRouter } from 'next/navigation'

export default function clearHistory({style,dict}:{style:React.CSSProperties,dict:any}) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false)

  const handleClear = async () => {
    setLoading(true)
    await Api.clearHistoryRecord()
    setOpen(false)
    setLoading(false)
    router.refresh()
  }

  return (
    <div style={style}>
      <ButtonComponent color="danger" variant="link" onClick={() => setOpen(true)}>
        {dict.dashboard.home.history.clearAll}
      </ButtonComponent>
      <ModalComponent
        title={dict.dashboard.home.history.confirmClear}
        open={open}
        onOk={handleClear}
        onCancel={() => setOpen(false)}
        okText={dict.dashboard.home.history.confirm}
        cancelText={dict.dashboard.home.history.cancel}
        confirmLoading={loading}
      >
        <p>{dict.dashboard.home.history.clearWarning}</p>
      </ModalComponent>
    </div>
  )
}