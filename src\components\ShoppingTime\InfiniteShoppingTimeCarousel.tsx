'use client';

import { useState, useEffect, useRef, useMemo } from 'react';
import Image from 'next/image';
import { ShoppingTimeProduct } from "@/types/product";
import { prepareImageForNextJs } from '@/utils/imageUtils';
import { formatCurrency } from '@/utils/currency';
import styles from './ShoppingTimeCarousel.module.css';

interface ShoppingTimeCarouselProps {
  products: ShoppingTimeProduct[];
  dict: any;
}

interface UserGroup {
  user: {
    username: string;
    nickname: string;
  };
  products: ShoppingTimeProduct[];
  latestTime: string;
  totalItems: number;
}

export default function InfiniteShoppingTimeCarousel({ products, dict }: ShoppingTimeCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0); // 初始为0，后面会根据数据调整
  const [isTransitioning, setIsTransitioning] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isResetting = useRef(false);

  // 按用户nickname进行分组聚合商品数据
  const userGroups = useMemo(() => {
    const groupMap = new Map<string, UserGroup>();

    products.forEach(product => {
      // 优先使用nickname作为分组键，如果没有nickname则使用username，最后使用user_id
      const groupKey = product.user?.nickname || product.user?.username || `user_${product.user_id}` || 'unknown';

      if (groupMap.has(groupKey)) {
        const group = groupMap.get(groupKey)!;
        group.products.push(product);
        group.totalItems += product.goodsnum;
        // 更新最新时间
        if (new Date(product.createtime) > new Date(group.latestTime)) {
          group.latestTime = product.createtime;
        }
      } else {
        // 创建新的用户组
        groupMap.set(groupKey, {
          user: product.user,
          products: [product],
          latestTime: product.createtime,
          totalItems: product.goodsnum
        });
      }
    });

    // 按最新购买时间排序，最新的在前面
    return Array.from(groupMap.values()).sort((a, b) =>
      new Date(b.latestTime).getTime() - new Date(a.latestTime).getTime()
    );
  }, [products]);

  // 创建扩展的轮播数据（克隆首尾元素实现无限循环）
  const carouselItems = useMemo(() => {
    if (userGroups.length === 0) return [];
    if (userGroups.length === 1) {
      // 如果只有一个元素，不需要无限循环
      return userGroups;
    }

    // 克隆最后一个元素到开头，克隆第一个元素到结尾
    return [
      userGroups[userGroups.length - 1], // 克隆最后一个
      ...userGroups, // 原始数据
      userGroups[0] // 克隆第一个
    ];
  }, [userGroups]);

  // 设置正确的初始索引
  useEffect(() => {
    if (carouselItems.length > 1) {
      // 如果有克隆元素，从索引1开始（跳过克隆的最后一个元素）
      setCurrentIndex(1);
    } else {
      // 如果只有一个元素或没有元素，从索引0开始
      setCurrentIndex(0);
    }
  }, [carouselItems.length]);

  // 自动播放
  useEffect(() => {
    if (userGroups.length <= 1) return; // 只有在有多个用户组时才自动播放

    const startAutoPlay = () => {
      intervalRef.current = setInterval(() => {
        handleNext();
      }, 4000);
    };

    const stopAutoPlay = () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };

    startAutoPlay();

    return () => stopAutoPlay();
  }, [userGroups.length]);

  // 处理过渡结束后的边界重置
  useEffect(() => {
    if (!isTransitioning || isResetting.current) return;

    const timer = setTimeout(() => {
      // 检查是否需要重置位置
      if (currentIndex === 0) {
        // 从克隆的最后一个跳到真实的最后一个
        isResetting.current = true;
        setCurrentIndex(userGroups.length);

        // 立即重置，无动画
        setTimeout(() => {
          isResetting.current = false;
          setIsTransitioning(false);
        }, 50);
      } else if (currentIndex === carouselItems.length - 1) {
        // 从克隆的第一个跳到真实的第一个
        isResetting.current = true;
        setCurrentIndex(1);

        // 立即重置，无动画
        setTimeout(() => {
          isResetting.current = false;
          setIsTransitioning(false);
        }, 50);
      } else {
        setIsTransitioning(false);
      }
    }, 500); // 与CSS transition时间匹配

    return () => clearTimeout(timer);
  }, [currentIndex, isTransitioning, userGroups.length, carouselItems.length]);

  const handleNext = () => {
    if (isTransitioning || isResetting.current) return;
    setIsTransitioning(true);
    setCurrentIndex(prev => prev + 1);
  };

  const handlePrev = () => {
    if (isTransitioning || isResetting.current) return;
    setIsTransitioning(true);
    setCurrentIndex(prev => prev - 1);
  };

  // 渲染用户卡片
  const renderUserCard = (userGroup: UserGroup, index: number) => {
    const productCount = userGroup.products.length;
    let minWidth = 'min-w-[320px]';

    if (productCount >= 6) {
      minWidth = 'min-w-[920px]';
    } else if (productCount >= 3) {
      minWidth = 'min-w-[920px]';
    } else if (productCount >= 2) {
      minWidth = 'min-w-[620px]';
    }

    return (
      <div
        key={`user-${userGroup.user.username}-${index}`}
        className={`bg-white rounded-lg p-4 shadow-sm border border-gray-100 ${minWidth} w-full flex-shrink-0 h-full`}
      >
        <div className="flex items-center space-x-2 mb-4">
          <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
            {(userGroup.user.nickname || userGroup.user.username || 'U').charAt(0).toUpperCase()}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-gray-800 text-sm truncate">
              {userGroup.user.nickname || userGroup.user.username}
            </h3>
          </div>
        </div>

        <div className={styles.productsContainer}>
          <div className={`${styles.productsList} ${styles.flexWrap} ${styles.justifyStart}`}>
            {userGroup.products.slice(0, 3).map((item, idx) => {
              const titleLength = item.goodsname?.length || 0;
              const titleLengthClass = titleLength <= 10 ? 'short' : titleLength <= 20 ? 'medium' : 'long';
              const detailUrl = item.goodsurl
                ? `/detail/${item.goodssite || 'taobao'}/?url=${encodeURIComponent(item.goodsurl)}`
                : '#';

              return (
                <a
                  key={`product-${item.id || item.goodsimg || idx}`}
                  href={detailUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`${styles.productCard} ${styles.adaptive} group`}
                  data-title-length={titleLengthClass}
                >
                  <div className={styles.productImage}>
                    <Image
                      src={prepareImageForNextJs(item.goodsimg)}
                      alt={item.goodsname || '商品图片'}
                      fill
                      sizes="(max-width: 480px) 150px, (max-width: 768px) 250px, 288px"
                      className="object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/images/default.jpg';
                      }}
                    />
                    {item.recommend === 1 && (
                      <div className={styles.recommendTag}>推荐</div>
                    )}
                  </div>
                  <div className="p-3 flex-1 flex flex-col">
                    <h3 className="text-md text-gray-800 line-clamp-2 mb-2 custom-h-48px">
                      {item.goodsname}
                    </h3>
                    <div className="flex items-center justify-between mt-auto">
                      <span className="text-[#FF6B00] font-medium">
                        <span className='text-sm sm:text-base md:text-lg lg:text-xl font-bold'>{formatCurrency(Number(item.goodsprice)).formatValue}</span>
                      </span>
                    </div>
                  </div>
                </a>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  if (userGroups.length === 0) {
    return (
      <div className="text-center text-gray-500 py-12">
        {dict?.home?.emptyList || '列表为空'}
      </div>
    );
  }

  return (
    <div className="relative w-full group px-8">
      <div className="overflow-hidden rounded-lg">
        <div
          ref={containerRef}
          className="flex"
          style={{
            transform: `translateX(-${currentIndex * 100}%)`,
            transition: (isTransitioning && !isResetting.current) ? 'transform 0.5s ease-in-out' : 'none'
          }}
        >
          {carouselItems.map((userGroup, index) => (
            <div key={`carousel-${index}`} className="w-full flex-shrink-0">
              {renderUserCard(userGroup, index)}
            </div>
          ))}
        </div>
      </div>

      {/* 导航按钮 */}
      {userGroups.length > 1 && (
        <>
          <button
            className="absolute -left-2 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-gray-700 hover:text-orange-600 p-3 rounded-full shadow-lg transition-all duration-300 z-30 border border-gray-200 opacity-0 group-hover:opacity-100"
            onClick={handlePrev}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            className="absolute -right-2 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-gray-700 hover:text-orange-600 p-3 rounded-full shadow-lg transition-all duration-300 z-30 border border-gray-200 opacity-0 group-hover:opacity-100"
            onClick={handleNext}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      )}
    </div>
  );
}
