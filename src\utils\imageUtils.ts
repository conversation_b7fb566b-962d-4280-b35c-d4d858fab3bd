/**
 * 图片URL处理工具函数
 */

/**
 * 处理协议相对URL，将 // 开头的URL转换为 https:// 开头的绝对URL
 * @param url - 原始图片URL
 * @param fallbackUrl - 当URL无效时的备用URL，默认为 '/images/default.jpg'
 * @returns 处理后的绝对URL
 */
export function normalizeImageUrl(url: string | null | undefined, fallbackUrl: string = '/images/default.jpg'): string {
  // 如果URL为空或无效，返回备用URL
  if (!url || typeof url !== 'string' || url.trim() === '') {
    return fallbackUrl;
  }

  const trimmedUrl = url.trim();

  // 如果是协议相对URL（以 // 开头），添加 https: 前缀
  if (trimmedUrl.startsWith('//')) {
    return `https:${trimmedUrl}`;
  }

  // 如果已经是绝对URL（http:// 或 https://），直接返回
  if (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://')) {
    return trimmedUrl;
  }

  // 如果是相对路径（以 / 开头），直接返回
  if (trimmedUrl.startsWith('/')) {
    return trimmedUrl;
  }

  // 其他情况，可能是相对路径，直接返回
  return trimmedUrl;
}

/**
 * 检查URL是否为有效的图片URL
 * @param url - 要检查的URL
 * @returns 是否为有效的图片URL
 */
export function isValidImageUrl(url: string | null | undefined): boolean {
  if (!url || typeof url !== 'string') {
    return false;
  }

  const trimmedUrl = url.trim();
  
  // 检查是否为空字符串
  if (trimmedUrl === '') {
    return false;
  }

  // 检查是否包含常见的图片文件扩展名
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp'];
  const hasImageExtension = imageExtensions.some(ext => 
    trimmedUrl.toLowerCase().includes(ext)
  );

  // 如果包含图片扩展名，认为是有效的
  if (hasImageExtension) {
    return true;
  }

  // 检查是否为阿里云CDN链接（通常不包含扩展名）
  if (trimmedUrl.includes('alicdn.com') || trimmedUrl.includes('aliyuncs.com')) {
    return true;
  }

  // 检查是否为其他CDN链接
  if (trimmedUrl.includes('cdn.') || trimmedUrl.includes('.cdn.')) {
    return true;
  }

  // 如果是以 http、https 或 // 开头的URL，认为可能是有效的
  if (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://') || trimmedUrl.startsWith('//')) {
    return true;
  }

  // 如果是相对路径，认为是有效的
  if (trimmedUrl.startsWith('/')) {
    return true;
  }

  return false;
}

/**
 * 获取安全的图片URL，结合了URL标准化和有效性检查
 * @param url - 原始图片URL
 * @param fallbackUrl - 备用URL
 * @returns 安全的图片URL
 */
export function getSafeImageUrl(url: string | null | undefined, fallbackUrl: string = '/images/default.jpg'): string {
  if (!isValidImageUrl(url)) {
    return fallbackUrl;
  }
  
  return normalizeImageUrl(url, fallbackUrl);
}

/**
 * 为Next.js Image组件准备图片URL
 * 确保URL符合Next.js Image组件的要求
 * @param url - 原始图片URL
 * @param fallbackUrl - 备用URL
 * @returns 适用于Next.js Image组件的URL
 */
export function prepareImageForNextJs(url: string | null | undefined, fallbackUrl: string = '/images/default.jpg'): string {
  return getSafeImageUrl(url, fallbackUrl);
}
