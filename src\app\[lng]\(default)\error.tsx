"use client"

import { useRouter, usePathname } from 'next/navigation'
import { Button } from 'antd'
import { useEffect, useState } from 'react'
import { getDictionary } from '@/dictionaries'
import type { Locale } from '@/config'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
    const router = useRouter()
    const pathname = usePathname()
    const [dict, setDict] = useState<any>(null)
    const [lng, setLng] = useState<Locale>('zh-cn')

    useEffect(() => {
        // 可以在这里记录错误到错误报告服务
        console.error(error)

        // 从路径中获取语言
        const pathSegments = pathname.split('/')
        const currentLng = pathSegments[1] && ['en', 'zh-cn', 'ja'].includes(pathSegments[1])
            ? pathSegments[1] as Locale
            : 'zh-cn'

        setLng(currentLng)

        // 加载对应语言的字典
        const loadDictionary = async () => {
            try {
                const dictionary = await getDictionary(currentLng)
                setDict(dictionary)
            } catch (error) {
                console.error('Failed to load dictionary:', error)
            }
        }

        loadDictionary()
    }, [error, pathname])

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="text-center px-4">
                <div className="animate-float">
                    <div className="text-[var(--base-color)] text-8xl mb-4">
                        <span className="i-material-symbols:error-outline"></span>
                    </div>
                </div>
                <h1 className="text-4xl font-bold mb-4 text-[var(--base-color)] animate-slide-up">
                    {dict?.error?.systemError || '出错了'}
                </h1>
                <p className="text-gray-600 mb-8 animate-slide-up-delay-200">
                    {dict?.error?.dontWorry || '抱歉，页面加载时发生了一些问题'}
                </p>
                {error.message && (
                    <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6 animate-slide-up-delay-300">
                        <p className="text-red-700 font-medium">
                            {dict?.common?.errorMessage || (lng === 'en' ? 'Error Message:' : lng === 'ja' ? 'エラーメッセージ:' : '错误信息:')}
                        </p>
                        <p className="text-red-600">{error.message}</p>
                        {error.stack && (
                            <details className="mt-2">
                                <summary className="text-red-500 cursor-pointer">
                                    {dict?.common?.viewDetails || (lng === 'en' ? 'View Details' : lng === 'ja' ? '詳細を見る' : '查看详情')}
                                </summary>
                                <pre className="text-left text-xs text-red-500 mt-2 whitespace-pre-wrap">
                                    {error.stack}
                                </pre>
                            </details>
                        )}
                    </div>
                )}
                <div className="flex justify-center space-x-4 animate-slide-up-delay-400">
                    <Button
                        type="primary"
                        size="large"
                        onClick={() => reset()}
                        className="bg-[var(--base-color)] hover:bg-[var(--base-color-hover)]"
                    >
                        {dict?.common?.retry || '重试'}
                    </Button>
                    <Button
                        type="default"
                        size="large"
                        onClick={() => router.back()}
                    >
                        {dict?.common?.goBack || (lng === 'en' ? 'Go Back' : lng === 'ja' ? '戻る' : '返回上一页')}
                    </Button>
                </div>
            </div>
        </div>
    )
}
