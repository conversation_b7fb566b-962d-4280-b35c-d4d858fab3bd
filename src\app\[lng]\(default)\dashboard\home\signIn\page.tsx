"use client";
import Signin from "../(component)/signin";
import { <PERSON><PERSON>, <PERSON>, But<PERSON>, Modal, Empty, Table } from "antd";
import AntdConfigProvider from "@/components/AntdConfigProvider";
import React, { useState, useEffect } from "react";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import Toast from "@/components/Toast";
import { useParams } from "next/navigation";
import { getDictionary } from "@/dictionaries";
import { Api } from "@/request/api";
import zhCN from "antd/locale/zh_CN";

export default function SigninPage() {
  const { lng } = useParams();
  const [dict, setDict] = useState<any>(null);
  const isTp6 = process.env.NEXT_PUBLIC_BACKEND_TYPE === "6";
  const [value, setValue] = useState(() => dayjs());
  const [selectedValue, setSelectedValue] = useState(() => dayjs());

  const [signinList, setSigninList] = useState([]);
  const [signinRulesList, setSigninRulesList] = useState([]);
  const [signinRankList, setSigninRankList] = useState([]);

  const [isSignModalOpen, setIsSignModalOpen] = useState(false);
  const [isAppendSignModalOpen, setIsAppendSignModalOpen] = useState(false);
  const [isRankModalOpen, setIsRankModalOpen] = useState(false);
  const [isRulesModalOpen, setIsRulesModalOpen] = useState(false);

  // 获取字典
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error("Failed to load dictionary:", error);
      }
    };
    fetchDictionary();
  }, [lng]);
  // 获取签到列表
  useEffect(() => {
    const getSigninList = async () => {
      try {
        const response = await Api.getSigninList({
          size: 31,
          date: dayjs().format("YYYY-MM-DD HH:mm:ss"),
        });
        console.log(response, "getSigninList");
        if (response.success) {
        } else {
          Toast.error(response.data || response.msg);
        }
      } catch (error) {
        console.log(error, "获取失败");
      }
    };
    getSigninList();
  }, []);
  // 获取签到排行榜
  useEffect(() => {
    const getSigninRank = async () => {
      try {
        const response = await Api.getSigninRank({ size: 10 });
        console.log(response, "获取签到排名");
        if (response.success) {
          setSigninRankList(response.data);
        } else {
          Toast.error(response.data || response.msg);
        }
      } catch (error) {
        console.log(error, "获取失败");
      }
    };
    getSigninRank();
  }, []);
  useEffect(() => {
    const loadConfig = async () => {
      const res = await Api.getConfigList();
      console.log(res, "siteConfig");
    };
    loadConfig();
  }, []);
  const onSelect = (newValue: Dayjs) => {
    const today = dayjs();
    const selectedDate = newValue.startOf("day");
    const dateStr = selectedDate.format("YYYY-MM-DD");

    setValue(newValue);
    setSelectedValue(newValue);

    if (selectedDate.isBefore(today, "day")) {
      setIsAppendSignModalOpen(true);
    } else if (selectedDate.isSame(today, "day")) {
      setIsSignModalOpen(true);
    } else {
      Toast.warning("不能签到未来日期");
    }
  };

  const onPanelChange = (newValue: Dayjs) => {
    console.log(newValue, "onPanelChange");
    setValue(newValue);
  };
  // 补签modal OK
  const handleAppendSignModalOpenOk = async () => {
    try {
      const response = await Api.appendSignin({
        date: selectedValue?.format("YYYY-MM-DD"),
      });
      console.log(response);
      if (response.success) {
        Toast.success("补签成功");
      } else {
        Toast.error(response.data || response.msg);
      }
      setIsAppendSignModalOpen(false);
    } catch (error) {
      Toast.error("补签失败");
      setIsAppendSignModalOpen(false);
    }
  };
  // 签到modal OK
  const handleSigninOk = async () => {
    try {
      const response = await Api.setSignin();
      if (response.success) {
        Toast.success(dict.dashboard.home.profile.signinSuccessMsg);
      } else {
        Toast.error(response.data || response.msg);
      }
      setIsSignModalOpen(false);
    } catch (error) {
      Toast.error(dict.dashboard.home.profile.signinErrorMsg);
      setIsSignModalOpen(false);
    }
  };
  // 排行榜modal OK
  const handleRankModalOpenOk = async () => {
    try {
      const response = await Api.setSignin();
      console.log(response);
      if (response.success) {
        Toast.success(dict.dashboard.home.profile.signinSuccessMsg);
      } else {
        Toast.error(response.data || response.msg);
      }
      setIsSignModalOpen(false);
    } catch (error) {
      console.log(error, "签到失败");
      Toast.error(dict.dashboard.home.profile.signinErrorMsg);
      setIsSignModalOpen(false);
    }
  };
  const columnsRank: any[] = [
    {
      title: "头像",
      dataIndex: "user.avatar",
      key: "name",
      render: (url:string) => <div><img src="url" alt="" className="w-20 h-20 rounded-full"/></div>,
    },
    {
      title: "昵称",
      dataIndex: "user.nickname",
      key: "age",
    },
    {
      title: "连续签到",
      dataIndex: "user.days",
      key: "days",
      render: (text:number) => <a>{text}天</a>,
    },
  ];

  const data: any[] = [
    {
      key: "1",
      name: "John Brown",
      age: 32,
      address: "New York No. 1 Lake Park",
      tags: ["nice", "developer"],
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      tags: ["loser"],
    },
    {
      key: "3",
      name: "Joe Black",
      age: 32,
      address: "Sydney No. 1 Lake Park",
      tags: ["cool", "teacher"],
    },
  ];

  return (
    <>
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold my-4">我的签到</h1>
        <div className="flex gap-4">
          <Button onClick={() => setIsRankModalOpen(true)}>排行榜</Button>
          <Button>签到积分规则</Button>
          <Button type="primary" style={{ backgroundColor: "#FF6000" }}>
            签到
          </Button>
        </div>
      </div>
      <Alert
        message={`你当前已经连续签到 1 天，明天继续签到可获得 2 积分${selectedValue?.format(
          "YYYY-MM-DD"
        )}`}
      />
      <AntdConfigProvider>
        <Calendar
          value={value}
          onSelect={onSelect}
          onPanelChange={onPanelChange}
        />
        {/* 签到modal */}
        <Modal
          title="签到"
          centered
          open={isSignModalOpen}
          onOk={handleSigninOk}
          onCancel={() => setIsSignModalOpen(false)}
        >
          <div className="max-h-[600px] flex gap-2 flex-wrap overflow-auto">
            确认签到吗？
          </div>
        </Modal>

        {/* 补签modal */}
        <Modal
          title="补签"
          centered
          open={isAppendSignModalOpen}
          onOk={handleAppendSignModalOpenOk}
          onCancel={() => setIsAppendSignModalOpen(false)}
        >
          <div className="max-h-[600px] flex gap-2 flex-wrap overflow-auto">
            确认进行补签日期：{selectedValue.format("YYYY-MM-DD")}？
            补签将消耗100积分
          </div>
        </Modal>

        {/* 排行榜modal */}
        <Modal
          title="排行榜"
          centered
          open={isRankModalOpen}
          onOk={() => setIsRankModalOpen(false)}
          footer={[
            <Button
              key="submit"
              type="primary"
              onClick={() => setIsRankModalOpen(false)}
            >
              确定
            </Button>,
          ]}
        >
          <div className="max-h-[600px] flex gap-2 flex-wrap overflow-auto align-middle justify-center">
            {signinRankList.length ? (
              <Table dataSource={data} columns={columnsRank} />
            ) : (
              <Empty description="暂无数据" />
            )}
          </div>
        </Modal>
      </AntdConfigProvider>
    </>
  );
}
