'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import ButtonComponent from '@/components/Button';
import { Modal } from 'antd';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { getDictionary } from "@/dictionaries";
import { Api } from '@/request/api';
import Toast from '@/components/Toast';

export default function SharePromotionPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const lng = params.lng as string;
  const [dict, setDict] = useState<any>(null);

  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };

    fetchDictionary();
  }, [lng]);

  useEffect(() => {
    const uc = searchParams.get('uc');
    const n = searchParams.get('n');

    if (uc && n) {
      const claimCoupon = async () => {
        try {
          const res = await Api.claimSharePromotion({ uc, n });
          if (res.success) {
            Toast.success(res.msg || '领取成功');
            // Optionally redirect or update UI
          } else {
            Toast.error(res.msg || '领取失败');
          }
        } catch (error) {
          Toast.error('网络错误，请稍后重试');
        }
      };
      claimCoupon();
    }
  }, [searchParams]);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const gotoReferral = async () => {
    try {
      // 检查推广联盟pro插件是否启用
      const { isPluginEnabled } = await import('@/utils/plugin');
      const pluginEnabled = await isPluginEnabled('invitefriendspro');

      // 根据插件状态决定跳转到哪个版本
      const targetPath = pluginEnabled ? '/dashboard/referralpro' : '/dashboard/referral';
      router.push(`/${lng}${targetPath}`);
    } catch (error) {
      console.error('检查插件状态失败:', error);
      // 如果检查失败，默认跳转到基础版
      router.push(`/${lng}/dashboard/referral`);
    }
  };

  return (
    <div className="w-full flex flex-col items-center">
      {/* 顶部横幅区域 */}
      <div className="w-full bg-[#ffebea] bg-[url('/images/sharepromotion.jpg')] bg-cover bg-center py-40 relative flex justify-center">
        <div className="max-w-[1200px] w-full px-5 relative z-10">
          <h1 className="text-6xl font-bold text-[#333] mb-4 md:text-6xl sm:text-2xl ">{process.env.NEXT_PUBLIC_BASE_NAME} {dict?.sharepromotion?.mainTitle}</h1>
          <p className="text-lg text-[#666] mb-6 md:text-2xl">{dict?.sharepromotion?.subTitle}</p>
          <ButtonComponent type="primary" size='large' className="!bg-[#ff7800] !border-[#ff7800] text-xl h-16 px-14 mr-8 rounded-md font-semibold" onClick={showModal}>
            {dict?.sharepromotion?.ruleTitle}
          </ButtonComponent>
          <ButtonComponent type="default" size='large' className="!border-[#ff7800] !text-[#ff7800] text-xl h-16 px-14 bg-white rounded-md font-semibold" onClick={gotoReferral}>
            {dict?.sharepromotion?.joinNow}
          </ButtonComponent>
        </div>
      </div>

      {/* 推广步骤区域 */}
      <div className="w-full max-w-[1200px] py-16 px-5">
        <h2 className="text-2xl font-bold mb-12 text-[#333] text-center">{dict?.sharepromotion?.stepsTitle}</h2>
        
        <div className="grid grid-cols-3 gap-6 md:grid-cols-3 sm:grid-cols-1 sm:gap-12">
          <div className="flex flex-col items-center">
            <div className="w-20 h-20 bg-[#fff0ee] rounded-full flex items-center justify-center mb-5">
              <Image 
                src="/images/step-register.svg" 
                alt={dict?.sharepromotion?.register}
                width={50} 
                height={50} 
              />
            </div>
            <h3 className="text-lg font-bold text-[#333] mb-2">STEP 1</h3>
            <p className="text-base text-[#666]">{dict?.sharepromotion?.step1}</p>
          </div>
          
          <div className="flex flex-col items-center">
            <div className="w-20 h-20 bg-[#fff0ee] rounded-full flex items-center justify-center mb-5">
              <Image 
                src="/images/step-share.svg" 
                alt={dict?.sharepromotion?.share}
                width={50} 
                height={50} 
              />
            </div>
            <h3 className="text-lg font-bold text-[#333] mb-2">STEP 2</h3>
            <p className="text-base text-[#666]">{dict?.sharepromotion?.step2}</p>
          </div>
          
          <div className="flex flex-col items-center">
            <div className="w-20 h-20 bg-[#fff0ee] rounded-full flex items-center justify-center mb-5">
              <Image 
                src="/images/step-earn.svg" 
                alt={dict?.sharepromotion?.step3} 
                width={50} 
                height={50} 
              />
            </div>
            <h3 className="text-lg font-bold text-[#333] mb-2">STEP 3</h3>
            <p className="text-base text-[#666]">{dict?.sharepromotion?.step3}</p>
          </div>
        </div>
      </div>

      {/* 推广规则弹窗 */}
      <AntdConfigProvider>
        <Modal
          title={dict?.sharepromotion?.ruleTitle}
          open={isModalOpen}
          onCancel={handleCancel}
          footer={null}
          width={700}
          centered
        >
          <div className="py-5">
            <h3 className="text-sm font-bold text-[#333] mt-0 mb-2">{dict?.sharepromotion?.qualificationTitle}</h3>
            <p className="text-sm text-[#666] leading-7 mb-2">{dict?.sharepromotion?.qualification1}</p>
            
            <h3 className="text-sm font-bold text-[#333] mt-5 mb-2">{dict?.sharepromotion?.commissionTitle}</h3>
            <p className="text-sm text-[#666] leading-7 mb-2">{dict?.sharepromotion?.commission1}</p>
            <p className="text-sm text-[#666] leading-7 mb-2">{dict?.sharepromotion?.commission2}</p>
            
            <h3 className="text-sm font-bold text-[#333] mt-5 mb-2">{dict?.sharepromotion?.methodTitle}</h3>
            <p className="text-sm text-[#666] leading-7 mb-2">{dict?.sharepromotion?.method1}</p>
            
            <h3 className="text-sm font-bold text-[#333] mt-5 mb-2">{dict?.sharepromotion?.upgradeTitle}</h3>
            <p className="text-sm text-[#666] leading-7 mb-2">{dict?.sharepromotion?.upgrade1}</p>
            
            <h3 className="text-sm font-bold text-[#333] mt-5 mb-2">{dict?.sharepromotion?.penaltyTitle}</h3>
            <p className="text-sm text-[#666] leading-7 mb-2">{dict?.sharepromotion?.penalty1}</p>
          </div>
        </Modal>
      </AntdConfigProvider>
    </div>
  );
}
