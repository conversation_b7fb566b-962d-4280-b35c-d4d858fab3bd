'use client'
import React, { useState, useEffect, useMemo } from 'react'
import OrderItem from '../confirm/(component)/OrderItem'
import PreviewOrderSummary from './PreviewOrderSummary'
import AddressSelect from '../confirmpackage/(components)/AddressSelect'
import ShippingMethodsDisplay from './(components)/ShippingMethodsDisplay'
import { App } from 'antd'
import { Locale } from '@/config'
import { Api } from '@/request/api'

interface CartItem {
    id: number;
    mall_goods_id: number;
    goods_id: string;
    goodsname: string;
    goodsimg: string;
    goodsprice: string;
    goodsnum: number;
    skuname: string;
    [key: string]: any;
}

interface PreviewPageClientProps {
    lng: Locale;
    dict: any;
    searchKey: string;
}
interface FeeInfo {
    freightdiscount:number,
    freight:number,
    oldfreight:number
}

export default function PreviewPageClient({ lng, dict, searchKey }: PreviewPageClientProps) {
    const [selectedAddressId, setSelectedAddressId] = useState<number | null>(null);
    const [selectedAddress_Id, setSelectedAddress_Id] = useState<number | null>(null);
    const [selectedShippingMethod, setSelectedShippingMethod] = useState<number | null>(null);
    const [productList, setProductList] = useState<CartItem[]>([]);
    const [shippingFee, setShippingFee] = useState<number>(0);
    const [shippingFeeInfo, setShippingFeeInfo] = useState<FeeInfo>();
    const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
    
    useEffect(() => {
        // Read data from localStorage, this must be executed in client environment
        let rawItem = window.localStorage.getItem(searchKey);
        if (rawItem) {
            let items = JSON.parse(rawItem);
            setProductList(items);
        }
    }, [searchKey]);

    // Calculate total weight and volume of products
    const totalWeight = useMemo(() => {
        return productList.reduce((sum: number, item: any) => sum + (item.goodsweight || 0), 0);
    }, [productList]);

    const totalVolume = useMemo(() => {
        return productList.reduce((sum: number, item: any) => sum + (item.goodsvolume || 0), 0);
    }, [productList]);

    // Calculate total price of products
    const totalPrice = useMemo(() => {
        return productList.reduce((sum: number, item: any) => sum + parseFloat(item.goodsprice) * item.goodsnum, 0);
    }, [productList]);
    
    let isOpen = false;

    // Product types
    const goodsType = productList.map((item: any) => item.goodstype)

    const handleAddressSelect = (addressId: number, id: number) => {
        setSelectedAddressId(addressId);
        setSelectedAddress_Id(id);
        // Reset shipping method when address changes
        setSelectedShippingMethod(null);
        setShippingFee(0);
    };

    // 预演包裹页面不需要用户选择运输方式，但需要自动选择第一个可用的运输方式
    const handleShippingMethodsLoaded = (methods: any[]) => {
        if (methods.length > 0 && !selectedShippingMethod) {
            // 自动选择第一个运输方式
            const firstMethod = methods[0];
            setSelectedShippingMethod(firstMethod.template_id);
            console.log('Auto selected shipping method:', firstMethod);
        }
    };

    // Update URL parameters
    useEffect(() => {
        const searchParams = new URLSearchParams(window.location.search);
        if (selectedShippingMethod) {
            searchParams.set('template_id', selectedShippingMethod.toString());
        }
        if (selectedAddress_Id) {
            searchParams.set('address_id', selectedAddress_Id.toString());
        }
        const newUrl = `${window.location.pathname}?${searchParams.toString()}`;
        window.history.replaceState({}, '', newUrl);
    }, [selectedShippingMethod, selectedAddress_Id]);
    
    return (
        <>
            {/* Address card area */}
            <div className="bg-white rounded-lg mb-6 p-4">
                <AddressSelect onSelect={handleAddressSelect} />
            </div>

            <h2 className="text-lg font-medium mb-3 pt-10">{dict?.confirm?.order?.transport?.title}</h2>
            
            {/* 运输方式信息展示区域（仅供预览，自动选择第一个） */}
            <div className="mb-6">
                <ShippingMethodsDisplay
                    dict={dict}
                    addressId={selectedAddressId}
                    weight={totalWeight}
                    volume={totalVolume}
                    goodstype={isTp5 ? goodsType : goodsType.join(',')}
                    stotalmoney={totalPrice}
                    onMethodsLoaded={handleShippingMethodsLoaded}
                />
            </div>
            
            {/* Use flex layout to create left and right columns */}
            <div className="flex flex-col md:flex-row gap-6 pt-10">
                <div className="flex-1">
                    <div className="mb-4">
                        <h2 className="text-lg font-medium mb-3">{dict?.confirm?.order?.productInfo}</h2>
                        <div className="bg-white p-4 rounded-lg">
                            <OrderItem products={productList}  dict={dict} showAccessorialService={false}/>
                        </div>
                    </div>
                </div>

                {/* Right sidebar - Order summary */}
                <div className="md:w-[380px]">
                    <PreviewOrderSummary 
                        couponIsOpen={isOpen} 
                        dict={dict} 
                        localkey={searchKey}
                        products={productList} 
                        totalProductPrice={totalPrice}
                        shippingFee={shippingFee}
                        order_goods_ids={productList.map(item => item.id)}
                        type={'previewpackage'}
                        shippingFeeInfo={shippingFeeInfo || { freightdiscount: 0, freight: 0, oldfreight: 0 }}
                    />
                </div>
            </div>
        </>
    );
} 
