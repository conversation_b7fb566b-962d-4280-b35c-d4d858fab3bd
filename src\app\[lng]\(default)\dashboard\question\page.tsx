'use client'
import React, { useEffect,useState } from 'react';
import MessageList from './components/MessageList';
import { useParams } from 'next/navigation';
import { getDictionary } from "@/dictionaries";
export default function Messages() {
  const { lng } = useParams();
  const [dict, setDict] = useState<any>(null); // 添加字典状态
    // 异步获取字典数据
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };

    fetchDictionary();
    }, [lng]);
  return (
    <div className="p-6">
      <MessageList  dict={dict} />
    </div>
  );
}