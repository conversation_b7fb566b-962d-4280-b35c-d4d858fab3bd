'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { Button, Progress } from 'antd'
import { clearAllCache } from '@/utils/cache'
import type { Locale } from '@/config'

interface CacheRefreshPageProps {
  params: Promise<{ lng: Locale }>
}

export default function CacheRefreshPage({ params }: CacheRefreshPageProps) {
  const [lng, setLng] = useState<Locale>('zh-cn')
  const [step, setStep] = useState(0)
  const [isClearing, setIsClearing] = useState(false)
  const [progress, setProgress] = useState(0)
  const searchParams = useSearchParams()
  const returnUrl = searchParams.get('return') || `/${lng}`

  useEffect(() => {
    try {
      params.then(({ lng }) => setLng(lng)).catch((err) => {
        console.error('Failed to get language from params:', err);
        setLng('zh-cn'); // fallback
      });
    } catch (err) {
      console.error('Error in params effect:', err);
      setLng('zh-cn'); // fallback
    }
  }, [params])

  const getSteps = (lng: Locale) => {
    const isEn = lng === 'en'
    return [
      {
        title: isEn ? 'Cache Issue Detected' : '检测到缓存问题',
        description: isEn
          ? 'We detected potential browser cache conflicts that may affect your experience.'
          : '系统检测到您的浏览器缓存可能存在冲突，这可能会影响正常使用。',
        icon: '🔍'
      },
      {
        title: isEn ? 'Preparing Cache Cleanup' : '准备清理缓存',
        description: isEn
          ? 'We will automatically clear your browser cache. This process only takes a few seconds.'
          : '我们将为您自动清理浏览器缓存，这个过程只需要几秒钟。',
        icon: '🧹'
      },
      {
        title: isEn ? 'Cleanup Complete' : '清理完成',
        description: isEn
          ? 'Cache has been successfully cleared. The page will refresh automatically for the best experience.'
          : '缓存已成功清理，页面将自动刷新以确保最佳体验。',
        icon: '✅'
      }
    ]
  }

  const steps = getSteps(lng)

  const handleAutoClear = async () => {
    setIsClearing(true)
    setStep(1)
    setProgress(0)

    // 模拟进度条
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval)
          return 90
        }
        return prev + 10
      })
    }, 200)

    try {
      // 等待一点时间让用户看到进度
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 清理缓存
      const success = clearAllCache()
      
      setProgress(100)
      setStep(2)
      
      if (success) {
        // 等待一下让用户看到完成状态
        setTimeout(() => {
          window.location.href = returnUrl
        }, 1500)
      } else {
        throw new Error(lng === 'en' ? 'Cache clearing failed' : '缓存清理失败')
      }
    } catch (error) {
      console.error(lng === 'en' ? 'Auto clear failed:' : '自动清理失败:', error)
      setIsClearing(false)
      setStep(0)
      setProgress(0)
    }
  }

  const handleManualClear = () => {
    try {
      clearAllCache()
      window.location.href = returnUrl
    } catch (error) {
      // 如果自动清理失败，提供手动指导
      alert(lng === 'en'
        ? 'Please manually clear browser cache:\n1. Press Ctrl+Shift+Delete (Windows) or Cmd+Shift+Delete (Mac)\n2. Select "All time"\n3. Check "Cached images and files" and "Cookies and other site data"\n4. Click "Clear data"'
        : '请手动清理浏览器缓存：\n1. 按 Ctrl+Shift+Delete (Windows) 或 Cmd+Shift+Delete (Mac)\n2. 选择"全部时间"\n3. 勾选"缓存的图片和文件"和"Cookie及其他网站数据"\n4. 点击"清除数据"')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full text-center">
        {/* 图标和标题 */}
        <div className="text-6xl mb-4">
          {steps[step].icon}
        </div>
        
        <h1 className="text-2xl font-bold text-gray-800 mb-2">
          {steps[step].title}
        </h1>
        
        <p className="text-gray-600 mb-8 leading-relaxed">
          {steps[step].description}
        </p>

        {/* 进度条 */}
        {isClearing && (
          <div className="mb-6">
            <Progress 
              percent={progress} 
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
              showInfo={false}
            />
            <p className="text-sm text-gray-500 mt-2">
              {lng === 'en' ? `Clearing cache... ${progress}%` : `正在清理缓存... ${progress}%`}
            </p>
          </div>
        )}

        {/* 操作按钮 */}
        {step === 0 && !isClearing && (
          <div className="space-y-4">
            <Button 
              type="primary" 
              size="large"
              onClick={handleAutoClear}
              className="w-full bg-blue-500 hover:bg-blue-600 border-blue-500 h-12 text-base"
            >
              🚀 {lng === 'en' ? 'Auto Clear Cache' : '一键自动清理'}
            </Button>
            
            <Button 
              type="default" 
              size="large"
              onClick={handleManualClear}
              className="w-full h-10"
            >
              {lng === 'en' ? 'Manual Clear' : '手动清理缓存'}
            </Button>
            
            <div className="pt-4">
              <a
                href={returnUrl}
                className="text-blue-500 hover:text-blue-600 text-sm"
              >
                {lng === 'en' ? 'Skip and return directly' : '跳过，直接返回'}
              </a>
            </div>
          </div>
        )}

        {step === 2 && (
          <div className="space-y-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <p className="text-green-700 font-medium">
                {lng === 'en' ? '✨ Clearing completed! Page will redirect automatically...' : '✨ 清理完成！页面即将自动跳转...'}
              </p>
            </div>
            
            <Button 
              type="primary" 
              size="large"
              onClick={() => window.location.href = returnUrl}
              className="w-full bg-green-500 hover:bg-green-600 border-green-500"
            >
              {lng === 'en' ? 'Return Now' : '立即返回'}
            </Button>
          </div>
        )}

        {/* 帮助信息 */}
        <div className="mt-8 p-4 bg-gray-50 rounded-lg text-left">
          <h3 className="font-medium text-gray-800 mb-2 text-center">
            {lng === 'en' ? '💡 Why clear cache?' : '💡 为什么需要清理缓存？'}
          </h3>
          <ul className="text-gray-600 text-sm space-y-1">
            {lng === 'en' ? (
              <>
                <li>• Fix page loading issues</li>
                <li>• Ensure latest website content</li>
                <li>• Improve browsing experience and speed</li>
                <li>• Fix login status inconsistencies</li>
              </>
            ) : (
              <>
                <li>• 解决页面加载异常问题</li>
                <li>• 确保获取最新的网站内容</li>
                <li>• 提升浏览体验和速度</li>
                <li>• 修复登录状态不一致问题</li>
              </>
            )}
          </ul>
        </div>
      </div>
    </div>
  )
}
