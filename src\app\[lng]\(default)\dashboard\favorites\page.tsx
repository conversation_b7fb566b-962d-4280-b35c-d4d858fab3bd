import { getServerData } from "@/request/server"
import { FavoriteResponse } from "@/types/favorite"
import ProductCard from "@/components/ProductCard"
import ClearFavorite from "./(component)/clearFavorite"
import DeletFavorite from "./(component)/deletFavorite"
import { getDictionary } from "@/dictionaries"
import { Locale } from '@/config';
export default async function FavoritesPage({
  params,
}: {
  params: Promise<{ lng: Locale }>

}) {
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
  const favorites = await getServerData<FavoriteResponse>(isTp5?'/api/favorite/lists':'/web/goods/favorite/favoriteList')

  const { lng } = await params;
  const dict = await getDictionary(lng);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{dict.dashboard.favorites.title}</h1>
        <ClearFavorite dict={dict} style={{
          display:favorites.data?.length === 0 ? 'none' : 'block'
        }} />
      </div>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {favorites.data?.map((item) => (
          <div key={item.id} className="relative">
           
            <DeletFavorite favorite={item.id} />
            <ProductCard
              key={item.id}
              product={{
                detail_url: item.goodsurl,
                pic_url: item.goodsimg.startsWith('//') ? `https:${item.goodsimg}` : item.goodsimg,
                title: item.goodsname,
                promotion_price: item.goodsprice
              }}
              platform={item.goodssite as 'taobao' | '1688' | 'jd'}
              dict={dict}
            />
          </div>
        ))}
      </div>
      {favorites.data?.length === 0 && (
        <div className="text-center text-gray-500 py-12">
          {dict.dashboard.favorites.emptyList}
        </div>
      )}
    </div>
  )
}
