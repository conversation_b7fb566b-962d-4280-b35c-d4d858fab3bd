import React, { useEffect, useState } from 'react';
import { List, Typography, Form, message } from 'antd';
import dayjs from 'dayjs';
import { Api } from '@/request/api';
import ButtonComponent from '@/components/Button';
import ModalComponent from '@/components/Modal';
import InputComponent from '@/components/Input';

const { Text } = Typography;

interface FeedbackItem {
  id: number;
  user_id: number;
  type: number;
  imgs: string;
  content: string;
  admin_id: number;
  reply: string | null;
  status: string;
  createtime: string;
  replytime: number;
  username: string;
  user?: {
    id: number;
    username: string;
    nickname: string;
    avatar?: string;
    email?: string;
  };
}

export default function MessageList({dict}:{dict:any}) {
  const [feedbacks, setFeedbacks] = useState<FeedbackItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'

  const fetchFeedbacks = async () => {
    try {
      setLoading(true);
      const response = await Api.getFeedbackList({});
      if (response.success) {
        const feedbackData = Array.isArray(response.data.data) ? response.data.data : [];
        setFeedbacks(feedbackData);
      }
    } catch (error) {
      message.error(dict?.dashboard?.question?.listFail);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFeedbacks();
  }, []);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    form.resetFields();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);
      let data: { content: any; user_id?: number } = {
        content: values.content
      }
      if(!isTp5){
        let infoStr = localStorage.getItem('info');
          if(infoStr){
              data.user_id = JSON.parse(infoStr).data.userinfo.id;
          }
      }
      const response = await Api.addFeedback(data);
      
      if (response.success) {
        message.success(dict?.dashboard?.question?.submitSuccess);
        handleCancel();
        fetchFeedbacks(); // 刷新列表
      } else {
        message.error(response.msg || dict?.dashboard?.question?.submitFail);
      }
    } catch (error) {
      console.error('提交反馈出错:', error);
      message.error(dict?.dashboard?.question?.submitRetry);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <>
      <div className="flex justify-between mb-4">
        <h2 className="text-xl font-medium"> {dict?.dashboard?.question?.title}</h2>
        <ButtonComponent type="primary" onClick={showModal}>
           {dict?.dashboard?.question?.submitBtn}
        </ButtonComponent>
      </div>
      
      <List
        className="message-list"
        loading={loading}
        dataSource={feedbacks}
        renderItem={(item) => (
          <List.Item key={item.id} className="message-item">
            <div className="flex w-full cursor-pointer hover:bg-gray-50 p-4">
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <Text strong>{item.user?.nickname || item.username}</Text>
                </div>
                <Text className="text-gray-600 block my-2">
                  {item.content}
                </Text>
                <Text type="secondary" className="text-sm">
                  {item.createtime}
                </Text>
                {item.reply && (
                  <div className="mt-2 p-2 bg-gray-50 rounded">
                    <Text type="secondary" className="text-sm">
                      {dict?.dashboard?.question?.reply}: <div dangerouslySetInnerHTML={{ __html: item.reply }}></div>
                    </Text>
                  </div>
                )}
              </div>
            </div>
          </List.Item>
        )}
      />

      <ModalComponent
        title= {dict?.dashboard?.question?.submitBtn}
        open={isModalOpen}
        onCancel={handleCancel}
        footer={[
          <ButtonComponent key="back" onClick={handleCancel}>
            {dict?.dashboard?.question?.cancel}
          </ButtonComponent>,
          <ButtonComponent 
            key="submit" 
            type="primary" 
            loading={submitting} 
            onClick={handleSubmit}
          >
            {dict?.dashboard?.question?.submit}
          </ButtonComponent>,
        ]}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="content"
            label={dict?.dashboard?.message?.content}
            rules={[{ required: true, message: dict?.dashboard?.question?.contentPlaceholder }]}
          >
            <InputComponent.TextArea rows={4} placeholder={dict?.dashboard?.question?.contentPlaceholder} />
          </Form.Item>
        </Form>
      </ModalComponent>
    </>
  );
} 