import { FC } from 'react';

interface PaginationProps {
  currentPage?: number;
  totalPages?: number;
  pageSize?: number;
  onPageChange?: (page: number) => void;
  prevText?: string;
  nextText?: string;
}

const Pagination: FC<PaginationProps> = ({
  currentPage = 1,
  totalPages = 10,
  pageSize = 10,
  onPageChange = () => {},
  prevText = '上一页',
  nextText = '下一页',
}) => {
  // 生成页码数组
  const getPageNumbers = () => {
    const pageNumbers: (number | string)[] = [];
    
    // 如果总页数小于等于 7 ，显示所有页码
    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // 总页数大于 7 时，使用省略号
      if (currentPage <= 3) {
        // 当前页在前 3 页
        for (let i = 1; i <= 5; i++) {
          pageNumbers.push(i);
        }
        pageNumbers.push('...');
        pageNumbers.push(totalPages);
      } else if (currentPage >= totalPages - 2) {
        // 当前页在后 3 页
        pageNumbers.push(1);
        pageNumbers.push('...');
        for (let i = totalPages - 4; i <= totalPages; i++) {
          pageNumbers.push(i);
        }
      } else {
        // 当前页在中间
        pageNumbers.push(1);
        pageNumbers.push('...');
        pageNumbers.push(currentPage - 1);
        pageNumbers.push(currentPage);
        pageNumbers.push(currentPage + 1);
        pageNumbers.push('...');
        pageNumbers.push(totalPages);
      }
    }
    
    return pageNumbers;
  };

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page);
    }
  };

  return (
    <div className="flex items-center justify-center py-6 bg-white border-t border-gray-100" style={{
      display: totalPages > 1 ? 'flex' : 'none'
    }}>
      {/* 上一页按钮 */}
      <button
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className={`
          px-5 py-2 mx-1 bg-gray-50 text-sm rounded-md transition-all
          ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-600 hover:bg-gray-100'}
        `}
      >
        {prevText}
      </button>

      {/* 页码按钮 */}
      <div className="flex mx-1">
        {getPageNumbers().map((page, index) => (
          <button
            key={index}
            onClick={() => typeof page === 'number' && handlePageChange(page)}
            disabled={typeof page !== 'number'}
            className={`
              w-10 h-10 mx-1 flex items-center justify-center text-sm rounded-md transition-all
              ${page === currentPage 
                ? 'bg-[#FF6B00] text-white' 
                : typeof page === 'number'
                  ? 'bg-gray-50 text-gray-600 hover:bg-gray-100' 
                  : 'bg-transparent text-gray-400'}
            `}
          >
            {page}
          </button>
        ))}
      </div>

      {/* 下一页按钮 */}
      <button
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className={`
          px-5 py-2 mx-1 bg-gray-50 text-sm rounded-md transition-all
          ${currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-600 hover:bg-gray-100'}
        `}
      >
        {nextText}
      </button>
    </div>
  );
};

export default Pagination;
