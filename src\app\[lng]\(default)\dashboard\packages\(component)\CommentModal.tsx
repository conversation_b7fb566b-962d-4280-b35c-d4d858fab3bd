import React, { useState, useEffect } from 'react';
import { Rate, Input, Upload, Button as AntButton, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import ModalComponent from '@/components/Modal';
import Button from '@/components/Button';
import { Api } from '@/request/api';
import Toast from '@/components/Toast';

const { TextArea } = Input;

interface CommentModalProps {
  open: boolean;
  onCancel: () => void;
  sendorderId: string;
  dict: any;
  onSuccess?: () => void;
  existingComments?: any[]; // 现有评论数据
  isReply?: boolean; // 是否为回复模式
}

const CommentModal: React.FC<CommentModalProps> = ({
  open,
  onCancel,
  sendorderId,
  dict,
  onSuccess,
  existingComments = [],
  isReply = false
}) => {
  const [loading, setLoading] = useState(false);
  const [content, setContent] = useState('');
  const [starlevel, setStarlevel] = useState(0);
  const [imageUrl, setImageUrl] = useState('');
  const [commentList, setCommentList] = useState<any[]>([]);

  // 当模态框打开时，检查并加载评论列表
  useEffect(() => {
    if (open) {
      loadCommentList();
    }
  }, [open, sendorderId]);

  const loadCommentList = async () => {
    try {
      const response = await Api.getCommentList({ size: '10' });
      if (response.success && response.data) {
        // 筛选当前订单的评论
        const orderComments = response.data.filter((comment: any) =>
          comment.sendorder_id === sendorderId
        );
        setCommentList(orderComments);
      }
    } catch (error) {
      console.error('加载评论列表失败:', error);
      setCommentList([]);
    }
  };

  const handleSubmit = async () => {
    if (!content.trim()) {
      Toast.error(dict?.dashboard?.packages?.commentRequired || '请输入评论内容');
      return;
    }

    setLoading(true);
    try {
      let response;

      if (commentList.length > 0) {
        // 有评论数据，进行回复
        const firstComment = commentList[0];
        response = await Api.replyComment({
          sendorder_id: sendorderId,
          pid: firstComment.id || firstComment.pid, // 使用第一条评论的ID作为父评论ID
          content: content.trim()
        });
      } else {
        // 没有评论数据，新增评论
        if (starlevel === 0) {
          Toast.error(dict?.dashboard?.packages?.commentStarRequired || '请选择评分');
          return;
        }

        response = await Api.addComment({
          sendorder_id: sendorderId,
          pid: '0', // 主评论，pid为0
          content: content.trim(),
          starlevel: starlevel.toString(),
          img: imageUrl
        });
      }

      if (response.success) {
        const successMsg = commentList.length > 0
          ? (dict?.dashboard?.packages?.replySuccess || '回复成功')
          : (dict?.dashboard?.packages?.commentSuccess || '评论成功');
        Toast.success(successMsg);

        setContent('');
        setStarlevel(0);
        setImageUrl('');
        onCancel();
        onSuccess?.();
      } else {
        Toast.error(response.msg || dict?.dashboard?.packages?.commentFail || '操作失败');
      }
    } catch (error) {
      Toast.error(dict?.dashboard?.packages?.commentFail || '操作失败');
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = async (file: File) => {
    try {
      const response = await Api.uploadImage(file);
      if (response.success) {
        setImageUrl(response.data.url || response.data.path);
        message.success('图片上传成功');
      } else {
        message.error('图片上传失败');
      }
    } catch (error) {
      message.error('图片上传失败');
    }
    return false; // 阻止默认上传行为
  };

  const handleCancel = () => {
    setContent('');
    setStarlevel(0);
    setImageUrl('');
    onCancel();
  };

  return (
    <ModalComponent
      title={commentList.length > 0
        ? (dict?.dashboard?.packages?.replyTitle || '回复评论')
        : (dict?.dashboard?.packages?.commentTitle || '包裹评论')
      }
      open={open}
      onCancel={handleCancel}
      centered
      footer={null}
      width={600}
    >
      <div className="space-y-6 p-4">
        {/* 显示现有评论列表 */}
        {commentList.length > 0 && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-3">
              {dict?.dashboard?.packages?.existingComments || '现有评论'}
            </h4>
            <div className="space-y-3 max-h-40 overflow-y-auto">
              {commentList.map((comment, index) => (
                <div key={index} className="bg-white p-3 rounded border">
                  <div className="flex items-center gap-2 mb-2">
                    {comment.starlevel && (
                      <Rate disabled value={parseInt(comment.starlevel)} style={{ fontSize: 14 }} />
                    )}
                    <span className="text-xs text-gray-500">{comment.createtime}</span>
                  </div>
                  <p className="text-sm text-gray-800">{comment.content}</p>
                  {comment.img && (
                    <img src={comment.img} alt="评论图片" className="w-16 h-16 object-cover rounded mt-2" />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 评分 - 只在新增评论时显示 */}
        {commentList.length === 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {dict?.dashboard?.packages?.commentStar || '评分'} <span className="text-red-500">*</span>
            </label>
            <Rate
              value={starlevel}
              onChange={setStarlevel}
              allowHalf={false}
              style={{ fontSize: 24 }}
            />
          </div>
        )}

        {/* 评论内容 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {commentList.length > 0
              ? (dict?.dashboard?.packages?.replyContent || '回复内容')
              : (dict?.dashboard?.packages?.commentContent || '评论内容')
            } <span className="text-red-500">*</span>
          </label>
          <TextArea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder={commentList.length > 0
              ? (dict?.dashboard?.packages?.replyPlaceholder || '请输入回复内容')
              : (dict?.dashboard?.packages?.commentPlaceholder || '请输入评论内容')
            }
            rows={4}
            maxLength={500}
            showCount
          />
        </div>

        {/* 图片上传 - 只在新增评论时显示 */}
        {commentList.length === 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {dict?.dashboard?.packages?.commentImage || '图片'} (可选)
            </label>
            <Upload
              beforeUpload={handleImageUpload}
              showUploadList={false}
              accept="image/*"
            >
              <AntButton icon={<UploadOutlined />}>
                上传图片
              </AntButton>
            </Upload>
            {imageUrl && (
              <div className="mt-2">
                <img
                  src={imageUrl}
                  alt="评论图片"
                  className="w-20 h-20 object-cover rounded border"
                />
              </div>
            )}
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button
            onClick={handleCancel}
            className="!h-10 !px-6"
          >
            {dict?.dashboard?.packages?.cancel || '取消'}
          </Button>
          <Button
            type="primary"
            onClick={handleSubmit}
            loading={loading}
            className="!h-10 !px-6"
          >
            {commentList.length > 0
              ? (dict?.dashboard?.packages?.replySubmit || '提交回复')
              : (dict?.dashboard?.packages?.commentSubmit || '提交评论')
            }
          </Button>
        </div>
      </div>
    </ModalComponent>
  );
};

export default CommentModal;
