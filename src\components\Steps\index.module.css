/* Steps Component Styles */
.stepsWrapper {
  display: flex;
  justify-content: center;
  padding: 2.5rem 1.5rem;
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
}

.customSteps {
  max-width: 960px !important;
  width: 100% !important;
  min-width: 0 !important;
}

/* Override Ant Design Steps layout */
.customSteps :global(.ant-steps) {
  display: flex !important;
  justify-content: space-between !important;
  max-width: 960px !important;
  width: 100% !important;
}

.customSteps :global(.ant-steps-item) {
  flex: 1 !important;
  display: inline-flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
  min-width: 0 !important;
  max-width: none !important;
  position: relative !important;
}

/* Step Icon Wrapper - Contains both the main circle and price tag */
.stepIconWrapper {
  position: relative;
  display: inline-block;
}

/* Main Step Circle */
.stepIcon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
  border: 2px solid #d1d5db;
  background-color: #f9fafb;
  color: #6b7280;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

/* Active Step */
.stepIcon.active {
  background-color: #f97316;
  border-color: #f97316;
  color: white;
}

/* Completed Step */
.stepIcon.completed {
  background-color: #f97316;
  border-color: #f97316;
  color: white;
}

/* Price Tag Icon */
.priceTag {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* Active/Completed Price Tag */
.priceTag.active {
  background-color: #f97316;
}

.priceTag.active i {
  font-size: 10px;
  color: white;
}

/* Inactive Price Tag */
.priceTag.inactive {
  background-color: #9ca3af;
}

.priceTag.inactive i {
  font-size: 10px;
  color: white;
}

/* Override Ant Design default styles */
.customSteps :global(.ant-steps-item-icon) {
  width: auto !important;
  height: auto !important;
  line-height: normal !important;
  border: none !important;
  background: transparent !important;
  margin: 0 !important;
}

.customSteps :global(.ant-steps-item-process .ant-steps-item-icon),
.customSteps :global(.ant-steps-item-finish .ant-steps-item-icon),
.customSteps :global(.ant-steps-item-wait .ant-steps-item-icon) {
  background: transparent !important;
  border: none !important;
}


/* Step content positioning */
.customSteps :global(.ant-steps-item-content) {
  margin-top: 25px !important;
  text-align: center;
}

.customSteps :global(.ant-steps-item-title) {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  text-align: center;
  white-space: normal;
  overflow: visible;
  text-overflow: unset;
  max-width: 120px;
  margin: 0 auto;
  word-wrap: break-word;
  hyphens: auto;
  padding: 0 4px;
}

/* Ensure the entire step item is centered */
.customSteps :global(.ant-steps-item-container) {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  width: 100% !important;
}

.customSteps :global(.ant-steps-item-icon) {
  margin: 0 auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

/* Step connecting lines - positioned between circles */
.customSteps :global(.ant-steps-item-tail) {
  position: absolute !important;
  top: 25px !important;
  left: 90% !important;
  width: 100% !important;
  height: 1px !important;
  padding: 0 !important;
  margin: 0 !important;
  transform: translateX(-50%) !important;
}

.customSteps :global(.ant-steps-item-tail::after) {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 25px !important;
  right: -25px !important;
  height: 2px !important;
  background-color: #e5e7eb !important;
  border-radius: 1px !important;
}

/* Completed step connecting lines */
.customSteps :global(.ant-steps-item-finish .ant-steps-item-tail::after) {
  background-color: #f97316 !important;
}

/* Hide connecting line for last step */
.customSteps :global(.ant-steps-item:last-child .ant-steps-item-tail) {
  display: none !important;
}

/* Active and completed step titles */
.customSteps :global(.ant-steps-item-process .ant-steps-item-title),
.customSteps :global(.ant-steps-item-finish .ant-steps-item-title) {
  color: #f97316;
}

.customSteps :global(.ant-steps-item-wait .ant-steps-item-title) {
  color: #9ca3af;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .stepIcon {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .priceTag {
    width: 16px;
    height: 16px;
    top: -1px;
    right: -1px;
  }

  .priceTag.active i,
  .priceTag.inactive i {
    font-size: 8px;
  }

  .customSteps :global(.ant-steps-item-tail) {
    top: 20px !important;
  }

  .customSteps :global(.ant-steps-item-tail::after) {
    left: 20px !important;
    right: -20px !important;
  }

  .customSteps :global(.ant-steps-item-title) {
    font-size: 12px;
    max-width: 70px;
    line-height: 1.2;
    padding: 0 2px;
  }

  .chineseLayout .customSteps :global(.ant-steps-item-title) {
    max-width: 65px;
  }

  .westernLayout .customSteps :global(.ant-steps-item-title) {
    max-width: 70px;
    font-size: 11px;
  }
}

/* Language-specific layouts */
.chineseLayout .customSteps :global(.ant-steps-item-title) {
  max-width: 100px;
  font-size: 14px;
}

.westernLayout .customSteps :global(.ant-steps-item-title) {
  max-width: 120px;
  font-size: 13px;
  line-height: 1.3;
}

/* Extra small screens */
@media (max-width: 480px) {
  .stepsWrapper {
    padding: 1rem 0.5rem;
  }

  .stepIcon {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }

  .priceTag {
    width: 14px;
    height: 14px;
  }

  .priceTag.active i,
  .priceTag.inactive i {
    font-size: 7px;
  }

  .customSteps :global(.ant-steps-item-title) {
    font-size: 11px;
    max-width: 60px;
    padding: 0 1px;
  }

  .chineseLayout .customSteps :global(.ant-steps-item-title) {
    max-width: 55px;
  }

  .westernLayout .customSteps :global(.ant-steps-item-title) {
    max-width: 60px;
    font-size: 10px;
  }
}
