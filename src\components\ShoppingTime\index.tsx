'use client';

import { useEffect, useState } from "react";
import { Api } from "@/request/api";
import Loading from "@/components/Loading";
import { ShoppingTimeProduct } from "@/types/product";
import ImprovedEmblaCarousel from "./ImprovedEmblaCarousel";

interface ShoppingTimeProps {
  dict: any;
}

export default function ShoppingTime({ dict }: ShoppingTimeProps) {
  const [products, setProducts] = useState<ShoppingTimeProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [isPluginEnabled, setIsPluginEnabled] = useState(false);

  useEffect(() => {
    const checkPluginAndFetchData = async () => {
      try {
        // 使用新的插件管理工具检查插件是否启用
        const { isPluginEnabled } = await import('@/utils/plugin');
        const pluginEnabled = await isPluginEnabled('shoppingtime');

        if (pluginEnabled) {
          setIsPluginEnabled(true);

          // 插件启用时才获取数据
          const response = await Api.getShoppingTimeList(10);
          if (response.success && response.data) {
            // 根据实际API响应结构，数据直接在response.data数组中
            setProducts(response.data || []);
          }
        } else {
          setIsPluginEnabled(false);
        }
      } catch (error) {
        console.error(dict?.errors?.fetchShoppingTimeFailed || '获取购物时报失败:', error);
        setIsPluginEnabled(false);
      } finally {
        setLoading(false);
      }
    };

    checkPluginAndFetchData();
  }, [dict]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h2 className="text-2xl font-bold text-center mb-6 text-black">{dict?.home?.shoppingTime || '购物时报'}</h2>
        <Loading />
      </div>
    );
  }

  // 如果插件未启用，则不显示组件
  if (!isPluginEnabled) return null;

  return (
    <div className="container mx-auto px-4 py-8">
      <h2 className="text-2xl font-bold text-center mb-6 text-black">{dict?.home?.shoppingTime || '购物时报'}</h2>
      {products?.length === 0 ? (
        <div className="text-center text-gray-500 py-12">
          {dict?.home?.emptyList || '列表为空'}
        </div>
      ) : (
        // 使用改进的Embla Carousel，将同一用户的商品放在一个区块容器中
        <ImprovedEmblaCarousel products={products} dict={dict} />
      )}
    </div>
  );
}
