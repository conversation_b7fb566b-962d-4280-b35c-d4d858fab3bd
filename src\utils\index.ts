export function UpperCaseFirst(str: string) {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
}

export function formatDate(dateString: string) {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-cn', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    }).replace(/\//g, '-')
} 

export function fixedUrl(url: string) {
    if(!url){
        return 'https://2925.com/mailv2/adPlatform/file/download?fileId=63ce51a4d50347e9a79c4fe0a3b8692d&server=2980'
    }
    return url.replace(/^\/\//, 'https://')
}
