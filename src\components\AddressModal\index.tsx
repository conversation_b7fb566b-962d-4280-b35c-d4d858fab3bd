'use client'

import React, { useState, useEffect } from 'react'
import { Form, Input, Select, Button, Switch } from 'antd'
import ModalComponent from '@/components/Modal'
import Toast from '@/components/Toast'
import { Api, AddressItem } from '@/request/api'

interface AddressModalProps {
  open: boolean
  onCancel: () => void
  onSuccess: () => void
  currentAddress?: AddressItem | null,
  dict: any
}

export default function AddressModal({ open, onCancel, onSuccess, currentAddress, dict }: AddressModalProps) {
  const [form] = Form.useForm()
  const [countryList, setCountryList] = useState<any[]>([])
  const [regionList, setRegionList] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const fetchCountryList = async () => {
      try {
        const response = await Api.getAreaList()
        if (response.success) {
          console.log('国家列表数据:', response.data)
          console.log('国家列表第一项:', response.data?.[0])
          setCountryList(response.data || [])
        } else {
          Toast.error(dict?.dashboard?.address?.countryFail || '获取国家列表失败')
        }
      } catch (error) {
        console.error('获取国家列表失败:', error)
        Toast.error(dict?.dashboard?.address?.countryFail || '获取国家列表失败')
      }
    }
    fetchCountryList()
  }, [])

  useEffect(() => {
    if (currentAddress && open) {
      console.log('Modal currentAddress:', currentAddress);
      console.log('国家:', currentAddress.country);
      console.log('地区:', currentAddress.province);
      
      // 延迟设置表单值，确保表单已经渲染
      setTimeout(() => {
        // 兼容两种数据结构：直接ID字段和嵌套对象字段
        const countryId = currentAddress.country_id || currentAddress.country?.id;
        const regionId = currentAddress.region_id || currentAddress.province?.id;

        const formValues = {
          consignee: currentAddress.consignee,
          telephone: currentAddress.telephone,
          country_id: countryId,
          region_id: regionId,
          address: currentAddress.address,
          zipcode: currentAddress.zipcode,
          isdefault: currentAddress.isdefault === 1,
          door_number: currentAddress.door_number,
          telephone2: currentAddress.telephone2,
          tax_number: currentAddress.tax_number,
          address_tag: currentAddress.address_tag
        };
        console.log('设置表单值:', formValues);
        console.log('国家ID:', countryId, '地区ID:', regionId);
        form.setFieldsValue(formValues);

        // 如果有国家ID，获取对应的地区列表，编辑模式下不重置地区选择
        if (countryId) {
          console.log('开始获取地区列表，国家ID:', countryId);
          handleCountryChange(countryId, false);
        }
      }, 100);
    } else {
      form.resetFields();
    }
  }, [currentAddress, form, open]);

  // 监听 open 状态变化
  useEffect(() => {
    if (!open) {
      form.resetFields();
    }
  }, [open, form]);

  const handleCountryChange = async (value: number, shouldResetRegion: boolean = true) => {
    try {
      console.log('获取地区列表，国家ID:', value, '是否重置地区:', shouldResetRegion);
      const response = await Api.getAreaList({ country: value });
      if (response.success) {
        console.log('地区列表数据:', response.data);
        setRegionList(response.data || []);
        // 只有在用户主动改变国家时才重置地区选择，编辑模式下初始化时不重置
        if (shouldResetRegion) {
          console.log('重置地区选择');
          form.setFieldsValue({
            region_id: undefined
          });
        } else {
          console.log('编辑模式，不重置地区选择，当前表单值:', form.getFieldsValue());
        }
      } else {
        Toast.error(dict?.dashboard?.address?.regionFail || '获取地区列表失败');
      }
    } catch (error) {
      console.error('获取地区列表失败:', error);
      Toast.error(dict?.dashboard?.address?.regionFail || '获取地区列表失败');
    }
  };

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      const submitData = {
        consignee: values.consignee,
        telephone: values.telephone,
        area_id: values.region_id,
        address: values.address,
        zipcode: values.zipcode || '',
        isdefault: values.isdefault ? 1 : 0,
        door_number: values.door_number || '',
        telephone2: values.telephone2 || '',
        tax_number: values.tax_number || '',
        address_tag: values.address_tag || '',
        country_id: values.country_id
      }

      let response;
      if (currentAddress) {
        response = await Api.updateAddress({
          ...submitData,
          address_id: currentAddress.id,
        })
      } else {
        response = await Api.addAddress(submitData)
      }

      if (response.success) {
        Toast.success(currentAddress ?
          (dict?.dashboard?.address?.updateSuccess || '更新成功') :
          (dict?.dashboard?.address?.addSuccess || '添加成功')
        );
        onSuccess();
        onCancel();
      } else {
        // 显示服务器返回的具体错误信息
        const errorMsg = response.data || response.message ||
          (currentAddress ?
            (dict?.dashboard?.address?.updateFail || '更新失败') :
            (dict?.dashboard?.address?.addFail || '添加失败')
          );
        Toast.error(errorMsg);
      }
    } catch (error: any) {
      console.error('提交地址失败:', error);
      // 显示更详细的错误信息
      const errorMsg = error?.message || error?.data ||
        (currentAddress ?
          (dict?.dashboard?.address?.updateFail || '更新失败') :
          (dict?.dashboard?.address?.addFail || '添加失败')
        );
      Toast.error(errorMsg);
    } finally {
      setLoading(false);
    }
  }

  return (
    <>
      <ModalComponent
        title={currentAddress ? dict?.dashboard?.address?.editTitle:  dict?.dashboard?.address?.addTitle}
        open={open}
        onCancel={onCancel}
        footer={null}
        width={800}
        className="address-modal"
        forceRender={false}
        getContainer={false}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="mt-4"
          initialValues={{
            isdefault: false,
            zipcode: '',
            telephone2: '',
            tax_number: '',
            address_tag: '',
            door_number: ''
          }}
          preserve={false}
        >
          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              label={dict?.dashboard?.address?.receiver}
              name="consignee"
              rules={[{ required: true, message: dict?.dashboard?.address?.receiverHint}]}
            >
              <Input size="large" placeholder={dict?.dashboard?.address?.receiverHint} />
            </Form.Item>

            <Form.Item
              label={dict?.dashboard?.address?.phone}
              name="telephone"
              rules={[
                { required: true, message: dict?.dashboard?.address?.phoneHint},
                { pattern: /^[0-9]{6,15}$/, message: dict?.dashboard?.address?.phoneError || '请输入6-15位数字的有效手机号' }
              ]}
            >
              <Input size="large" placeholder={dict?.dashboard?.address?.phoneHint} />
            </Form.Item>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              label={dict?.dashboard?.address?.country}
              name="country_id"
              rules={[{ required: true, message: dict?.dashboard?.address?.countryHint}]}
            >
              <Select
                size="large"
                placeholder={dict?.dashboard?.address?.countryHint}
                onChange={handleCountryChange}
                options={countryList}
                fieldNames={{ label: 'name', value: 'value' }}
              />
            </Form.Item>

            <Form.Item
              label={dict?.dashboard?.address?.region}
              name="region_id"
              rules={[{ required: true, message: dict?.dashboard?.address?.regionHint }]}
            >
              <Select
                size="large"
                placeholder={ dict?.dashboard?.address?.regionHint}
                options={regionList}
                disabled={regionList.length === 0}
                fieldNames={{ label: 'name', value: 'value' }}
              />
            </Form.Item>
          </div>

          <Form.Item
            label={ dict?.dashboard?.address?.detail}
            name="address"
            rules={[{ required: true, message:  dict?.dashboard?.address?.detailHint}, { min: 5, message: dict?.dashboard?.address?.detailMinLength }]}
          >
            <Input.TextArea
              size="large"
              placeholder={dict?.dashboard?.address?.detailHint}
              autoSize={{ minRows: 2, maxRows: 4 }}
            />
          </Form.Item>

          <div className="grid grid-cols-2 gap-4">
            {/* <Form.Item
              label="门牌号"
              name="door_number"
            >
              <Input size="large" placeholder="请输入门牌号" />
            </Form.Item> */}

            <Form.Item
              label={dict?.dashboard?.address?.postcode}
              name="zipcode"
              rules={[{ pattern: /^[A-Za-z0-9\-\s]{3,12}$/, message: dict?.dashboard?.address?.postcodeError || '请输入3-12位有效的邮政编码' }]}
            >
              <Input size="large" placeholder={dict?.dashboard?.address?.postcodeHint} />
            </Form.Item>
          </div>

          {/* <div className="grid grid-cols-2 gap-4">
            <Form.Item
              label="备用电话"
              name="telephone2"
            >
              <Input size="large" placeholder="请输入备用电话" />
            </Form.Item>

            <Form.Item
              label="税号"
              name="tax_number"
            >
              <Input size="large" placeholder="请输入税号" />
            </Form.Item>
          </div> */}

          {/* <Form.Item
            label="地址标签"
            name="address_tag"
          >
            <Input size="large" placeholder="请输入地址标签" />
          </Form.Item> */}

          <Form.Item
            label={dict?.dashboard?.address?.setDefault}
            name="isdefault"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item className="mb-0 text-right">
            <Button onClick={onCancel} className="mr-4 h-10 px-8" disabled={loading}>
              {dict?.dashboard?.address?.cancel}
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{ backgroundColor: 'var(--base-color)' }}
              className="h-10 px-8"
            >
              {dict?.dashboard?.address?.confirm}
            </Button>
          </Form.Item>
        </Form>
      </ModalComponent>
    </>
  )
} 