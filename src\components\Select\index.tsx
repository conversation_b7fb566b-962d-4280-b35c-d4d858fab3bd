'use client';

import { Select, SelectProps } from 'antd';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import React, { ReactNode } from 'react';

export default function SelectComponent({ children, ...props }: SelectProps & { children?: ReactNode }) {
  return (
    <AntdConfigProvider>
      <Select {...props}>
        {children && children}
      </Select>
    </AntdConfigProvider>
  );
}