'use client';

import { useState, useEffect, useRef, useMemo } from 'react';
import Image from 'next/image';
import { ShoppingTimeProduct } from "@/types/product";
import { prepareImageForNextJs } from '@/utils/imageUtils';
import { formatCurrency } from '@/utils/currency';
import styles from './ShoppingTimeCarousel.module.css';

interface ShoppingTimeCarouselProps {
  products: ShoppingTimeProduct[];
  dict: any;
}

interface UserGroup {
  user: {
    username: string;
    nickname: string;
  };
  products: ShoppingTimeProduct[];
  latestTime: string;
  totalItems: number;
}

export default function ShoppingTimeCarousel({ products, dict }: ShoppingTimeCarouselProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, startX: 0 });
  const [hasDragged, setHasDragged] = useState(false);

  const containerRef = useRef<HTMLDivElement>(null);
  const trackRef = useRef<HTMLDivElement>(null);
  const currentX = useRef(0);
  const itemWidth = useRef(0);
  const isResetting = useRef(false);

  // 按用户nickname进行分组聚合商品数据
  const userGroups = useMemo(() => {
    const groupMap = new Map<string, UserGroup>();

    products.forEach(product => {
      // 优先使用nickname作为分组键，如果没有nickname则使用username，最后使用user_id
      const groupKey = product.user?.nickname || product.user?.username || `user_${product.user_id}` || 'unknown';

      if (groupMap.has(groupKey)) {
        const group = groupMap.get(groupKey)!;
        group.products.push(product);
        group.totalItems += product.goodsnum;
        // 更新最新时间
        if (new Date(product.createtime) > new Date(group.latestTime)) {
          group.latestTime = product.createtime;
        }
      } else {
        // 创建新的用户组
        groupMap.set(groupKey, {
          user: product.user,
          products: [product],
          latestTime: product.createtime,
          totalItems: product.goodsnum
        });
      }
    });

    // 按最新购买时间排序，最新的在前面
    return Array.from(groupMap.values()).sort((a, b) =>
      new Date(b.latestTime).getTime() - new Date(a.latestTime).getTime()
    );
  }, [products]);

  // 设置位置并处理无限循环
  const setTransform = (x: number, transition = true) => {
    const track = trackRef.current;
    if (!track || userGroups.length === 0 || isResetting.current) return;

    currentX.current = x;
    track.style.transition = transition ? 'transform 300ms ease-out' : 'none';
    track.style.transform = `translateX(${x}px)`;

    if (!transition) {
      track.offsetHeight; // 强制重排
    }
  };

  // 检查并处理无限循环重置
  const checkInfiniteLoop = () => {
    if (!itemWidth.current || userGroups.length === 0 || isResetting.current) return;

    const track = trackRef.current;
    if (!track) return;

    const totalItems = userGroups.length;
    const singleWidth = itemWidth.current;
    const currentPos = currentX.current;

    // 如果滚动到了最后一个克隆元素（完全显示），重置到第一个真实元素
    if (currentPos <= -(singleWidth * (totalItems + 1))) {
      isResetting.current = true;

      // 立即无动画重置到第一个真实元素
      track.style.transition = 'none';
      track.style.transform = `translateX(${-singleWidth}px)`;
      track.offsetHeight; // 强制重排
      currentX.current = -singleWidth;

      // 短暂延迟后恢复过渡效果
      setTimeout(() => {
        track.style.transition = 'transform 300ms ease-out';
        isResetting.current = false;
      }, 50);
    }
    // 如果滚动到了第一个克隆元素（完全显示），重置到最后一个真实元素
    else if (currentPos >= 0) {
      isResetting.current = true;

      // 立即无动画重置到最后一个真实元素
      track.style.transition = 'none';
      track.style.transform = `translateX(${-singleWidth * totalItems}px)`;
      track.offsetHeight; // 强制重排
      currentX.current = -singleWidth * totalItems;

      // 短暂延迟后恢复过渡效果
      setTimeout(() => {
        track.style.transition = 'transform 300ms ease-out';
        isResetting.current = false;
      }, 50);
    }
  };



  // 初始化
  useEffect(() => {
    const init = () => {
      if (!containerRef.current || !trackRef.current || userGroups.length === 0) return;

      const cards = containerRef.current.querySelectorAll('[data-user-card]');
      if (cards.length === 0) return;

      // 计算单个卡片宽度（包括间距）
      const firstCard = cards[0] as HTMLElement;
      const cardWidth = firstCard.offsetWidth + 24; // 包括gap (6px * 4)
      itemWidth.current = cardWidth;

      // 设置初始位置到第一个真实元素（跳过克隆的最后一个元素）
      const track = trackRef.current;
      if (track) {
        // 禁用过渡效果
        track.style.transition = 'none';
        // 设置初始位置
        track.style.transform = `translateX(${-cardWidth}px)`;
        // 强制重排
        track.offsetHeight;
        // 更新当前位置引用
        currentX.current = -cardWidth;
      }
    };

    // 延迟初始化，确保DOM已完全渲染
    const timer = setTimeout(init, 500);
    return () => clearTimeout(timer);
  }, [userGroups]);

  // 监听过渡结束事件，处理无限循环
  useEffect(() => {
    const track = trackRef.current;
    if (!track) return;

    const handleTransitionEnd = () => {
      checkInfiniteLoop();
    };

    track.addEventListener('transitionend', handleTransitionEnd);
    return () => track.removeEventListener('transitionend', handleTransitionEnd);
  }, [userGroups.length]);

  // 导航函数
  const handleNext = () => {
    if (!itemWidth.current) return;
    const newX = currentX.current - itemWidth.current;
    setTransform(newX);
  };

  const handlePrev = () => {
    if (!itemWidth.current) return;
    const newX = currentX.current + itemWidth.current;
    setTransform(newX);
  };

  // 拖拽处理
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setHasDragged(false);
    setDragStart({ x: e.clientX, startX: currentX.current });
    e.preventDefault();
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;
      
      const deltaX = e.clientX - dragStart.x;
      if (Math.abs(deltaX) > 5) setHasDragged(true);
      
      const newX = dragStart.startX + deltaX;
      setTransform(newX, false);
    };

    const handleMouseUp = () => {
      if (isDragging) {
        setIsDragging(false);

        // 拖拽结束后检查是否需要处理无限循环
        setTimeout(() => {
          checkInfiniteLoop();
          setHasDragged(false);
        }, 100);
      }
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragStart]);

  // 渲染用户卡片
  const renderUserCard = (userGroup: UserGroup, keyPrefix: string, index?: number) => {
    const productCount = userGroup.products.length;
    let minWidth = 'min-w-[320px]';

    if (productCount >= 6) {
      minWidth = 'min-w-[920px]';
    } else if (productCount >= 3) {
      minWidth = 'min-w-[920px]';
    } else if (productCount >= 2) {
      minWidth = 'min-w-[620px]';
    }

    return (
      <div
        key={`${keyPrefix}-${userGroup.user.username}-${index}`}
        className={`bg-white rounded-lg p-4 shadow-sm border border-gray-100 ${minWidth} w-fit flex-shrink-0`}
        data-user-card
      >
        <div className="flex items-center space-x-2 mb-4">
          <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
            {(userGroup.user.nickname || userGroup.user.username || 'U').charAt(0).toUpperCase()}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-gray-800 text-sm truncate">
              {userGroup.user.nickname || userGroup.user.username}
            </h3>
          </div>
        </div>

        <div className={styles.productsContainer}>
          <div className={`${styles.productsList} ${styles.flexWrap} ${styles.justifyStart}`}>
            {userGroup.products.slice(0, 3).map((item, index) => {
              const titleLength = item.goodsname?.length || 0;
              const titleLengthClass = titleLength <= 10 ? 'short' : titleLength <= 20 ? 'medium' : 'long';
              const detailUrl = item.goodsurl
                ? `/detail/${item.goodssite || 'taobao'}/?url=${encodeURIComponent(item.goodsurl)}`
                : '#';

              return (
                <a
                  key={`${keyPrefix}-${item.id || item.goodsimg || index}`}
                  href={detailUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`${styles.productCard} ${styles.adaptive} group`}
                  data-title-length={titleLengthClass}
                  onClick={(e) => {
                    if (isDragging || hasDragged) {
                      e.preventDefault();
                    }
                  }}
                >
                  <div className={styles.productImage}>
                    <Image
                      src={prepareImageForNextJs(item.goodsimg)}
                      alt={item.goodsname || '商品图片'}
                      fill
                      sizes="(max-width: 480px) 150px, (max-width: 768px) 250px, 288px"
                      className="object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/images/default.jpg';
                      }}
                    />
                    {item.recommend === 1 && (
                      <div className={styles.recommendTag}>推荐</div>
                    )}
                  </div>
                  <div className="p-3 flex-1 flex flex-col">
                    <h3 className="text-md text-gray-800 line-clamp-2 mb-2 custom-h-48px">
                      {item.goodsname}
                    </h3>
                    <div className="flex items-center justify-between mt-auto">
                      <span className="text-[#FF6B00] font-medium">
                        <span className='text-sm sm:text-base md:text-lg lg:text-xl font-bold'>{formatCurrency(Number(item.goodsprice)).formatValue}</span>
                      </span>
                    </div>
                  </div>
                </a>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="relative w-full group" ref={containerRef}>
      <div className="overflow-hidden rounded-lg">
        <div
          ref={trackRef}
          className="flex gap-6 pl-6 pr-6"
          style={{
            width: 'fit-content',
            willChange: 'transform',
            cursor: isDragging ? 'grabbing' : 'grab',
          }}
          onMouseDown={handleMouseDown}
        >
          {/* 克隆最后一个到开头 */}
          {userGroups.length > 0 && renderUserCard(userGroups[userGroups.length - 1], 'clone-last', -1)}
          
          {/* 原始内容 */}
          {userGroups.map((userGroup, index) =>
            renderUserCard(userGroup, `original-${index}`, index)
          )}
          
          {/* 克隆第一个到结尾 */}
          {userGroups.length > 0 && renderUserCard(userGroups[0], 'clone-first', userGroups.length)}
        </div>
      </div>

      {/* 导航按钮 */}
      {userGroups.length > 1 && (
        <>
          <button
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-gray-700 hover:text-orange-600 p-3 rounded-full shadow-lg transition-all duration-300 z-20 border border-gray-200 opacity-0 group-hover:opacity-100"
            onClick={handlePrev}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-gray-700 hover:text-orange-600 p-3 rounded-full shadow-lg transition-all duration-300 z-20 border border-gray-200 opacity-0 group-hover:opacity-100"
            onClick={handleNext}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      )}
    </div>
  );
}
