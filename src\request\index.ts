import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios';
import { md5 } from 'js-md5';
import { ERROR_CODES } from '@/utils/errorHandler';

let successCode = 1//默认tp5
if (process.env.NEXT_PUBLIC_BACKEND_TYPE == '6') {
  successCode = 200
}

/**
 * 从路径中获取当前语言
 */
const getCurrentLangFromPath = () => {
  if (typeof window !== 'undefined') {
    // 从URL路径中获取语言
    const pathSegments = window.location.pathname.split('/');
    if (pathSegments[1] && pathSegments[1].length > 1) {
      // 检查是否是有效的语言代码
      const validLangs = ['en', 'zh-cn', 'ja']; // 这里应该从配置文件导入
      const langFromPath = pathSegments[1].toLowerCase();

      if (validLangs.includes(langFromPath)) {
        // 保存到localStorage以便后续使用
        localStorage.setItem('selectedLanguage', langFromPath);
        return langFromPath;
      }
    }

    // 如果URL中没有有效语言，尝试从localStorage获取
    const savedLanguage = localStorage.getItem('selectedLanguage');
    if (savedLanguage) {
      return savedLanguage;
    }
  }
  return 'zh-cn'; // 默认语言
};

/**
 * 获取当前货币设置
 */
const getCurrentCurrencyFromPath = () => {
  if (typeof window !== 'undefined') {
    // 优先从URL查询参数获取货币
    const urlParams = new URLSearchParams(window.location.search);
    const currencyParam = urlParams.get('currency');

    if (currencyParam) {
      // 保存到localStorage以便后续使用
      localStorage.setItem('selectedCurrency', currencyParam);
      return currencyParam;
    }

    // 如果URL中没有货币参数，尝试从localStorage获取
    const savedCurrency = localStorage.getItem('selectedCurrency');
    if (savedCurrency) {
      return savedCurrency;
    }

    // 尝试从cookie获取货币
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'currency' && value) {
        // 保存到localStorage以便后续使用
        localStorage.setItem('selectedCurrency', value);
        return value;
      }
    }
  }
  return 'CNY'; // 默认货币
};

export const getTokenFromStorage = (): string | null => {
  try {
    // First try to get from localStorage
    const infoStr = localStorage.getItem('info');
    if (infoStr) {
      try {
        const info = JSON.parse(infoStr);
        const token = info?.data?.userinfo?.token;
        if (token) return token;
      } catch (parseError) {
        console.error('Failed to parse localStorage info:', parseError);
        // Clear invalid data
        localStorage.removeItem('info');
      }
    }

    // Fallback to cookies if localStorage doesn't have token
    const isTp6 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '6';
    const tokenName = isTp6 ? 'access_token' : 'token';

    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === tokenName && value) {
        return value;
      }
    }
  } catch (error) {
    console.error('🚨 Error getting token from storage:', error);
  }

  return null;
};

// 检查用户是否已登录
export const isUserLoggedIn = (): boolean => {
  return !!getTokenFromStorage();
};
/**
 * 检查当前环境是否为安全上下文
 */
function isSecureContext(): boolean {
  if (typeof window === 'undefined') {
    return true; // SSR环境，假设为安全
  }

  // 检查是否为安全上下文
  if ('isSecureContext' in window) {
    return (window as any).isSecureContext;
  }

  // 备用检查：HTTPS 或 localhost
  const protocol = (window as any).location.protocol;
  const hostname = (window as any).location.hostname;

  return protocol === 'https:' ||
         hostname === 'localhost' ||
         hostname === '127.0.0.1' ||
         hostname === '::1';
}

/**
 * 生成UUID的兼容性函数
 */
function generateUUID(): string {
  const isSecure = isSecureContext();



  // 首先尝试使用现代浏览器的 crypto.randomUUID()
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    try {
      return crypto.randomUUID();
    } catch (error) {
      console.warn('🚨 crypto.randomUUID() failed (likely due to insecure context):', error);
      console.warn('💡 Consider using HTTPS for better security');
    }
  }

  // 备用方案：使用 crypto.getRandomValues()
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    try {
      const array = new Uint8Array(16);
      crypto.getRandomValues(array);

      // 转换为UUID格式 (xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx)
      const hex = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
      return [
        hex.slice(0, 8),
        hex.slice(8, 12),
        '4' + hex.slice(13, 16), // 版本4
        ((parseInt(hex.slice(16, 17), 16) & 0x3) | 0x8).toString(16) + hex.slice(17, 20), // 变体位
        hex.slice(20, 32)
      ].join('-');
    } catch (error) {
      console.warn('🚨 crypto.getRandomValues() failed:', error);
    }
  }

  // 最后的备用方案：使用 Math.random()
  if (!isSecure) {
    console.warn('⚠️ Using Math.random() for UUID generation due to insecure context (HTTP)');
    console.warn('💡 For better security, consider deploying with HTTPS');
  }

  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 生成browse_id
 */
function getBrowseId() {
  try {
    // Check if we're in a browser environment
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return generateUUID(); // Return a temporary ID for SSR
    }

    // 优先从本地存储获取 browse_id
    let browse_id = localStorage.getItem('browse_id');
    if (!browse_id) {
      browse_id = generateUUID();
      localStorage.setItem('browse_id', browse_id);
    }
    return browse_id;
  } catch (error) {
    console.error('🚨 Error getting browse ID:', error);
    // Return a fallback ID if localStorage fails
    return generateUUID();
  }
}

// 创建 axios 实例
const getBaseURL = () => {
  // 当 BACKEND_TYPE 为 '6' 时，使用相对路径让 Next.js 处理代理
  if (process.env.NEXT_PUBLIC_BACKEND_TYPE === '6') {
    return ''; // 使用相对路径，让 Next.js rewrites 处理
  }
  return process.env.NEXT_PUBLIC_API_URL || '';
};

const service: AxiosInstance = axios.create({
  baseURL: getBaseURL(),
  timeout: 30000, // 增加超时时间到 30 秒
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 在请求头中添加语言和货币
    config.headers['lang'] = getCurrentLangFromPath();
    config.headers['currency'] = getCurrentCurrencyFromPath();
    config.headers['clienttype'] = 'pc';
    config.headers['browseid'] = getBrowseId();
    config.headers['pageurl'] = typeof window !== 'undefined' ? window.location.origin + window.location.pathname + window.location.search : '';

    // 添加token到请求头
    const token = getTokenFromStorage();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }



    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

service.interceptors.response.use(
  (response: AxiosResponse) => {
    // Add safety check for response and data
    if (!response || !response.data) {
      return Promise.reject(new Error('Invalid response structure'));
    }

    const { data } = response;

    if (data.code === successCode) {
      return data.data;
    } else {
      const currentLang = getCurrentLangFromPath();
      const currentPath = typeof window !== 'undefined' ? window.location.pathname + window.location.search : '';

      // 对于需要邮箱验证的登录错误，返回完整的响应数据而不是抛出错误
      if (data.code === 15024) {
        return data;
      }

      if (data.code === ERROR_CODES.TOKEN_INVALID) {
        // 定义不需要重定向到登录页面的公开页面
        const publicPages = ['/help', '/sizecomparison', '/estimate', '/sharepromotion'];
        const isPublicPage = publicPages.some(page =>
          window.location.pathname.includes(page)
        );

        if (typeof window !== 'undefined' &&
            window.location.pathname !== '/' + getCurrentLangFromPath() &&
            window.location.pathname.indexOf('/search') == -1 &&
            window.location.pathname.indexOf('/detail') == -1 &&
            !isPublicPage) {
          const loginUrl = `/${currentLang}/login?callback=${encodeURIComponent(currentPath)}`;
          window.location.href = loginUrl;
        }
      } else if (data.code === 18008) {
        const orderUrl = `/${currentLang}/dashboard/orders`;
        window.location.href = orderUrl;
      }
      return Promise.reject(new Error(data.message || data.msg || '请求失败'));
    }
  },
  (error) => {

    // 处理 HTTP 错误
    if (error.response) {
      switch (error.response.status) {
        case 401:
          console.error('用户未登录或登录状态已过期');
          // 定义不需要重定向到登录页面的公开页面
          const publicPages = ['/help', '/sizecomparison', '/estimate', '/sharepromotion'];
          const isPublicPage = publicPages.some(page =>
            window.location.pathname.includes(page)
          );

          // 使用 window.location 而不是 router
          if (typeof window !== 'undefined' &&
              window.location.pathname !== '/' + getCurrentLangFromPath() &&
              window.location.pathname.indexOf('/search') === -1 &&
              window.location.pathname.indexOf('/detail') === -1 &&
              !isPublicPage) {
            const currentLang = getCurrentLangFromPath();
            const currentPath = window.location.pathname + window.location.search;
            const loginUrl = `/${currentLang}/login?callback=${encodeURIComponent(currentPath)}`;
            window.location.href = loginUrl;
          }
          break;
        case 403:
          console.error('Permission denied / 没有权限访问 / アクセス権限がありません');
          break;
        case 404:
          console.error('Resource not found / 请求的资源不存在 / リクエストされたリソースが存在しません');
          break;
        case 500:
          console.error('Server error / 服务器错误 / サーバーエラー');
          break;
        default:
          console.error('Network error / 网络错误 / ネットワークエラー');
      }
    } else if (error.code === 'ECONNABORTED') {
      console.error('Request timeout / 请求超时，请检查网络连接 / リクエストタイムアウト、ネットワーク接続を確認してください');
    } else if (error.code === 'ERR_NETWORK') {
      console.error('Network connection failed / 网络连接失败，请检查网络设置 / ネットワーク接続に失敗しました、ネットワーク設定を確認してください');
    } else if (axios.isCancel(error) || (error.message && (error.message.includes('取消请求') || error.message.includes('canceled')))) {
      // 请求被取消，这是正常行为，不需要显示错误
      console.log('请求被取消:', error.message);
      return Promise.reject(error); // 对于取消的请求，仍然需要reject，但不显示错误
    } else {
      console.error('未知错误:', error.message);
    }
    return Promise.reject(error);
  }
);


function genTP6Paramters(data: any) {
  const timestamp = Date.now();

  // 获取当前语言
  const lang_code = getCurrentLangFromPath();

  // 创建一个对象，包含所有需要签名的参数
  let signData = {
    ...data,
    timestamp,
    lang_code,
  };

  // 过滤掉数组和对象类型的参数
  let signParams: Record<string, string | number> = {};
  Object.keys(signData).forEach(key => {
    const value = signData[key];
    if (typeof value === 'string' || typeof value === 'number') {
      signParams[key] = value;
    }
  });

  // 按字典序排序并拼接参数
  const signString = Object.keys(signParams)
    .sort()
    .map(key => `${key}=${signParams[key]}`)
    .join('&');

  // 计算 MD5 签名
  const sign = md5(signString);

  // 返回最终的参数对象
  let result = {
    ...(data instanceof FormData ? {} : data),
    timestamp,
    lang_code,
    sign_type: 'MD5',
    sign
  };
  // 如果是 FormData，手动添加字段
  if (data instanceof FormData) {
    data.forEach((value, key) => {
      if (!(key in result)) {
        result[key] = value;
      }
    });
  }
  return result;
}

// 封装请求方法
export const request = {
  get<T = any>(url: string, params?: Record<string, string | number>, config?: InternalAxiosRequestConfig): Promise<T> {
    return service.get(url, { ...config, params });
  },

  post<T = any>(url: string, data?: any, config?: InternalAxiosRequestConfig): Promise<T> {
    let finalData = data
    if (process.env.NEXT_PUBLIC_BACKEND_TYPE == '6') {
      finalData = genTP6Paramters(data);
    }
    return service.post(url, finalData, config);
  },

  put<T = any>(url: string, data?: any, config?: InternalAxiosRequestConfig): Promise<T> {
    return service.put(url, data, config);
  },

  delete<T = any>(url: string, params?: any, config?: InternalAxiosRequestConfig): Promise<T> {
    return service.delete(url, { ...config, params });
  },
};

export default service; 