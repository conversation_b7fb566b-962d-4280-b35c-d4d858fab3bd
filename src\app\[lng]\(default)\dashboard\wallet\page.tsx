'use client';

import { useState, useEffect ,useRef } from 'react';
import { Api } from '@/request/api';
import type { WalletListResponse, WalletRecord } from '@/types/wallet';
import { Empty, Card, Skeleton } from 'antd';
import Button from '@/components/Button';
import DatePicker from '@/components/DatePicker';
import TabsComponent from '@/components/Tabs';
import Pagination from '@/components/Pagination';
import dayjs from 'dayjs';
import { WalletOutlined, TrophyOutlined } from '@ant-design/icons';
import { useRouter,useParams } from 'next/navigation';
import RechargeModal from '@/components/RechargeModal';
import CouponContent from './components/CouponContent';
import RemitContent from './components/RemitContent';
import WithdrawContent from './components/WithdrawContent';
import WithdrawModal from '@/components/WithdrawModal';
import { formatCurrency } from '@/utils/currency';
import { getDictionary } from "@/dictionaries";
import Toast from '@/components/Toast';
import ToastHelper from '@/utils/toastHelper';

import type { Locale } from '@/config';
const { RangePicker } = DatePicker;

// 交易类型常量
const TRANSACTION_TYPE = {
  ORDER_EXPENDITURE: 1,         // 订单支出
  PARCEL_EXPENSES: 2,           // 包裹支出
  RECHARGE: 3,                  // 充值
  WITHDRAW: 4,                  // 提现
  HANDLING_FEE: 5,              // 支付手续费
  ADMINISTRATOR_OPERATION: 6,    // 管理员操作
  REMITTANCE: 7,                // 汇款
  REBATE: 8,                    // 返佣
  USE_COUPON: 9,                // 优惠券
  ORDER_REFUND: 10,             // 订单退款
  CHARGE: 11,                   // 代充
  SHIPPING_FEE: 12,             // 国际运费
  PROBLEM_PIECES: 13,           // 问题件
  OPEN_VIP: 14,                 // 开通VIP
  CHANGE_GOODSPRICE: 15,        // 修改订单价格
  SENDORDER_REFUND: 16,         // 运单退款
  PAY_FOR_ANTHOR: 17,           // 代付
  SENDORDER_REHEARSAL_REFUND: 18, // 预演包裹退款
  SENDORDER_REHEARSAL: 19,      // 预演包裹 预演费用支出
  SENDORDER_REHEARSAL_FREIGHT: 20 // 预演包裹 运费支出
};

export default function WalletPage() {
  const router = useRouter();
  const params = useParams();
  const lng = params.lng as Locale;
  const [dict, setDict] = useState<any>(null);
  const [walletData, setWalletData] = useState<WalletListResponse | null>(null);
  const [dateRange, setDateRange] = useState<[string, string]>(['', '']);
  const [activeTab, setActiveTab] = useState('all');
  const [transactionType, setTransactionType] = useState('all');
  const [loading, setLoading] = useState(false);
  const [activeTopTab, setActiveTopTab] = useState('balance');
  const [showRechargeModal, setShowRechargeModal] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [pointData, setPointData] = useState<any>(null);
  const [pointLoading, setPointLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [activeSubTab, setActiveSubTab] = useState('transaction');
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
  // 创建ref来引用子组件
  const withdrawContentRef = useRef<{ refresh: () => void }>(null);

  // 获取字典数据
  useEffect(() => {
    const loadDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };
    
    loadDictionary();
  }, [lng]);

  const fetchWalletList = async (starttime = '', endtime = '', type = '', page = 1) => {
    setLoading(true);
    try {
      const res = await Api.getWalletList({ action: type, starttime, endtime, page, size: pageSize });
      if (res.success) {
        setWalletData(res.data);
        // 设置分页信息
        if (res.data.last_page) {
          setTotalPages(res.data.last_page);
        }
      } else {
        Toast.error(dict?.dashboard?.wallet?.getRecordsFailed);
      }
    } catch (error) {
      console.error('获取钱包记录失败:', error);
      Toast.error(dict?.dashboard?.wallet?.networkError);
    } finally {
      setLoading(false);
    }
  };

  const fetchPointList = async () => {
    setPointLoading(true);
    try {
      const res = await Api.getPointList({size:20});
      if (res.success) {
        setPointData(res.data.data || res.data);
      } else {
        ToastHelper.showError('fetch_points_failed', lng as string);
      }
    } catch (error) {
      console.error('获取积分记录失败:', error);
      ToastHelper.showNetworkError(lng as string);
    } finally {
      setPointLoading(false);
    }
  };

  useEffect(() => {
    fetchWalletList('', '', '', 1);
  }, []);

  useEffect(() => {
    if (activeTopTab === 'points') {
      fetchPointList();
    }
  }, [activeTopTab]);

  const handleDateChange = (dates: any) => {
    setCurrentPage(1); // 重置到第一页
    if (!dates) {
      setDateRange(['', '']);
      fetchWalletList('', '', transactionType !== 'all' ? transactionType : '', 1);
      return;
    }
    const [start, end] = dates;
    const startStr = start ? dayjs(start).format('YYYY-MM-DD') : '';
    const endStr = end ? dayjs(end).format('YYYY-MM-DD') : '';
    setDateRange([startStr, endStr]);
    fetchWalletList(startStr, endStr, transactionType !== 'all' ? transactionType : '', 1);
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setCurrentPage(1); // 重置到第一页
    if (key === 'income') {
      fetchWalletList(dateRange[0], dateRange[1], 'income', 1);
      setTransactionType('income');
    } else if (key === 'expense') {
      fetchWalletList(dateRange[0], dateRange[1], 'expense', 1);
      setTransactionType('expense');
    } else {
      fetchWalletList(dateRange[0], dateRange[1], '', 1);
      setTransactionType('all');
    }
  };

  const handleRecharge = () => {
    setShowRechargeModal(true);
  };

  const handleRechargeConfirm = async (amount: number) => {
    try {
      // 获取当前选择的货币
      const selectedCurrency = localStorage.getItem('selectedCurrency') || 'CNY';
      const res = await Api.recharge({ money: amount, currency: selectedCurrency as any });
      if (res.success) {
        let trade_sn = isTp5 ? res.data : res.data.trade_sn
        router.push(`/pay?tradesn=${trade_sn}&type=cz`);
        setShowRechargeModal(false);
      } else {
        Toast.error(res.msg || dict?.dashboard?.wallet?.depositFailed);
      }
    } catch (error) {
      console.error('充值失败:', error);
      Toast.error(dict?.dashboard?.wallet?.networkError);
    }
  };
  const handleWithdrawConfirm = async () => {
   console.log('handleWithdrawConfirm:');
    // 调用子组件的刷新方法
    withdrawContentRef.current?.refresh();
    setShowWithdrawModal(false);
  };
  

  const handleWithdraw = () => {
    // router.push(`/dashboard/cash`);
    setShowWithdrawModal(true);
  };

  const handleTopTabChange = (key: string) => {
    setActiveTopTab(key);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchWalletList(dateRange[0], dateRange[1], transactionType !== 'all' ? transactionType : '', page);
  };

  const currentBalance = walletData?.data[0]?.after_source || walletData?.data[0]?.after || 0.00;

  // 判断交易类型是否为支出
  const isExpense = (type: number) => {
    return type === TRANSACTION_TYPE.ORDER_EXPENDITURE || 
           type === TRANSACTION_TYPE.PARCEL_EXPENSES || 
           type === TRANSACTION_TYPE.WITHDRAW || 
           type === TRANSACTION_TYPE.HANDLING_FEE || 
           type === TRANSACTION_TYPE.CHARGE || 
           type === TRANSACTION_TYPE.SHIPPING_FEE || 
           type === TRANSACTION_TYPE.OPEN_VIP || 
           type === TRANSACTION_TYPE.PAY_FOR_ANTHOR || 
           type === TRANSACTION_TYPE.SENDORDER_REHEARSAL || 
           type === TRANSACTION_TYPE.SENDORDER_REHEARSAL_FREIGHT;
  };

  // 判断积分是否为支出（负数表示支出，正数表示收入）
  const isPointExpense = (score: number) => { 
    return score < 0;
  };

  // 判断交易类型
  const getTransactionTypeInfo = (record: WalletRecord) => {
    const orderId = record.memo?.match(/#(\d+)/)?.[1] || '';

    if (record.type === TRANSACTION_TYPE.RECHARGE) {
      return dict?.dashboard?.wallet?.alipayDeposit;
    } else if (record.type === TRANSACTION_TYPE.WITHDRAW) {
      return dict?.dashboard?.wallet?.withdraw;
    } else if (record.type === TRANSACTION_TYPE.ORDER_REFUND) {
      return `${dict?.dashboard?.wallet?.orderRefund} #${orderId}`;
    } else if (record.type === TRANSACTION_TYPE.ORDER_EXPENDITURE) {
      return `${dict?.dashboard?.wallet?.orderPayment} #${orderId}`;
    }
    return '';
  };

  const topTabItems = [
    {
      key: 'balance',
      label: dict?.dashboard?.wallet?.remaining,
    },
    {
      key: 'coupon',
      label: dict?.dashboard?.wallet?.coupon,
    },
    {
      key: 'points',
      label: dict?.dashboard?.wallet?.pointUnit,
    },
  ];

  const handleExchangeCoupon = async (couponId: number) => {
    try {
      const res = await Api.exchangeCoupon({
        coupon_id: couponId,
        num: 1
      });
      if (res.success) {
        Toast.success(dict?.dashboard?.wallet?.redeemSuccess);
        // 刷新积分数据
        fetchPointList();
      } else {
        Toast.error(res.msg ||dict?.dashboard?.wallet?.redeemFail);
      }
    } catch (error) {
      console.error('兑换失败:', error);
      Toast.error(dict?.dashboard?.wallet?.networkError);
    }
  };

  // 渲染不同的内容区域
  const renderContent = () => {
    switch (activeTopTab) {
      case 'balance':
        return (
          <div className="mx-auto px-4">
            {/* 余额卡片 */}
            <Card className="mb-6! shadow-sm border-0">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                <div className="mb-4 md:mb-0">
                  <div className="flex items-center gap-2 mb-2">
                    <WalletOutlined className="text-xl text-orange-500" />
                    <span className="text-gray-700 font-medium">{dict?.dashboard?.wallet?.title}</span>
                  </div>
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold" style={{ color: process.env.NEXT_PUBLIC_BASE_COLOR as any }}>
                      {formatCurrency(Number(currentBalance)).formatValue}
                    </span>
                  </div>
                </div>
                <div className="flex gap-3">
                  <Button 
                    type="primary" 
                    className="bg-orange-500 hover:bg-orange-600 flex items-center gap-1 h-10"
                    onClick={handleRecharge}
                  >
                   {dict?.dashboard?.wallet?.deposit}
                  </Button>
                  <Button 
                    type="primary" 
                    className="bg-orange-500 hover:bg-orange-600 flex items-center gap-1 h-10"
                    onClick={handleWithdraw}
                  >
                   {dict?.dashboard?.wallet?.withdraw}
                  </Button>
                </div>
              </div>
            </Card>
            {/* 二级标签导航 （交易记录 ,提现记录，汇款记录 */}
            <TabsComponent
              activeKey={activeSubTab}
              onChange={setActiveSubTab}
              items={[
                { key: 'transaction', label: dict?.dashboard?.wallet?.transactionRecords || '交易记录' },
                { key: 'remittance', label: dict?.dashboard?.wallet?.remittanceRecords || '汇款记录' },
                { key: 'withdrawal', label: dict?.dashboard?.wallet?.withdrawalRecords || '提现记录' },
              ]}
            />

            {/* 动态内容区域 */}
            {renderSubTabContent()}
          </div>
        );
      case 'coupon':
        return <CouponContent dict={dict} />;
      case 'points':
        return (
          <div className="mx-auto px-4">
            <Card className="mb-6 shadow-sm border-0">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                <div className="mb-4 md:mb-0">
                  <div className="flex items-center gap-2 mb-2">
                    <TrophyOutlined className="text-xl text-orange-500" />
                    <span className="text-gray-700 font-medium">{dict?.dashboard?.wallet?.points}</span>
                  </div>
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold text-orange-500">
                      {pointData?.list?.data?.[0]?.after || 0}
                    </span>
                    <span className="text-gray-400 ml-2 text-sm">{dict?.dashboard?.wallet?.pointUnit}</span>
                  </div>
                </div>
              </div>
            </Card>

            {/* 积分兑换区域 */}
            <Card className="mb-6 shadow-sm border-0">
              <h3 className="text-lg font-medium mb-4">{dict?.dashboard?.wallet?.redeemTitle}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {pointData?.coupons?.data?.map((coupon: any) => (
                  <div key={coupon.id} className="border rounded-lg p-4">
                    <div className="flex items-center gap-4">
                      <div className="w-16 h-16 flex-shrink-0">
                        <img 
                          src={coupon.moneyimg} 
                          alt={dict?.dashboard?.wallet?.coupon}
                          className="w-full h-full object-cover rounded"
                        />
                      </div>
                      <div className="flex-1">
                        <div className="text-lg font-bold text-orange-500">{formatCurrency(Number(coupon.money)).formatValue}</div>
                        <div className="text-sm text-gray-500">{dict?.dashboard?.wallet?.pointsNeeded}: {coupon.score}</div>
                        <div className="text-xs text-gray-400">{coupon.userlevel.name}</div>
                      </div>
                      <Button 
                        type="primary"
                        className="bg-orange-500 hover:bg-orange-600"
                        disabled={Number(pointData?.list?.data?.[0]?.after || 0) < coupon.score}
                        onClick={() => handleExchangeCoupon(coupon.id)}
                      >
                        {dict?.dashboard?.wallet?.redeemNow}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* 积分记录表格 */}
            <Card className="shadow-sm border-0">
              <div className="flex items-center justify-between mb-5">
                <h3 className="text-lg font-medium m-0">{dict?.dashboard?.wallet?.pointLog}</h3>
              </div>

              {pointLoading ? (
                <Skeleton active paragraph={{ rows: 5 }} />
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full border-collapse">
                    <thead>
                      <tr className="border-b text-gray-600 bg-gray-50">
                        <th className="py-3 px-4 text-left font-medium text-sm">{dict?.dashboard?.wallet?.time}</th>
                        <th className="py-3 px-4 text-left font-medium text-sm">{dict?.dashboard?.wallet?.category}</th>
                        <th className="py-3 px-4 text-left font-medium text-sm">{dict?.dashboard?.wallet?.inOut}</th>
                        <th className="py-3 px-4 text-left font-medium text-sm">{dict?.dashboard?.wallet?.pointUnit}</th>
                        <th className="py-3 px-4 text-left font-medium text-sm">{dict?.dashboard?.wallet?.remark}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {pointData?.list?.data && pointData.list.data.length > 0 ? (
                        pointData.list.data.map((record: any) => (
                          <tr key={record.id} className="border-b hover:bg-gray-50">
                            <td className="py-3 px-4 text-gray-700">{record.createtime}</td>
                            <td className="py-3 px-4">
                              <span className={`px-2 py-1 rounded-full text-xs ${isPointExpense(record.score) ? 'bg-red-50 text-red-500' : 'bg-green-50 text-green-500'}`}>
                                {record.type_text}
                              </span>
                            </td>
                            <td className="py-3 px-4">
                              <span className={`${isPointExpense(record.score) ? 'text-red-500' : 'text-green-500'} font-medium`}>
                                {record.score > 0 ? '+' : ''}{record.score}
                              </span>
                            </td>
                            <td className="py-3 px-4">{record.after}</td>
                            <td className="py-3 px-4 text-gray-500">{record.memo}</td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={5} className="py-8 text-center text-gray-500">
                            <Empty description={dict?.dashboard?.wallet?.noPtsRecords} />
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              )}
            </Card>
          </div>
        );
      default:
        return null;
    }
  };

  // 添加子标签内容渲染函数
  const renderSubTabContent = () => {
    switch(activeSubTab) {
      case 'transaction':
        return (
           <Card className="shadow-sm border-0">
              <div className="flex items-center justify-between mb-5">
                <h3 className="text-lg font-medium m-0">{dict?.dashboard?.wallet?.records}</h3>
                <div className="flex gap-4">
                  <RangePicker 
                    onChange={handleDateChange} 
                    placeholder={['-/-/-', '-/-/-']}
                    className="w-64"
                  />
                 
                </div>
              </div>

              {/* 交易类型过滤标签 */}
              <div className="flex mb-5 bg-gray-50 rounded-lg p-1 w-fit">
                <div 
                  className={`px-4 py-2 cursor-pointer rounded-md transition-colors ${activeTab === 'all' ? 'bg-white shadow-sm' : 'hover:bg-gray-100'}`}
                  onClick={() => handleTabChange('all')}
                >
                  {dict?.dashboard?.wallet?.all}
                </div>
                <div 
                  className={`px-4 py-2 cursor-pointer rounded-md transition-colors ${activeTab === 'income' ? 'bg-white shadow-sm' : 'hover:bg-gray-100'}`}
                  onClick={() => handleTabChange('income')}
                >
                 {dict?.dashboard?.wallet?.income}
                </div>
                <div 
                  className={`px-4 py-2 cursor-pointer rounded-md transition-colors ${activeTab === 'expense' ? 'bg-white shadow-sm' : 'hover:bg-gray-100'}`}
                  onClick={() => handleTabChange('expense')}
                >
                   {dict?.dashboard?.wallet?.expense}
                </div>
              </div>

              {/* 交易记录表格 */}
              {loading ? (
                <Skeleton active paragraph={{ rows: 5 }} />
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full border-collapse">
                    <thead>
                      <tr className="border-b text-gray-600 bg-gray-50">
                        <th className="py-3 px-4 text-left font-medium text-sm">{dict?.dashboard?.wallet?.time}</th>
                        <th className="py-3 px-4 text-left font-medium text-sm">{dict?.dashboard?.wallet?.category}</th>
                        <th className="py-3 px-4 text-left font-medium text-sm">{dict?.dashboard?.wallet?.inOut}</th>
                        <th className="py-3 px-4 text-left font-medium text-sm">{dict?.dashboard?.wallet?.remaining}</th>
                        <th className="py-3 px-4 text-left font-medium text-sm">{dict?.dashboard?.wallet?.transNo}</th>
                        <th className="py-3 px-4 text-left font-medium text-sm">{dict?.dashboard?.wallet?.remark}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {walletData?.data && walletData.data.length > 0 ? (
                        walletData.data.map((record) => (
                          <tr key={record.id} className="border-b hover:bg-gray-50">
                            <td className="py-3 px-4 text-gray-700">{record.createtime}</td>
                            <td className="py-3 px-4">
                              <span className={`px-2 py-1 rounded-full text-xs ${isExpense(record.type) ? 'bg-red-50 text-red-500' : 'bg-green-50 text-green-500'}`}>
                                {record.type_text}
                              </span>
                            </td>
                            <td className="py-3 px-4">
                              <span className={`${isExpense(record.type) ? 'text-red-500' : 'text-green-500'} font-medium`}>
                               {formatCurrency(Number(record.money)).formatValue}
                              </span>
                            </td>
                            <td className="py-3 px-4">{formatCurrency(Number(record.after)).formatValue}</td>
                            <td className="py-3 px-4 text-gray-500 font-mono text-xs">{record.memo}</td>
                            <td className="py-3 px-4 text-gray-500">{getTransactionTypeInfo(record)}</td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={6} className="py-8 text-center text-gray-500">
                            <Empty description={dict?.dashboard?.wallet?.noRecords} />
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              )}

              {/* 分页组件 */}
              {!loading && walletData?.data && walletData.data.length > 0 && totalPages > 1 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  pageSize={pageSize}
                  onPageChange={handlePageChange}
                  prevText={dict?.dashboard?.wallet?.prevPage || '上一页'}
                  nextText={dict?.dashboard?.wallet?.nextPage || '下一页'}
                />
              )}
            </Card>
        );
        
      case 'remittance':
        return (
          <Card className="shadow-sm border-0 mt-4">
            {/* 汇款记录内容 */}
            <div >
              <RemitContent  />
            </div>
          </Card>
        );
        
        case 'withdrawal':
        return (
          <Card className="shadow-sm border-0 mt-4">
            {/* 提现记录内容 */}
            <div >
                <WithdrawContent ref={withdrawContentRef} />
            </div>
          </Card>
        );
      
      default:
        return null;
    }
  };
  return (
    <div className="bg-gray-50 min-h-screen pb-6">

      <div className="border-b bg-white sticky top-0 z-10 mb-6">
        <TabsComponent
          activeKey={activeTopTab}
          onChange={handleTopTabChange}
          items={topTabItems}
          className="px-4 mx-auto"
          tabBarStyle={{ margin: 0 }}
        />
      </div>

      {renderContent()}
        { <WithdrawModal 
        open={showWithdrawModal}
        onCancel={() => setShowWithdrawModal(false)}
        onOk={handleWithdrawConfirm}
      />}

      {dict && <RechargeModal 
        open={showRechargeModal}
        onCancel={() => setShowRechargeModal(false)}
        onOk={handleRechargeConfirm}
        dict={dict}
      />}
    </div>
  );
}
  