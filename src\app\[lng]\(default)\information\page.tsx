"use client";

import React, { useState, useEffect } from "react";
import { Api } from "@/request/api";
import Toast from "@/components/Toast";
import { useParams } from "next/navigation";
import { getDictionary } from "@/dictionaries";
import { useSearchParams } from "next/navigation";
import Loading from "@/components/Loading";

export default function Information() {
  const { lng } = useParams();
  const [dict, setDict] = useState<any>(null);
  const [information, setInformation] = useState<any>(null);
  const searchParams = useSearchParams();
  const id = searchParams.get("id");

  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error("Failed to load dictionary:", error);
      }
    };
    fetchDictionary();
  }, [lng]);
  useEffect(() => {
    const fetchInformation = async () => {
      if (!id) return;

      try {
        // 首先尝试使用文章接口
        const res = await Api.getArticleDetail(parseInt(id));
        console.log('Article API response:', res);

        if (res.code === 200 && res.data && Array.isArray(res.data) && res.data.length > 0) {
          // 文章API返回的是数组格式，取第一个元素
          setInformation(res.data[0]);
        } else {
          // 如果文章接口失败，尝试信息接口
          const infoRes = await Api.getInformationDetail(parseInt(id));
          console.log('Information API response:', infoRes);
          setInformation(infoRes.data);
        }
      } catch (error) {
        console.error("Failed to load Information:", error);
        // 如果文章接口失败，尝试信息接口作为备选
        try {
          const infoRes = await Api.getInformationDetail(parseInt(id));
          setInformation(infoRes.data);
        } catch (fallbackError) {
          console.error("Failed to load Information from fallback:", fallbackError);
        }
      }
    };
    fetchInformation();
  }, [lng, id]);

  if (!information) {
    return (
      <div className="container mx-auto py-6 min-h-[calc(100vh-400px)]">
        <Loading height="calc(100vh - 400px)" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 min-h-[calc(100vh-400px)]">
     
      {information?.content || information?.description ? (
        <div
          className="prose max-w-none"
          dangerouslySetInnerHTML={{
            __html: information.content || information.description || ''
          }}
        />
      ) : (
        <div className="text-center text-gray-500">
          <p>{dict?.help?.error || '文章内容不存在或加载失败'}</p>
          <p className="text-sm mt-2">
            {lng === 'en' ? `Article ID: ${id}` : lng === 'ja' ? `記事ID: ${id}` : `文章ID: ${id}`}
          </p>
        </div>
      )}
    </div>
  );
}
