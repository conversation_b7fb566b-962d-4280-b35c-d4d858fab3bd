type AsyncFunction = (...args: any[]) => Promise<any>;

export function catchError() {
  return function (
    target: any,
    propertyKey: string | symbol,
    descriptor: TypedPropertyDescriptor<AsyncFunction>
  ): TypedPropertyDescriptor<AsyncFunction> {
    const originalMethod = descriptor.value!;

    descriptor.value = async function (...args: any[]) {
      try {
        const result = await originalMethod.apply(this, args);
        return {
          success: true,
          data: result
        };
      } catch (error: any) {
        console.error('[装饰器报错] 请求出现错误：', error);

        // 如果 error.response.data 存在，说明是axios的HTTP错误，且服务器有返回
        if (error?.response?.data) {
          // 将后端的完整错误信息(code, msg, data)返回
          return {
            success: false,
            ...error.response.data
          };
        }

        // 其他错误 (如网络问题、代码执行错误)
        return {
          success: false,
          msg: error instanceof Error ? error.message : '操作失败'
        };
      }
    };

    return descriptor;
  };
} 