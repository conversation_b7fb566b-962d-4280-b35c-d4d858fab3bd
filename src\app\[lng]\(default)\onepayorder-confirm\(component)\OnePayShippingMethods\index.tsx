'use client'
import React, { useState, useEffect } from 'react'
import { Radio, Spin, Empty, message } from 'antd'
import { Api } from '@/request/api'
import { formatCurrency } from '@/utils/currency'
import { useSearchParams } from 'next/navigation'

interface ShippingMethodFee {
  totalfee: number;
  firstfee: string;
  continuefee: number;
  sendfee: string;
  customsfee: string;
  serverfee: string;
  fuelfee: string;
  freemoney: string;
}

interface ShippingMethod {
  template_id: number;
  fee: ShippingMethodFee;
  weigh: number;
  firstweight: number;
  continueweight: number;
  continuefee: string;
  firstfee: string;
  ban: string;
  cycle: string;
  feature: string;
  minweight: number;
  maxweight: number;
  logo: string;
  customsfee: string;
  name: string;
  type: string[];
  insurance_rate: string;
}

interface OnePayShippingMethodsProps {
  dict: any;
  cartData?: any; // Cart data for calculating shipping
}

export default function OnePayShippingMethods({ dict, cartData }: OnePayShippingMethodsProps) {
  const [loading, setLoading] = useState(false);
  const [methods, setMethods] = useState<ShippingMethod[]>([]);
  const [selectedMethod, setSelectedMethod] = useState<number | null>(null);
  const searchParams = useSearchParams();

  useEffect(() => {
    const addressId = searchParams.get('address_id');
    if (addressId) {
      setTimeout(async () => { 
        await fetchShippingMethods(Number(addressId));
      }, 500);
    }
  }, [searchParams.get('address_id')]);

  const fetchShippingMethods = async (addressId: number) => {
    if (!addressId) return;

    setLoading(true);
    try {
      // First, get the address details to obtain the area_id
      const addressResponse = await Api.getAddressList();
      if (!addressResponse.success || !addressResponse.data) {
        console.error('Failed to get address list');
        setLoading(false);
        return;
      }

      // Find the selected address by its ID
      const selectedAddress = addressResponse.data.find((addr: any) => addr.id === addressId);
      if (!selectedAddress) {
        console.error('Selected address not found');
        setLoading(false);
        return;
      }

      // For one-time payment, we need to calculate shipping based on cart items
      const cart_ids = searchParams.get('cart_ids');
      if (!cart_ids) return;

      // Calculate shipping parameters from cart data
      let totalWeight = 1;
      let totalVolume = 1;
      let goodsType;
      let totalPrice = 100;

      if (cartData && cartData.cartlist && Array.isArray(cartData.cartlist)) {
        console.log('111',cartData.cartlist[0].goodstype)
        goodsType = cartData.cartlist[0].goodstype;
        // Calculate total weight and volume from cart items
        totalWeight = cartData.cartlist.reduce((sum: number, item: any) => {
          const quantity = item.goodsnum || 1;
          return sum + ((item.weight || 0.5) * quantity); // Default 0.5kg if no weight
        }, 0);

        totalVolume = cartData.cartlist.reduce((sum: number, item: any) => {
          const quantity = item.goodsnum || 1;
          return sum + ((item.volume || 100) * quantity); // Default 100cm³ if no volume
        }, 0);


        // Calculate total price
        totalPrice = cartData.cartlist.reduce((sum: number, item: any) => {
          const quantity = item.goodsnum || 1;
          const price = parseFloat(String(item.goodsprice)) || 0;
          return sum + (price * quantity);
        }, 0);
      }

      // Fetch shipping estimates using the area_id from the selected address
      const response = await Api.getEstimates({
        area_id: selectedAddress.area_id, // Use area_id instead of address id
        weight: totalWeight,
        volume: totalVolume,
        goodstype: Number(goodsType) || 0, // 传递单个数字而不是数组
        stotalmoney: totalPrice,
        clienttype: 'pc'
      });

      if (response.success) {
        setMethods(response.data);

        // Check if there's a pre-selected method from URL
        const templateIdFromUrl = searchParams.get('template_id');
        let selectedMethodInfo = null;

        if (templateIdFromUrl) {
          const preSelectedMethod = response.data.find((method: ShippingMethod) =>
            method.template_id === Number(templateIdFromUrl)
          );
          if (preSelectedMethod) {
            setSelectedMethod(preSelectedMethod.template_id);
            selectedMethodInfo = preSelectedMethod;
          } else if (response.data.length > 0) {
            setSelectedMethod(response.data[0].template_id);
            updateUrlParams(response.data[0].template_id);
            selectedMethodInfo = response.data[0];
          }
        } else if (response.data.length > 0) {
          // Default to first method
          setSelectedMethod(response.data[0].template_id);
          updateUrlParams(response.data[0].template_id);
          selectedMethodInfo = response.data[0];
        }

        // Store initial selected shipping method info
        if (selectedMethodInfo && typeof window !== 'undefined') {
          window.selectedShippingMethod = {
            template_id: selectedMethodInfo.template_id,
            fee: selectedMethodInfo.fee,
            name: selectedMethodInfo.name
          };


        }
      } else {
        message.error(dict?.confirm?.order?.transport?.fetchError || 'Failed to fetch shipping methods');
      }
    } catch (error) {
      console.error('Error fetching shipping methods:', error);
      message.error(dict?.confirm?.order?.transport?.loadError || 'Failed to load shipping methods');
    } finally {
      setLoading(false);
    }
  };

  const updateUrlParams = (templateId: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('template_id', templateId.toString());
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
  };

  const handleMethodChange = (e: any) => {
    const templateId = e.target.value;
    setSelectedMethod(templateId);
    updateUrlParams(templateId);

    // Store selected shipping method info in window object for order summary
    const selectedMethodInfo = methods.find(method => method.template_id === templateId);
    if (selectedMethodInfo && typeof window !== 'undefined') {
      window.selectedShippingMethod = {
        template_id: templateId,
        fee: selectedMethodInfo.fee,
        name: selectedMethodInfo.name
      };



      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('shippingMethodChanged', {
        detail: window.selectedShippingMethod
      }));
    }
  };

  return (
    <div>
      <Spin spinning={loading}>
        {methods.length > 0 ? (
          <div>
            <Radio.Group onChange={handleMethodChange} value={selectedMethod} className="w-full">
              <div className="space-y-4">
                {methods.map((method) => (
                  <div
                    key={method.template_id}
                    className={`border rounded p-4 ${
                      selectedMethod === method.template_id
                        ? 'border-orange-200 bg-orange-50'
                        : 'border-gray-200'
                    }`}
                  >
                    <Radio value={method.template_id} className="w-full">
                      <div className="flex justify-between items-start gap-4">
                        <div className="flex items-start space-x-3 flex-1 min-w-0">
                          {method.logo ? (
                            <img
                              src={method.logo}
                              alt={method.name}
                              className="w-12 h-8 object-contain rounded flex-shrink-0"
                            />
                          ) : (
                            <div className="w-12 h-8 bg-gray-600 rounded flex items-center justify-center flex-shrink-0">
                              <span className="text-white text-xs font-bold">
                                {method.name.substring(0, 3)}
                              </span>
                            </div>
                          )}
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm">
                              {method.name} {method.cycle && `${method.cycle}`}
                            </div>
                            {method.feature && (
                              <div className="text-xs text-gray-500 mt-1 break-words">
                                {method.feature}
                              </div>
                            )}
                            <div className="flex flex-wrap gap-x-4 gap-y-1 text-xs text-gray-500 mt-2">
                              <span className="whitespace-nowrap">{dict?.confirm?.transport?.firstWeight || 'First Weight'}：{method.firstweight}g/{formatCurrency(parseFloat(method.firstfee || '0')).formatValue}</span>
                              <span className="whitespace-nowrap">{dict?.confirm?.transport?.continueWeight || 'Additional Weight'}：{method.continueweight}g/{formatCurrency(parseFloat(method.continuefee || '0')).formatValue}</span>
                              {method.minweight && method.maxweight && (
                                <span className="whitespace-nowrap">{dict?.confirm?.transport?.weightRange || 'Weight Range'}：{method.minweight}g-{method.maxweight}g</span>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="text-right flex-shrink-0 min-w-[120px]">
                          <div className="text-orange-500 text-sm font-medium whitespace-nowrap">
                            {dict?.confirm?.onepayorder?.shipping?.shippingFee || 'Shipping Fee'} {formatCurrency(method.fee.totalfee).formatValue}
                          </div>
                          <div className="text-xs text-gray-500 mt-1 space-y-1">
                            {method.fee.fuelfee && parseFloat(method.fee.fuelfee) > 0 && (
                              <div className="whitespace-nowrap">{dict?.confirm?.onepayorder?.summary?.fuelFee || 'Fuel Fee'} {formatCurrency(parseFloat(method.fee.fuelfee)).formatValue}</div>
                            )}
                            {method.fee.sendfee && parseFloat(method.fee.sendfee) > 0 && (
                              <div className="whitespace-nowrap">{dict?.confirm?.onepayorder?.shipping?.deliveryFee || 'Delivery Fee'} {formatCurrency(parseFloat(method.fee.sendfee)).formatValue}</div>
                            )}
                            {method.fee.serverfee && parseFloat(method.fee.serverfee) > 0 && (
                              <div className="whitespace-nowrap">{dict?.confirm?.onepayorder?.summary?.transportServiceFee || 'Service Fee'} {formatCurrency(parseFloat(method.fee.serverfee)).formatValue}</div>
                            )}
                            {method.fee.customsfee && parseFloat(method.fee.customsfee) > 0 && (
                              <div className="whitespace-nowrap">{dict?.confirm?.onepayorder?.summary?.customsFee || 'Customs Fee'} {formatCurrency(parseFloat(method.fee.customsfee)).formatValue}</div>
                            )}
                          </div>
                        </div>
                      </div>
                    </Radio>
                  </div>
                ))}
              </div>
            </Radio.Group>

            {/* Shipping Notice */}
            <div className="mt-6">
              <div className="flex items-center mb-3">
                <div className="w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center mr-2">
                  <span className="text-white text-xs">!</span>
                </div>
                <span className="text-sm font-medium text-gray-700">{dict?.confirm?.onepayorder?.shipping?.shippingNotice || 'Shipping Notice'}</span>
              </div>
              <div className="text-xs text-gray-600 space-y-1">
                <div>• {dict?.confirm?.onepayorder?.shipping?.actualShippingNotice || 'Actual shipping fee is subject to final settlement. We will notify you promptly if there are any differences after warehouse re-measurement'}</div>
                <div>• {dict?.confirm?.onepayorder?.shipping?.volumeWeightFormula || 'Volume Weight = Length(cm) × Width(cm) × Height(cm) ÷ Volume Weight'}</div>
                <div>• {dict?.confirm?.onepayorder?.shipping?.chargeableWeightFormula || 'Chargeable Weight = max (Actual Weight, Volume Weight)'}</div>
                <div>• {dict?.confirm?.onepayorder?.shipping?.securityCheckNotice || 'All packages are shipped after security inspection'}</div>
              </div>
            </div>
          </div>
        ) : (
          !loading && <Empty description="暂无可用运输方式"/>
        )}
      </Spin>
    </div>
  );
}
