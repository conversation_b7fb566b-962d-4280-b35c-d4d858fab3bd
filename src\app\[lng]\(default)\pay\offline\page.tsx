"use client";
import { Select, Radio, Form, Input, Upload } from "antd";
import Button from "@/components/Button";
import { LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import { useState, useEffect } from "react";
import Toast from "@/components/Toast";
import { Api } from "@/request/api";
import { useSearchParams, useRouter } from "next/navigation";
import { md5 } from "js-md5";
import { useParams } from "next/navigation";
import { getDictionary } from "@/dictionaries";

export default function OfflinePage() {
  // 地区列表
  const [areaList, setAreaList] = useState(
    [] as { name: string; area_id: Number; id: Number }[]
  );
  const [selectedAreaId, setSelectedAreaId] = useState<number | null>();
  // 充值方式
  const [rechargeTypeList, setRechargeTypeList] = useState(
    [] as { name: string; id: string; code: string }[]
  );
  const [selectedRechargeType, setSelectedRechargeType] = useState("");
  //   收款人信息
  const [payeeInfo, setPayeeInfo] = useState(
    {} as { name: string; account: string; explain: string }
  );

  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [voucherPic, setVoucherPic] = useState<string | undefined>(undefined);
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === "5";
  const searchParams = useSearchParams();
  const router = useRouter();
  const { lng } = useParams();
  const [dict, setDict] = useState<any>(null);

  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error("Failed to load dictionary:", error);
      }
    };
    fetchDictionary();
  }, [lng]);
  useEffect(() => {
    // 获取地区列表
    const fetchAreaList = async () => {
      try {
        const response = await Api.getOfflineAreaList();
        if (response?.success) {
          let data = response.data || [];
          setAreaList(data);
          if (data.length) {
            setSelectedAreaId(data[0].area_id); // 默认选中第一个地区
            getRechargeTypeList(data[0].area_id); // 默认获取第一个地区的充值方式
          }
        } else {
          Toast.error(response?.msg || dict?.pay?.offlinePay?.uploadError);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchAreaList();
  }, []);
  // 获取充值方式列表（根据地区ID）
  const getRechargeTypeList = async (id: number) => {
    console.log("获取充值方式:地区id:", id);
    try {
      const response = await Api.getOfflinePayList({ area_id: id });
      if (response?.success) {
        let data = response.data || [];
        setRechargeTypeList(data);
        if (data.length) {
          setSelectedRechargeType(data[0].id); // 默认选中第一个地区的充值方式
          getOfflineDetail(data[0].id); // 获取默认充值方式的详情
        }
      } else {
        Toast.error(response?.msg || dict?.pay?.offlinePay?.fetchError);
      }
    } catch (error) {
      console.error("获取充值方式列表失败:", error);
      Toast.error(dict?.pay?.offlinePay?.fetchError);
    }
  };
  // 获取线下充值详情
  const getOfflineDetail = async (id: string) => {
    let response = await Api.getOfflineDetail({
      tradesn: searchParams.get("tradesn"),
      payid: id,
    });

    if (response?.success) {
      let data = response?.data || {};
      setPayeeInfo({
        name: data.cardholder,
        account: data.cardno,
        explain: data.content,
      });
      form.setFieldsValue({
        currency: data.currency,
        money: data.totalmoney,
      });
    }
    console.log("获取线下充值详情:充值方式id:", id);
  };

  const handleSave = async () => {
    try {
      setSaveLoading(true);
      const values = await form.validateFields();
      values.pic = voucherPic;
      console.log("Form values:", values);
      let data = {
        tradesn: searchParams.get("tradesn"),
        paytype: "offline",
        secretkey: md5(String(values.currency) + ":" + String(values.money)),
        paycode:
          rechargeTypeList.find((item) => item.id === selectedRechargeType)
            ?.code || "",
      };
      data = Object.assign(values, data);
      const res = await Api.addRemit(data);
      if (res?.success) {
        Toast.success(dict?.pay?.offlinePay?.submitSuccess);
        router.push("/dashboard/wallet");
      } else {
        Toast.error(res?.msg || dict?.pay?.offlinePay?.submitFailed);
      }
    } catch (e) {
      console.log("Error in handleSave:", e);
    } finally {
      setSaveLoading(false);
    }
  };
  const beforeUpload = (file: any) => {
    console.log("Before upload file:", file);
    const isImage = file.type.startsWith("image/");
    if (!isImage) {
      Toast.error(dict?.pay?.offlinePay?.imageTypeError);
      return Upload.LIST_IGNORE;
    }

    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      Toast.error(dict?.pay?.offlinePay?.imageSizeError);
      return Upload.LIST_IGNORE;
    }

    return true;
  };
  // 自定义上传处理
  const customUploadRequest = async (options: any) => {
    const { file, onSuccess, onError } = options;
    setUploading(true);

    try {
      // 将文件转换为Base64格式
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = async (e) => {
        const base64String = e.target?.result as string;
        if (base64String) {
          // 使用uploadImage方法上传Base64格式的图片
          const response = isTp5
            ? await Api.uploadImage(base64String)
            : await Api.uploadImage(file);
          if (response.success || response.code === 1) {
            // 处理成功响应
            const imageUrl =
              response.data?.items?.item?.name || response.data?.url || "";
            setVoucherPic(imageUrl);
            onSuccess({ ...response, url: imageUrl }, file);
            Toast.success(dict?.pay?.offlinePay?.uploadSuccess);
          } else {
            // 处理失败响应
            onError(new Error(response.msg || dict?.pay?.offlinePay?.uploadError));
            Toast.error(response.msg || dict?.pay?.offlinePay?.uploadError);
          }
        }
      };
    } catch (error) {
      console.error("上传错误:", error);
      onError(error);
      Toast.error(dict?.pay?.offlinePay?.uploadError);
    } finally {
      setUploading(false);
    }
  };
  const uploadButton = (
    <div>
      {uploading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>{dict?.pay?.offlinePay?.uploadImage}</div>
    </div>
  );
  return (
    <div className="min-h-screen px-6 py-4">
      <div className="max-w-[1200px] mx-auto">
        <h1 className="text-2xl font-medium m-y-6">
          {dict?.pay?.offlinePay?.title}
        </h1>
        <div className="bg-gray-50 p-6 rounded-lg">
          <p> {dict?.pay?.offlinePay?.rechargeNotice}</p>
          <p className="text-gray text-sm"> {dict?.pay?.offlinePay?.verifyInfo}</p>
        </div>

        <div className="w-[90%] mx-auto">
          {/* step 1 */}
          <div className="mb-12">
            <div className="flex items-center gap-2 my-6">
              <div className="rounded-full size-8 bg-[#f97316] text-center text-white leading-8 text-xl">
                1
              </div>
              <div className="text-sm">
                <span className="font-bold text-lg mr-1"> {dict?.pay?.offlinePay?.notTransferred1}</span>
                {dict?.pay?.offlinePay?.notTransferred2}
              </div>
            </div>
            <div className="ml-8">
              <div className="text-sm">
              {dict?.pay?.offlinePay?.notTransferred3}
                <span className="font-bold">  {dict?.pay?.offlinePay?.notTransferred4}</span>。
              </div>
              <div className="my-4">
                <div className="mb-2 font-bold text-sm">{dict?.pay?.offlinePay?.region}</div>
                {/* list */}
                <Select
                  value={selectedAreaId}
                  onChange={(value: number) => {
                    setSelectedAreaId(value);
                    getRechargeTypeList(value);
                  }}
                  style={{ width: 320 }}
                >
                  {areaList.map((item) => (
                    <Select.Option value={item.area_id}>
                      {item.name}
                    </Select.Option>
                  ))}
                </Select>
              </div>
              <div className="my-4">
                <div className="mb-2 font-bold text-sm">{dict?.pay?.offlinePay?.paymentMethod}</div>
                {/* list */}
                <Radio.Group
                  value={selectedRechargeType}
                  onChange={(e) => {
                    console.log(e.target.value, "onChange");
                    setSelectedRechargeType(e.target.value);
                    getOfflineDetail(e.target.value);
                  }}
                >
                  <div className=" flex gap-4">
                    {rechargeTypeList?.map((item) => (
                      <Radio key={item.id} value={item.id} className="py-2">
                        <div className="flex items-center gap-3">
                          <div className="flex-1">
                            <div className="font-medium">{item.name}</div>
                          </div>
                        </div>
                      </Radio>
                    ))}
                  </div>
                </Radio.Group>
                <div className="pt-4 w-125">
                  <div className="border-1 border-[var(--base-color)] p-y-2 p-x-4 text-sm flex flex-col gap-4 rounded-lg ">
                    <div className="font-bold text-base color-[var(--base-color)]">
                    {dict?.pay?.offlinePay?.payeeInfo}
                    </div>
                    <div>
                      <span className="min-w-25 inline-block m-r-2">
                      {dict?.pay?.offlinePay?.payeeName}
                      </span>
                      {payeeInfo?.name}
                    </div>
                    <div>
                      <span className="min-w-25 inline-block m-r-2">
                      {dict?.pay?.offlinePay?.payeeAccount}
                      </span>
                      {payeeInfo?.account}
                    </div>
                    <div>
                      <span className="min-w-25 inline-block m-r-2">{dict?.pay?.offlinePay?.description}</span>
                      {payeeInfo?.explain}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* step 2 */}
          {payeeInfo.name && (
            <div>
              <div className="flex items-center gap-2 my-6">
                <div className="rounded-full size-8 bg-[#f97316] text-center text-white leading-8 text-xl">
                  2
                </div>
                <div className="text-sm">
                  <span className="font-bold text-lg mr-1">{dict?.pay?.offlinePay?.transferred1}</span>
                </div>
              </div>
              <div className="ml-8">
                <Form
                  form={form}
                  initialValues={{
                    accountname: "",
                    bankname: "",
                    txn: "",
                    pic: "",
                    currency: "",
                    money: 0,
                    paytype: "",
                    tradesn: "",
                    secretkey: "",
                  }}
                  layout="vertical"
                  style={{ maxWidth: 500 }}
                >
                  <Form.Item
                    label={dict?.pay?.offlinePay?.remitterName}
                    name="accountname"
                    rules={[{ required: true, message: dict?.pay?.offlinePay?.remitterNameHint }]}
                  >
                    <Input />
                  </Form.Item>
                  <Form.Item
                    label={ dict?.pay?.offlinePay?.remitterBank}
                    name="bankname"
                    rules={[{ required: true, message:  dict?.pay?.offlinePay?.remitterBankHint }]}
                  >
                    <Input />
                  </Form.Item>
                  <Form.Item label={dict?.pay?.offlinePay?.voucherNo} name="txn">
                    <Input placeholder= {dict?.pay?.offlinePay?.voucherNoHint} />
                  </Form.Item>

                  <Form.Item
                    label={dict?.pay?.offlinePay?.voucherImage}
                    name="pic"
                    rules={[{ required: true, message: dict?.pay?.offlinePay?.voucherImageHint }]}
                  >
                    <Upload
                      name="file"
                      listType="picture-card"
                      className=""
                      showUploadList={false}
                      customRequest={customUploadRequest}
                      beforeUpload={beforeUpload}
                    >
                      {voucherPic ? (
                        <div className="w-full h-full flex items-center justify-center bg-gray-50 rounded-lg">
                          <img
                            src={voucherPic}
                            alt={dict?.pay?.offlinePay?.voucherPreview}
                            className="max-w-full max-h-full object-contain"
                          />
                        </div>
                      ) : (
                        uploadButton
                      )}
                    </Upload>
                  </Form.Item>
                  <div className="flex gap-4">
                    <Form.Item label={dict?.pay?.offlinePay?.currency} name="currency">
                      <Input disabled />
                    </Form.Item>
                    <Form.Item label={dict?.pay?.offlinePay?.amount}  name="money">
                      <Input disabled />
                    </Form.Item>
                  </div>
                  <Form.Item>
                    <Button
                      type="primary"
                      loading={saveLoading}
                      size="large"
                      onClick={handleSave}
                      className="bg-[var(--base-color)] hover:bg-[var(--base-color-hover)]"
                    >
                      {dict?.pay?.offlinePay?.submitBtn} 
                    </Button>
                  </Form.Item>
                </Form>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
