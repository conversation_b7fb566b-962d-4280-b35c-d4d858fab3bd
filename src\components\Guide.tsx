'use client';

import { useEffect, useState } from 'react'
import Joyride, { CallBackProps, STATUS, Step } from 'react-joyride'

interface GuideProps {
  steps: {
    target: string;
    content: string;
    disableBeacon?: boolean;
  }[];
  name: string;
  dict: any;
}

export default function Guide({ steps, name, dict }: GuideProps) {
  const [run, setRun] = useState(false);
  const [joyrideSteps, setJoyrideSteps] = useState<Step[]>([]);

  useEffect(() => {
    // 安全地检查localStorage，避免SSR时不可用的问题
    if (typeof window === 'undefined') return;

    const hasSeenGuide = localStorage.getItem(`guide-${name}`)
    if (hasSeenGuide) return

    setJoyrideSteps(steps.map(step => ({
      ...step,
      content: dict[step.content] || step.content
    })) as Step[]);

    setRun(true);
  }, [steps, name, dict])

  const handleJoyrideCallback = (data: CallBackProps) => {
    const { status } = data
    if (status === STATUS.FINISHED || status === STATUS.SKIPPED) {
      // 安全地设置localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem(`guide-${name}`, 'true')
      }
      setRun(false);
    }
  }

  return (
    <Joyride
      steps={joyrideSteps}
      run={run}
      callback={handleJoyrideCallback}
      continuous={true}
      showProgress={true}
      showSkipButton={true}
      styles={{
        options: {
          zIndex: 10000,
        },
      }}
    />
  )
} 