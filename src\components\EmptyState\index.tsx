'use client'

import Button from '@/components/Button'
import Image from 'next/image'

interface EmptyStateProps {
  type?: 'no-results' | 'timeout' | 'error'
  title?: string
  description?: string
  showRetry?: boolean
  onRetry?: () => void
  showManualEntry?: boolean
  onManualEntry?: () => void
  dict?: any
}

export default function EmptyState({
  type = 'no-results',
  title,
  description,
  showRetry = false,
  onRetry,
  showManualEntry = false,
  onManualEntry,
  dict
}: EmptyStateProps) {
  
  const getEmptyStateConfig = () => {
    switch (type) {
      case 'timeout':
        return {
          icon: (
            <div className="w-24 h-24 mx-auto mb-6 bg-orange-100 rounded-full flex items-center justify-center">
              <svg className="w-12 h-12 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          ),
          defaultTitle: dict?.search?.timeout?.title || '加载超时',
          defaultDescription: dict?.search?.timeout?.description || '网络连接较慢，请稍后重试或尝试手工填单',
          bgColor: 'bg-orange-50'
        }
      case 'error':
        return {
          icon: (
            <div className="w-24 h-24 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
              <svg className="w-12 h-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          ),
          defaultTitle: dict?.search?.error?.title || '加载失败',
          defaultDescription: dict?.search?.error?.description || '获取商品信息时出现问题，请重试或尝试手工填单',
          bgColor: 'bg-red-50'
        }
      default: // no-results
        return {
          icon: (
            <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
              <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          ),
          defaultTitle: dict?.search?.noResults?.title || '暂无商品',
          defaultDescription: dict?.search?.noResults?.description || '没有找到相关商品，请尝试其他关键词或手工填单',
          bgColor: 'bg-gray-50'
        }
    }
  }

  const config = getEmptyStateConfig()

  return (
    <div className={`min-h-[400px] flex items-center justify-center ${config.bgColor} rounded-lg`}>
      <div className="text-center px-8 py-12 max-w-md mx-auto">
        {config.icon}
        
        <h3 className="text-xl font-semibold text-gray-800 mb-3">
          {title || config.defaultTitle}
        </h3>
        
        <p className="text-gray-600 mb-8 leading-relaxed">
          {description || config.defaultDescription}
        </p>
        
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {showRetry && onRetry && (
            <Button
              type="primary"
              size="large"
              onClick={onRetry}
              className="px-6"
            >
              {dict?.common?.retry || '重试'}
            </Button>
          )}

          {showManualEntry && onManualEntry && (
            <Button
              size="large"
              onClick={onManualEntry}
              className="px-6"
            >
              {dict?.common?.manualEntry || '手工填单'}
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
