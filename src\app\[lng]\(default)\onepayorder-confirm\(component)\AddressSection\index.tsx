'use client';

import React, { useRef } from 'react';
import OnePayAddressCard from '../OnePayAddressCard';

interface AddressSectionProps {
  dict: any;
}

export default function AddressSection({ dict }: AddressSectionProps) {
  const addressCardRef = useRef<any>(null);

  const handleAddNewAddress = () => {
    if (addressCardRef.current && addressCardRef.current.openAddressModal) {
      addressCardRef.current.openAddressModal();
    }
  };

  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-sm font-medium text-gray-700">{dict?.confirm?.onepayorder?.address?.shippingAddress || 'Shipping Address'}</h4>
        <button
          onClick={handleAddNewAddress}
          className="text-orange-500 text-sm hover:text-orange-600"
        >
          + {dict?.confirm?.onepayorder?.address?.addNewAddress || 'Add New Address'}
        </button>
      </div>
      <OnePayAddressCard ref={addressCardRef} dict={dict} />
    </div>
  );
}
