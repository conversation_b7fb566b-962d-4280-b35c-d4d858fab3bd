/* Help Center Styles */

/* Search input styling */
.help-search .ant-input-search {
  border-radius: 8px;
}

.help-search .ant-input-search-button {
  background-color: #FF6B00;
  border-color: #FF6B00;
  border-radius: 0 8px 8px 0;
}

.help-search .ant-input-search-button:hover {
  background-color: #e55a00;
  border-color: #e55a00;
}

.help-search .ant-input {
  border-radius: 8px 0 0 8px;
  padding: 12px 16px;
  font-size: 16px;
}

/* Article content styling */
.article-content {
  line-height: 1.8;
  color: #374151;
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
  color: #1f2937;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.article-content h1 {
  font-size: 1.875rem;
}

.article-content h2 {
  font-size: 1.5rem;
}

.article-content h3 {
  font-size: 1.25rem;
}

.article-content p {
  margin-bottom: 1em;
}

.article-content ul,
.article-content ol {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.article-content li {
  margin-bottom: 0.5em;
}

.article-content a {
  color: #FF6B00;
  text-decoration: none;
}

.article-content a:hover {
  color: #e55a00;
  text-decoration: underline;
}

.article-content img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1em 0;
}

.article-content blockquote {
  border-left: 4px solid #FF6B00;
  padding-left: 1em;
  margin: 1em 0;
  background-color: #f9fafb;
  padding: 1em;
  border-radius: 0 8px 8px 0;
}

.article-content code {
  background-color: #f3f4f6;
  padding: 0.25em 0.5em;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.875em;
}

.article-content pre {
  background-color: #f3f4f6;
  padding: 1em;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1em 0;
}

.article-content pre code {
  background-color: transparent;
  padding: 0;
}

/* Modal styling */
.help-article-modal .ant-modal-header {
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 24px;
}

.help-article-modal .ant-modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.help-article-modal .ant-modal-body {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.help-article-modal .ant-modal-content {
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.help-article-modal .ant-modal-mask {
  backdrop-filter: blur(4px);
  background-color: rgba(0, 0, 0, 0.6);
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .help-search .ant-input {
    font-size: 14px;
    padding: 10px 12px;
  }

  .help-search .ant-input-search-button {
    padding: 0 12px;
  }

  .article-content {
    font-size: 14px;
  }

  .help-article-modal .ant-modal-body {
    padding: 16px;
    max-height: 60vh;
  }

  .help-article-modal .ant-modal-content {
    margin: 16px;
    border-radius: 8px;
  }

  .help-article-modal .ant-modal-header {
    padding: 12px 16px;
  }

  .category-card {
    margin-bottom: 16px;
  }

  .article-card {
    margin-bottom: 12px;
  }
}

/* Hover effects */
.category-card:hover {
  transform: translateY(-2px);
  transition: all 0.2s ease-in-out;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.article-card:hover {
  border-color: #FF6B00;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 4px 12px rgba(255, 107, 0, 0.1);
}

/* Back button styling */
.back-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #FF6B00;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.back-button:hover {
  color: #e55a00;
  transform: translateX(-2px);
}

/* Search results styling */
.search-results-count {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 16px;
}

/* Category icon styling */
.category-icon {
  transition: transform 0.2s ease-in-out;
}

.category-card:hover .category-icon {
  transform: scale(1.1);
}

/* Loading skeleton customization */
.ant-skeleton-content .ant-skeleton-title {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.ant-skeleton-content .ant-skeleton-paragraph > li {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
