.messageContainer {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  display: flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  line-height: 1.5;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12);
  animation: slideDown 0.3s ease;
}

.success {
  background-color: #52c41a;
}

.error {
  background-color: #ff4d4f;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translate(-50%, -100%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
} 