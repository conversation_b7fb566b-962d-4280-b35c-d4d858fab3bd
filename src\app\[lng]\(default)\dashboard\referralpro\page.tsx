'use client';

import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import { getDictionary } from '@/dictionaries';
import { Api } from '@/request/api';
import Toast from '@/components/Toast';
import Button from '@/components/Button';
import Select from '@/components/Select';
import DatePicker from '@/components/DatePicker';
import Modal from '@/components/Modal';
import Input from '@/components/Input';
import { Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { formatCurrency } from '@/utils/currency';
import { prepareImageForNextJs } from '@/utils/imageUtils';

// 定义推广等级接口
interface PromotionLevel {
  id: number;
  level: number;
  name: string;
  experience: number | string;
  invite_count?: number;
  rate: number;
  description?: string;
  status?: string;
}

// 定义用户等级接口
interface UserLevel {
  level: number;
  name: string;
  rate: number;
  experience: string;
}

// 定义邀请统计接口
interface InviteCount {
  user_total_num: number;
  user_reg_num: number;
  user_active_num: number;
}

// 定义金额统计接口
interface AmountCount {
  amount: number;
  freeze_amount: number;
  withdrawal_amount: number;
  expect_amount: number;
  total_amount: number;
}

// 定义邀请列表接口
interface InvitationList {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  data: any[];
}

// 定义API响应接口
interface PromotionDetailResponse {
  qrcode_url?: string;
  nextLevel?: PromotionLevel;
  userLevel?: UserLevel;
  promotionList?: PromotionLevel[];
  inviteCount?: InviteCount;
  amountCount?: AmountCount;
  invitationList?: InvitationList;
  inviteCode?: string;
  inviteUrl?: string;
}

// 定义用户信息接口
interface UserInfo {
  id: number;
  username: string;
  nickname?: string;
  avatar?: string;
  inviteUrl?: string;
  inviteCode?: string;
  email?: string;
}

export default function PromotionPage() {
  const { lng } = useParams();
  const router = useRouter();
  const [dict, setDict] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [promotionDetail, setPromotionDetail] = useState<PromotionDetailResponse | null>(null);

  // 佣金记录相关状态
  const [activeTab, setActiveTab] = useState('commissionAmount');

  // 各个标签页的数据状态
  const [experienceList, setExperienceList] = useState<any[]>([]);
  const [activeUserList, setActiveUserList] = useState<any[]>([]);
  const [amountList, setAmountList] = useState<any[]>([]);
  const [frozenAmountList, setFrozenAmountList] = useState<any[]>([]);
  const [settlementList, setSettlementList] = useState<any[]>([]);
  const [scoreList, setScoreList] = useState<any[]>([]);
  const [inviteList, setInviteList] = useState<any[]>([]);
  const [amountTypeList, setAmountTypeList] = useState<{[key: string]: string}>({});
  const [frozenTypeList, setFrozenTypeList] = useState<{[key: string]: string}>({});
  const [scoreTypeList, setScoreTypeList] = useState<{[key: string]: string}>({});
  const [tabLoading, setTabLoading] = useState(false);
  const [showEmailForm, setShowEmailForm] = useState<boolean>(false);
  const [emailInput, setEmailInput] = useState<string>('');
  const [sendingEmail, setSendingEmail] = useState<boolean>(false);

  // 提现modal相关状态
  const [showWithdrawModal, setShowWithdrawModal] = useState<boolean>(false);
  const [withdrawEmail, setWithdrawEmail] = useState<string>('');
  const [withdrawEmailCode, setWithdrawEmailCode] = useState<string>('');
  const [withdrawAmount, setWithdrawAmount] = useState<string>('');
  const [emailCountdown, setEmailCountdown] = useState<number>(0);
  const [sendingWithdrawEmail, setSendingWithdrawEmail] = useState<boolean>(false);
  const [submittingWithdraw, setSubmittingWithdraw] = useState<boolean>(false);
  const emailTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 分页状态
  const [pagination, setPagination] = useState({
    current_page: 1,
    per_page: 20,
    total: 0,
    last_page: 1
  });

  // 筛选状态
  const [filters, setFilters] = useState({
    type: '',
    startDate: '',
    endDate: ''
  });
  

  useEffect(() => {
    // 当 filters 更新时（包括 type），执行 fetchTabData
      fetchTabData(activeTab, 1);
    
  }, [filters]);  // 监听 filters 的变化
  // 异步获取字典数据
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };

    fetchDictionary();
  }, [lng]);

  // 获取用户信息
  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        // 首先尝试从本地存储获取用户信息
        let userInfoFromStorage = null;
        if (typeof window !== 'undefined') {
          try {
            const infoStr = localStorage.getItem('info');
            if (infoStr) {
              const info = JSON.parse(infoStr);
              userInfoFromStorage = info?.data?.userinfo;
            }
          } catch (error) {
            console.error('Failed to parse user info from localStorage:', error);
          }
        }

        // 使用 /api/v1.0/web/member/user/info 接口获取最新用户信息
        const response = await Api.getUserInfo();
        if (response?.success && response?.data) {
          // 从API响应中获取用户信息，数据结构为 data.userinfo
          const userinfo = response.data.userinfo || response.data;
          const { id, username, nickname, avatar, inviteUrl, inviteCode, email } = userinfo;
          setUserInfo({
            id,
            username,
            nickname: nickname || username,
            avatar,
            inviteUrl,
            inviteCode,
            email
          });
        } else if (userInfoFromStorage) {
          // 如果API调用失败，使用本地存储的用户信息作为备用
          const { id, username, nickname, avatar, inviteUrl, inviteCode, email } = userInfoFromStorage;
          setUserInfo({
            id,
            username,
            nickname: nickname || username,
            avatar,
            inviteUrl,
            inviteCode,
            email
          });
        }

        // 获取推广相关数据
        await fetchPromotionData();

        // 获取佣金类型列表
        await fetchAmountTypeList();

        // 获取冻结类型列表
        await fetchFrozenTypeList();

        //获取积分类型列表
        await fetchScoreTypeList();

        // 加载默认标签页数据
        await fetchTabData('commissionAmount');
      } catch (error) {
        console.error('Failed to fetch user info:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserInfo();
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (emailTimerRef.current) {
        clearInterval(emailTimerRef.current);
      }
    };
  }, []);

  // 获取推广数据
  const fetchPromotionData = async () => {
    try {
      // 调用新的推广详情API
      const response = await Api.getPromotionDetail();

      // @catchError装饰器会将数据包装在 { success: true, data: ... } 中
      if (response?.success && response?.data) {
        setPromotionDetail(response.data);

        // 从推广详情中获取inviteCode和inviteUrl，更新用户信息
        if (response.data.inviteCode || response.data.inviteUrl) {
          setUserInfo(prev => prev ? ({
            ...prev,
            inviteCode: response.data.inviteCode || prev.inviteCode,
            inviteUrl: response.data.inviteUrl || prev.inviteUrl
          }) : null);
        }
      } else {
        console.log('API响应格式不正确或没有数据');
      }
    } catch (error) {
      console.error('Failed to fetch promotion data:', error);
    }
  };

  // 获取佣金类型列表
  const fetchAmountTypeList = async () => {
    try {
      const response = await Api.getAmountTypeList();
      if (response?.success && response?.data) {
        setAmountTypeList(response.data);
      } else if (response?.data) {
        // 如果没有success字段，直接使用data
        setAmountTypeList(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch amount type list:', error);
    }
  };

  //获取积分类型列表
  const fetchScoreTypeList = async () => {
    try {
      const response = await Api.getScoreTypeList();
      if (response?.success && response?.data) {
        setScoreTypeList(response.data);
      } else if (response?.data) {
        // 如果没有success字段，直接使用data
        setScoreTypeList(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch amount type list:', error);
    }
  };

  // 获取冻结类型列表
  const fetchFrozenTypeList = async () => {
    try {
      const response = await Api.getFrozenTypeList();
      if (response?.success && response?.data) {
        setFrozenTypeList(response.data);
      } else if (response?.data) {
        // 如果没有success字段，直接使用data
        setFrozenTypeList(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch frozen type list:', error);
    }
  };



  // 复制推广链接
  const handleCopyLink = async () => {
    try {
      let registerUrl = `${window.location.origin}/register`;
      // 优先使用从detail_promotion接口获取的inviteUrl
      let promoLink = userInfo?.inviteUrl || promotionDetail?.inviteUrl || `${registerUrl}?ref=${userInfo?.id}`;

      const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5';
      if (!isTp5) {
        try {
          const res = await Api.getInviteLink();
          if (res && res.link) {
            promoLink = registerUrl + res.link;
          }
        } catch (error) {
          console.log(error, '获取推广链接失败');
        }
      }

      await navigator.clipboard.writeText(promoLink);
      // Toast.success(dict?.promotion?.copyLink + dict?.referralpro?.messages?.copyLinkSuccess);
      Toast.success(dict?.referralpro?.messages?.copyLinkSuccess);
    } catch (error) {
      console.log(error)
      Toast.error(dict?.referralpro?.messages?.copyLinkFail || '复制失败，请手动复制');
    }
  };

  // 下载二维码
  const handleDownloadQR = () => {
    if (promotionDetail?.qrcode_url) {
      const link = document.createElement('a');
      link.href = promotionDetail.qrcode_url;
      link.download = 'promotion-qrcode.png';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      Toast.success(dict?.referralpro?.messages?.qrDownloadSuccess || '二维码下载成功');
    } else {
      Toast.error(dict?.referralpro?.messages?.qrNotAvailable || '二维码不可用');
    }
  };

  // 复制邀请码
  const handleCopyInviteCode = async () => {
    try {
      // 优先使用从detail_promotion接口返回的inviteCode字段作为邀请码
      const inviteCode = promotionDetail?.inviteCode || userInfo?.inviteCode || userInfo?.id?.toString() || userInfo?.username || 'INVITE001';

      await navigator.clipboard.writeText(inviteCode);
      Toast.success(dict?.referralpro?.messages?.copyLinkSuccess);
      // Toast.success(dict?.referralpro?.messages?.inviteCodeCopySuccess?.replace('{code}', inviteCode) || `邀请码 ${inviteCode} 复制成功`);
    } catch (error) {
      console.log(error)
      Toast.error(dict?.referralpro?.messages?.copyLinkFail || '复制失败，请手动复制');
    }
  };

  // 发送邮件邀请
  const handleSendInviteEmail = async () => {
    if (!emailInput || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailInput)) {
      Toast.error(dict?.referralpro?.messages?.invalidEmail || '请输入有效的邮箱地址');
      return;
    }

    setSendingEmail(true);
    try {
      const response = await Api.emailAdd({ email: emailInput });
      if (response.success) {
        Toast.success(dict?.referralpro?.messages?.inviteEmailSent || '邀请邮件发送成功');
        setEmailInput('');
        setShowEmailForm(false);
        // 重新获取邀请列表
        fetchTabData('inviteList', 1);
      } else {
        console.log(response)
        Toast.error(response.msg || dict?.referralpro?.messages?.sendFailed || '发送失败');
      }
    } catch (error) {
      console.error('发送邀请邮件失败', error);
      Toast.error(dict?.referralpro?.messages?.sendFailedRetry || '发送失败，请稍后重试');
    } finally {
      setSendingEmail(false);
    }
  };

  // 提现
  const handleWithdraw = () => {
    // 检查是否有可提现余额
    const availableAmount = promotionDetail?.amountCount?.amount || 0;
    if (availableAmount <= 0) {
      Toast.error(dict?.referralpro?.messages?.noWithdrawBalance || '当前佣金余额为0，无法提现');
      return;
    }

    // 检查用户是否有邮箱
    if (!userInfo?.email) {
      Toast.error(dict?.referralpro?.messages?.noEmailError || '用户信息中未找到邮箱，请先完善个人信息');
      return;
    }

    // 显示modal，自动填入用户邮箱
    setWithdrawEmail(userInfo.email);
    setShowWithdrawModal(true);
  };

  // 发送提现邮箱验证码
  const handleSendWithdrawEmailCode = async () => {
    // 使用用户信息中的邮箱
    const emailToUse = userInfo?.email || withdrawEmail;
    if (!emailToUse || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailToUse)) {
      Toast.error(dict?.referralpro?.messages?.enterValidEmail || '请输入有效的邮箱地址');
      return;
    }

    setSendingWithdrawEmail(true);
    try { 
      const response = await Api.sendEmailCode({
        email: emailToUse,
        event: 'promotion_withdraw'
      });

      if (response?.success) {
        Toast.success(dict?.referralpro?.messages?.verificationCodeSent || '验证码已发送到您的邮箱');
        startWithdrawEmailCountdown();
      } else {
        Toast.error(response?.msg || dict?.referralpro?.messages?.sendCodeFailed || '发送验证码失败，请稍后重试');
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      Toast.error(dict?.referralpro?.messages?.sendCodeFailed || '发送验证码失败，请稍后重试');
    } finally {
      setSendingWithdrawEmail(false);
    }
  };

  // 开始邮箱验证码倒计时
  const startWithdrawEmailCountdown = () => {
    setEmailCountdown(60);
    emailTimerRef.current = setInterval(() => {
      setEmailCountdown((prev) => {
        if (prev <= 1) {
          if (emailTimerRef.current) {
            clearInterval(emailTimerRef.current);
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // 提交提现申请
  const handleSubmitWithdraw = async () => {
    // 使用用户信息中的邮箱
    const emailToUse = userInfo?.email || withdrawEmail;
    if (!emailToUse || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailToUse)) {
      Toast.error(dict?.referralpro?.messages?.enterValidEmail || '请输入有效的邮箱地址');
      return;
    }

    if (!withdrawEmailCode) {
      Toast.error(dict?.referralpro?.messages?.enterVerificationCode || '请输入邮箱验证码');
      return;
    }

    if (!withdrawAmount || parseFloat(withdrawAmount) <= 0) {
      Toast.error(dict?.referralpro?.messages?.enterValidAmount || '请输入有效的提现金额');
      return;
    }

    setSubmittingWithdraw(true);
    try {
      const response = await Api.withdrawCommission({
        email: emailToUse,
        captcha: withdrawEmailCode,
        money: parseFloat(withdrawAmount)
      });

      if (response?.success) {
        Toast.success(dict?.referralpro?.messages?.withdrawSubmitted || '提现申请已提交，请等待审核');
        setShowWithdrawModal(false);
        // 重置表单（保留用户邮箱）
        setWithdrawEmailCode('');
        setWithdrawAmount('');
        setEmailCountdown(0);
        if (emailTimerRef.current) {
          clearInterval(emailTimerRef.current);
        }
        // 刷新推广数据
        await fetchPromotionData();
      } else {
        Toast.error(response?.msg || dict?.referralpro?.messages?.withdrawFailed || '提现申请失败，请稍后重试');
      }
    } catch (error) {
      console.error('提现申请失败:', error);
      Toast.error(dict?.referralpro?.messages?.withdrawFailed || '提现申请失败，请稍后重试');
    } finally {
      setSubmittingWithdraw(false);
    }
  };

  // 关闭提现modal
  const handleCloseWithdrawModal = () => {
    setShowWithdrawModal(false);
    // 不清空邮箱字段，因为它是从用户信息中获取的
    setWithdrawEmailCode('');
    setWithdrawAmount('');
    setEmailCountdown(0);
    if (emailTimerRef.current) {
      clearInterval(emailTimerRef.current);
    }
  };

  // 跳转到帮助页面
  const handleNavigateToHelp = () => {
    router.push(`/${lng}/help`);
  };

  // 获取标签页数据
  const fetchTabData = async (tabName: string, page: number = 1) => {
    setTabLoading(true);
    try {
      // 构建API参数，包含分页和筛选条件
      const apiParams = {
        page,
        size: 20,
        ...(filters.startDate && { start_date: filters.startDate }),
        ...(filters.endDate && { end_date: filters.endDate })
      };

      // 为佣金相关API添加ways参数，使用正确的参数名
      const amountApiParams = {
        page,
        size: 20,
        ...(filters.type && { ways: parseInt(filters.type) }),
        ...(filters.startDate && { start_time: filters.startDate }),
        ...(filters.endDate && { end_time: filters.endDate })
      };

      // 为其他API添加type参数
      const otherApiParams = {
        ...apiParams,
        ...(filters.type && { type: filters.type })
      };

      switch (tabName) {
        case 'experienceDetails':
          // 构建经验明细API参数，使用start_time/end_time
          const expApiParams = {
            page,
            size: 20,
            ...(filters.startDate && { start_time: filters.startDate }),
            ...(filters.endDate && { end_time: filters.endDate })
          };
          const expResponse = await Api.getExperienceList(expApiParams);
          if (expResponse) {
            // API返回的数据结构: { total, per_page, current_page, last_page, data: [] }
            const responseData = expResponse?.data || expResponse;
            // 按时间倒序排列（最新的在前面）
            const sortedData = (responseData?.data || []).sort((a: any, b: any) => {
              const timeA = a.createtime ? new Date(a.createtime).getTime() : (a.time ? (typeof a.time === 'number' ? a.time * 1000 : new Date(a.time).getTime()) : (a.id || 0));
              const timeB = b.createtime ? new Date(b.createtime).getTime() : (b.time ? (typeof b.time === 'number' ? b.time * 1000 : new Date(b.time).getTime()) : (b.id || 0));
              return timeB - timeA; // 倒序：新的在前
            });
            setExperienceList(sortedData);
            if (responseData?.total !== undefined) {
              setPagination({
                current_page: responseData.current_page || page,
                per_page: responseData.per_page || 20,
                total: responseData.total || 0,
                last_page: responseData.last_page || 1
              });
            }
          }
          break;
        case 'inviteList':
          // 使用专门的邀请列表API
          const inviteParams = {
            size: apiParams.size,
            page: apiParams.page,
            ...(filters.startDate && { start_date: filters.startDate }),
            ...(filters.endDate && { end_date: filters.endDate })
          };
          const inviteResponse = await Api.getReferralList(inviteParams);
          if (inviteResponse?.success && inviteResponse?.data) {
            const responseData = inviteResponse.data;
            // 按时间倒序排列（最新的在前面）
            const sortedData = (responseData?.data || []).sort((a: any, b: any) => {
              const timeA = a.createtime ? new Date(a.createtime).getTime() : (a.regtime ? (typeof a.regtime === 'number' ? a.regtime * 1000 : new Date(a.regtime).getTime()) : (a.id || 0));
              const timeB = b.createtime ? new Date(b.createtime).getTime() : (b.regtime ? (typeof b.regtime === 'number' ? b.regtime * 1000 : new Date(b.regtime).getTime()) : (b.id || 0));
              return timeB - timeA; // 倒序：新的在前
            });
            setInviteList(sortedData);
            if (responseData?.total !== undefined) {
              setPagination({
                current_page: responseData.current_page || page,
                per_page: responseData.per_page || 20,
                total: responseData.total || 0,
                last_page: responseData.last_page || 1
              });
            }
          }
          break;
        case 'activeUsers':
          const activeResponse = await Api.getActiveUserList(otherApiParams);
          if (activeResponse) {
            // API返回的数据结构: { total, per_page, current_page, last_page, data: [] }
            const responseData = activeResponse?.data || activeResponse;
            // 按时间倒序排列（最新的在前面）
            const sortedData = (responseData?.data || []).sort((a: any, b: any) => {
              const timeA = a.createtime ? new Date(a.createtime).getTime() : (a.regtime ? (typeof a.regtime === 'number' ? a.regtime * 1000 : new Date(a.regtime).getTime()) : (a.id || a.user_id || 0));
              const timeB = b.createtime ? new Date(b.createtime).getTime() : (b.regtime ? (typeof b.regtime === 'number' ? b.regtime * 1000 : new Date(b.regtime).getTime()) : (b.id || b.user_id || 0));
              return timeB - timeA; // 倒序：新的在前
            });
            setActiveUserList(sortedData);
            if (responseData?.total !== undefined) {
              setPagination({
                current_page: responseData.current_page || page,
                per_page: responseData.per_page || 20,
                total: responseData.total || 0,
                last_page: responseData.last_page || 1
              });
            }
          }
          break;
        case 'commissionAmount':
          const amountResponse = await Api.getAmountList(amountApiParams);
          if (amountResponse) {
            // API返回的数据结构: { total, per_page, current_page, last_page, data: [] }
            const responseData = amountResponse?.data || amountResponse;
            // 按时间倒序排列（最新的在前面）
            const sortedData = (responseData?.data || []).sort((a: any, b: any) => {
              const timeA = a.createtime ? new Date(a.createtime).getTime() : (a.time ? (typeof a.time === 'number' ? a.time * 1000 : new Date(a.time).getTime()) : (a.id || 0));
              const timeB = b.createtime ? new Date(b.createtime).getTime() : (b.time ? (typeof b.time === 'number' ? b.time * 1000 : new Date(b.time).getTime()) : (b.id || 0));
              return timeB - timeA; // 倒序：新的在前
            });
            setAmountList(sortedData);
            if (responseData?.total !== undefined) {
              setPagination({
                current_page: responseData.current_page || page,
                per_page: responseData.per_page || 20,
                total: responseData.total || 0,
                last_page: responseData.last_page || 1
              });
            }
          }
          break;
        case 'settlementRecords':
          // 构建结算记录API参数
          const settlementApiParams = {
            page,
            size: 20,
            ...(filters.startDate && { start_time: filters.startDate }),
            ...(filters.endDate && { end_time: filters.endDate })
          };
          const settlementResponse = await Api.getSettlementList(settlementApiParams);
          if (settlementResponse) {
            // API返回的数据结构: { total, per_page, current_page, last_page, data: [] }
            const responseData = settlementResponse?.data || settlementResponse;
            // 按时间倒序排列（最新的在前面）
            const sortedData = (responseData?.data || []).sort((a: any, b: any) => {
              const timeA = a.createtime ? new Date(a.createtime).getTime() : (a.time ? (typeof a.time === 'number' ? a.time * 1000 : new Date(a.time).getTime()) : (a.id || 0));
              const timeB = b.createtime ? new Date(b.createtime).getTime() : (b.time ? (typeof b.time === 'number' ? b.time * 1000 : new Date(b.time).getTime()) : (b.id || 0));
              return timeB - timeA; // 倒序：新的在前
            });
            setSettlementList(sortedData);
            if (responseData?.total !== undefined) {
              setPagination({
                current_page: responseData.current_page || page,
                per_page: responseData.per_page || 20,
                total: responseData.total || 0,
                last_page: responseData.last_page || 1
              });
            }
          }
          break;
        case 'frozenAmount':
          // 构建冻结金额API参数
          const frozenApiParams = {
            page,
            size: 20,
            ...(filters.type && { ways: parseInt(filters.type) }),
            ...(filters.startDate && { start_time: filters.startDate }),
            ...(filters.endDate && { end_time: filters.endDate })
          };
          const frozenResponse = await Api.getFrozenAmountList(frozenApiParams);
          if (frozenResponse) {
            // API返回的数据结构: { total, per_page, current_page, last_page, data: [] }
            const responseData = frozenResponse?.data || frozenResponse;
            // 按时间倒序排列（最新的在前面）
            const sortedData = (responseData?.data || []).sort((a: any, b: any) => {
              const timeA = a.createtime ? new Date(a.createtime).getTime() : (a.time ? (typeof a.time === 'number' ? a.time * 1000 : new Date(a.time).getTime()) : (a.id || 0));
              const timeB = b.createtime ? new Date(b.createtime).getTime() : (b.time ? (typeof b.time === 'number' ? b.time * 1000 : new Date(b.time).getTime()) : (b.id || 0));
              return timeB - timeA; // 倒序：新的在前
            });
            setFrozenAmountList(sortedData);
            if (responseData?.total !== undefined) {
              setPagination({
                current_page: responseData.current_page || page,
                per_page: responseData.per_page || 20,
                total: responseData.total || 0,
                last_page: responseData.last_page || 1
              });
            }
          }
          break;
        case 'goInvite':
          // 去邀请标签页不需要加载数据，只显示发送邀请邮件的表单
          break;
        case 'pointsList':
          // 构建积分列表API参数
          const scoreApiParams = {
            size: 20,
            page,
            ...(filters.type && { type: parseInt(filters.type) as -1 | -2 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 }),
            ...(filters.startDate && { start_time: filters.startDate }),
            ...(filters.endDate && { end_time: filters.endDate })
          };
          console.log(scoreApiParams)
          const scoreResponse = await Api.getScoreList(scoreApiParams);
          if (scoreResponse?.success && scoreResponse?.data?.list) {
            // API返回的数据结构: { list: { total, per_page, current_page, last_page, data: [] } }
            const responseData = scoreResponse.data.list;
            // 按时间倒序排列（最新的在前面）
            const sortedData = (responseData?.data || []).sort((a: any, b: any) => {
              const timeA = a.createtime ? new Date(a.createtime).getTime() : (a.time ? (typeof a.time === 'number' ? a.time * 1000 : new Date(a.time).getTime()) : (a.id || 0));
              const timeB = b.createtime ? new Date(b.createtime).getTime() : (b.time ? (typeof b.time === 'number' ? b.time * 1000 : new Date(b.time).getTime()) : (b.id || 0));
              return timeB - timeA; // 倒序：新的在前
            });
            setScoreList(sortedData);
            if (responseData?.total !== undefined) {
              setPagination({
                current_page: responseData.current_page || page,
                per_page: responseData.per_page || 20,
                total: responseData.total || 0,
                last_page: responseData.last_page || 1
              });
            }
          } else if (scoreResponse?.data?.list) {
            // 如果没有success字段，直接使用data.list
            const responseData = scoreResponse.data.list;
            // 按时间倒序排列（最新的在前面）
            const sortedData = (responseData?.data || []).sort((a: any, b: any) => {
              const timeA = a.createtime ? new Date(a.createtime).getTime() : (a.time ? (typeof a.time === 'number' ? a.time * 1000 : new Date(a.time).getTime()) : (a.id || 0));
              const timeB = b.createtime ? new Date(b.createtime).getTime() : (b.time ? (typeof b.time === 'number' ? b.time * 1000 : new Date(b.time).getTime()) : (b.id || 0));
              return timeB - timeA; // 倒序：新的在前
            });
            setScoreList(sortedData);
            if (responseData?.total !== undefined) {
              setPagination({
                current_page: responseData.current_page || page,
                per_page: responseData.per_page || 20,
                total: responseData.total || 0,
                last_page: responseData.last_page || 1
              });
            }
          }
          break;
      }
    } catch (error) {
      console.error('获取标签页数据失败:', error);
      Toast.error(dict?.referralpro?.messages?.dataLoadFailed || '获取数据失败，请稍后重试');
    } finally {
      setTabLoading(false);
    }
  };

  // 处理标签页切换
  const handleTabChange = (tabName: string) => {
    setActiveTab(tabName);
    setFilters({
      type: '',
      startDate: '',
      endDate: ''
    });
    //  fetchTabData(tabName);
  };

  // 处理分页切换
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= pagination.last_page) {
      fetchTabData(activeTab, page);
    }
  };

  // 处理筛选
  const handleSearch = () => {
    fetchTabData(activeTab, 1); // 重新从第一页开始搜索
  };

  // 重置筛选
  const handleReset = () => {
    setFilters({
      type: '',
      startDate: '',
      endDate: ''
    });
    // fetchTabData(activeTab, 1);
  };

  if (loading || !dict) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <button
        onClick={handleNavigateToHelp}
        className=" bg-orange-500 text-white px-3 py-1 rounded-xl text-sm hover:bg-orange-600 transition-colors"
      >
        {dict?.referralpro?.title || '推广协议与规则'}
      </button>
      {/* 顶部用户信息区域 */}
      <div className="bg-white p-3 pb-0 shadow-sm">
        <div className="flex items-center mb-6">
          <div className="mr-4">
            {userInfo?.avatar ? (
              <Image
                src={prepareImageForNextJs(userInfo.avatar)}
                alt="avatar"
                width={64}
                height={64}
                className="w-16 h-16 rounded-full object-cover border-2 border-orange-200"
              />
            ) : (
              <div className="w-16 h-16 rounded-full bg-orange-500 flex items-center justify-center text-white text-xl font-bold">
                {userInfo?.nickname?.charAt(0) || 'U'}
              </div>
            )}
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-800">{userInfo?.nickname || userInfo?.username || 'User'}</h2>
            <div className="flex items-center mt-2 space-x-6 text-sm text-gray-600">
              <span>{dict?.referralpro?.totalInviteUsers || '总邀请用户'}: <span className="font-bold text-gray-800">{promotionDetail?.inviteCount?.user_total_num || 0}</span></span>
              <span>{dict?.referralpro?.activatedUsers || '已激活用户'}: <span className="font-bold text-gray-800">{promotionDetail?.inviteCount?.user_reg_num || 0}</span></span>
              <span className="flex items-center">
                {dict?.referralpro?.activeUsers || '活跃用户'}: <span className="font-bold text-gray-800">{promotionDetail?.inviteCount?.user_active_num || 0}</span>
                <Tooltip
                  title={dict?.referralpro?.activeUsersTooltip || "统计当前自然月内，在平台完成下单并付款，且数量不少于10单的用户"}
                  placement="top"
                  color="#374151"
                >
                  <QuestionCircleOutlined className="ml-1 text-gray-400 cursor-help hover:text-gray-600 transition-colors" />
                </Tooltip>
              </span>
            </div>
          </div>
        </div>

        {/* 账户余额信息 */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          <div className="p-4 bg-gray-50 rounded-lg relative">
            <div className="text-left">
              <div className="text-sm mb-1">{dict?.referralpro?.commissionBalance || '佣金余额'}</div>
              <div className="text-red-500">
                <span className='text-black bold'>{formatCurrency(promotionDetail?.amountCount?.amount || 0).formatValue}</span>
              </div>
            </div>
            <button
              onClick={handleWithdraw}
              className="absolute top-4 right-4 bg-orange-500 text-white px-5 py-1 rounded text-sm hover:bg-orange-600 transition-colors"
            >
              {dict?.promotion?.withdraw || '提现'}
            </button>
          </div>
          <div className="col-span-3 px-6 py-4 bg-gray-50 rounded-lg relative">
            <div className="grid grid-cols-3 gap-6 h-full">
              <div className="text-left">
                <div className="text-sm mb-1 flex items-center">
                  {dict?.referralpro?.frozenBalance || '冻结余额'}
                  <Tooltip
                    title={
                      <div>
                        <div>{dict?.referralpro?.frozenBalanceTooltip || '您邀请的用户完成运单收货后，对应的佣金将先进入冻结金额等待结算，结算后自动转入佣金余额'}</div>
                        <div className="mt-1">{dict?.referralpro?.settlementDate || '系统每月结算日为 X 日'}</div>
                      </div>
                    }
                    placement="top"
                    color="#374151"
                  >
                    <QuestionCircleOutlined className="ml-1 text-gray-400 cursor-help hover:text-gray-600 transition-colors" />
                  </Tooltip>
                </div>
                <div className="text-red-500">
                   <span className='text-black bold'>{formatCurrency(promotionDetail?.amountCount?.freeze_amount || 0).formatValue}</span>
                </div>
              </div>
              <div className="text-left">
                <div className="text-sm mb-1 flex items-center">
                  {dict?.referralpro?.estimatedCommission || '预计佣金'}
                  <Tooltip
                    title={dict?.referralpro?.estimatedCommissionTooltip || "统计您所邀请用户已付款但尚未收货的运单对应的佣金金额"}
                    placement="top"
                    color="#374151"
                  >
                    <QuestionCircleOutlined className="ml-1 text-gray-400 cursor-help hover:text-gray-600 transition-colors" />
                  </Tooltip>
                </div>
                <div className="text-red-500">
                   <span className='text-black bold'>{formatCurrency(promotionDetail?.amountCount?.expect_amount || 0).formatValue}</span>
                </div>
              </div>
              <div className="text-left">
                <div className="text-sm mb-1">{dict?.referralpro?.totalWithdrawal || '累计提现'}</div>
                <div className="text-red-500">
                   <span className='text-black bold'>{formatCurrency(promotionDetail?.amountCount?.withdrawal_amount || 0).formatValue}</span>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>

      {/* 等级信息区域 */}
      <div className="bg-white p-6 shadow-sm">
        <h3 className="text-lg font-bold mb-4">{dict?.referralpro?.myLevel || '我的等级'}</h3>
        <div className="mb-4">
          <div className="text-sm font-bold mb-2">
            {promotionDetail?.userLevel?.name || 'LV1 梦想推广者'}
          </div>
          <div className="text-sm text-gray-600 mb-4">
            {dict?.referralpro?.bonusRatio || '奖金比例'}: <span className='text-red-500'>{promotionDetail?.userLevel?.rate || 2}%</span>
          </div>

          {/* 进度条 */}
          <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
            <div
              className="bg-orange-500 h-2 rounded-full transition-all duration-300"
              style={{
                width: `${Math.min(
                  (parseFloat(promotionDetail?.userLevel?.experience?.toString() || '0') /
                   parseFloat(promotionDetail?.nextLevel?.experience?.toString() || '1000')) * 100,
                  100
                )}%`
              }}
            ></div>
          </div>
          <div className="text-sm text-gray-600">
            {dict?.referralpro?.experienceValue || '经验值'}: {promotionDetail?.userLevel?.experience || '0'}/{promotionDetail?.nextLevel?.experience || 1000}
          </div>
        </div>

        {/* 等级展示区域 */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {promotionDetail?.promotionList && promotionDetail.promotionList.length > 0 ? (
            promotionDetail.promotionList.map((level) => (
              <div
                key={level.id}
                className={`p-3 rounded border bg-white ${level.level === promotionDetail?.userLevel?.level
                    ? 'border-2 border-orange-500'
                    : 'border border-gray-200'
                  }`}
              >
                <div className="text-center">
                  <div className={`text-sm font-bold mb-1 ${level.level === promotionDetail?.userLevel?.level
                      ? 'text-gray-800'
                      : 'text-gray-600'
                    }`}>
                    {level.name}
                  </div>
                  <div className="text-xs text-gray-600 mb-1">
                    {dict?.referralpro?.bonusRatio || '奖金比例'}: {level.rate}%
                  </div>
                  <div className="text-xs text-gray-500">
                    {level.experience}{dict?.referralpro?.startingFrom || '起步'}
                  </div>
                </div>
              </div>
            ))
          ) : (
            // 如果没有数据，显示提示信息
            <div className="col-span-full p-8 text-center text-gray-500">
              <div className="text-lg mb-2">{dict?.referralpro?.noLevelInfo || '暂无等级信息'}</div>
              <div className="text-sm text-gray-400">{dict?.referralpro?.levelDataLoading || '等级数据加载中，请稍后刷新页面'}</div>
            </div>
          )}
        </div>
      </div>

      {/* 推广方式说明 */}
      <div className="bg-white p-6">
        <h3 className="text-lg font-bold mb-4">{dict?.referralpro?.promotionMethod || '推广方式'}</h3>
        <p className="text-sm leading-relaxed">
          {dict?.referralpro?.promotionMethodDesc || '通过扫描二维码或点击推广链接，或在注册时填写您的推广码，将自动成为您的推广用户'}
        </p>
      </div>

      {/* 操作按钮区域 */}
      <div className="bg-white">
        <div className="grid grid-cols-3 gap-4">
          <button
            className="flex items-center justify-center py-3 px-4 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
            onClick={handleDownloadQR}
          >
            <span className="text-sm mr-2">{dict?.referralpro?.downloadInviteQR || '下载邀请二维码'}</span>
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M3 3h6v6H3V3zm2 2v2h2V5H5zm10-2h6v6h-6V3zm2 2v2h2V5h-2zM3 15h6v6H3v-6zm2 2v2h2v-2H5zm8-2h2v2h-2v-2zm0 4h2v2h-2v-2zm4-4h2v2h-2v-2zm0 4h2v2h-2v-2zm-8-8h2v2h-2V9zm4 0h2v2h-2V9z"/>
              <path d="M19 13v2h2v-2h-2zm0 4v2h2v-2h-2z"/>
            </svg>
          </button>

          <button
            className="flex items-center justify-center py-3 px-4 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
            onClick={handleCopyLink}
          >
            <span className="text-sm mr-2">{dict?.referralpro?.copyInviteLink || '复制邀请链接'}</span>
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M10.59 13.41c.41.39.41 1.03 0 1.42-.39.39-1.03.39-1.42 0a5.003 5.003 0 0 1 0-7.07l3.54-3.54a5.003 5.003 0 0 1 7.07 0 5.003 5.003 0 0 1 0 7.07l-1.49 1.49c.01-.82-.12-1.64-.4-2.42l.47-.48a2.982 2.982 0 0 0 0-4.24 2.982 2.982 0 0 0-4.24 0l-3.53 3.53a2.982 2.982 0 0 0 0 4.24zm2.82-4.24c.39-.39 1.03-.39 1.42 0a5.003 5.003 0 0 1 0 7.07l-3.54 3.54a5.003 5.003 0 0 1-7.07 0 5.003 5.003 0 0 1 0-7.07l1.49-1.49c-.01.82.12 1.64.4 2.43l-.47.47a2.982 2.982 0 0 0 0 4.24 2.982 2.982 0 0 0 4.24 0l3.53-3.53a2.982 2.982 0 0 0 0-4.24.996.996 0 0 1 0-1.42z"/>
            </svg>
          </button>

          <button
            className="flex items-center justify-center py-3 px-4 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
            onClick={handleCopyInviteCode}
          >
            <span className="text-sm mr-2">{dict?.referralpro?.copyInviteCode || '复制邀请码'}</span>
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
              <path d="M10 9h7v2h-7V9zm0 3h7v2h-7v-2zm0 3h7v2h-7v-2z"/>
            </svg>
          </button>
        </div>
      </div>

      {/* 佣金记录表格 */}
      <div className="bg-white mt-4 shadow-sm mb-10">
        {/* 标签页导航 */}
        <div className="flex border-b border-gray-200">
          {[
            { key: 'commissionAmount', label: dict?.referralpro?.commissionAmountTab || '佣金金额' },
            { key: 'frozenAmount', label: dict?.referralpro?.frozenAmountTab || '冻结金额' },
            { key: 'settlementRecords', label: dict?.referralpro?.settlementRecordsTab || '结算记录' },
            { key: 'pointsList', label: dict?.referralpro?.pointsListTab || '积分列表' },
            { key: 'experienceDetails', label: dict?.referralpro?.experienceDetailsTab || '经验明细' },
            { key: 'activeUsers', label: dict?.referralpro?.activeUsersTab || '活跃用户' },
            { key: 'inviteList', label: dict?.referralpro?.inviteListTab || '邀请列表' },
            { key: 'goInvite', label: dict?.referralpro?.goInviteTab || '去邀请' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => handleTabChange(tab.key)}
              className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors ${activeTab === tab.key
                  ? 'border-orange-500 text-orange-500'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* 筛选区域 - 根据不同标签页显示不同的筛选条件 */}
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            {/* 佣金金额标签页的筛选 */}
            {activeTab === 'commissionAmount' && (
              <>
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-600">{dict?.referralpro?.typeFilter || '类型'}</label>
                  <Select
                    placeholder={dict?.referralpro?.allTypes || '全部'}
                    style={{ width: 140 }}
                    value={filters.type || undefined}
                    onChange={(value) => setFilters({...filters, type: value || ''})}
                    options={[
                      { value: '', label: dict?.referralpro?.allTypes || '全部' },
                      ...Object.entries(amountTypeList).map(([key, value]) => ({
                        value: key,
                        label: value
                      }))
                    ]}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-600">{dict?.referralpro?.timeFilter || '时间'}</label>
                  <DatePicker.RangePicker
                    placeholder={[dict?.referralpro?.startDate || '开始日期', dict?.referralpro?.endDate || '结束日期']}
                    style={{ width: 240 }}
                    value={filters.startDate && filters.endDate ? [
                      dayjs(filters.startDate),
                      dayjs(filters.endDate)
                    ] : null}
                    onChange={(dates: any) => {
                      if (dates) {
                        setFilters({
                          ...filters,
                          startDate: dates[0]?.format('YYYY-MM-DD') || '',
                          endDate: dates[1]?.format('YYYY-MM-DD') || ''
                        });
                      } else {
                        setFilters({...filters, startDate: '', endDate: ''});
                      }
                    }}
                  />
                </div>
              </>
            )}

            {/* 冻结金额标签页的筛选 */}
            {activeTab === 'frozenAmount' && (
              <>
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-600">{dict?.referralpro?.typeFilter || '类型'}</label>
                  <Select
                    placeholder={dict?.referralpro?.allTypes || '全部'}
                    style={{ width: 140 }}
                    value={filters.type || undefined}
                    onChange={(value) => setFilters({...filters, type: value || ''})}
                    options={[
                      { value: '', label: dict?.referralpro?.allTypes || '全部' },
                      ...Object.entries(frozenTypeList).map(([key, value]) => ({
                        value: key,
                        label: value
                      }))
                    ]}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-600">{dict?.referralpro?.timeFilter || '时间'}</label>
                  <DatePicker.RangePicker
                    placeholder={[dict?.referralpro?.startDate || '开始日期', dict?.referralpro?.endDate || '结束日期']}
                    style={{ width: 240 }}
                    value={filters.startDate && filters.endDate ? [
                      dayjs(filters.startDate),
                      dayjs(filters.endDate)
                    ] : null}
                    onChange={(dates: any) => {
                      if (dates) {
                        setFilters({
                          ...filters,
                          startDate: dates[0]?.format('YYYY-MM-DD') || '',
                          endDate: dates[1]?.format('YYYY-MM-DD') || ''
                        });
                      } else {
                        setFilters({...filters, startDate: '', endDate: ''});
                      }
                    }}
                  />
                </div>
              </>
            )}

            {/* 邀请列表的筛选 */}
            {activeTab === 'inviteList' && (
              <>
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-600">{dict?.referralpro?.timeFilter || '时间'}</label>
                  <DatePicker.RangePicker
                    placeholder={[dict?.referralpro?.startDate || '开始日期', dict?.referralpro?.endDate || '结束日期']}
                    style={{ width: 240 }}
                    value={filters.startDate && filters.endDate ? [
                      dayjs(filters.startDate),
                      dayjs(filters.endDate)
                    ] : null}
                    onChange={(dates: any) => {
                      if (dates) {
                        setFilters({
                          ...filters,
                          startDate: dates[0]?.format('YYYY-MM-DD') || '',
                          endDate: dates[1]?.format('YYYY-MM-DD') || ''
                        });
                      } else {
                        setFilters({...filters, startDate: '', endDate: ''});
                      }
                    }}
                  />
                </div>
              </>
            )}

            {/* 经验明细的筛选 */}
            {activeTab === 'experienceDetails' && (
              <>

                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-600">{dict?.referralpro?.timeFilter || '时间'}</label>
                  <DatePicker.RangePicker
                    placeholder={[dict?.referralpro?.startDate || '开始日期', dict?.referralpro?.endDate || '结束日期']}
                    style={{ width: 240 }}
                    value={filters.startDate && filters.endDate ? [
                      dayjs(filters.startDate),
                      dayjs(filters.endDate)
                    ] : null}
                    onChange={(dates: any) => {
                      if (dates) {
                        setFilters({
                          ...filters,
                          startDate: dates[0]?.format('YYYY-MM-DD') || '',
                          endDate: dates[1]?.format('YYYY-MM-DD') || ''
                        });
                      } else {
                        setFilters({...filters, startDate: '', endDate: ''});
                      }
                    }}
                  />
                </div>
              </>
            )}

            {/* 活跃用户的筛选 */}
            {activeTab === 'activeUsers' && (
              <>
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-600">{dict?.referralpro?.timeFilter || '时间'}</label>
                  <DatePicker.RangePicker
                    placeholder={[dict?.referralpro?.startDate || '开始日期', dict?.referralpro?.endDate || '结束日期']}
                    style={{ width: 240 }}
                    value={filters.startDate && filters.endDate ? [
                      dayjs(filters.startDate),
                      dayjs(filters.endDate)
                    ] : null}
                    onChange={(dates: any) => {
                      if (dates) {
                        setFilters({
                          ...filters,
                          startDate: dates[0]?.format('YYYY-MM-DD') || '',
                          endDate: dates[1]?.format('YYYY-MM-DD') || ''
                        });
                      } else {
                        setFilters({...filters, startDate: '', endDate: ''});
                      }
                    }}
                  />
                </div>
              </>
            )}

            {/* 积分列表的筛选 */}
            {activeTab === 'pointsList' && (
              <>
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-600">{dict?.referralpro?.typeFilter || '类型'}</label>
                  <Select
                    placeholder={dict?.referralpro?.allTypes || '全部'}
                    style={{ width: 140 }}
                    value={filters.type || undefined}
                    onChange={(value) => setFilters({...filters, type: value || ''})}
                    options={[ { value: '', label: dict?.referralpro?.allTypes || '全部' },
                      ...Object.entries(scoreTypeList).map(([key, value]) => ({
                        value: key,
                        label: value
                      }))
                    ]}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-600">{dict?.referralpro?.timeFilter || '时间'}</label>
                  <DatePicker.RangePicker
                    placeholder={[dict?.referralpro?.startTime || '开始时间', dict?.referralpro?.endTime || '结束时间']}
                    style={{ width: 240 }}
                    value={filters.startDate && filters.endDate ? [
                      dayjs(filters.startDate),
                      dayjs(filters.endDate)
                    ] : null}
                    onChange={(dates: any) => {
                      if (dates) {
                        setFilters({
                          ...filters,
                          startDate: dates[0]?.format('YYYY-MM-DD') || '',
                          endDate: dates[1]?.format('YYYY-MM-DD') || ''
                        });
                      } else {
                        setFilters({...filters, startDate: '', endDate: ''});
                      }
                    }}
                  />
                </div>
              </>
            )}

            {/* 结算记录的筛选 */}
            {activeTab === 'settlementRecords' && (
              <>
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-600">{dict?.referralpro?.settlementTimeFilter || '结算时间'}</label>
                  <DatePicker.RangePicker
                    placeholder={[dict?.referralpro?.startTime || '开始时间', dict?.referralpro?.endTime || '结算时间']}
                    style={{ width: 240 }}
                    value={filters.startDate && filters.endDate ? [
                      dayjs(filters.startDate),
                      dayjs(filters.endDate)
                    ] : null}
                    onChange={(dates: any) => {
                      if (dates) {
                        setFilters({
                          ...filters,
                          startDate: dates[0]?.format('YYYY-MM-DD') || '',
                          endDate: dates[1]?.format('YYYY-MM-DD') || ''
                        });
                      } else {
                        setFilters({...filters, startDate: '', endDate: ''});
                      }
                    }}
                  />
                </div>
              </>
            )}

            {/* 去邀请标签页不显示筛选 */}
            {!(['goInvite'].includes(activeTab)) && (
              <>
                <Button
                  type="primary"
                  style={{ backgroundColor: '#FF6000' }}
                  onClick={handleSearch}
                >
                  {dict?.referralpro?.search || '搜索'}
                </Button>
                <Button
                  onClick={handleReset}
                >
                  {dict?.referralpro?.reset || '重置'}
                </Button>
              </>
            )}
          </div>
        </div>

        {/* 表格内容 */}
        <div className="overflow-x-auto">
          {tabLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-orange-500"></div>
              <span className="ml-2 text-gray-600">{dict?.referralpro?.loading || '加载中...'}</span>
            </div>
          ) : (
            <>
              {/* 佣金金额标签页 */}
              {activeTab === 'commissionAmount' && (
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100 border-b border-gray-200">
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.id || 'ID'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.type || '类型'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.beforeAmount || '变动前金额'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.changeAmount || '变动金额'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.afterAmount || '变动后金额'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.time || '时间'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.remark || '备注'}</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {amountList.length > 0 ? amountList.map((record: any, index: number) => (
                      <tr key={record.id || index} className={`border-b border-gray-200 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.id || '-'}</td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.ways_text || amountTypeList[record.ways] || record.type || record.change_type || record.source || '-'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {formatCurrency(parseFloat(record.before) || record.before_amount || record.prev_amount || 0).formatValue}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          <span className={parseFloat(record.amount) > 0 ? 'text-green-600' : 'text-red-600'}>
                            {parseFloat(record.amount) > 0 ? '+' : ''}
                            {formatCurrency(Math.abs(parseFloat(record.amount) || 0)).formatValue}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {formatCurrency(parseFloat(record.after) || record.after_amount || record.current_amount || 0).formatValue}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.createtime || (record.time ? (typeof record.time === 'number' ? new Date(record.time * 1000).toLocaleString() : record.time) : '-')}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.memo || record.remark || record.description || '-'}</td>
                      </tr>
                    )) : (
                      <tr>
                        <td colSpan={7} className="px-4 py-8 text-center text-gray-500">
                          {dict?.referralpro?.noCommissionRecords || '暂无佣金记录'}
                          <div className="text-xs text-gray-400 mt-1">{dict?.referralpro?.commissionRecordsDesc || '推广成功后将显示佣金变动记录'}</div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              )}

              {/* 冻结金额标签页 */}
              {activeTab === 'frozenAmount' && (
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100 border-b border-gray-200">
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.id || 'ID'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.type || '类型'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.beforeAmount || '变动前金额'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.changeAmount || '变动金额'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.afterAmount || '变动后金额'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.time || '时间'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.remark || '备注'}</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {frozenAmountList.length > 0 ? frozenAmountList.map((record: any, index: number) => (
                      <tr key={record.id || index} className={`border-b border-gray-200 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.id || '-'}</td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.ways_text || frozenTypeList[record.ways] || record.type || record.change_type || record.source || '-'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {formatCurrency(parseFloat(record.before) || record.before_amount || record.prev_amount || 0).formatValue}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          <span className={parseFloat(record.amount) > 0 ? 'text-green-600' : 'text-red-600'}>
                            {parseFloat(record.amount) > 0 ? '+' : ''}
                            {formatCurrency(Math.abs(parseFloat(record.amount) || 0)).formatValue}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {formatCurrency(parseFloat(record.after) || record.after_amount || record.current_amount || 0).formatValue}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.createtime || (record.time ? (typeof record.time === 'number' ? new Date(record.time * 1000).toLocaleString() : record.time) : '-')}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.memo || record.remark || record.description || '-'}</td>
                      </tr>
                    )) : (
                      <tr>
                        <td colSpan={7} className="px-4 py-8 text-center text-gray-500">
                          {dict?.referralpro?.noFrozenRecords || '暂无冻结金额记录'}
                          <div className="text-xs text-gray-400 mt-1">{dict?.referralpro?.frozenRecordsDesc || '冻结金额变动记录将显示在此处'}</div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              )}

              {/* 结算记录标签页 */}
              {activeTab === 'settlementRecords' && (
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100 border-b border-gray-200">
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.id || 'ID'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.settlementAmount || '结算金额'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.settlementPeriod || '结算时间段'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.createTime || '创建时间'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.remark || '备注'}</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {settlementList.length > 0 ? settlementList.map((record: any, index: number) => (
                      <tr key={record.id || index} className={`border-b border-gray-200 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.id || '-'}</td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          <span className="text-green-600">
                            {formatCurrency(parseFloat(record.amount) || 0).formatValue}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.start_time_text && record.end_time_text
                            ? `${record.start_time_text} ~ ${record.end_time_text}`
                            : '-'
                          }
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.createtime || '-'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.memo || record.remark || record.description || '-'}</td>
                      </tr>
                    )) : (
                      <tr>
                        <td colSpan={5} className="px-4 py-8 text-center text-gray-500">
                          {dict?.referralpro?.noSettlementRecords || '暂无结算记录'}
                          <div className="text-xs text-gray-400 mt-1">{dict?.referralpro?.settlementRecordsDesc || '佣金结算记录将显示在此处'}</div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              )}

              {/* 经验明细标签页 */}
              {activeTab === 'experienceDetails' && (
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100 border-b border-gray-200">
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.id || 'ID'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.experienceValue || '经验值'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.source || '来源'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.time || '时间'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.remark || '备注'}</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {experienceList.length > 0 ? experienceList.map((record: any, index: number) => (
                      <tr key={record.id || index} className={`border-b border-gray-200 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.id || '-'}</td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          <span className="text-green-600">+{record.experience || record.exp_value || 0}</span>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.source || record.type || record.source_type || '-'}</td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.createtime ? new Date(record.createtime).toLocaleString() :
                           record.time ? (typeof record.time === 'number' ? new Date(record.time * 1000).toLocaleString() : record.time) : '-'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.remark || record.description || record.memo || '-'}</td>
                      </tr>
                    )) : (
                      <tr>
                        <td colSpan={5} className="px-4 py-8 text-center text-gray-500">
                          {dict?.referralpro?.noExperienceRecords || '暂无经验记录'}
                          <div className="text-xs text-gray-400 mt-1">{dict?.referralpro?.experienceRecordsDesc || '完成推广任务可获得经验值'}</div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              )}

              {/* 邀请列表标签页 */}
              {activeTab === 'inviteList' && (
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100 border-b border-gray-200">
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.id || 'ID'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.inviteUsername || '邀请用户名'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.email || '邮箱'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.status || '状态'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.inviteTime || '邀请时间'}</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {inviteList.length > 0 ? inviteList.map((record: any, index: number) => (
                      <tr key={record.id} className={`border-b border-gray-200 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.id}</td>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.reg_username || record.username || record.reg_user_name || '-'}</td>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.regemail || record.email || '-'}</td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          <span className={`px-2 py-1 rounded text-xs ${
                            record.status === 2 ? 'bg-green-100 text-green-800' :
                            record.status === 1 ? 'bg-blue-100 text-blue-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {record.status === 2 ? (dict?.referralpro?.activated || '已激活') :
                             record.status === 1 ? (dict?.referralpro?.registered || '已注册') :
                             (dict?.referralpro?.unregistered || '未注册')}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.createtime ? record.createtime :
                           record.regtime ? new Date(record.regtime * 1000).toLocaleString('zh-CN', {
                             year: 'numeric',
                             month: '2-digit',
                             day: '2-digit',
                             hour: '2-digit',
                             minute: '2-digit',
                             second: '2-digit'
                           }) : '-'}
                        </td>
                      </tr>
                    )) : (
                      <tr>
                        <td colSpan={5} className="px-4 py-8 text-center text-gray-500">{dict?.referralpro?.noInviteRecords || '暂无邀请记录'}</td>
                      </tr>
                    )}
                  </tbody>
                </table>
              )}

              {/* 活跃用户标签页 */}
              {activeTab === 'activeUsers' && (
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100 border-b border-gray-200">
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.id || 'ID'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.inviteUsername || '邀请用户名'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.productAmount || '商品金额'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.orderCount || '订单数量'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.shippingAmount || '运费金额'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.shippingCount || '运单数量'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.registerTime || '注册时间'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.activeMonth || '活跃月份'}</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {activeUserList.length > 0 ? activeUserList.map((record: any, index: number) => (
                      <tr key={record.id || index} className={`border-b border-gray-200 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.id || record.user_id || '-'}</td>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.username || record.nickname || record.invite_username || record.reg_username || '-'}</td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {formatCurrency(parseFloat(record.goods_amount || record.product_amount || record.total_amount || 0)).formatValue}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.order_count || record.orders || record.order_num || 0}</td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {formatCurrency(parseFloat(record.shipping_amount || record.express_amount || record.freight_amount || 0)).formatValue}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.shipping_count || record.express_count || record.sendorder_count || 0}</td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.register_time || record.reg_time || record.createtime ||
                           (record.regtime ? new Date(record.regtime * 1000).toLocaleString('zh-CN', {
                             year: 'numeric',
                             month: '2-digit',
                             day: '2-digit',
                             hour: '2-digit',
                             minute: '2-digit',
                             second: '2-digit'
                           }) : '-')}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.active_month || record.month || '-'}</td>
                      </tr>
                    )) : (
                      <tr>
                        <td colSpan={8} className="px-4 py-8 text-center text-gray-500">
                          {dict?.referralpro?.noActiveUsers || '暂无活跃用户'}
                          <div className="text-xs text-gray-400 mt-1">{dict?.referralpro?.activeUsersDesc || '当月完成10单以上的用户将显示在此处'}</div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              )}

              {/* 去邀请标签页 - 发送邀请邮件 */}
              {activeTab === 'goInvite' && (
                <div className="p-6">
                  <h3 className="text-lg font-bold mb-4">{dict?.referralpro?.sendInviteEmail || '发送邀请邮件'}</h3>
                  <div className="mb-6">
                    <div className="flex items-center gap-3 mb-4">
                      <Button
                        type="primary"
                        className="flex items-center"
                        onClick={() => setShowEmailForm(!showEmailForm)}
                        style={{ backgroundColor: '#FF6000' }}
                      >
                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                          <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                        </svg>
                        {dict?.referralpro?.sendInviteEmail || '发送邀请邮件'}
                      </Button>
                    </div>

                    {showEmailForm && (
                      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                        <div className="flex flex-wrap items-center gap-3">
                          <div className="flex-grow">
                            <input
                              type="email"
                              value={emailInput}
                              onChange={(e) => setEmailInput(e.target.value)}
                              placeholder={dict?.referralpro?.enterFriendEmail || "输入好友邮箱"}
                              className="w-full p-2 border border-gray-200 rounded-md"
                            />
                          </div>
                          <Button
                            type="primary"
                            onClick={handleSendInviteEmail}
                            loading={sendingEmail}
                            style={{ backgroundColor: '#FF6000' }}
                          >
                            {dict?.referralpro?.sendInvite || '发送邀请邮件'}
                          </Button>
                        </div>
                      </div>
                    )}

                    <div className="text-sm text-gray-600">
                      <p className="mb-2">{dict?.referralpro?.inviteEmailDesc || '通过邮件邀请好友注册，好友成功注册后您将获得推广奖励。'}</p>
                      <p>{dict?.referralpro?.inviteMethodsDesc || '您也可以通过以下方式邀请好友：'}</p>
                      <ul className="list-disc list-inside mt-2 space-y-1">
                        <li>{dict?.referralpro?.sharePromotionLink || '分享推广链接'}</li>
                        <li>{dict?.referralpro?.sharePromotionQR || '分享推广二维码'}</li>
                        <li>{dict?.referralpro?.shareInviteCode || '分享邀请码'}</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {/* 积分列表标签页 */}
              {activeTab === 'pointsList' && (
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100 border-b border-gray-200">
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.id || 'ID'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.type || '类型'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.beforePoints || '变动前积分'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.changePoints || '变动积分'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.afterPoints || '变动后积分'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.time || '时间'}</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">{dict?.referralpro?.remark || '备注'}</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {scoreList.length > 0 ? scoreList.map((record: any, index: number) => (
                      <tr key={record.id || index} className={`border-b border-gray-200 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.id || '-'}</td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.type_text || record.type || '-'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.before || 0}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          <span className={parseFloat(record.score) > 0 ? 'text-green-600' : 'text-red-600'}>
                            {parseFloat(record.score) > 0 ? '+' : ''}
                            {Math.abs(parseFloat(record.score) || 0)}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.after || 0}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.createtime || '-'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">{record.memo || record.remark || record.description || '-'}</td>
                      </tr>
                    )) : (
                      <tr>
                        <td colSpan={7} className="px-4 py-8 text-center text-gray-500">
                          {dict?.referralpro?.noPointsRecords || '暂无积分记录'}
                          <div className="text-xs text-gray-400 mt-1">{dict?.referralpro?.pointsRecordsDesc || '积分变动记录将显示在此处'}</div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              )}
            </>
          )}
        </div>

        {/* 分页控件 */}
        {!tabLoading && pagination.total > pagination.per_page && (
          <div className="flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200">
            <div className="flex items-center text-sm text-gray-700">
              <span>
                {dict?.referralpro?.pagination?.showing || '显示第'} {((pagination.current_page - 1) * pagination.per_page) + 1} {dict?.referralpro?.pagination?.to || '到'}{' '}
                {Math.min(pagination.current_page * pagination.per_page, pagination.total)} {dict?.referralpro?.pagination?.of || '条，共'} {pagination.total} {dict?.referralpro?.pagination?.records || '条记录'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={() => handlePageChange(pagination.current_page - 1)}
                disabled={pagination.current_page <= 1}
                size="small"
              >
                {dict?.referralpro?.pagination?.previous || '上一页'}
              </Button>
              <span className="px-3 py-1 text-sm">
                {dict?.referralpro?.pagination?.page || '第'} {pagination.current_page} {dict?.referralpro?.pagination?.totalPages || '页，共'} {pagination.last_page} {dict?.referralpro?.pagination?.pages || '页'}
              </span>
              <Button
                onClick={() => handlePageChange(pagination.current_page + 1)}
                disabled={pagination.current_page >= pagination.last_page}
                size="small"
              >
                {dict?.referralpro?.pagination?.next || '下一页'}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* 提现Modal */}
      <Modal
        title={dict?.referralpro?.withdrawModal?.title || "提现"}
        open={showWithdrawModal}
        onCancel={handleCloseWithdrawModal}
        footer={[
          <Button key="cancel" onClick={handleCloseWithdrawModal}>
            {dict?.referralpro?.withdrawModal?.cancel || '取消'}
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleSubmitWithdraw}
            loading={submittingWithdraw}
            style={{ backgroundColor: '#FF6000' }}
          >
            {dict?.referralpro?.withdrawModal?.confirm || '确认'}
          </Button>
        ]}
        centered
        maskClosable={false}
        width={500}
      >
        <div className="space-y-4">
          <div className="text-sm text-gray-600 mb-4">
            {dict?.referralpro?.withdrawModal?.description || '佣金余额将提现至您的账户'}
          </div>

          {/* 提现金额 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {dict?.referralpro?.withdrawModal?.withdrawAmount || '提现金额'}
              <span className="text-sm text-gray-500 font-normal ml-2">
                ({dict?.referralpro?.withdrawModal?.availableBalance || '可提现余额'}: {formatCurrency(promotionDetail?.amountCount?.amount || 0).formatValue})
              </span>
            </label>
            <Input
              placeholder={dict?.referralpro?.withdrawModal?.enterAmount || "请输入提现金额"}
              value={withdrawAmount}
              onChange={(e) => setWithdrawAmount(e.target.value)}
              type="number"
              min="0"
              step="0.01"
            />
            { parseFloat(withdrawAmount) > parseFloat(String(promotionDetail?.amountCount?.amount || 0)) && <p className="text-red-500 text-sm mt-2">{dict?.referralpro?.withdrawModal?.verificationMoney}</p>} 
          </div>

          {/* 验证邮箱 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {dict?.referralpro?.withdrawModal?.verificationEmail || '验证邮箱'}
            </label>
            <Input
              placeholder={dict?.referralpro?.withdrawModal?.enterEmail || "请输入验证邮箱"}
              value={withdrawEmail}
              type="email"
              readOnly={true}
              disabled={true}
              style={{
                backgroundColor: '#f5f5f5',
                cursor: 'not-allowed',
                color: '#666'
              }}
            />
          </div>

          {/* 验证码 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {dict?.referralpro?.withdrawModal?.verificationCode || '验证码'}
            </label>
            <div className="flex space-x-2">
              <Input
                placeholder={dict?.referralpro?.withdrawModal?.enterCode || "请输入验证码"}
                value={withdrawEmailCode}
                onChange={(e) => setWithdrawEmailCode(e.target.value)}
                className="flex-1"
              />
              <Button
                onClick={handleSendWithdrawEmailCode}
                loading={sendingWithdrawEmail}
                disabled={emailCountdown > 0}
                style={{
                  backgroundColor: emailCountdown > 0 ? '#ccc' : '#FF6000',
                  borderColor: emailCountdown > 0 ? '#ccc' : '#FF6000'
                }}
                type="primary"
              >
                {emailCountdown > 0 ? `${emailCountdown}s` : (dict?.referralpro?.withdrawModal?.sendCode || '发送验证码')}
              </Button>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
}
