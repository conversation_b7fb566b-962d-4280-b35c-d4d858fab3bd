'use client'
import React, { useState,useEffect } from 'react';
import TabsComponent from '@/components/Tabs';
import type { TabsProps } from 'antd';
import MessageList from './components/MessageList';
import { useParams } from 'next/navigation';
import { getDictionary } from "@/dictionaries";

export default function Messages() {
  const { lng } = useParams();
  const [activeKey, setActiveKey] = useState<'all' | 'unread' | 'read'>('all');
  const [dict, setDict] = useState<any>(null); // 添加字典状态
  // 异步获取字典数据
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };

    fetchDictionary();
  }, [lng]);
  const items: TabsProps['items'] = [
    {
      key: 'all',
      label: dict?.dashboard?.message?.all,
    },
    {
      key: 'unread',
      label:dict?.dashboard?.message?.unread,
    },
    {
      key: 'read',
      label: dict?.dashboard?.message?.read,
    },
  ];
  return (
    <div className="p-6">
      <TabsComponent 
        activeKey={activeKey}
        items={items}
        onChange={(key) => setActiveKey(key as 'all' | 'unread' | 'read')}
        className="message-tabs"
      />
      <MessageList type={activeKey} dict={dict} />
    </div>
  );
}