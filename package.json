{"name": "new-one-buy-next", "version": "0.1.0", "private": true, "scripts": {"start": "next start", "lint": "next lint", "dev:6": "env-cmd -f .env.development.tp6 next dev", "build:6": "env-cmd -f .env.production.tp6 next build", "dev": "env-cmd -f .env.development next dev", "build": "env-cmd -f .env.production next build"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@formatjs/intl-localematcher": "^0.6.1", "@fortawesome/fontawesome-free": "^6.7.2", "@iconify-json/carbon": "^1.2.8", "@iconify-json/fa6-regular": "^1.2.3", "@iconify-json/fa6-solid": "^1.2.3", "@reactuses/core": "^6.0.1", "@types/js-cookie": "^3.0.6", "@types/jsbarcode": "^3.11.4", "@types/react-joyride": "^2.0.2", "antd": "^5.24.8", "axios": "^1.8.4", "dayjs": "^1.11.13", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "env-cmd": "^10.1.0", "framer-motion": "^12.7.3", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "jsbarcode": "^3.12.1", "negotiator": "^1.0.0", "next": "15.3.0", "next-international": "^1.3.1", "next-intl": "^4.1.0", "react": "^19.1.0", "react-countup": "^6.5.3", "react-dom": "^19.1.0", "react-fast-marquee": "^1.6.5", "react-intersection-observer": "^9.16.0", "react-joyride": "^3.0.0-7", "react-use": "^17.6.0", "saas": "^1.0.0", "swiper": "^11.2.10", "zustand": "^5.0.3"}, "devDependencies": {"@iconify-json/ant-design": "^1.2.5", "@iconify-json/heroicons": "^1.2.2", "@iconify-json/ic": "^1.2.2", "@iconify-json/material-symbols": "^1.2.20", "@iconify-json/solar": "^1.2.2", "@iconify/json": "^2.2.329", "@tailwindcss/postcss": "^4", "@types/negotiator": "^0.6.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@unocss/postcss": "^66.1.0-beta.10", "@unocss/preset-icons": "^66.1.0-beta.10", "@unocss/preset-wind3": "^66.1.0-beta.12", "@unocss/reset": "^66.1.0-beta.10", "@unocss/webpack": "^66.1.0-beta.10", "eslint": "9.25.1", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5", "unocss": "^66.1.0-beta.10"}}