'use client';

import { Button, ButtonProps } from 'antd';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import React, { ReactNode } from 'react';

export default function ButtonComponent({ children, ...props }: ButtonProps & { children?: ReactNode }) {
  return (
    <AntdConfigProvider>
      <Button {...props}>
        {children && children}
      </Button>
    </AntdConfigProvider>
  );
}