'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { getDictionary } from '@/dictionaries';
import { locales } from '@/config';
import type { Locale } from '@/config';
import { Api } from '@/request/api';

export default function Footer({ dict = {} }: { dict?: any }) {
  const pathname = usePathname();
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5';

  // 状态管理
  const [navList, setNavList] = useState<any[]>([]);
  const [dynamicDict, setDynamicDict] = useState(dict);
  const [configList, setConfigList] = useState<any>({});

  // 从路径中提取语言代码
  const pathSegments = pathname.split('/');
  const currentLocale = pathSegments[1] || 'zh-cn';

  // 获取配置数据
  useEffect(() => {
    const fetchConfigList = async () => {
      try {
        const response = isTp5 ? await Api.getConfigList() : await Api.getConfigList();
        if (response?.success || response) {
          const config = response?.data || response;
          setConfigList(config);
        }
      } catch (error) {
        console.error('Failed to load config in Footer:', error);
        // 使用默认配置
        setConfigList({
          site: { name: 'onebuy' },
          foot_list: []
        });
      }
    };

    fetchConfigList();
  }, [isTp5]);

  // 动态更新字典数据
  useEffect(() => {
    const updateDictionary = async () => {
      try {
        // 确保currentLocale是有效的语言代码
        const validLocale = locales.includes(currentLocale) ? currentLocale as Locale : 'zh-cn';
        console.log('🔄 Footer updating dictionary for locale:', validLocale);
        const newDict = await getDictionary(validLocale);
        setDynamicDict(newDict);
        console.log('✅ Footer dictionary updated successfully');
      } catch (error) {
        console.error('❌ Failed to load dictionary in Footer:', error);
        // 如果加载失败，保持使用传入的dict
        setDynamicDict(dict);
      }
    };

    // 添加延迟确保路径已经更新
    const timer = setTimeout(updateDictionary, 100);
    return () => clearTimeout(timer);
  }, [currentLocale, dict, pathname]);

  // 获取导航列表
  useEffect(() => {
    const fetchNavList = async () => {
      try {
        let navData = [];
        if (isTp5) {
          const response = await Api.getNavList({ type: 'foot' });
          navData = response?.data || [];
        } else {
          navData = configList?.foot_list || [];
        }

        // 确保navData是数组
        if (Array.isArray(navData)) {
          setNavList(navData);
        } else {
          setNavList([]);
        }
      } catch (error) {
        console.error('🚨 Failed to load footer navigation:', error);
        setNavList([]);
      }
    };

    // 只有在configList加载完成后才执行导航列表请求
    if (Object.keys(configList).length > 0) {
      fetchNavList();
    }
  }, [isTp5, configList]);


  return (
    <footer className="bg-[#111827] text-white py-12">
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row gap-8 justify-between">
          {/* 左侧品牌区 */}
          <div className="lg:w-1/3">
            <div className="mb-6">
              {/* {configList?.site?.logo ? (
                <img src={configList.site.logo} alt="Logo" className="w-[130px] h-[40px] mb-4" />
              ) : (
                <div className="flex items-center font-bold text-2xl text-white mb-4">onebuy</div>
              )} */}
              <p className="text-sm text-gray-400 ">
                {dynamicDict?.home?.footer?.about?.description}
              </p>
            </div>
            {/* 社交媒体图标 */}
            <div className="flex space-x-4 mb-6">
             {configList?.site?.weixin_way &&  <a href={configList?.site?.weixin_way} target='_blank' className="text-gray-400 hover:text-white">
                <div className="i-simple-icons-wechat text-2xl"></div>
              </a>}
             {configList?.site?.facebook &&   <a href={configList?.site?.facebook} target='_blank' className="text-gray-400 hover:text-white">
                <div className="i-simple-icons-facebook text-2xl"></div>
              </a>}
             {configList?.site?.instagram_way &&  <a href={configList?.site?.instagram_way} target='_blank' className="text-gray-400 hover:text-white">
                <div className="i-simple-icons-instagram text-2xl"></div>
              </a>}
             
             
            </div>
            {/* 支付方式 */}
            <div>
              <h4 className="text-sm font-medium mb-3">{dynamicDict?.footer?.paymentMethods || '支付方式'}</h4>
              <div className="flex items-center space-x-2">
                <div className="i-simple-icons-visa w-8 h-5"></div>
                <div className="i-simple-icons-mastercard w-8 h-5"></div>
                <div className="i-simple-icons-paypal w-8 h-5"></div>
                <div className="i-simple-icons-applepay w-8 h-5"></div>
                <div className="i-simple-icons-alipay w-8 h-5"></div>
              </div>
            </div>
          </div>

          {/* 右侧导航区 */}
          <div className="lg:w-1/3">
            <div className="flex  gap-20 justify-end">
              {navList && navList.length > 0 ? navList.map((nav: any) => (
                <div key={nav.id || nav.name}>
                  <h4 className="text-base font-medium mb-4">{nav.name}</h4>
                  <ul className="space-y-2">
                    {nav.childlist && Array.isArray(nav.childlist) ? nav.childlist.map((child: any) => (
                      <li key={child.id || child.name}>
                        <a
                          href={child.linkurl}
                          target={child.target}
                          className="text-sm text-gray-400 hover:text-white transition-colors duration-300"
                        >
                          {child.name}
                        </a>
                      </li>
                    )) : null}
                  </ul>
                </div>
              )) : (
                <div className="col-span-full text-center text-gray-400">
                  <p>导航信息加载中...</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 底部版权信息 */}
        <div className="mt-12 pt-8 border-t border-gray-700">
          <div className="flex flex-wrap justify-center space-x-6 text-xs text-gray-400">
            <span> {configList?.site?.beian}</span>
            <a href={`/information?id=${configList?.site?.basic_privacy_policy}`} target='_blank' className="hover:text-white">{dynamicDict?.footer?.privacyPolicy || '隐私政策'}</a>
            <a href={`/information?id=${configList?.site?.service_agreement}`} target='_blank'className="hover:text-white">{dynamicDict?.footer?.termsOfService || '服务协议'}</a>
            <a href={`/information?id=${configList?.site?.cookie_statement}`} target='_blank'className="hover:text-white">{dynamicDict?.footer?.cookiePolicy || 'Cookie 声明'}</a>
            <a href={`/information?id=${configList?.site?.disclaimer}`} target='_blank' className="hover:text-white">{dynamicDict?.footer?.disclaimer || '免责声明'}</a>
          </div>
        </div>
      </div>
    </footer>
  )
}
