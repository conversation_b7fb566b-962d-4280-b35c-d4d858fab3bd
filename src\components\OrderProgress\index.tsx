import Image from "next/image"
export default function OrderProgress({dict}: {dict: any}) {
    return <>
        <div className="flex items-center justify-between bg-[#FFF9F5] rounded-lg mb-4 p-4">
            <div className="flex-1 flex flex-row justify-center items-center">
                <div className="text-[#FF6A00] font-bold text-2xl mr-2">01</div>
                <div className="text-[#333333] font-bold"> {dict?.order?.progress?.order} </div>
            </div>
            <div className="text-[#FFCBA8] text-4xl mx-2">&raquo;</div>
            <div className="flex-1 flex flex-row justify-center items-center">
                <div className="text-[#FF6A00] font-bold text-2xl mr-2">02</div>
                <div className="text-[#333333] font-bold"> {dict?.order?.progress?.check} </div>
            </div>
            <div className="text-[#FFCBA8] text-4xl mx-2">&raquo;</div>
            <div className="flex-1 flex flex-row justify-center items-center">
                <div className="text-[#FF6A00] font-bold text-2xl mr-2">03</div>
                <div className="text-[#333333] font-bold">{dict?.order?.progress?.submit}  </div>
            </div>
            <div className="text-[#FFCBA8] text-4xl mx-2">&raquo;</div>
            <div className="flex-1 flex flex-row justify-center items-center">
                <div className="text-[#FF6A00] font-bold text-2xl mr-2">04</div>
                <div className="text-[#333333] font-bold"> {dict?.order?.progress?.receive} </div>
            </div>
        </div>
        <div className="relative h-[120px] mb-4" data-tg-vip>
            <Image
                src="/images/home-banner.png"
                draggable="false"
                alt="VIP Banner"
                width={2156}
                height={176}
                className="w-full h-full rounded-lg object-cover"
            />
        </div></>
}