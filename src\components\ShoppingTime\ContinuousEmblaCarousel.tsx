'use client';

import { useMemo, useCallback } from 'react';
import Image from 'next/image';
import useEmblaCarousel from 'embla-carousel-react';
import Autoplay from 'embla-carousel-autoplay';
import { ShoppingTimeProduct } from "@/types/product";
import { prepareImageForNextJs } from '@/utils/imageUtils';
import { formatCurrency } from '@/utils/currency';
import styles from './ShoppingTimeCarousel.module.css';

interface ShoppingTimeCarouselProps {
  products: ShoppingTimeProduct[];
  dict: any;
}

interface UserGroup {
  user: {
    username: string;
    nickname: string;
  };
  products: ShoppingTimeProduct[];
  latestTime: string;
  totalItems: number;
}

export default function ContinuousEmblaCarousel({ products, dict }: ShoppingTimeCarouselProps) {
  // 按用户nickname进行分组聚合商品数据
  const userGroups = useMemo(() => {
    const groupMap = new Map<string, UserGroup>();

    products.forEach(product => {
      // 优先使用nickname作为分组键，如果没有nickname则使用username，最后使用user_id
      const groupKey = product.user?.nickname || product.user?.username || `user_${product.user_id}` || 'unknown';

      if (groupMap.has(groupKey)) {
        const group = groupMap.get(groupKey)!;
        group.products.push(product);
        group.totalItems += product.goodsnum;
        // 更新最新时间
        if (new Date(product.createtime) > new Date(group.latestTime)) {
          group.latestTime = product.createtime;
        }
      } else {
        // 创建新的用户组
        groupMap.set(groupKey, {
          user: product.user,
          products: [product],
          latestTime: product.createtime,
          totalItems: product.goodsnum
        });
      }
    });

    // 按最新购买时间排序，最新的在前面
    return Array.from(groupMap.values()).sort((a, b) =>
      new Date(b.latestTime).getTime() - new Date(a.latestTime).getTime()
    );
  }, [products]);

  // 将所有商品展平为连续的商品流
  const allProducts = useMemo(() => {
    const productStream: Array<{ product: ShoppingTimeProduct; user: UserGroup['user']; isFirstInGroup: boolean }> = [];
    
    userGroups.forEach((userGroup, groupIndex) => {
      userGroup.products.forEach((product, productIndex) => {
        productStream.push({
          product,
          user: userGroup.user,
          isFirstInGroup: productIndex === 0
        });
      });
    });

    return productStream;
  }, [userGroups]);

  // Embla Carousel 配置 - 连续滚动模式
  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop: true,
      align: 'start',
      skipSnaps: false,
      dragFree: true, // 启用自由拖拽
      containScroll: false,
      slidesToScroll: 1,
      duration: 20,
    },
    [
      Autoplay({
        delay: 2500,
        stopOnInteraction: false,
        stopOnMouseEnter: true,
        stopOnFocusIn: true,
      })
    ]
  );

  // 导航按钮处理
  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  // 渲染单个商品卡片
  const renderProductCard = (item: { product: ShoppingTimeProduct; user: UserGroup['user']; isFirstInGroup: boolean }, index: number) => {
    const { product, user, isFirstInGroup } = item;
    const titleLength = product.goodsname?.length || 0;
    const titleLengthClass = titleLength <= 10 ? 'short' : titleLength <= 20 ? 'medium' : 'long';
    const detailUrl = product.goodsurl
      ? `/detail/${product.goodssite || 'taobao'}/?url=${encodeURIComponent(product.goodsurl)}`
      : '#';

    return (
      <div
        key={`product-${product.id || product.goodsimg || index}`}
        className="embla__slide flex-shrink-0"
        style={{ flex: '0 0 auto', minWidth: '288px' }}
      >
        <div className="relative">
          {/* 用户信息标签 - 仅在组内第一个商品显示 */}
          {isFirstInGroup && (
            <div className="absolute top-2 left-2 z-20 bg-gradient-to-r from-orange-400 to-orange-600 text-white px-2 py-1 rounded-md text-xs font-medium shadow-lg">
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-white/30 rounded-full flex items-center justify-center text-xs font-bold">
                  {(user.nickname || user.username || 'U').charAt(0).toUpperCase()}
                </div>
                <span className="truncate max-w-16 text-xs">
                  {user.nickname || user.username}
                </span>
              </div>
            </div>
          )}

          <a
            href={detailUrl}
            target="_blank"
            rel="noopener noreferrer"
            className={`${styles.productCard} ${styles.adaptive} group block mr-4`}
            data-title-length={titleLengthClass}
          >
            <div className={styles.productImage}>
              <Image
                src={prepareImageForNextJs(product.goodsimg)}
                alt={product.goodsname || '商品图片'}
                fill
                sizes="(max-width: 480px) 150px, (max-width: 768px) 250px, 288px"
                className="object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/images/default.jpg';
                }}
              />
              {product.recommend === 1 && (
                <div className={styles.recommendTag}>推荐</div>
              )}
            </div>
            <div className="p-3 flex-1 flex flex-col">
              <h3 className="text-md text-gray-800 line-clamp-2 mb-2 custom-h-48px">
                {product.goodsname}
              </h3>
              <div className="flex items-center justify-between mt-auto">
                <span className="text-[#FF6B00] font-medium">
                  <span className='text-sm sm:text-base md:text-lg lg:text-xl font-bold'>
                    {formatCurrency(Number(product.goodsprice)).formatValue}
                  </span>
                </span>
              </div>
            </div>
          </a>
        </div>
      </div>
    );
  };

  return (
    <div className="relative w-full group px-4">
      {allProducts.length > 0 ? (
        <>
          <div className="embla overflow-hidden pt-8" ref={emblaRef}>
            <div className="embla__container flex">
              {allProducts.map((item, index) => renderProductCard(item, index))}
            </div>
          </div>

          {/* 导航按钮 - 仅在hover时显示 */}
          <button
            className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-gray-700 hover:text-orange-600 p-3 rounded-full shadow-lg transition-all duration-300 z-30 border border-gray-200 opacity-0 group-hover:opacity-100"
            onClick={scrollPrev}
            aria-label="上一个"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-gray-700 hover:text-orange-600 p-3 rounded-full shadow-lg transition-all duration-300 z-30 border border-gray-200 opacity-0 group-hover:opacity-100"
            onClick={scrollNext}
            aria-label="下一个"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      ) : (
        <div className="text-center text-gray-500 py-12">
          {dict?.home?.emptyList || '列表为空'}
        </div>
      )}
    </div>
  );
}
