'use client';

import Link from 'next/link';
import { useEffect, useState } from 'react';
import { getDictionary } from '@/dictionaries';

export default function NotFound() {
  const [dict, setDict] = useState<any>(null);
  const [lng, setLng] = useState('zh-cn');

  useEffect(() => {
    // 从URL路径中获取语言
    const pathSegments = window.location.pathname.split('/');
    const currentLng = pathSegments[1] && ['en', 'zh-cn', 'ja'].includes(pathSegments[1])
      ? pathSegments[1]
      : 'zh-cn';

    setLng(currentLng);

    // 加载对应语言的字典
    const loadDictionary = async () => {
      try {
        const dictionary = await getDictionary(currentLng as any);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };

    loadDictionary();
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <h2 className="text-4xl font-bold mb-4">404</h2>
      <p className="text-xl mb-6">
        {dict?.common?.pageNotFound || '页面未找到'}
      </p>
      <Link href={`/${lng}`} className="text-blue-500 hover:text-blue-600">
        {dict?.nav?.backToHome || '返回首页'}
      </Link>
    </div>
  );
}