import { getDictionary } from "@/dictionaries";
import type { Locale } from "@/config";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import SideNav from "@/components/SideNav";
import CacheValidator from "@/components/CacheValidator";
import ConfigLoadingWrapper from "@/components/ConfigLoadingWrapper";
import ErrorBoundary from "@/components/ErrorBoundary";
import GlobalErrorHandler from "@/components/GlobalErrorHandler";

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ lng: Locale }>;
}>) {
  const { lng } = await params;
  const dict = await getDictionary(lng);

  return (
    <>
      <GlobalErrorHandler />
      <CacheValidator />
      <ErrorBoundary>
        <ConfigLoadingWrapper currentLng={lng}>
          <Header dict={dict} />
          <main className="grow-1">{children}</main>
          <Footer dict={dict} />
          {/* 侧边导航 */}
          <SideNav />
        </ConfigLoadingWrapper>
      </ErrorBoundary>
    </>
  );
}
