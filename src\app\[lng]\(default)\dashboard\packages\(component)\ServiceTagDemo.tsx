'use client'

import React, { useState } from 'react'
import PhotoViewer from './PhotoViewer'

// 演示用的服务数据
const demoServices = [
    { id: 1, name: 'package', title: '打包' },
    { id: 2, name: 'photo', title: '拍照' },
    { id: 3, name: 'insurance', title: '保险' }
]

// 演示用的照片数据
const demoPhotos = [
    {
        id: 1,
        image: 'https://via.placeholder.com/800x600/FF6B6B/FFFFFF?text=运单照片+1'
    },
    {
        id: 2,
        image: 'https://via.placeholder.com/800x600/4ECDC4/FFFFFF?text=运单照片+2'
    },
    {
        id: 3,
        image: 'https://via.placeholder.com/800x600/45B7D1/FFFFFF?text=运单照片+3'
    }
]

// 模拟字典数据
const mockDict = {
    confirm: {
        server: {
            package: '打包',
            photo: '拍照',
            insurance: '保险'
        }
    }
}

interface PhotoItem {
    id: number;
    image: string;
}

export default function ServiceTagDemo() {
    const [photoList, setPhotoList] = useState<PhotoItem[]>([])
    const [photoIsModal, setPhotoIsModal] = useState(false)
    const [photoViewerModal, setPhotoViewerModal] = useState(false)
    const [selectedPhotoIndex, setSelectedPhotoIndex] = useState(0)

    const handleViewPhoto = async () => {
        // 模拟API调用
        console.log('点击了拍照标签')
        setPhotoList(demoPhotos)
        setPhotoIsModal(true)
    }

    const handlePhotoClick = (index: number) => {
        setSelectedPhotoIndex(index)
        setPhotoViewerModal(true)
        setPhotoIsModal(false)
    }

    const handleClosePhotoViewer = () => {
        setPhotoViewerModal(false)
        setPhotoIsModal(true)
    }

    return (
        <div className="p-6">
            <h2 className="text-2xl font-bold mb-6">运单服务标签演示</h2>
            
            <div className="mb-6">
                <p className="text-gray-600 mb-4">
                    这是运单中的服务标签，其中"拍照"标签可以点击查看照片：
                </p>
            </div>

            {/* 服务标签 */}
            <div className="flex align-center mb-6">
                {demoServices.map((item: any) => (
                    <p 
                        key={item.id} 
                        className={`inline-flex items-center mr-2 px-2.5 py-1 bg-orange-50/50 text-orange-500 rounded-full text-xs font-medium border border-orange-100 ${
                            item.name === 'photo' ? 'cursor-pointer hover:bg-orange-100 transition-colors' : ''
                        }`}
                        onClick={item.name === 'photo' ? handleViewPhoto : undefined}
                    >
                        {item.name === 'photo' && <i className="fas fa-camera mr-1"></i>}
                        {(mockDict?.confirm.server as any)?.[item.name]}
                    </p>
                ))}
            </div>

            <div className="text-sm text-gray-500">
                点击"拍照"标签可以查看运单照片
            </div>

            {/* 照片列表模态框 */}
            {photoIsModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold">运单照片</h3>
                            <button 
                                onClick={() => setPhotoIsModal(false)}
                                className="text-gray-500 hover:text-gray-700"
                            >
                                <i className="fas fa-times"></i>
                            </button>
                        </div>
                        <div className="grid grid-cols-2 gap-4 mb-6 overflow-y-auto max-h-[500px]">
                            {photoList.map((item: any, index: number) => (
                                <img 
                                    src={item.image}  
                                    key={item.id}  
                                    className="w-full h-36 object-contain rounded-lg cursor-pointer hover:opacity-80 transition-opacity" 
                                    onClick={() => handlePhotoClick(index)}
                                    alt={`运单照片 ${index + 1}`}
                                />
                            ))}
                        </div>
                    </div>
                </div>
            )}

            {/* 照片查看器 */}
            <PhotoViewer
                photos={photoList}
                isOpen={photoViewerModal}
                onClose={handleClosePhotoViewer}
                initialIndex={selectedPhotoIndex}
            />
        </div>
    )
}
