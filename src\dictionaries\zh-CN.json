{"title": "Onebuy - 全球购物平台", "description": "Onebuy - 全球购物平台", "common": {"retry": "重试", "manualEntry": "手工填单", "pageNotFound": "页面未找到", "errorMessage": "错误信息", "viewDetails": "查看详情", "goBack": "返回上一页"}, "nav": {"home": "首页", "about": "关于", "transport": "转运", "alliance": "推广联盟", "shipping": "运费估算", "help": "帮助中心", "contact": "联系我们", "more": "更多", "login": "登录", "register": "注册", "selfservice": "手工填单", "sizecomparison": "尺码对照", "dashboard": "会员中心", "orders": "订单", "warehouse": "仓库", "packages": "包裹", "previewPackages": "预演包裹", "logout": "退出账号", "backToHome": "返回首页"}, "confirm": {"server": {"unpack": "拆包", "pack": "打包", "photo": "拍照", "insurance": "保险"}, "onepayorder": {"title": "一次付款订单确认", "couponNotice": "一次付款订单只能使用'全部'使用范围的优惠券", "shippingAddress": "收货地址", "shippingMethod": "运输方式", "productSummary": "商品汇总", "packageServices": "包裹附加服务", "productTotal": "商品总计", "packageSummary": "包裹汇总", "packageTotal": "包裹总计", "grandTotal": "订单总计", "submitOrder": "提交一次付款订单", "coupon": {"title": "可用优惠券", "noCoupons": "暂无可用优惠券", "selectCoupon": "选择优惠券", "loading": "加载中...", "fetchingCoupons": "正在获取优惠券", "validUntil": "有效期", "noExpiry": "无"}, "summary": {"productSummary": "商品摘要", "productAmount": "商品金额", "shippingToChina": "到中国仓运费", "orderAdditionalService": "订单附加服务费", "productServiceFee": "商品服务费", "couponDiscount": "优惠券折扣", "productPayable": "商品应付金额", "packageAdditionalServices": "包裹附加服务", "packageSummary": "包裹摘要", "discountRate": "折扣率", "deliveryFee": "配送费", "customsFee": "关税费", "transportServiceFee": "运输服务费", "fuelFee": "燃油费", "internationalShipping": "国际运费", "packageServiceFee": "包裹服务费", "packageAdditionalServiceFee": "包裹附加服务费", "packagePayable": "包裹应付金额", "saved": "已节省", "total": "总计", "serviceTerms": "《Onebuy服务条款》", "returnPolicy": "《退换物流政策》", "termsOfService": "《服务条款》", "privacyPolicy": "《隐私政策》", "faq": "《常见问题》", "agreeTerms": "我已阅读并同意条款", "submitting": "提交中...", "submit": "提交", "notice": "温馨提示", "noticeText": "提交订单并付款后，我们将为您采购并发货，请耐心等待。发货后，您可以通过包裹中的国际跟踪号查看包裹物理。", "loading": "正在加载数据..."}, "validation": {"selectAddressAndShipping": "请选择收货地址和运输方式", "cartInfoMissing": "购物车信息缺失", "agreeTermsFirst": "请先阅读并同意条款", "fillPhotoRemarks": "请填写拍照备注"}, "messages": {"orderSubmitSuccess": "订单提交成功", "orderSubmitFailed": "订单提交失败", "orderSubmitRetry": "订单提交失败，请重试"}, "services": {"vacuum": "真空包装", "photo": "拍照", "insurance": "保险", "insuranceDesc": "商品总价 * 2%"}, "coupons": {"availableCoupons": "可用优惠券", "viewAll": "查看全部", "noCoupons": "暂无优惠券", "noCouponsDesc": "无可用优惠券"}, "productInfo": {"shippingToChina": "到中国仓运费", "specifications": "规格", "additionalServices": "附加服务", "packageInfo": "包裹信息"}, "address": {"shippingAddress": "收货地址", "addNewAddress": "添加新地址", "default": "默认", "noAddresses": "暂无收货地址", "noAddressesFound": "未找到地址"}, "shipping": {"shippingMethod": "运输方式", "shippingFee": "运费", "deliveryFee": "配送费", "shippingNotice": "运输说明", "actualShippingNotice": "实际运费以最终结算为准，仓库重新测量后如有差异会及时通知", "volumeWeightFormula": "体积重量 = 长(cm) × 宽(cm) × 高(cm) ÷ 体积重量", "chargeableWeightFormula": "计费重量 = max (实际重量，体积重量)", "securityCheckNotice": "所有包裹均经过安全检查后发货"}}, "order": {"productInfo": "产品信息", "noProducts": "暂无商品信息", "returnToCart": "请返回购物车重新选择商品", "invalidData": "商品数据异常", "refreshPage": "请刷新页面或重新选择商品", "officialStore": "官方网店", "coupons": {"fetchFail": "获取优惠券列表失败", "list": "优惠券列表", "applySuccess": "优惠券使用成功", "activateFail": "优惠券激活失败！"}, "errors": {"missingInfo": "缺少订单信息", "submitFail": "提交订单失败", "missingAddressAndShipping": "请选择收货地址和运输方式", "missingAddress": "请选择收货地址", "previewSubmitSuccess": "预览包裹提交成功", "previewSubmitFail": "提交预览包裹失败!", "previewSubmitError": "提交预览包裹出错", "fetchServerListFail": "获取附加服务失败"}, "summary": {"title": "订单摘要", "serviceFee": "服务费", "retailPrice": "零售价", "discount": "折扣", "shippingFee": "运费", "coupon": "优惠券", "extraService": "附加服务", "serverfee": "订单服务费", "total": "全部的", "saved": "已节省", "excludeShipping": "不包括国际运费", "returnPolicy": "《退换物流政策》", "terms": "《服务条款》", "privacy": "《隐私条款》", "faq": "《常见问题》", "agreement": "我已阅读并同意条款", "submit": "提交", "internationalFee": "国际运费", "photoCount": "照片数量", "photoUnit": "照片", "remark": "备注", "remarkHint": "请输入备注", "totalFee": "费用合计", "savedAmount": "已节省", "includeShipping": "包含国际运费", "includeService": "包含附加服务", "previewPackageFee": "预演包裹费用", "includePreviewPackageFee": "包含预演包裹费用", "includeAdditionalServiceFee": "包含附加服务费用", "submitPreviewPackage": "提交预演包裹", "tips": {"title": "温馨提示", "content": "提交订单并付款后，请等待卖家发货，到仓后我们再通知包裹。"}}, "addition": {"service": "附加服务", "photoCount": "照片数量", "photo": "照片", "remark": "备注", "remarkPlaceholder": "请输入备注"}, "transport": {"title": "国际运输方式", "fetchError": "获取国际运输方式失败", "loadError": "获取国际运输方式出错", "addressFirst": "请先选择收货地址", "estimate": "预计送达", "restriction": "限制", "feature": "线路特点", "noAvailable": "暂无可用的运输方式", "firstWeight": "首重", "continueWeight": "续重", "weightRange": "重量范围"}}, "sendorder": {"sendorderTitle": "运单摘要", "discount_rate": "折扣率", "saved": "已节省"}}, "home": {"banner": {"title": "让全球用户平价买到中国商品", "subtitle": "一站式跨境购物解决方案"}, "all": "全部", "hotProducts": "为你推荐", "emptyList": "列表为空", "shoppingTime": "购物时报", "services": {"title": "一站式购物服务", "noData": "暂无服务信息", "items": {"easyOrder": {"title": "轻松下单", "description": "无需在主要电商平台上有账号，你能在几秒钟内轻松在我们平台下单，体验中国商品"}, "warehouse": {"title": "采购至中国仓库", "description": "选择你心仪的商品，我们帮你将商品采购至中国的仓库，确保商品安全无虞"}, "inspection": {"title": "验货服务", "description": "一旦您的产品到达 Onebuy 仓库，您可以要求我们的专业服务团队为您的商品拍摄照片，网上显示的和您看到的一致"}, "shipping": {"title": "国际运输服务", "description": "在我们仓库收到您的产品后，我们可以用快速和可靠的物流服务将您的包裹运至全球任何国家"}}}, "footer": {"about": {"title": "Onebuy", "description": "Onebuy 是您值得信赖的全球购物伙伴。我们提供专业的海外代购和转运服务，让您足不出户就能买遍全球。优质服务，品质保证，让跨境购物变得轻松简单。"}, "service": {"title": "客户服务", "items": {"help": "帮助中心", "faq": "常见问题", "contact": "联系我们"}}, "guide": {"title": "代购指南", "items": {"taobao": "淘宝代购", "tutorial": "新手教程", "checklist": "购物清单"}}, "payment": {"title": "支付方式", "methods": ["Visa", "MC", "AMEX", "PP", "银联"]}, "copyright": "版权所有 © 2020-2024 Onebuy.com 保留所有权利"}, "error": {"title": "正在为您优化体验", "description": "检测到需要刷新数据，正在跳转到优化页面...", "autoRedirect": "如果页面没有自动跳转，请点击下方按钮", "goToOptimize": "前往优化页面"}}, "errors": {"fetchHotCategoriesFailed": "获取热销分类失败", "fetchProductsFailed": "获取商品失败", "fetchShoppingTimeFailed": "获取购物时报失败"}, "languageSwitcher": {"selectLanguage": "选择语言", "selectCurrency": "选择货币"}, "about": {"title": "关于 Next.js 国际化", "solution": "Next.js App Router 国际化方案", "features": {"dynamicRoutes": "使用 App Router 的动态路由参数 [lng] 来处理不同语言", "middleware": "利用中间件拦截请求，自动识别用户语言并重定向", "jsonStorage": "通过 JSON 文件存储不同语言的翻译内容", "clientComponent": "使用客户端组件实现语言切换功能", "serverComponent": "通过服务器组件加载对应语言的翻译内容"}}, "help": {"title": "帮助中心", "subtitle": "为您提供全面的购物指南和常见问题解答", "views": "次查看", "search": {"placeholder": "搜索帮助内容...", "button": "搜索", "noResults": "未找到相关内容", "resultsCount": "找到 {count} 条相关内容"}, "categories": {"title": "帮助分类", "viewAll": "查看全部", "articles": "篇文章"}, "articles": {"title": "相关文章", "readMore": "阅读更多", "backToCategories": "返回分类", "lastUpdated": "最后更新", "relatedArticles": "相关文章"}, "loading": "加载中...", "error": "加载失败，请重试", "empty": "暂无内容"}, "search": {"foundProducts": "为您找到{platform}平台{query}相关商品约 {count} 件", "searchResults": "搜索结果", "productSource": "商品来源：", "all": "全部", "taobao": "淘宝", "1688": "1688", "wechat": "微信", "jd": "京东", "micro": "微店", "sortBy": "排序方式：", "default": "默认", "sales": "销量", "price": "价格", "sold": "已售 {count}", "placeholder": "输入商品名称/链接", "searchButton": "搜索", "hotSearch": "热搜:", "hotWords": ["手机", "连衣裙", "运动鞋"], "noResults": {"title": "暂无商品", "description": "没有找到相关商品，请尝试其他关键词或手工填单"}, "timeout": {"title": "加载超时", "description": "网络连接较慢，请稍后重试或尝试手工填单"}, "error": {"title": "加载失败", "description": "获取商品信息时出现问题，请重试或尝试手工填单"}}, "mall": {"title": "商城货架", "subtitle": "精选商品，品质保证", "products": "件商品", "more": "更多", "backToMall": "返回商城", "categories": {"title": "商品分类", "all": "全部分类"}, "sort": {"title": "排序方式", "default": "默认排序", "sales": "销量优先", "credit": "信用优先", "priceAsc": "价格从低到高", "priceDesc": "价格从高到低"}, "product": {"viewDetail": "查看详情", "addToCart": "加入购物车", "price": "价格", "sales": "销量", "credit": "信用"}, "pagination": {"prev": "上一页", "next": "下一页", "total": "共 {total} 件商品"}, "loading": "加载中...", "error": {"loadCategories": "加载分类失败", "loadProducts": "加载商品失败", "loadFloors": "加载楼层失败", "invalidCategory": "无效的分类ID", "retry": "重试"}, "empty": {"title": "暂无商品", "description": "该分类下暂时没有商品", "noProducts": "暂无商品", "floorEmpty": "该楼层暂时没有商品", "noFloors": "暂无楼层数据", "tryLater": "请稍后再试"}}, "login": {"title": "账号登录", "orText": "或", "emailLabel": "邮箱", "emailPlaceholder": "请输入邮箱", "passwordLabel": "密码", "passwordPlaceholder": "请输入密码", "forgotPassword": "忘记密码?", "rememberMe": "记住我", "loginButton": "登录", "noAccount": "还没有账号?", "goRegister": "去注册", "socialLoginText": "使用社交账号登录", "invalidEmail": "请输入有效的邮箱地址", "loginFailed": "登录失败，请检查账号密码", "facebookLogin": "使用Facebook登录", "googleLogin": "使用Google登录", "login": "登录中 ...", "emailRequired": "请输入邮箱", "codePlaceholder": "邮箱验证码", "codeRequired": "请输入邮箱验证码", "sendCode": "发送验证码", "sending": "发送中", "codeSent": "验证码已发送", "sendCodeError": "发送验证码失败，请稍后重试"}, "register": {"title": "注册", "emailRequired": "请输入邮箱", "invalidEmail": "请输入有效的邮箱地址", "sendCodeError": "发送验证码失败，请稍后重试", "passwordRule": "密码长度为6-16位，必须包含大小写字母和数字", "passwordMismatch": "两次输入的密码不一致", "agreeTermsRequired": "请同意用户协议和隐私政策", "registerError": "注册失败，请稍后重试", "backToHome": "返回首页", "emailPlaceholder": "邮箱", "passwordPlaceholder": "密码", "confirmPasswordPlaceholder": "确认密码", "invitationCodePlaceholder": "输入邀请码（可选）", "agreeToTerms": "我已阅读并同意", "userAgreement": "用户注册协议", "declaration": "声明", "registerButton": "注册", "haveAccount": "已有账户？", "loginLink": "登录", "enterVerificationCode": "请输入验证码", "cancel": "取消", "confirm": "确认", "verificationCodeSent": "验证码已发送至您的邮箱", "resend": "重新发送", "agreeToTermsText": "我已阅读并同意", "and": "和", "sendCodeFailed": "发送验证码失败，请稍后重试", "registerFailed": "注册失败，请稍后重试", "mobileRequired": "请输入手机号", "invalidMobile": "请输入有效的手机号", "mobilePlaceholder": "手机号", "mobileCodePlaceholder": "请输入手机验证码", "sendMobileCode": "发送手机验证码", "mobileCodeSent": "手机验证码已发送", "sendMobileCodeFailed": "发送手机验证码失败，请稍后重试", "selectCountryCode": "选择国家/地区", "verificationCodeSentMobile": "验证码已发送至您的手机"}, "forgot": {"title": "忘记密码", "enterEmail": "请输入您的邮箱，我们将发送验证码", "enterCode": "请输入验证码", "enterPassword": "请输入新密码", "emailPlaceholder": "邮箱", "codePlaceholder": "验证码", "passwordPlaceholder": "新密码", "confirmPasswordPlaceholder": "确认新密码", "sendCodeButton": "发送验证码", "verifyButton": "验证", "resetButton": "重置密码", "backToVerify": "返回验证", "backToEmail": "返回修改邮箱", "rememberPassword": "记得密码？", "loginLink": "登录", "emailRequired": "请输入邮箱", "invalidEmail": "请输入有效的邮箱地址", "sendCodeError": "发送验证码失败，请稍后重试", "codeRequired": "请输入验证码", "codeError": "验证码错误，请重新输入", "codeVerifyError": "验证码验证失败，请稍后重试", "passwordRequired": "请输入新密码", "passwordRule": "密码长度为6-16位，必须包含大小写字母和数字", "passwordMismatch": "两次输入的密码不一致", "resetError": "重置密码失败，请稍后重试", "emailSent": "邮件已发送", "emailSendError": "邮件发送失败", "countdownText": "秒后可重新发送", "countdownReturn": "秒后可返回"}, "dashboard": {"home": {"profile": {"title": "个人信息", "description": "这里显示您的个人信息，包括头像、昵称和ID", "signin": "签到", "signinSuccessMsg": "签到成功！", "signinErrorMsg": "签到失败", "loading": "加载中...", "alreadySigned": "今日已签到", "signingIn": "签到中..."}, "anno": {"title": "公告", "empty": "空"}, "menu": {"profile": "会员中心", "cart": "购物车", "favorites": "收藏夹"}, "balance": {"title": "余额", "description": "这里是您的账户余额，您可以充值和提现"}, "vip": {"title": "VIP", "description": "查看最新活动！"}, "history": {"title": "我的历史", "empty": "暂无历史记录", "clearAll": "清空历史", "confirmClear": "确认清空", "confirm": "确认", "cancel": "取消", "clearWarning": "确定要清空所有历史吗？此操作不可恢复"}, "signin": {"title": "签到"}}, "orders": {"status": {"pending": "待付款", "paid": "已付款", "send": "已邮寄", "processing": "处理中", "shipped": "已发货", "completed": "已完成", "cancelled": "已取消"}, "steps": {"order": "下单", "payment": "付款", "inspection": "验货", "shipping": "发货", "receipt": "收货"}, "list": {"searchPlaceholder": "店铺名称 / 订单号 / 商品名称", "startDate": "开始日期", "endDate": "结束日期", "searchButton": "搜索", "orderMsg": "订单留言", "message": "留言", "inputHint": "请输入留言内容", "confirmBtn": "提交", "cancelBtn": "取消", "msgSuccess": "留言成功", "msgFail": "留言失败", "sendMessage": "发送留言", "noMessages": "暂无留言", "photographyRequirements": "精细拍照要求", "status": {"all": "全部", "pendingReview": "未审核", "waitingPayment": "待付款", "paid": "已付款", "shipped": "已邮寄", "delivered": "已收货", "invalid": "无效单"}, "item": {"cancel": "取消", "confirmCancel": "确认取消订单", "confirm": "确认", "cancelPrompt": "您确定要取消这个订单吗？", "cancelSuccess": "取消订单成功", "cancelFail": "取消订单失败", "shop": "店铺", "orderNo": "订单号", "createTime": "创建时间", "payment": "支付", "awaitingSupplement": "待补款", "detail": "订单详情"}, "goodsName": "商品名称", "totalPrice": "商品总价", "quantity": "数量", "operation": "商品操作", "freight": "运费", "logistics": "物流", "unitPrice": "单价", "price": "价格"}, "detail": {"title": "订单信息", "orderNo": "订单编号", "orderType": "订单类型", "selfOrder": "自营订单", "normalOrder": "普通订单", "sellerName": "卖家名称", "expressCompany": "快递公司", "expressNo": "快递单号", "orderStatus": "订单状态", "orderTime": "下单时间", "payMethod": "付款方式", "onlinePay": "在线支付", "goodsList": "商品列表", "goodsImg": "商品图片", "goodsInfo": "商品信息", "price": "单价", "quantity": "数量", "status": "状态", "subtotal": "小计", "remark": "备注", "goodsAmount": "商品金额", "serviceFee": "服务费", "domesticFee": "国内运费", "totalPay": "实付金额", "backToList": "返回订单列表"}}, "account": {"changeMobile": {"title": "账户", "newMobile": "新手机号", "verificationCode": "验证码", "sendCode": "发送验证码", "countdown": "{countdown}秒"}, "changeEmail": {"title": "修改邮箱", "newEmail": "新邮箱", "verificationCode": "验证码", "sendCode": "发送验证码", "countdown": "{countdown}秒"}, "title": "个人资料", "security": "账户安全", "saveSuccess": "保存成功", "saveFail": "保存失败", "formError": "请填写完整", "pwdMismatch": "两次新密码不一致", "updateSuccess": "修改成功", "updateFail": "修改失败", "phoneHint": "请输入新手机号", "codeSent": "验证码已发送", "sendFail": "发送失败", "emailHint": "请输入新邮箱", "avatarSuccess": "头像上传成功", "avatarTypeError": "请选择图片文件(jpg, png等)", "avatarSizeError": "图片大小不能超过5MB", "avatarSaveFail": "头像保存失败", "uploadFail": "上传失败", "avatarUploadFail": "头像上传失败", "uploadTitle": "上传头像", "username": "用户名", "gender": "性别", "male": "男", "female": "女", "email": "邮箱", "modifyEmail": "修改邮箱", "phone": "手机号", "modifyPhone": "修改手机", "birthday": "出生日期", "reset": "重置", "save": "保存", "changePwd": "修改密码", "pwdTips": "更高强度的密码让您的账户更加安全。建议定期更改密码，密码需由数字和字母组成，长度大于6位。", "oldPwd": "旧密码", "newPwd": "新密码", "confirmPwd": "确认新密码", "confirm": "确认", "cancel": "取消", "payPassword": {"title": "支付密码", "tips": "设置支付密码后，在支付时需要验证密码，以增强账户的安全性。", "set": "设置支付密码", "change": "修改支付密码", "notSet": "未设置", "alreadySet": "已设置", "sendCode": "发送邮箱验证码", "createTitle": "设置支付密码", "changeTitle": "修改支付密码", "newPassword": "新支付密码", "confirmPassword": "确认支付密码", "currentPassword": "当前支付密码", "passwordPlaceholder": "请输入6位数字密码", "confirmPlaceholder": "请再次输入密码", "currentPlaceholder": "请输入当前密码", "verifyCodePlaceholder": "请输入验证码", "verifyCodeRequired": "请输入验证码", "noEmail": "请先绑定邮箱", "passwordMismatch": "两次输入的密码不一致", "passwordFormat": "支付密码必须是6位数字", "setSuccess": "支付密码设置成功", "changeSuccess": "支付密码修改成功", "setFail": "支付密码设置失败", "changeFail": "支付密码修改失败", "validateFail": "支付密码验证失败", "required": "请输入支付密码"}}, "sidebar": {"message": "消息", "address": "地址管理", "referral": "推荐官", "question": "咨询"}, "wallet": {"title": "账户余额", "deposit": "充值", "records": "交易记录", "all": "全部", "income": "收入", "expense": "支出", "time": "时间", "category": "类别", "inOut": "收入/支出", "remaining": "余额", "transNo": "交易编号", "remark": "备注", "noRecords": "暂无交易记录", "points": "可用积分", "pointUnit": "积分", "pointExchange": "积分兑换优惠券", "pointLog": "积分记录", "pointType": "积分类别", "shopPoints": "购物积分", "checkinReward": "签到奖励", "pointConvert": "积分兑换", "reviewReward": "评价奖励", "eventReward": "活动奖励", "coupon": "优惠券", "alipayDeposit": "支付宝充值", "withdraw": "提现", "orderRefund": "订单退款", "orderPayment": "订单支付", "depositFailed": "充值失败", "networkError": "网络错误，请稍后重试", "getRecordsFailed": "获取交易记录失败", "getFail": "获取优惠券列表失败", "inputTip": "请输入优惠券码", "activeOk": "优惠券激活成功", "activeFail": "优惠券激活失败", "canUse": "可使用", "used": "已使用", "expired": "已过期", "inputTitle": "输入优惠券码", "activeBtn": "激活优惠券", "loading": "加载中", "noData": "暂无优惠券", "validDate": "有效期至", "noExpiry": "无", "redeemNow": "立即兑换", "pointsNeeded": "所需积分", "redeemTitle": "积分兑换", "redeemSuccess": "兑换成功", "redeemFail": "兑换失败", "noPtsRecords": "暂无积分记录", "transactionRecords": "交易记录", "withdrawalRecords": "提现记录", "remittanceRecords": "汇款记录", "recharge": {"title": "充值", "prompt": "请输入有效的充值金额", "fail": "充值失败", "success": "充值请求已提交，请在支付页面完成支付", "failMsg": "充值失败，请稍后再试", "confirm": "确认充值", "tip": "输入或选择充值金额", "quick": "快速选择"}}, "cart": {"steps": {"select": "选择商品", "confirm": "确认订单", "payProduct": "支付产品费用", "inspect": "检验及仓储", "submit": "提交包裹", "submitPreview": "提交预演包裹", "payShipping": "支付国际运费", "receive": "等待收货"}, "empty": "您的购物车目前是空的", "actions": {"goShopping": "去购物", "deleteSelected": "删除选中商品", "clearCart": "清空购物车", "submit": "提交", "selectAll": "全选"}, "summary": {"title": "订单摘要", "tip": "最终价格将在下一页确认", "retailPrice": "零售价", "total": "预计总额", "excludeShipping": "不包括国际运费"}, "notifications": {"quantityUpdateFail": "更新数量失败，请重试", "remarkCleared": "备注已清空", "remarkUpdated": "备注已更新", "remarkUpdateFail": "更新备注失败，请重试", "itemDeleted": "商品已删除", "deleteFail": "删除商品失败，请重试", "noSelection": "请先选择要删除的商品", "batchDeleted": "选中商品已删除", "fetchFail": "获取购物车数据失败", "batchDeleteFail": "批量删除商品失败，请重试", "alreadyEmpty": "购物车已经是空的", "cleared": "购物车已清空", "clearFail": "清空购物车失败，请重试", "selectItems": "请先选择商品", "1688QuantityNotAllowed": "1688商品不允许修改数量"}, "table": {"productInfo": "商品信息", "unitPrice": "单价", "quantity": "数量", "subtotal": "小计", "action": "操作", "remarks": "备注", "enterRemarks": "请输入备注", "addRemarks": "添加备注"}, "labels": {"1688Product": "1688商品", "1688QuantityTooltip": "1688商品不允许修改数量"}, "hint": {"title": "温馨提示", "content": "此价格不包括国际运费", "viewShipping": "查看运费详情"}, "guessYouLike": "猜你喜欢", "refresh": "换一批", "emptyList": "列表为空", "pagination": {"prev": "上一页", "next": "下一页"}}, "favorites": {"title": "我的收藏", "soldOut": "已售", "emptyList": "暂无收藏商品", "clearAll": "清空收藏", "confirmClear": "确认清空", "confirm": "确认", "cancel": "取消", "clearWarning": "确定要清空所有收藏吗？此操作不可恢复"}, "warehouse": {"title": "我的仓库", "searchPlaceholder": "搜索商品", "searchButton": "搜索", "fetchError": "获取数据失败", "selectPrompt": "请选择要提交的商品", "itemInfo": "物品信息", "itemNumber": "编号", "pieces": "件", "quantityWeight": "数量/重量", "totalPrice": "总价", "location": "仓位", "unassigned": "未分配", "shippingInfo": "物流信息", "noData": "暂无", "noTracking": "暂无单号", "storageTime": "入库时间", "status": "状态", "selectedCount": "已选择", "unit": "项", "emptyData": "暂无仓库数据", "selectAll": "全选当页", "invertSelect": "反选当页", "clearAll": "清空所有", "submitShipping": "提交运单", "total": "总", "items": "条"}, "packages": {"all": "全部", "placeholder": "包裹号", "search": "搜索", "cancelOk": "取消订单成功", "cancelFail": "取消订单失败", "receiveOk": "确认收货成功", "receiveFail": "确认收货失败", "msgOk": "留言成功", "msgFail": "留言失败", "noMessages": "暂无留言", "confirmCancel": "确认取消订单", "confirmReceipt": "确认收货", "confirmReceiptMsg": "您确定要确认收货吗？", "ok": "确认", "cancel": "取消", "cancelAsk": "您确定要取消这个订单吗？", "orderMsg": "包裹留言", "submit": "提交", "msgHint": "请输入留言内容", "packageNo": "包裹号", "createDay": "创建日期", "shipFee": "国际运费", "extraFee": "增值服务费", "additionalFee": "附加服务费", "confirmGet": "确认收货", "leaveMsg": "留言", "cancelOrder": "取消订单", "payment": "支付", "table": {"productInfo": "商品信息", "weightVolume": "重量/体积", "price": "价格", "status": "状态", "actions": "操作", "logistics": "查看物流"}, "viewPhoto": "查看照片", "sendorderPhoto": "包裹图片", "noPhoto": "当前暂无图片", "YQTrackTitle": "包裹物流", "comment": "评论", "commentTitle": "包裹评论", "commentContent": "评论内容", "commentStar": "评分", "commentImage": "图片", "commentSubmit": "提交评论", "commentSuccess": "评论成功", "commentFail": "评论失败", "commentPlaceholder": "请输入评论内容", "commentRequired": "请输入评论内容", "commentStarRequired": "请选择评分"}, "previewPackages": {"title": "预演包裹管理", "searchPlaceholder": "请搜索关键词", "keywordsLabel": "关键词搜索", "search": "搜索", "reset": "重置", "statusFilter": "状态筛选", "allStatus": "全部状态", "noData": "暂无预演包裹数据", "confirmCancel": "确认取消", "confirmCancelContent": "确定要取消预演包裹 {sn} 吗？此操作不可撤销。", "confirm": "确认取消", "cancel": "取消", "cancelSuccess": "预演包裹取消成功", "cancelFailed": "取消预演包裹失败", "paymentSuccess": "运单生成成功，正在跳转到支付页面...", "paymentFailed": "运单生成失败", "refundReasonRequired": "请填写退款理由", "refundSubmitSuccess": "退款申请提交成功", "refundSubmitFailed": "退款申请提交失败", "shippingChangeSuccess": "运费方式更改成功", "shippingChangeFailed": "运费方式更改失败", "selectShippingTemplate": "请选择运费模板", "getWaybillInfoFailed": "获取运单信息失败", "payWaybillFailed": "支付运单失败", "productNumber": "商品编号", "defaultProductInfo": "预演包裹商品信息", "cancelled": "已取消", "previewPackageFee": "预演包裹费用", "valueAddedFee": "增值费", "serviceFee": "服务费", "table": {"productDetails": "商品详情", "weight": "重量", "volume": "体积", "recipient": "收件人", "status": "状态", "totalFee": "总费用", "actions": "操作"}, "packageInfo": "预演包裹信息", "packageNumber": "预演包裹编号", "createTime": "创建时间", "totalCost": "总费用", "previewFee": "预演包裹费用", "valueFee": "增值费", "defaultProduct": "预演包裹商品信息", "waybillPaid": "运单已付款", "actions": {"pay": "付款", "cancel": "取消", "applyRefund": "申请退款", "payShipping": "支付运费", "submitWaybill": "提交运单", "payWaybill": "支付运单"}, "status": {"pendingPayment": "待付款", "paid": "已付款", "packed": "已打包", "waybillUnpaid": "运单未付款", "waybillPaid": "运单已付款", "cancelled": "已取消", "refunding": "退款中", "refunded": "已退款"}, "confirmCancelBtn": "确认取消", "cancelBtn": "取消", "fetchListFailed": "获取预演包裹列表失败", "fetchStatusFailed": "获取状态列表失败", "freightPaymentSuccess": "运费支付提交成功", "freightPaymentFailed": "运费支付提交失败", "waybillGenerateSuccess": "运单生成成功，正在跳转到支付页面...", "waybillGenerateFailed": "运单生成失败", "pagination": {"prev": "上一页", "next": "下一页"}, "detail": {"title": "预演包裹详情", "close": "关闭", "basicInfo": "基本信息", "packageNumber": "包裹编号", "username": "用户名", "status": "状态", "shippingMethod": "运输方式", "previewFee": "预演费用", "totalAmount": "总金额", "actualWeight": "实际重量", "actualVolume": "实际体积", "createTime": "创建时间", "updateTime": "更新时间", "addressInfo": "收货地址信息", "recipient": "收件人", "weight": "重量", "volume": "体积", "phone": "电话", "region": "地区", "postcode": "邮编", "detailAddress": "详细地址", "productInfo": "商品信息", "productCount": "包含商品 ({count}件)", "productNumber": "商品编号", "quantity": "数量", "productPrice": "商品价格", "serviceFee": "服务费", "seller": "卖家", "warehouse": "仓位", "remark": "备注", "spec": "规格"}, "refund": {"title": "申请退款", "cancel": "取消", "submit": "提交申请", "packageInfo": "预演包裹信息", "packageNumber": "预演包裹编号", "previewFee": "预演费用", "productInfo": "商品信息", "reasonLabel": "退款理由", "reasonRequired": "退款理由 *", "reasonPlaceholder": "请详细说明退款理由...", "reasonHint": "请填写退款理由", "submitSuccess": "退款申请提交成功", "submitFailed": "退款申请提交失败"}, "shipping": {"title": "更改运费方式", "cancel": "取消", "confirm": "确认更改", "templateLabel": "选择运费模板", "templateRequired": "选择运费模板 *", "templatePlaceholder": "请选择运费模板", "templateHint": "请选择运费模板", "standardTemplate": "标准运费模板", "fastTemplate": "快速运费模板", "economyTemplate": "经济运费模板", "packageNumber": "预演包裹编号", "currentMethod": "当前运费方式", "changeSuccess": "运费方式更改成功", "changeFailed": "运费方式更改失败"}}, "message": {"all": "全部", "unread": "未读", "read": "已读", "getFail": "获取消息列表失败", "markFail": "标记已读失败", "markBtn": "标记已读"}, "question": {"title": "我的反馈", "submitBtn": "提交反馈", "listFail": "获取反馈列表失败", "submitSuccess": "反馈提交成功", "submitFail": "提交失败", "submitRetry": "提交失败，请重试", "reply": "回复", "cancel": "取消", "submit": "提交", "content": "反馈内容", "contentHint": "请输入反馈内容", "contentPlaceholder": "请描述您的问题或建议..."}, "address": {"title": "收货地址管理", "text": "收货地址", "addNew": "添加新地址", "empty": "暂无收货地址", "noAddressesFound": "未找到地址", "deleteSuccess": "地址删除成功", "deleteFail": "删除地址失败", "default": "默认", "edit": "编辑", "delete": "删除", "addTitle": "添加收货地址", "editTitle": "编辑收货地址", "receiver": "收件人", "receiverHint": "请输入收件人姓名", "phone": "手机号码", "phoneHint": "请输入手机号码", "phoneError": "请输入正确的手机号码", "country": "国家", "countryHint": "请选择国家", "region": "地区", "regionHint": "请选择地区", "detail": "详细地址", "detailHint": "请输入详细地址", "detailMinLength": "请输入不少于5位的详细地址", "postcode": "邮政编码", "postcodeHint": "请输入邮政编码", "postcodeError": "请输入正确邮政编码", "setDefault": "设为默认地址", "cancel": "取消", "confirm": "确认", "countryFail": "获取国家列表失败", "regionFail": "获取地区列表失败", "updateSuccess": "更新地址成功", "addSuccess": "添加地址成功", "addFail": "添加地址失败", "updateFail": "更新地址失败", "confirmDelete": "确认删除", "confirmDeleteContent": "确定要删除这个地址吗？"}, "referral": {"emailError": "请输入有效的邮箱地址", "sendSuccess": "邀请邮件已发送", "sendFail": "发送失败，请稍后重试", "resendSuccess": "邀请邮件已重新发送至", "copySuccess": "推广链接已复制到剪贴板", "copyFail": "复制失败，请手动复制", "recordTitle": "返佣记录", "recordId": "返佣ID", "subordinateUsers": "下级用户", "shippingFee": "国际运费", "commission": "获得佣金", "time": "时间", "noRecords": "暂无返佣记录", "pendingStatus": "待注册", "inviteEmail": "邀请邮箱", "inviteTime": "邀请时间", "action": "操作", "resend": "重新发送", "noPending": "暂无待注册用户", "registeredStatus": "已注册", "userEmail": "用户邮箱", "regTime": "注册时间", "spendAmount": "消费金额", "noRegistered": "暂无已注册用户", "totalCommission": "佣金总额", "copyLink": "复制推广链接", "sendInvite": "发送邀请邮件", "emailHint": "请输入好友邮箱地址", "sendBtn": "发送邀请"}, "cash": {"title": "提现记录", "applyBtn": "申请提现", "time": "申请时间", "amount": "提现金额", "fee": "手续费", "bank": "银行名称", "cardNumber": "卡号", "name": "收款人", "status": "状态", "noRecords": "暂无提现记录", "deleted": "删除作废", "reviewFailed": "审核失败", "pending": "申请中", "approved": "审核通过", "paidSuccess": "付款成功", "paidFailed": "付款失败", "unknown": "未知状态"}, "remittance": {"title": "汇款记录", "startDate": "开始日期", "endDate": "结束日期", "searchBtn": "查询", "remitTime": "汇款时间", "senderName": "汇款人姓名", "paymentMethod": "汇款方式", "bankName": "汇款银行", "amount": "金额", "status": "状态", "processTime": "处理时间", "noRecords": "无记录"}, "addcash": {"feeRate": "提现服务费率", "serviceFee": "提现服务费", "feeRange": "服务费收取范围", "receiverHint": "请输入转账的收款人", "receiverLabel": "收款人", "accountLabel": "收款账号", "accountHint": "请填写转账银行卡号", "accountRequired": "需要填写银行卡号", "bankLabel": "收款机构", "bankHint": "请输入收款银行", "selectBank": "选择银行", "amountLabel": "转账金额", "amountHint": "请输入转账金额", "amountMultiple": "请输入100的倍数金额", "totalAmount": "你需要支付", "receiveAmount": "转入", "emailVerify": "提现邮箱验证", "email": "邮箱", "verificationCode": "验证码", "sendCode": "发送验证码", "confirm": "确认", "cancel": "取消", "networkError": "网络错误，请稍后重试", "submitSuccess": "提现申请已提交", "submitFailed": "提现申请失败"}, "onepayorders": {"title": "一次付款订单", "search": "按订单号搜索...", "searchPlaceholder": "请输入关键词搜索...", "searchButton": "搜索", "all": "全部", "noOrders": "未找到一次付款订单", "noOrdersDescription": "您还没有一次付款订单", "viewDetails": "查看详情", "cancel": "取消", "delete": "删除", "confirmCancel": "确定要取消这个一次付款订单吗？", "confirmDelete": "确定要删除这个一次付款订单吗？此操作无法撤销。", "cancelSuccess": "订单取消成功", "deleteSuccess": "订单删除成功", "fetchError": "获取订单失败", "orderNumber": "订单号", "createTime": "创建时间", "shippingToChina": "到中国仓运费", "orderTotal": "订单总金额", "table": {"productInfo": "商品信息", "unitPrice": "单价", "quantity": "数量", "message": "留言", "logisticsName": "物流名称", "logistics": "物流", "amountServiceFee": "金额/服务费", "status": "状态/操作"}, "product": {"group": "群组", "productAmount": "商品金额", "internationalShipping": "国际运费", "valueAddedFee": "增值费", "serviceFee": "服务费"}, "buttons": {"message": "留言", "dhlLogistics": "DHL物流", "pay": "支付", "cancel": "取消", "generating": "生成中..."}, "orderMessage": "订单留言", "confirmBtn": "确认", "cancelBtn": "取消", "messageRequired": "请输入留言内容", "messageSuccess": "留言发送成功", "messageFailed": "留言发送失败", "inputHint": "请输入您的留言...", "sendMessage": "发送留言", "noMessages": "暂无留言", "admin": "管理员", "statusLabels": {"all": "全部", "pendingPayment": "待付款", "paid": "已付款", "pendingShipment": "待发货", "shipped": "已发货", "received": "已收货", "invalid": "无效单", "refundSuccess": "退款成功"}, "detail": {"title": "一次付款订单详情", "back": "返回", "orderInfo": "订单信息", "orderNumber": "订单号", "status": "状态", "createdTime": "创建时间", "updatedTime": "更新时间", "currency": "货币", "totalAmount": "总金额", "shopTotal": "商品总计", "otherFees": "其他费用", "subOrder": "子订单", "seller": "卖家", "subOrderTotal": "子订单总计", "product": "商品", "price": "价格", "quantity": "数量", "total": "小计", "confirmReceipt": "确认收货", "confirmReceiptMsg": "确定要确认收货吗？", "receiptSuccess": "确认收货成功"}}}, "detail": {"specifications": "商品规格", "size": "尺码", "color": "颜色分类", "productLink": "产品链接", "refresh": "刷新", "outOfStock": "无库存", "shippingFee": "运费", "to": "至", "warehouse": "仓库", "quantity": "数量", "inventory": "库存", "remark": "备注", "remarkHint": "请输入备注信息", "buyNow": "立即购买", "adding": "添加中...", "addToCart": "加入购物车", "favorited": "已收藏", "favorite": "收藏", "details": "图文详情", "wholesalePrice": "批发价", "perPiece": "/件", "addToCartSuccess": "成功加入购物车！", "quantityNotMeet": "数量不满足起批量要求", "authError": "认证状态异常，请刷新页面重试", "emptyUrl": "商品 URL 不能为空", "productNotFound": "未找到商品信息", "image": "图片", "sales ": "销量", "loadFailed": "商品详情加载超时或失败。", "solution1": "您可以", "refreshPage": "刷新页面", "solution2": "重试，或前往", "manualOrder": "手工填单", "emptyProductId": "商品 ID 不能为空", "fetchProductFailed": "获取商品详情失败", "loginRequired": "请登录后重试", "favoriteRemoved": "已取消收藏", "favoriteAdded": "已加入收藏", "operationFailed": "操作失败，请重试", "addToCartFailed": "加入购物车失败，请重试", "buyFailed": "购买失败，请重试", "checkingFavoriteStatus": "检查收藏状态 - 开始", "userNotLoggedIn": "用户未登录，设置收藏状态为0", "addHistoryFailed": "添加浏览记录失败失败", "noStockSku": "没有找到有库存的SKU", "autoSelectedSku": "自动选择的SKU", "favoriteOperationFailed": "收藏操作失败", "originalApiResponse": "原始 API 响应", "tryParseSkuData": "尝试解析 SKU 数据", "buyFailedError": "购买失败", "onePayOrder": "一次付款", "remarkNoMeet": "批发商品起批量为{minQuantity}件，请增加数量", "remarkMeet": "已满足起批量要求（≥{minQuantity}件）", "tieredPrice": "阶梯价格", "unit": "件", "priceComparison": {"title": "商品比价", "refresh": "换一批", "noProducts": "暂无比价商品", "loading": "正在加载比价数据...", "error": "获取比价数据失败", "platforms": {"1688": "阿里巴巴", "taobao": "淘宝", "jd": "京东", "micro": "微店"}}}, "order": {"progress": {"order": "下订单", "check": "仓库质检", "submit": "提交包裹", "receive": "签收快递"}}, "estimate": {"bannerTitle": "全球物流 轻松配送", "bannerDesc": "快速响应、安全可靠的国际物流服务，让您的包裹轻松到达全球各地。精准的运费计算，透明的价格体系，为您提供最优质的配送体验", "title": "运费估算", "destination": "目的地", "selectDestination": "选择目的地", "province": "省份/州", "selectProvince": "选择省份/州", "goodsType": "商品类型", "selectGoodsType": "选择商品类型", "weight": "商品重量", "inputWeight": "输入商品重量", "length": "长", "inputLength": "请输入长度", "width": "宽", "inputWidth": "请输入宽度", "height": "高", "inputHeight": "请输入高度", "calculateBtn": "计算运费", "resultTitle": "运费估算结果", "deliveryTime": "预计送达", "baseWeight": "首重", "additionalWeight": "续重", "feeDetails": "费用明细", "shippingFee": "运费", "tax": "关税", "serviceFee": "服务费", "fuelFee": "燃油费", "restrictions": "限制", "disclaimer": "*运费估算仅供参考，实际费用可能因货物特性、季节性变化、燃油附加费等因素而有所不同。", "noShippingMethods": "暂无可用运输方式", "noShippingTips": "根据您提供的信息，暂时没有找到合适的运输方式。请调整商品信息或联系客服获取帮助。", "feature": "线路特点", "ban": "限制"}, "sharepromotion": {"mainTitle": "推荐赚取佣金", "subTitle": "轻松开启您的赚钱之旅，成为我们的推广合伙人", "ruleTitle": "推广规则", "joinNow": "立即加入", "stepsTitle": "推广步骤", "step1": "注册成为推广员", "step2": "分享商品链接", "step3": "获得佣金", "register": "注册", "share": "分享", "qualificationTitle": "推广资格", "qualification1": "1. 年满18周岁，拥有独立承担民事责任的能力，遵守平台规则和相关法律法规。", "commissionTitle": "佣金规则", "commission1": "1. 根据推广等级获得不同比例的佣金，最高可享受商品售价15%的佣金比例。", "commission2": "2. 佣金每月结算一次，满100元可提现。", "methodTitle": "推广方式", "method1": "1. 可通过社交媒体、个人网站、博客等渠道进行推广，禁止虚假宣传和恶意营销", "upgradeTitle": "升级机制", "upgrade1": "1.推广业绩达标可升级至更高等级，享受更高佣金比例和专属活动资源。", "penaltyTitle": "违规处理", "penalty1": "1. 违反平台规则将被降级或取消推广资格，情节严重者将被永久封禁账号。"}, "promotion": {"invitedUsers": "受邀请用户", "activatedUsers": "已激活用户", "activeUsers": "活跃用户", "commissionBalance": "佣金余额", "frozenAmount": "冻结金额", "estimatedCommission": "预计佣金", "myLevel": "我的等级", "dreamPromoter": "梦想推广者", "commissionRate": "佣金比例", "experience": "经验值", "upgradeCondition": "升级条件", "lv1Title": "LV1 梦想推广者", "lv2Title": "LV2 初级推广者", "lv3Title": "LV3 中级推广者", "lv4Title": "LV4 超级推广者", "lv1Rate": "2%", "lv2Rate": "3.5%", "lv3Rate": "5%", "lv4Rate": "6.5%", "lv1Condition": "0起步", "lv2Condition": "1000起步", "lv3Condition": "5000起步", "lv4Condition": "10000起步", "promotionMethod": "推广方式", "promotionMethodDesc": "通过扫描二维码或点击推广链接，或在注册时填写您的推广码，将自动成为您的推广用户", "downloadQR": "下载推广二维码", "copyLink": "复制推广链接", "promoteProducts": "推广商品", "withdraw": "提现", "balanceTab": "佣金余额", "frozenTab": "冻结金额", "recordsTab": "结算记录", "accumulatedTab": "积分列表", "experienceTab": "经验明细", "inviteTab": "邀请列表", "helpTab": "去咨询"}, "referralpro": {"title": "推广协议与规则", "totalInviteUsers": "总邀请用户", "activatedUsers": "已激活用户", "activeUsers": "活跃用户", "activeUsersTooltip": "统计当前自然月内，在平台完成下单并付款，且数量不少于10单的用户", "commissionBalance": "佣金余额", "frozenBalance": "冻结余额", "frozenBalanceTooltip": "您邀请的用户完成运单收货后，对应的佣金将先进入冻结金额等待结算，结算后自动转入佣金余额", "settlementDate": "系统每月结算日为 X 日", "estimatedCommission": "预计佣金", "estimatedCommissionTooltip": "统计您所邀请用户已付款但尚未收货的运单对应的佣金金额", "totalWithdrawal": "累计提现", "myLevel": "我的等级", "bonusRatio": "奖金比例", "experienceValue": "经验值", "startingFrom": "起步", "noLevelInfo": "暂无等级信息", "levelDataLoading": "等级数据加载中，请稍后刷新页面", "promotionMethod": "推广方式", "promotionMethodDesc": "通过扫描二维码或点击推广链接，或在注册时填写您的推广码，将自动成为您的推广用户", "downloadInviteQR": "下载邀请二维码", "copyInviteLink": "复制邀请链接", "copyInviteCode": "复制邀请码", "commissionAmountTab": "佣金金额", "frozenAmountTab": "冻结金额", "settlementRecordsTab": "结算记录", "pointsListTab": "积分列表", "experienceDetailsTab": "经验明细", "activeUsersTab": "活跃用户", "inviteListTab": "邀请列表", "goInviteTab": "去邀请", "typeFilter": "类型", "allTypes": "全部", "timeFilter": "时间", "settlementTimeFilter": "结算时间", "startDate": "开始日期", "endDate": "结束日期", "startTime": "开始时间", "endTime": "结束时间", "search": "搜索", "reset": "重置", "loading": "加载中...", "id": "ID", "type": "类型", "beforeAmount": "变动前金额", "changeAmount": "变动金额", "afterAmount": "变动后金额", "time": "时间", "remark": "备注", "noCommissionRecords": "暂无佣金记录", "commissionRecordsDesc": "推广成功后将显示佣金变动记录", "noFrozenRecords": "暂无冻结金额记录", "frozenRecordsDesc": "冻结金额变动记录将显示在此处", "settlementAmount": "结算金额", "settlementPeriod": "结算时间段", "createTime": "创建时间", "noSettlementRecords": "暂无结算记录", "settlementRecordsDesc": "佣金结算记录将显示在此处", "source": "来源", "noExperienceRecords": "暂无经验记录", "experienceRecordsDesc": "完成推广任务可获得经验值", "inviteUsername": "邀请用户名", "email": "邮箱", "status": "状态", "inviteTime": "邀请时间", "activated": "已激活", "registered": "已注册", "unregistered": "未注册", "noInviteRecords": "暂无邀请记录", "productAmount": "商品金额", "orderCount": "订单数量", "shippingAmount": "运费金额", "shippingCount": "运单数量", "registerTime": "注册时间", "activeMonth": "活跃月份", "noActiveUsers": "暂无活跃用户", "activeUsersDesc": "当月完成10单以上的用户将显示在此处", "sendInviteEmail": "发送邀请邮件", "enterFriendEmail": "输入好友邮箱", "sendInvite": "发送邀请邮件", "inviteEmailDesc": "通过邮件邀请好友注册，好友成功注册后您将获得推广奖励。", "inviteMethodsDesc": "您也可以通过以下方式邀请好友：", "sharePromotionLink": "分享推广链接", "sharePromotionQR": "分享推广二维码", "shareInviteCode": "分享邀请码", "beforePoints": "变动前积分", "changePoints": "变动积分", "afterPoints": "变动后积分", "noPointsRecords": "暂无积分记录", "pointsRecordsDesc": "积分变动记录将显示在此处", "pointsTypes": {"registration": "注册赠送", "inviterReward": "邀请者奖励", "inviteeGift": "被邀请者赠送", "delivery": "收货", "sharing": "分享", "signin": "签到", "adminOperation": "管理员操作", "promotionPoints": "推广积分", "upgrade": "升级", "couponExchange": "兑换优惠券"}, "pagination": {"showing": "显示第", "to": "到", "of": "条，共", "records": "条记录", "page": "第", "totalPages": "页，共", "pages": "页", "previous": "上一页", "next": "下一页"}, "withdrawModal": {"title": "提现", "description": "佣金余额将提现至您的账户", "withdrawAmount": "提现金额", "availableBalance": "可提现余额", "enterAmount": "请输入提现金额", "verificationMoney": "不能大于可提现余额！", "verificationEmail": "验证邮箱", "enterEmail": "请输入验证邮箱", "verificationCode": "验证码", "enterCode": "请输入验证码", "sendCode": "发送验证码", "cancel": "取消", "confirm": "确认"}, "messages": {"copyLinkSuccess": "复制成功", "copyLinkFail": "复制失败，请手动复制", "qrDownloadSuccess": "二维码下载成功", "qrNotAvailable": "二维码不可用", "inviteCodeCopySuccess": "邀请码 {code} 复制成功", "invalidEmail": "请输入有效的邮箱地址", "inviteEmailSent": "邀请邮件发送成功", "sendFailed": "发送失败", "sendFailedRetry": "发送失败，请稍后重试", "noWithdrawBalance": "当前佣金余额为0，无法提现", "verificationCodeSent": "验证码已发送到您的邮箱", "sendCodeFailed": "发送验证码失败，请稍后重试", "enterValidEmail": "请输入有效的邮箱地址", "enterVerificationCode": "请输入邮箱验证码", "enterValidAmount": "请输入有效的提现金额", "withdrawSubmitted": "提现申请已提交，请等待审核", "withdrawFailed": "提现申请失败，请稍后重试", "dataLoadFailed": "获取数据失败，请稍后重试", "tabDataLoadFailed": "获取标签页数据失败"}}, "transport": {"steps": {"submit": "提交包裹", "description": "收到包裹后，您可以在'我的仓库'中选择物品进行打包和运输。", "action": "提交包裹 >", "payment": "支付运费", "paymentDescription": "若包裹重量超出限制，您的国际包裹将自动取消。请及时完成支付。", "paymentAction": "支付国际运费 >"}, "guideTitle": "转运服务教程", "guideDesc": "快速了解转运流程，轻松完成海外购物。我们提供专业的转运服务，帮助您解决海外物流难题。", "copyBtn": "复制 Onebuy 收货地址", "addressTitle": "收货地址", "receiver": "收件人：某某-3-<PERSON><PERSON><PERSON><PERSON>", "phone": "联系电话：4008208820", "address": "地址：江西省新余市城南街道人民路xxxxx号", "packageTitle": "填写包裹信息", "packageNameHint": "请输入包裹名称", "trackingNumberHint": "请输入快递单号", "deletePackage": "删除包裹信息", "addMore": "继续添加", "agreement": "《转运服务协议》", "submitBtn": "提交", "agreeAndAccept": "同意并接受", "error": "提交失败", "authError": "请先登录", "success": "提交成功", "copySuccess": "复制成功", "copyFailed": "复制失败", "fillPackageInfo": "请填写包裹信息", "fillComplete": "请填写完整", "agreeTerms": "请同意转运服务协议", "receiverLabel": "收件人：", "phoneLabel": "联系电话：", "addressLabel": "地址："}, "pay": {"online": "在线支付", "offline": "线下支付", "title": "订单支付", "titleCZ": "充值支付", "payPreviewPackage": "支付预演包裹", "orderNo": "订单编号", "amount": "应付金额", "actually": "实付金额", "currencyFee": "支付手续费", "selectMethod": "选择支付方式", "payNow": "立即支付", "fail": "支付失败", "getLinkFail": "获取支付链接失败", "success": "支付成功", "selectMethodHint": "请选择支付方式", "goUse": "去使用", "orderNotExist": "订单号不存在", "getOrderFail": "获取订单信息失败", "validity": "有效期", "coupon": "优惠券", "useNow": "立即使用", "discount": "折", "cancel": "取消", "invalidAmount": "支付金额不能小于0", "couponFullDiscount": "优惠券已完全抵扣订单金额", "availableCoupons": "可用优惠券", "unavailableCoupons": "不可用优惠券", "used": "已使用", "expired": "已过期", "noAvailableCoupons": "暂无可用优惠券", "noAvailableCouponsDesc": "您当前没有可用的优惠券", "noUnavailableCoupons": "暂无不可用优惠券", "noUnavailableCouponsDesc": "您没有已使用或过期的优惠券", "viewAll": "查看全部", "collapse": "收起", "noExpiry": "无", "payPasswordVerify": {"title": "支付密码验证", "placeholder": "请输入支付密码", "confirm": "确认支付", "cancel": "取消", "required": "请输入支付密码", "verifyFail": "支付密码验证失败", "tips": "为了您的账户安全，请输入支付密码"}, "setPasswordReminder": {"title": "设置支付密码", "tips": "为了您的账户安全，请先设置支付密码后再进行支付。", "goToSet": "去设置", "cancel": "取消"}, "offlinePay": {"title": "线下支付", "rechargeNotice": "线下充值使用须知", "verifyInfo": "请仔细核汇款信息以及上传汇款信息", "notTransferred1": "若您还未汇款", "notTransferred2": "如果您已经完成该步骤，请跳过至第二步", "notTransferred3": "获取收款人信息，去对应网点填写汇款信息时", "notTransferred4": "请仔细检查拼写", "region": "地区", "paymentMethod": "充值方式", "payeeInfo": "收款人信息", "payeeName": "收款人账户名", "payeeAccount": "收款人账号", "description": "说明", "transferred1": "若您已经汇款", "remitterName": "汇款人姓名", "remitterNameHint": "请输入汇款人姓名", "remitterBank": "汇款银行", "remitterBankHint": "请输入汇款银行", "voucherNo": "汇款凭证号", "voucherNoHint": "如银行提供汇款凭证号，请填写", "voucherImage": "凭证图片", "voucherImageHint": "请上传汇款凭证图片", "voucherPreview": "凭证图片预览", "currency": "汇款币种", "amount": "汇款金额", "submitBtn": "我已经汇款，提交信息", "fetchError": "获取充值方式列表失败", "fetchAreaError": "获取地区列表失败", "submitSuccess": "提交成功！", "submitFailed": "提交失败!", "imageTypeError": "请选择图片文件(jpg, png等)", "imageSizeError": "图片大小不能超过5MB", "uploadError": "上传失败，请重试！", "uploadSuccess": "上传成功！", "uploadImage": "上传图片"}}, "sizecomparison": {"size": "尺码", "womensShirtChart": "女装尺码对照表", "womensShirtTitle": "女士衬衫尺码对照表", "international": "国际", "bust": "胸围", "waistline": "腰围", "Shirt": "衬衫", "shoulder": "肩宽", "height": "身高", "womensShirtTip1": "号型定义：“号”指人体的身高，以cm为单位，是设计和选购服装长短的依据；", "womensShirtTip2": "“型”指人体的胸围和腰围，以cm为单位，是设计和选购服装肥瘦的依据。", "womensShirtTip3": "体型分类：以人体的胸围与腰围的差数为依据来划分体型，并将体型分为四类，体型分类代号分别为Y(偏瘦)、A（正常）、B（偏胖）、C（肥胖）", "dressChart": "连衣裙尺码对照表", "dress": "连衣裙", "dressTip1": "亚洲码和欧码：各地人体结构在形体存在某些相似和尺寸上又存在明显不同。亚码，欧码是指亚、欧两地域不同的码系，而前者和后者又可继续细分，为了方便，各国都建立自己的原型并规纳统计各尺码各部位的具体尺寸形成各国自己统一的号型规格。我国当然也有自己统一的号型规格，但南方与北方在运用对照的时候，要根据具体款型风格结合本地人体的结构尺寸特征作出应有的松量及尺寸加放修改才会合身。", "womensPantsChart": "女裤尺码对照表", "pants": "裤子", "feet": "英尺", "foot": "脚长", "model": "型号", "hip": "臀围", "womensPantsTip1": "如何测量裤侧长：从腰部开始测量一直到脚裸的长度就是裤侧长。", "womensPantsTip2": "如何测量腰围：经脐点(om)的腰部水平围长。", "womensPantsTip3": "标准腰围计算方法：腰围=身高的1/2减19厘米(如:身高160cm的标准腰围=160cm/2-19=61cm)", "mensChart": "男装尺码对照表", "mensShirtChart": "男士衬衫尺码对照表", "length": "衣长", "clothing": "服装", "mensShirtTip1": "这只是标准尺码，实际生活中因个人体型差异较大，本尺码仅作参考之用。请您在试穿之后量身选择适合自己的尺寸", "mensSuitChart": "男士西装尺码对照表", "spec": "规格", "version": "版型", "sleeve": "袖长", "overweight": "偏胖", "standard": "标准", "mensSuitTip1": "男式西服按照男士体型可分为偏瘦型、标准型、偏胖型。", "mensPantsChart": "男士裤子尺码对照表", "mensPants": "男士裤子", "inch": "英寸", "clothRuler": "布尺", "clothingSize": "服装尺码", "kidsClothingChart": "儿童服装尺码对照表", "babyClothingChart": "婴儿服装尺码对照表", "babyClothingTip1": "宝贝幼嫩肌肤，需要用心呵护。童装与成人服饰最大的区别在于用料和印染技术，购买时注意布料成分，保证毎一件衣服都对宝贝的皮肤无任何刺激。", "chineseKidsChart": "中国儿童服装尺码对照表", "yearOld": "岁", "months": "月", "age": "年龄", "chineseKidsTip1": "吊牌上的执行标准的含义：A类，B类，C类的含义：这是国标中对甲醛含量的规定，A类是婴糼儿用品，B类是直接接触皮肤，C类是非直接接触皮肤。", "womensLingerieChart": "女士内衣尺码对照表", "braChart": "胸罩尺寸对照表", "china": "中国", "europe": "欧洲", "cup": "罩杯", "bustMeasureTip1": "胸围测量方法：穿着无衬垫文胸，测量胸部最丰满处的围度", "bustMeasureTip2": "罩杯尺寸由胸围和下胸围的差值决定", "underwearChart": "内裤尺码对照表", "waistMeasureTip1": "腰围测量方法：测量自然腰线处的围度，保持卷尺松紧适度", "waistMeasureTip2": "臀围测量方法：测量臀部最丰满处的围度", "shoeChart": "鞋码对照表", "womensShoeChart": "女士鞋码对照表", "usa": "美国", "uk": "英国", "japan": "日本", "shoeTip1": "女士鞋码可能因品牌而异，本表仅供参考", "mensShoeChart": "男士鞋码对照表", "shoeTip2": "男士鞋码可能因品牌而异，本表仅供参考", "footMeasureTip": "测量脚长方法：站在纸上描出脚型，测量从脚跟到脚尖的长度（厘米）", "measureGuide": "身体尺寸测量指南", "guide1": "1、胸围测量方法", "guideDesc1": "直立，两臂自然下垂。皮尺前面放在乳头上缘(女子放在乳房上)，后面置于肩胛骨下角处，测量安静时、吸气和呼气时的胸围。注意不要耸肩或弯腰。", "guide2": "2、腰围测量方法", "guideDesc2": "裤子腰围：测量两边接缝处一周；净腰围：在腰间最细处围量一周", "guide3": "3、臀围测量方法", "guideDesc3": "裤子臀围：从腰口往下在最宽处测量一周；净臀围：沿臀部最丰满处测量一周", "guide4": "4、体重测量", "guideDesc4": "穿着背心和短裤，平稳站立在体重计上测量", "guide5": "5、小腿围测量(左右腿)", "guideDesc5": "直立，体重均匀分布在两腿上，测量小腿最粗处的围度", "guide6": "6、裤长测量", "guideDesc6": "从腰口到裤脚底边的距离；休闲裤、牛仔裤不含脚口贴边", "guide7": "7、净裤长测量", "guideDesc7": "从腰口到实际裤脚处的距离；男士标准长度到皮鞋鞋帮和鞋底交接处", "guide8": "8、大腿围测量(左右腿)", "guideDesc8": "两脚分开自然站立，间距约15厘米，测量大腿最粗部位的围度"}, "selfservice": {"manualOrder": "手工填单服务", "shippingFee": "卖家到Onebuy仓库运费", "serviceDesc": "我们提供专业的海外代购和转运服务，覆盖中国各大电商平台。从商品购买、清关、仓储到配送，一站式解决您的跨境购物需求。优质服务，品质保证，让跨境购物变得轻松简单。", "productLink": "商品链接", "productLinkHint": "请输入商品链接", "linkRule": "商品链接必须以 https:// 开头", "productName": "商品名称", "productNameHint": "请输入商品名称", "specs": "规格信息", "specsHint": "请输入规格信息", "specsDetail": "请输入规格信息（如颜色、尺寸等）", "productImage": "商品图片", "uploadHint": "请上传商品图片", "uploading": "上传中...", "uploadTip": "点击或拖拽图片上传", "imageRule": "支持 jpg、png、webp 格式，单张不超过 5M", "remark": "商品备注", "remarkHint": "请输入商品描述", "price": "商品价格", "priceHint": "请输入商品价格", "quantity": "数量", "quantityHint": "请输入数量", "inputShipping": "请输入运费", "addProduct": "添加商品", "addedProduct": "已添加商品", "delete": "删除", "noImage": "暂无图片", "linkCol": "链接", "priceCol": "价格", "shippingCol": "运费", "subtotal": "总价", "total": "总计", "acceptTerms": "我已阅读并接受", "serviceAgreement": "《代购服务协议》", "clearConfirm": "确认清空", "clearMessage": "确定要清空所有已添加的商品吗？", "confirm": "确认", "cancel": "取消", "cleared": "已清空所有商品", "clearList": "清空列表", "submitCart": "提交购物车", "discllaimer": "免责声明", "disclaimerDetail": "Onebuy上展示的所有可代购的商品均是从第三方代购平台检索的商品，并非Onebuy直接销售的商品。因此，如上述产品及及收价问题，所有责任均由第三方平台相应卖家承担，Onebuy不承担任何相关、附带或连带责任。", "feeDescription": "费用说明", "feeNote": "商品单价为原始价格，不含税费和运费", "warehouseFee": "卖家发货至Onebuy仓库运费", "priceAlert": "商品价格如有变动，我们会与您联系", "imageFormat": "只支持 JPG/PNG/WEBP 格式的图片", "sizeLimit": "图片大小不能超过5MB", "uploadFailed": "图片上传失败", "fillRequired": "请填写必填项", "addSuccess": "商品已添加到列表", "agreeFirst": "请阅读并同意底部协议", "minProduct": "请至少添加一个商品", "cartSuccess": "商品已成功添加到购物车", "cartFailed": "添加到购物车失败", "submitRetry": "提交失败，请重试", "step1Title": "提交商品信息", "step1Desc": "填写商品详情", "step2Title": "支付商品及运费", "step2Desc": "确认支付信息", "step3Title": "等待卖家发货", "step3Desc": "商品处理中"}, "error": {"systemError": "系统遇到了一些问题", "dontWorry": "别担心，我们正在为您准备解决方案...", "autoRedirect": "页面将在 3 秒后自动跳转到修复页面", "goToFix": "立即前往修复页面", "retryPage": "重试当前页面", "whatIsThis": "这是什么问题？", "reasons": ["浏览器缓存数据冲突", "登录状态不同步", "需要刷新页面数据"]}, "cache": {"optimizing": "正在清理缓存...", "steps": {"clearing": {"title": "正在清理缓存", "description": "正在清理浏览器缓存数据，请稍候..."}, "refreshing": {"title": "正在刷新数据", "description": "正在重新加载最新数据，马上就好..."}, "complete": {"title": "优化完成", "description": "缓存清理完成，即将跳转到目标页面"}}}, "console": {"parseError": "解析siteData失败", "apiRequest": "发起API请求", "urlParseError": "解析URL出错", "uploadFailed": "上传失败", "fileProcessError": "处理文件时出错", "copyFailed": "复制失败", "extractFromCnProps": "从 cn_props_list 中提取属性", "configInitComplete": "配置初始化完成，准备显示页面", "configInitFailed": "配置初始化失败"}, "previewPackage": {"title": "预演包裹", "description": "预演包裹功能说明", "shippingInfo": {"previewOnly": "仅供预览，无需选择运输方式"}}, "productDetail": {"size": "尺码", "colorCategory": "颜色分类"}}