import React from 'react';

interface AddressCardProps {
  recipient: string;
  phone: string;
  addressDetail: string;
  isDefaultAddress: boolean;
  onEdit: () => void;
  onDelete: () => void;
  dict: any
}

export default function AddressCard({ recipient, phone, addressDetail, isDefaultAddress, onEdit, onDelete, dict }: AddressCardProps) {
  return (
    <div className="p-4 border rounded-lg relative">
      {/* 默认地址标签 - 左下方 */}
      {isDefaultAddress && (
        <div className="absolute bottom-3 left-3 bg-[#FF6000] text-white px-2 py-0.5 text-xs rounded-full">
           {dict?.dashboard?.address?.default}
        </div>
      )}
      <div className="flex flex-col gap-2 pb-8">
        <div className="flex items-center gap-3">
          <span className="font-medium">{recipient}</span>
          <span className="text-gray-500">{phone}</span>
        </div>
        <div className="text-gray-600 text-sm">
          {addressDetail}
        </div>
      </div>
      <button
        onClick={onEdit}
        className="absolute bottom-3 right-3 text-[#FF6000] hover:bg-[#FF6000] hover:text-white px-3 py-1 rounded-full text-xs border border-[#FF6000]"
      >
       {dict?.dashboard?.address?.edit}
      </button>
      <button
        onClick={onDelete}
        className="absolute bottom-3 right-18 text-[#FF6000] hover:bg-[#FF6000] hover:text-white px-3 py-1 rounded-full text-xs border border-[#FF6000]"
      >
        {dict?.dashboard?.address?.delete}
      </button>
    </div>
  );
} 