import React, { useEffect, useState } from 'react';
import { List, Typography, Button, message } from 'antd';
import dayjs from 'dayjs';
import { Api } from '@/request/api';

const { Text } = Typography;

interface MessageItem {
  id: number;
  title: string;
  content: string;
  createtime: number;
  status?: number; // 已读状态，undefined 或 null 为未读
}

interface MessageListProps {
  type: 'all' | 'unread' | 'read';
}

export default function MessageList({ type, dict }: MessageListProps & {dict:any}) {
  const [messages, setMessages] = useState<MessageItem[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchMessages = async () => {
    try {
      setLoading(true);
      const response = await Api.getMessageList({
        size: 20,
        is_read: type === 'all' ? undefined : type === 'read' ? 1 : 0
      });
      if (response.success) {
        setMessages(response.data.data);
      }
    } catch (error) {
      message.error(dict?.dashboard?.message?.getFail);
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAsRead = async (e: React.MouseEvent, id: number) => {
    e.stopPropagation();
    try {
      const response = await Api.markMessageRead({ message_id: id });
      if (response.success) {
        fetchMessages(); // 重新获取列表
      }
    } catch (error) {
     console.error(dict?.dashboard?.message?.markFail);
    }
  };

  useEffect(() => {
    fetchMessages();
  }, [type]);

  return (
    <List
      className="message-list"
      loading={loading}
      dataSource={messages}
      renderItem={(item) => (
        <List.Item key={item.id} className="message-item">
          <div className="flex w-full cursor-pointer hover:bg-gray-50 p-4">
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <Text strong>{item.title}</Text>
                {!item.status && (
                  <span className="bg-red-500 w-2 h-2 rounded-full" />
                )}
              </div>
              <Text className="text-gray-600 block my-2">
                <div dangerouslySetInnerHTML={{ __html: item.content }} />
              </Text>
              <Text type="secondary" className="text-sm">
                {item.createtime}
              </Text>
            </div>
            {!item.status && (
              <div className="flex items-center ml-4">
                <Button 
                  type="link" 
                  onClick={(e) => handleMarkAsRead(e, item.id)}
                  className="text-blue-500 hover:text-blue-600"
                >
                 { dict?.dashboard?.message?.markBtn}
                </Button>
              </div>
            )}
          </div>
        </List.Item>
      )}
    />
  );
} 