'use client'

import React, { useState } from 'react'
import PhotoViewer from './PhotoViewer'
import Button from '@/components/Button'

// 演示用的照片数据
const demoPhotos = [
    {
        id: 1,
        image: 'https://via.placeholder.com/800x600/FF6B6B/FFFFFF?text=Photo+1'
    },
    {
        id: 2,
        image: 'https://via.placeholder.com/800x600/4ECDC4/FFFFFF?text=Photo+2'
    },
    {
        id: 3,
        image: 'https://via.placeholder.com/800x600/45B7D1/FFFFFF?text=Photo+3'
    },
    {
        id: 4,
        image: 'https://via.placeholder.com/800x600/96CEB4/FFFFFF?text=Photo+4'
    }
]

export default function PhotoViewerDemo() {
    const [isViewerOpen, setIsViewerOpen] = useState(false)
    const [selectedIndex, setSelectedIndex] = useState(0)

    const handlePhotoClick = (index: number) => {
        setSelectedIndex(index)
        setIsViewerOpen(true)
    }

    return (
        <div className="p-6">
            <h2 className="text-2xl font-bold mb-6">运单照片查看功能演示</h2>
            
            <div className="mb-6">
                <p className="text-gray-600 mb-4">
                    点击下方任意照片可以查看大图，支持以下功能：
                </p>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                    <li>点击照片查看大图</li>
                    <li>左右箭头按钮切换照片</li>
                    <li>键盘左右箭头键切换照片</li>
                    <li>ESC键关闭查看器</li>
                    <li>底部缩略图快速跳转</li>
                </ul>
            </div>

            {/* 照片网格 */}
            <div className="grid grid-cols-2 gap-4 mb-6 max-w-md">
                {demoPhotos.map((photo, index) => (
                    <img
                        key={photo.id}
                        src={photo.image}
                        alt={`演示照片 ${index + 1}`}
                        className="w-full h-36 object-contain rounded-lg cursor-pointer hover:opacity-80 transition-opacity border border-gray-200"
                        onClick={() => handlePhotoClick(index)}
                    />
                ))}
            </div>

            <Button onClick={() => setIsViewerOpen(true)}>
                打开照片查看器
            </Button>

            {/* 照片查看器 */}
            <PhotoViewer
                photos={demoPhotos}
                isOpen={isViewerOpen}
                onClose={() => setIsViewerOpen(false)}
                initialIndex={selectedIndex}
            />
        </div>
    )
}
