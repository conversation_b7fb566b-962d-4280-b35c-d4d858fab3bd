'use client'

import React, { useState, useEffect } from 'react'
import { SearchOutlined } from '@ant-design/icons'
import { Form } from 'antd'
import { Api } from '@/request/api'
import { formatCurrency, formatUSDPrice } from '@/utils/currency'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { getDictionary } from '@/dictionaries'
import Button from '@/components/Button'
import Input from '@/components/Input'
import Loading from '@/components/Loading'
import EmptyState from '@/components/EmptyState'
import AntdConfigProvider from '@/components/AntdConfigProvider'
import Tabs from '@/components/Tabs'
import Message from '@/components/CustomMessage'
import ModalComponent from '@/components/Modal'
import Toast from '@/components/Toast'

interface OnePayOrderGoods {
  id: number;
  goodsname: string;
  goodsimg: string;
  goodsprice: string;
  goodsnum: number;
  skuname: string;
  goodsurl: string;
  goodsseller: string;
  sellerurl: string;
  serverfee: number;
  serverdiscount: number;
  sendprice: string;
  totalmoney: string;
  status_text: string;
  status_id: number;
  expressno?: string; // 物流单号
  expressname?: string; // 物流公司名称
  server?: any; // 商品附加服务
}

interface OnePayOrderShop {
  id: number;
  ordersn: string;
  goodsseller: string;
  sellerurl: string;
  status_text: string;
  status_id: number;
  totalmoney: number;
  totalserverfee: number;
  totalgoodsmoney: number;
  goods: OnePayOrderGoods[];
}

interface OnePayOrderPackage {
  id: number;
  freight: string;
  serverfee: string;
  serverdiscount: number;
  freightdiscount: number;
  countweight: number;
  countvolume: number;
}

interface OnePayOrder {
  id: number;
  orderssn: string;
  user_id: number;
  isonepay: number;
  status_id: number;
  status_text: string;
  currencycode: string;
  createtime: string;
  updatetime: string;
  totalmoney: number;
  shop_totalmoney: number;
  otherfee: number;
  decode_currencyvalue: any;
  shop_list: OnePayOrderShop[];
  package: OnePayOrderPackage;
  order_serve?: any; // 商品附加服务
  sendorder_server?: any; // 包裹附加服务
}

interface OnePayOrderListParams {
  status_id?: number;
  keyword?: string;
}

export default function OnePayOrdersPage() {
  const [orders, setOrders] = useState<OnePayOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [dict, setDict] = useState<any>(null);
  const [statusList, setStatusList] = useState<Array<{ key: string; label: string }>>([]);
  const [activeTab, setActiveTab] = useState<string>('-1');
  const [form] = Form.useForm();
  const { lng } = useParams();
  const router = useRouter();

  // 留言功能状态
  const [isMessageModalOpen, setIsMessageModalOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [currentOrderGoodsId, setCurrentOrderGoodsId] = useState<number | null>(null);
  const [messageLoading, setMessageLoading] = useState(false);
  const [messageList, setMessageList] = useState([]);

  // 取消订单确认模态框状态
  const [cancelModalOpen, setCancelModalOpen] = useState(false);
  const [orderToCancel, setOrderToCancel] = useState<OnePayOrder | null>(null);

  // 支付加载状态
  const [paymentLoading, setPaymentLoading] = useState<number | null>(null);

  // Load dictionary
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };
    fetchDictionary();
  }, [lng]);

  // Fetch one-time payment orders
  const fetchOrders = async (params: OnePayOrderListParams = {}) => {
    try {
      setLoading(true);
      const apiParams: any = {
        keyword: params.keyword || ''
      };

      // 只有当不是"全部"tab时才传递状态参数
      if (activeTab !== '-1') {
        apiParams.status_id = Number(activeTab);
      }

      const response = await Api.getOnePayOrderList(apiParams);
      if (response.success) {
        // 按创建时间倒序排列（最新的在前）
        const sortedOrders = [...(response.data || [])].sort((a, b) =>
          new Date(b.createtime).getTime() - new Date(a.createtime).getTime()
        );
        setOrders(sortedOrders);
      } else {
        Message.error(dict?.dashboard?.onepayorders?.fetchError || 'Failed to fetch orders');
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      Message.error(dict?.dashboard?.onepayorders?.fetchError || 'Failed to fetch orders');
    } finally {
      setLoading(false);
      setSearchLoading(false);
    }
  };

  // 留言功能处理方法
  const handleMessage = async (order_goods_id: number) => {
    setCurrentOrderGoodsId(order_goods_id);
    try {
      const res = await Api.OrderMessage(order_goods_id);
      if (res.success) {
        setIsMessageModalOpen(true);
        // 按时间正序排列留言（最早的在上面，最新的在下面）
        const sortedMessages = res.data.sort((a: any, b: any) => {
          return new Date(a.createtime).getTime() - new Date(b.createtime).getTime();
        });
        setMessageList(sortedMessages);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
      Toast.error(dict?.dashboard?.onepayorders?.fetchError || 'Failed to fetch messages');
    }
  };

  const handleMessageSubmit = async () => {
    if (!message.trim()) {
      Toast.error(dict?.dashboard?.onepayorders?.messageRequired || 'Please enter a message');
      return;
    }

    setMessageLoading(true);
    try {
      const res = await Api.createOrderReply({
        order_goods_id: currentOrderGoodsId,
        content: message.trim()
      });
      if (res.success) {
        Toast.success(dict?.dashboard?.onepayorders?.messageSuccess || 'Message sent successfully');
        fetchOrders(); // 刷新订单列表
        // 重新获取留言列表以保持时间排序
        if (currentOrderGoodsId) {
          const messageRes = await Api.OrderMessage(currentOrderGoodsId);
          if (messageRes.success) {
            const sortedMessages = messageRes.data.sort((a: any, b: any) => {
              return new Date(a.createtime).getTime() - new Date(b.createtime).getTime();
            });
            setMessageList(sortedMessages);
          }
        }
        setIsMessageModalOpen(false);
        setMessage('');
        setCurrentOrderGoodsId(null);
      } else {
        Toast.error(res.msg || dict?.dashboard?.onepayorders?.messageFailed || 'Failed to send message');
      }
    } catch (error) {
      Toast.error(dict?.dashboard?.onepayorders?.messageFailed || 'Failed to send message');
    } finally {
      setMessageLoading(false);
    }
  };

  // Handle payment for unpaid orders
  const handlePayment = async (order: OnePayOrder) => {
    try {
      setPaymentLoading(order.id); // 设置当前订单为加载状态

        router.push(`/${lng}/pay?order_id=${order.id}&onepayorder=1`);
    
    } catch (error) {
      Toast.error(dict?.dashboard?.onepayorders?.fetchError || 'Failed to generate payment invoice');
    } finally {
      setPaymentLoading(null); // 清除加载状态
    }
  };

  // Handle cancel order
  const handleCancelOrder = (order: OnePayOrder) => {
    setOrderToCancel(order);
    setCancelModalOpen(true);
  };

  // Handle confirm cancel order
  const handleConfirmCancel = async () => {
    if (!orderToCancel) return;

    try {
      const response = await Api.cancelOnePayOrder({ orders_id: orderToCancel.id });
      if (response.success) {
        Toast.success(dict?.dashboard?.onepayorders?.cancelSuccess || 'Order cancelled successfully');

        // 立即从当前订单列表中移除被取消的订单
        setOrders(prevOrders => prevOrders.filter(order => order.id !== orderToCancel.id));

        // 同时刷新订单列表以确保数据同步
        setTimeout(() => {
          fetchOrders();
        }, 500); // 延迟500ms刷新，给服务器时间更新状态
      } else {
        Toast.error(response.msg || dict?.dashboard?.onepayorders?.fetchError || 'Failed to cancel order');
      }
    } catch (error) {
      Toast.error(dict?.dashboard?.onepayorders?.fetchError || 'Failed to cancel order');
    } finally {
      setCancelModalOpen(false);
      setOrderToCancel(null);
    }
  };

  // 初始化状态列表
  useEffect(() => {
    const init = async () => {
      try {
        const statusListResponse = await Api.getOnePayOrderStatusList();
        if (statusListResponse.success) {
          let statusListData = Object.keys(statusListResponse.data).map((key) => ({
            key,
            label: statusListResponse.data[key]
          }));

          // 在状态列表前面添加"全部"选项
          const allTabsData = [
            {
              key: '-1',
              label: dict?.dashboard?.onepayorders?.all || 'All'
            },
            ...statusListData
          ];

          setStatusList(allTabsData);
        }
      } catch (error) {
        // 如果API不存在，使用默认状态列表
        const defaultStatusList = [
          { key: '-1', label: dict?.dashboard?.onepayorders?.statusLabels?.all || 'All' },
          { key: '0', label: dict?.dashboard?.onepayorders?.statusLabels?.uncheck || 'Pending Review' },
          { key: '10', label: dict?.dashboard?.onepayorders?.statusLabels?.unpaid || 'Pending Payment' },
          { key: '11', label: dict?.dashboard?.onepayorders?.statusLabels?.refill || 'Pending Refill' },
          { key: '20', label: dict?.dashboard?.onepayorders?.statusLabels?.paid || 'Paid' },
          { key: '23', label: dict?.dashboard?.onepayorders?.statusLabels?.unshipped || 'Pending Shipment' },
          { key: '25', label: dict?.dashboard?.onepayorders?.statusLabels?.shipped || 'Shipped' },
          { key: '29', label: dict?.dashboard?.onepayorders?.statusLabels?.receipted || 'Received' },
          { key: '30', label: dict?.dashboard?.onepayorders?.statusLabels?.invalid || 'Invalid' },
          { key: '40', label: dict?.dashboard?.onepayorders?.statusLabels?.refundsment || 'Refunded' }
        ];
        setStatusList(defaultStatusList);
      }
    };
    init();
  }, [lng, dict]);

  // 当activeTab改变时获取订单数据
  useEffect(() => {
    if (statusList.length > 0) {
      fetchOrders();
    }
  }, [activeTab, statusList]);

  // Handle keyword search
  const handleSearch = () => {
    const values = form.getFieldsValue();
    setSearchLoading(true);
    fetchOrders({
      keyword: values.keyword || searchKeyword
    });
  };

  // Handle tab change
  const handleTabChange = (activeKey: string) => {
    setActiveTab(activeKey);
  };



  // Get status color
  const getStatusColor = (statusId: number) => {
    switch (statusId) {
      case 0: return 'text-orange-600';   // UNCHECK - 待审核
      case 10: return 'text-yellow-600';  // UNPAID - 待付款
      case 11: return 'text-yellow-700';  // REFILL - 待补款
      case 20: return 'text-green-600';   // PAID - 已付款
      case 23: return 'text-blue-600';    // UNSHIPPED - 待发货(一次付款)
      case 25: return 'text-purple-600';  // SHIPPED - 已发货(一次付款)
      case 29: return 'text-green-700';   // RECEIPTED - 确认收货(一次付款)
      case 30: return 'text-red-600';     // INVALID - 无效单
      case 40: return 'text-green-600';   // REFUNDSMENT - 已退款
      default: return 'text-gray-600';
    }
  };

  // 解析附加服务信息
  const parseAdditionalServices = (product: OnePayOrderGoods, order: OnePayOrder) => {
    const services: string[] = [];

    // 解析商品附加服务 (product.server 或 order.order_serve)
    const productServices = product.server || order.order_serve;
    if (productServices && typeof productServices === 'object') {
      Object.entries(productServices).forEach(([key, service]: [string, any]) => {
        if (service && (service.selected || service.value)) {
          const serviceName = service.title || service.name || key;
          services.push(serviceName);
        }
      });
    }

    // 解析包裹附加服务 (order.sendorder_server)
    if (order.sendorder_server && typeof order.sendorder_server === 'object') {
      Object.entries(order.sendorder_server).forEach(([key, service]: [string, any]) => {
        if (service && (service.selected || service.value)) {
          const serviceName = service.title || service.name || key;
          services.push(serviceName);
        }
      });
    }

    return services.length > 0 ? services.join('、') : null;
  };

// 价格显示组件，支持多货币
const PriceDisplay = ({ value, className = "", inline = false }: { value: number; className?: string; inline?: boolean }) => {
  const [usdPrice, setUsdPrice] = useState<any>(null);

  useEffect(() => {
    const loadUSDPrice = async () => {
      const usdPriceData = await formatUSDPrice(value);
      setUsdPrice(usdPriceData);
    };
    loadUSDPrice();
  }, [value]);

  const mainPrice = formatCurrency(value);

  if (inline) {
    return (
      <span className={className}>
        <span className="font-medium text-[#FF6000]">
          {mainPrice.formatValue}
        </span>
        {usdPrice && (
          <span className="text-xs text-gray-500 ml-2">
            ({usdPrice.formatValue})
          </span>
        )}
      </span>
    );
  }

  return (
    <div className={className}>
      <div className="font-medium text-[#FF6000]">
        {mainPrice.formatValue}
      </div>
      {usdPrice && (
        <div className="text-xs text-gray-500 mt-1">
          {usdPrice.formatValue}
        </div>
      )}
    </div>
  );
};

  if (!dict) {
    return <Loading height="400px" />;
  }

  return (
    <AntdConfigProvider>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold mb-4">{dict?.dashboard?.onepayorders?.title || 'One-Time Payment Orders'}</h1>

          {/* Search Form */}
          <Form
            form={form}
            layout="inline"
            className="mb-6 flex flex-wrap"
            style={{
              marginBottom: '20px'
            }}
            onFinish={() => {
              setSearchLoading(true);
              handleSearch();
            }}
            initialValues={{
              keyword: ''
            }}
          >
            <Form.Item name="keyword" className="mb-2">
              <Input
                placeholder={dict?.dashboard?.onepayorders?.searchPlaceholder || "Enter keywords to search..."}
                prefix={<SearchOutlined className="text-[#86909C]" />}
                style={{ width: 320 }}
                size="large"
              />
            </Form.Item>

            <Form.Item className="mb-2">
              <Button
                type="primary"
                htmlType="submit"
                loading={searchLoading}
                size="large"
              >
                {dict?.dashboard?.onepayorders?.searchButton || 'Search'}
              </Button>
            </Form.Item>
          </Form>

          {/* Status Tabs */}
          <Tabs defaultActiveKey={activeTab.toString()} onChange={handleTabChange} items={statusList}>
          </Tabs>
        </div>

        {loading ? (
          <Loading height="300px" />
        ) : orders.length > 0 ? (
        <div className="space-y-4">
          {orders.map((order) => (
            <div key={order.id} className="bg-white">
              {/* Order Header */}
              <div className="px-6 py-3">
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center space-x-8">
                    <span>{dict?.dashboard?.onepayorders?.orderNumber || 'Order Number'}: {order.orderssn}</span>
                    <span>{dict?.dashboard?.onepayorders?.createTime || 'Create Time'}: {order.createtime}</span>
                    <span>{dict?.dashboard?.onepayorders?.shippingToChina || 'Shipping to China'}: <PriceDisplay value={order.package ? parseFloat(order.package.freight) : 0} inline={true} /></span>
                  </div>
                  <div className="flex items-center space-x-6">
                    <span className="text-[#FF6000] font-medium">{dict?.dashboard?.onepayorders?.orderTotal || 'Order Total'}: <PriceDisplay value={order.totalmoney} inline={true} /></span>
                  </div>
                </div>
              </div>

              {/* Products List */}
              {order.shop_list && order.shop_list.map((shop) => (
                <div key={shop.id}>
                  {/* Products Header */}
                  {shop.goods && shop.goods.length > 0 && (
                    <div className="px-6 py-2 bg-gray-50 border-t border-b border-gray-200">
                      <div className="flex items-center text-xs text-gray-600 font-medium">
                        <div className="w-14 mr-3"></div> {/* Image placeholder */}
                        <div className="flex-1 mr-4">{dict?.dashboard?.onepayorders?.table?.productInfo || 'Product Info'}</div>
                        <div className="w-16 text-center mr-4">{dict?.dashboard?.onepayorders?.table?.unitPrice || 'Unit Price'}</div>
                        <div className="w-8 text-center mr-4">{dict?.dashboard?.onepayorders?.table?.quantity || 'Quantity'}</div>
                        <div className="w-16 text-center mr-4">{dict?.dashboard?.onepayorders?.table?.message || 'Message'}</div>
                        <div className="w-32 text-center mr-4">{dict?.dashboard?.onepayorders?.table?.logisticsName || 'Logistics Name'}</div>
                        <div className="w-28 text-center mr-4">{dict?.dashboard?.onepayorders?.table?.amountServiceFee || 'Amount/Service Fee'}</div>
                        <div className="w-32 text-center">{dict?.dashboard?.onepayorders?.table?.status || 'Status'}</div>
                      </div>
                    </div>
                  )}
                  {/* Products */}
                  {shop.goods && shop.goods.map((product, index) => (
                    <div key={product.id} className="px-6 py-3">
                      <div className="flex items-center">
                        {/* Product Image */}
                        <div className="w-14 h-14 flex-shrink-0 mr-3">
                          <img
                            src={product.goodsimg || '/images/default.jpg'}
                            alt={product.goodsname}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/images/default.jpg';
                            }}
                          />
                        </div>

                        {/* Product Info */}
                        <div className="flex-1 min-w-0 mr-4">
                          <h4 className="text-sm text-gray-900 line-clamp-2 mb-1">
                            {product.goodsname}
                          </h4>
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="text-xs text-gray-500">{dict?.dashboard?.onepayorders?.product?.group || 'Group'}: 39</span>
                            {product.skuname && (
                              <span className="text-xs text-gray-500">{product.skuname}</span>
                            )}
                          </div>
                          {parseAdditionalServices(product, order) && (
                            <div className="inline-block px-1 py-0.5 text-xs bg-gray-100 text-gray-600 rounded text-xs">
                              【{parseAdditionalServices(product, order)}】
                            </div>
                          )}
                        </div>

                        {/* Price Column */}
                        <div className="w-16 text-center mr-4">
                          <PriceDisplay value={parseFloat(product.goodsprice)} className="text-base" />
                        </div>

                        {/* Quantity Column */}
                        <div className="w-8 text-center mr-4">
                          <span className="text-sm text-gray-900">{product.goodsnum}</span>
                        </div>

                        {/* Message Column */}
                        <div className="w-16 text-center mr-4">
                          <button
                            className="px-2 py-1 text-xs border border-gray-300 rounded text-gray-600 hover:bg-gray-50"
                            onClick={() => handleMessage(product.id)}
                          >
                            {dict?.dashboard?.onepayorders?.buttons?.message || 'Message'}
                          </button>
                        </div>

                        {/* Logistics Name Column */}
                        <div className="w-32 text-center mr-4">
                          <div className="px-2 py-1 text-xs border border-gray-300 rounded text-gray-600 hover:bg-gray-50">
                            <div className="font-medium">
                              {product.expressname || dict?.dashboard?.onepayorders?.buttons?.dhlLogistics || 'DHL Logistics'}
                            </div>
                            {product.expressno && (
                              <div className="text-gray-500 mt-1">
                                {product.expressno}
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Amount and Service Fee Column */}
                        <div className="w-28 text-right mr-4">
                          <PriceDisplay value={parseFloat(product.totalmoney)} className="text-base mb-1" />
                          <div className="text-xs text-gray-500 leading-tight">
                            <div>{dict?.dashboard?.onepayorders?.product?.productAmount || 'Product Amount'} {formatCurrency(parseFloat(product.goodsprice) * product.goodsnum).formatValue}</div>
                            <div>{dict?.dashboard?.onepayorders?.product?.internationalShipping || 'International Shipping'} {formatCurrency(parseFloat(product.sendprice || '0')).formatValue}</div>
                            <div>{dict?.dashboard?.onepayorders?.product?.valueAddedFee || 'Value-added Fee'} {formatCurrency(product.serverfee - product.serverdiscount).formatValue}</div>
                            <div>{dict?.dashboard?.onepayorders?.product?.serviceFee || 'Service Fee'} {formatCurrency(product.serverdiscount).formatValue}</div>
                          </div>
                        </div>

                        {/* Status Column */}
                        <div className="w-32 text-center">
                          {/* 根据订单状态决定显示主订单状态还是商品状态 */}
                          {(order.status_id === 30 || order.status_id === 40 || order.status_id === 20 || order.status_id === 0 || order.status_id === 29) ? (
                            <span className={getStatusColor(order.status_id)}>{order.status_text}</span>
                          ) : (
                            <div className="space-y-1">
                              <div>
                                <span className={getStatusColor(product.status_id)}>{product.status_text}</span>
                              </div>
                              {/* 只在第一个商品显示操作按钮，避免重复 */}
                              {(order.status_id === 10 || order.status_id === 11) && index === 0 && (
                                <div className="flex flex-col space-y-1">
                                  <Button
                                    type="primary"
                                    size="small"
                                    onClick={() => handlePayment(order)}
                                    loading={paymentLoading === order.id}
                                    disabled={paymentLoading === order.id}
                                    className="bg-[#f97316] hover:bg-[#ea580c] border-[#f97316] hover:border-[#ea580c] text-white px-2 py-1 text-xs w-full"
                                  >
                                    {paymentLoading === order.id ? (dict?.dashboard?.onepayorders?.buttons?.generating || '生成中...') : (dict?.dashboard?.onepayorders?.buttons?.pay || '支付')}
                                  </Button>
                                  <Button
                                    type="default"
                                    size="small"
                                    onClick={() => handleCancelOrder(order)}
                                    className="border-gray-300 text-gray-600 hover:border-gray-400 hover:text-gray-700 px-2 py-1 text-xs w-full"
                                  >
                                    {dict?.dashboard?.onepayorders?.buttons?.cancel || '取消'}
                                  </Button>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          ))}
        </div>
      ) : (
          <EmptyState
            type="no-results"
            title={dict?.dashboard?.onepayorders?.noOrders || "No one-time payment orders found"}
            description={dict?.dashboard?.onepayorders?.noOrdersDescription || "You don't have any one-time payment orders yet"}
            dict={dict}
          />
        )}
      </div>

      {/* 留言弹窗 */}
      <ModalComponent
        title={dict?.dashboard?.onepayorders?.orderMessage || 'Order Message'}
        open={isMessageModalOpen}
        onOk={handleMessageSubmit}
        onCancel={() => {
          setIsMessageModalOpen(false);
          setMessage('');
          setCurrentOrderGoodsId(null);
        }}
        okText={dict?.dashboard?.onepayorders?.confirmBtn || 'Confirm'}
        centered
        cancelText={dict?.dashboard?.onepayorders?.cancelBtn || 'Cancel'}
        confirmLoading={messageLoading}
        footer={null}
      >
        <div>
          {/* 留言列表区域 */}
          <div className="p-6 max-h-[60vh] overflow-y-auto">
            <div className="space-y-6" id="messageList">
              {messageList.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  {dict?.dashboard?.onepayorders?.noMessages || '暂无留言'}
                </div>
              ) : (
                messageList.map((remark: any, index: number) => (
                <div key={index}>
                  {remark.admin_id == 0 ? (
                    <div className="flex gap-4 justify-end">
                      <div className="w-80 flex px-4 py-2 border border-gray-200 rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-500">{remark.createtime}</span>
                          </div>
                          <p className="text-gray-700 text-sm">{remark.content}</p>
                        </div>
                        {remark?.userinfo?.avatar ? (
                          <div className="w-10 h-10 rounded-full flex">
                            <img src={remark?.userinfo?.avatar} alt="User avatar" className="rounded-full" />
                          </div>
                        ) : (
                          <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                            {remark?.userinfo?.username?.charAt(0)?.toUpperCase() || 'U'}
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="flex gap-4">
                      <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        A
                      </div>
                      <div className="w-80 flex px-4 py-2 border border-gray-200 rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-900">
                              {dict?.dashboard?.onepayorders?.admin || 'Admin'}
                            </span>
                            <span className="text-sm text-gray-500">{remark.createtime}</span>
                          </div>
                          <p className="text-gray-700 text-sm">{remark.content}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                ))
              )}
            </div>
          </div>
          {/* 发送留言区域 */}
          <div className="px-6 py-4 border-t bg-gray-50 rounded-lg">
            <div className="flex gap-4">
              <textarea
                id="messageInput"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="flex-1 h-20 p-3 border border-gray-200 rounded-lg resize-none focus:outline-none focus:border-orange-500 text-sm"
                placeholder={dict?.dashboard?.onepayorders?.inputHint || 'Please enter your message...'}
              ></textarea>
              <button
                onClick={handleMessageSubmit}
                className="px-6 h-10 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors whitespace-nowrap self-end"
              >
                {dict?.dashboard?.onepayorders?.sendMessage || 'Send Message'}
              </button>
            </div>
          </div>
        </div>
      </ModalComponent>

      {/* 取消订单确认模态框 */}
      <ModalComponent
        title={dict?.dashboard?.onepayorders?.confirmCancel || 'Confirm Cancel Order'}
        open={cancelModalOpen}
        onOk={handleConfirmCancel}
        onCancel={() => {
          setCancelModalOpen(false);
          setOrderToCancel(null);
        }}
        okText={dict?.dashboard?.onepayorders?.confirmBtn || 'Confirm'}
        cancelText={dict?.dashboard?.onepayorders?.cancelBtn || 'Cancel'}
        centered
        okType="danger"
      >
        <p>
          {orderToCancel && (
            `${dict?.dashboard?.onepayorders?.confirmCancel || 'Are you sure you want to cancel this one-time payment order?'} (${orderToCancel.orderssn})`
          )}
        </p>
      </ModalComponent>
    </AntdConfigProvider>
  );
}
