"use client"

import OrderProgress from "@/components/OrderProgress"
import { useState, useEffect } from "react"
import Image from "next/image"
import { Table, Tag, Spin, message } from "antd"
import Button from "@/components/Button"
import Input from "@/components/Input"
import Pagination from "@/components/Pagination"
import type { ColumnsType } from "antd/es/table"
import { Api } from "@/request/api"
import Link from "next/link"
import { useParams, useRouter } from "next/navigation"
import { fixedUrl } from "@/utils"
import { formatCurrency } from "@/utils/currency"
import { getDictionary } from "@/dictionaries"
import { isPluginEnabled } from "@/utils/plugin"
// 避免使用Api类型，直接使用axios请求
interface WarehouseItem {
  id: number
  order_id: number
  goodssn: string
  goodsname: string
  goodsimg: string
  goodsurl: string
  goodsnum: number
  goodsweight: number
  goodsvolume: number
  totalmoney: string
  warehouse: string
  express: string
  expressname: string
  expresscode: string
  createtime: string
  updatetime: string
  status_text: string
  goodsprice:string
  skuname:string
  goodsseller?: string // 添加goodsseller字段
  key?: string // 为Table组件添加key字段
}

export default function WarehousePage() {
  const { lng } = useParams()
  const router = useRouter()
  const [warehouseList, setWarehouseList] = useState<WarehouseItem[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [submitting, setSubmitting] = useState(false)
  const [searchText, setSearchText] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [total, setTotal] = useState(0)
  const [isPreviewPluginEnabled, setIsPreviewPluginEnabled] = useState(false)
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'

  const [dict, setDict] = useState<any>(null); // 添加字典状态
  // 异步获取字典数据
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };
    fetchDictionary();
  }, [lng]);

  useEffect(() => {
    fetchWarehouseList(searchText)
    checkPreviewPlugin()
  }, [])

  // 检查预览包裹插件状态
  const checkPreviewPlugin = async () => {
    try {
      const enabled = await isPluginEnabled('packagepreview')
      setIsPreviewPluginEnabled(enabled)
    } catch (error) {
      console.error('检查预览包裹插件状态失败:', error)
      setIsPreviewPluginEnabled(false)
    }
  }

  const fetchWarehouseList = async (search = "", page = currentPage, size = pageSize) => {
    setLoading(true)
    const res = await Api.getWarehouseList(search, page, size)
    console.log('仓库列表API响应:', res)

    if (res.success) {
      // 根据不同的API响应结构处理数据
      let data, totalCount

      if (isTp5) {
        data = res.data
        totalCount = res.total || res.data?.total || 0
      } else {
        data = res.data?.data || res.data
        totalCount = res.data?.total || res.total || 0
      }

      if (data) {
        const dataWithKeys = data.map((item: WarehouseItem) => ({
          ...item,
          key: item.id.toString()
        }))
        setWarehouseList(dataWithKeys)
        setCurrentPage(page)
        setPageSize(size)
        setTotal(totalCount)
        console.log('设置总数:', totalCount)
      }
    } else {
      message.error(res.data?.msg || '获取数据失败')
    }
    setLoading(false)
  }

  const handleSearch = (value: string) => {
    setSearchText(value)
    setCurrentPage(1)
    fetchWarehouseList(value, 1, pageSize)
  }

  const handleSubmitOrder = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning(dict?.dashboard?.warehouse?.selectPrompt)
      return
    }
    let dataItem = [];
    for (let i = 0; i < selectedRowKeys.length; i++) {
      let findItem = warehouseList.filter(item => item.key == selectedRowKeys[i])
      if (findItem) {
        dataItem.push(findItem[0]);
      }
    }
    let key = `confirmpackage-${selectedRowKeys.join()}`;
    let dataItemJSON = JSON.stringify(dataItem);
    window.localStorage.setItem(key, dataItemJSON);
    router.push(`/${lng}/confirmpackage?key=${key}`);
  }

  const handleSubmitPreviewPackage = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning(dict?.dashboard?.warehouse?.selectPrompt)
      return
    }
    let dataItem = [];
    for (let i = 0; i < selectedRowKeys.length; i++) {
      let findItem = warehouseList.filter(item => item.key == selectedRowKeys[i])
      if (findItem) {
        dataItem.push(findItem[0]);
      }
    }
    let key = `previewpackage-${selectedRowKeys.join()}`;
    let dataItemJSON = JSON.stringify(dataItem);
    window.localStorage.setItem(key, dataItemJSON);
    router.push(`/${lng}/previewpackage?key=${key}`);
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchWarehouseList(searchText, page, pageSize)
  }

  const columns: ColumnsType<WarehouseItem> = [
    {
      title: dict?.dashboard?.warehouse?.itemInfo,
      key: "goodsInfo",
      render: (_, record) => (
        <div className="flex items-center">
          <div className="w-16 h-16 relative mr-3 flex-shrink-0">
            <Image
              src={fixedUrl(record.goodsimg)}
              alt={record.goodsname}
              fill
              className="object-cover rounded"
              sizes="64px"
            />
          </div>
          <div>
            {record.goodsseller === 'Buy Yourself' || record.goodsseller === 'OneBuy' || record.goodsseller === 'By yourself' ? (
              <div className="font-medium max-w-[200px] line-clamp-2">{record.goodsname}</div>
            ) : (
              <Link target="_blank" href={`/${lng}/detail/taobao?url=${record.goodsurl}`}><div className="font-medium max-w-[200px] line-clamp-2">{record.goodsname}</div></Link>
            )}
            <div className="text-sm text-gray-500 mt-1"> {dict?.dashboard?.warehouse?.itemNumber}: {record.goodssn}</div>
          </div>
        </div>
      ),
    },
    {
      title:  dict?.dashboard?.warehouse?.quantityWeight,
      key: "numWeight",
      render: (_, record) => (
        <>
          <div>{record.goodsnum} 件</div>
          <div className="text-sm text-gray-500 mt-1">{record.goodsweight}g / {record.goodsvolume}cm³</div>
        </>
      ),
    },
    {
      title:  dict?.dashboard?.warehouse?.totalPrice,
      dataIndex: "totalmoney",
      key: "totalmoney",
      render: (totalmoney) => <div className="font-medium">{formatCurrency(Number(totalmoney)).formatValue}</div>,
    },
    {
      title: dict?.dashboard?.warehouse?.location,
      dataIndex: "warehouse",
      key: "warehouse",
      render: (warehouse) => <div>{warehouse ||  dict?.dashboard?.warehouse?.unassigned}</div>,
    },
    {
      title:  dict?.dashboard?.warehouse?.shippingInfo,
      key: "express",
      render: (_, record) => (
        <>
          <div>{record.expressname || dict?.dashboard?.warehouse?.noData}</div>
          <div className="text-sm text-gray-500 mt-1">{record.express ||  dict?.dashboard?.warehouse?.noTracking}</div>
        </>
      ),
    },
    {
      title:  dict?.dashboard?.warehouse?.storageTime,
      dataIndex: "createtime",
      key: "createtime",
    },
    {
      title:  dict?.dashboard?.warehouse?.status,
      key: "status",
      dataIndex: "status_text",
      render: (status_text) => (
        <Tag color={status_text === "可提交运单" ? "success" : "warning"}>
          {status_text}
        </Tag>
      ),
    },
  ]

  return (
    <div className="min-h-screen px-6 py-4">
      <OrderProgress  dict={dict} />
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold">{ dict?.dashboard?.warehouse?.title}</h1><div className="w-64">
          <Input.Search
            placeholder={dict?.dashboard?.warehouse?.searchPlaceholder}
            allowClear
            enterButton={ dict?.dashboard?.warehouse?.searchButton}
            onSearch={handleSearch}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchText(e.target.value)}
          />
        </div>
      </div>

      <div className="mb-4 flex justify-between items-center">
        <div>
          {selectedRowKeys.length > 0 && (
            <span className="mr-4">{ dict?.dashboard?.warehouse?.selectedCount} {selectedRowKeys.length} { dict?.dashboard?.warehouse?.unit}</span>
          )}
        </div>

      </div>

      <Spin spinning={loading}>
        <Table
          rowSelection={{
            selectedRowKeys,
            onChange: (keys) => {
              console.log('选中的keys:', keys)
              setSelectedRowKeys(keys)
            },
            getCheckboxProps: (record) => {
              return {
                disabled: false,
                name: record.goodsname,
              }
            },
          }}
          columns={columns}
          dataSource={warehouseList}
          rowKey="key"
          pagination={false}
          locale={{
            emptyText: dict?.dashboard?.warehouse?.emptyData,
            selectionAll: dict?.dashboard?.warehouse?.selectAll,
            selectInvert: dict?.dashboard?.warehouse?.invertSelect,
            selectNone: dict?.dashboard?.warehouse?.clearAll
          }}
        />

        {/* 自定义分页组件 */}
        {total > pageSize && (
          <div className="mt-4 flex justify-center">
            <Pagination
              currentPage={currentPage}
              totalPages={Math.ceil(total / pageSize)}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              prevText={dict?.dashboard?.warehouse?.prevPage || '上一页'}
              nextText={dict?.dashboard?.warehouse?.nextPage || '下一页'}
            />
          </div>
        )}
      </Spin>
      <div className="mt-4 flex justify-end gap-3">
        {isPreviewPluginEnabled && (
          <Button
            type="primary"
            onClick={handleSubmitPreviewPackage}
            loading={submitting}
            disabled={selectedRowKeys.length === 0}
          >
            {dict?.confirm?.order?.summary?.submitPreviewPackage}
          </Button>
        )}
        <Button
          type="primary"
          onClick={handleSubmitOrder}
          loading={submitting}
          disabled={selectedRowKeys.length === 0}
        >
          {dict?.dashboard?.warehouse?.submitShipping}
        </Button>
      </div>
    </div>
  )
}
