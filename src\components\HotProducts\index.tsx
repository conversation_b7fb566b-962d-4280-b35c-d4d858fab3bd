'use client';

import { HotProductResponse } from "@/types/product";
import ProductCard from "@/components/ProductCard";
import { useEffect, useState } from "react";
import { Api } from "@/request/api";
import Loading from "@/components/Loading";

interface HotProductsProps {
  dict: any;
}

export default function HotProducts({ dict }: HotProductsProps) {
  const [keywords, setKeywords] = useState<any[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedKeyword, setSelectedKeyword] = useState<string>(dict?.home?.all || '全部');

  useEffect(() => {
    const fetchHotKeywords = async () => {
      try {
        const response = await Api.getHotProductskeywords(10);
        if (response.success) {
          setKeywords(response.data.data);
          fetchHotProducts(dict?.home?.all || '全部');
        }
      } catch (error) {
        console.error(dict?.errors?.fetchHotCategoriesFailed || '获取热销分类失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchHotKeywords();
  }, [dict]);

  const fetchHotProducts = async (keyword: string) => {
    setLoading(true);
    try {
      // 统一使用 goodsRecommendList 接口
      const params = keyword === (dict?.home?.all || '全部')
        ? { size: 10 }
        : { keyword, size: 12 };

      const response = await Api.getGoodsRecommendList(params);

      if (response.success) {
        // 处理分页数据结构，商品数据在 response.data.data 中
        setProducts(response.data?.data || []);
      }
    } catch (error) {
      console.error(dict?.errors?.fetchProductsFailed || '获取商品失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleKeywordClick = (keyword: string) => {
    setSelectedKeyword(keyword);
    fetchHotProducts(keyword);
  };

  if (!keywords?.length) return null;

  return (
    <div className="container mx-auto px-4 py-8">
      <h2 className="text-2xl font-bold text-center mb-6">{dict?.home?.hotProducts || '为你推荐'}</h2>
      <div className="flex flex-wrap gap-2 mb-6 overflow-x-auto whitespace-nowrap">
        <button
          onClick={() => handleKeywordClick(dict?.home?.all || '全部')}
          className={`px-3 py-1 rounded-sm text-sm transition-all ${
            selectedKeyword === (dict?.home?.all || '全部')
              ? 'bg-[#ff6000] text-white'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }`}
        >
          {dict?.home?.all || '全部'}
        </button>
        {keywords.map((item) => (
          <button
            key={item.id}
            onClick={() => handleKeywordClick(item.keyword)}
            className={`px-3 py-1 rounded-sm text-sm transition-all ${
              selectedKeyword === item.keyword
                ? 'bg-[#ff6000] text-white'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            {item.keyword}
          </button>
        ))}
      </div>
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <Loading height="120px" />
        </div>
      ) : products?.length === 0 ? (
        <div className="text-center text-gray-500 py-12">
          {dict?.home?.emptyList || '列表为空'}
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {products?.map((item, index) => (
            <ProductCard
              key={item.id || item.goodsimg || item.pic_url}
              product={{
                title: item.title,
                pic_url: item.goodsimg || item.pic_url,
                detail_url: item.goodsurl || item.detail_url,
                price: item.price,
                promotion_price: item.promotionprice || item.promotion_price,
                nick: item.nick || '',
                recommend: item.recommend // 使用API返回的原始值
              }}
              platform={item.goodssite || 'taobao'}
              dict={dict}
            />
          ))}
        </div>
      )}
    </div>
  );
} 