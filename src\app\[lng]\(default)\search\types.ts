import { Locale } from "@/config";

export interface Product {
  id: string;
  title: string;
  price: number;
  image: string;
  platform: 'taobao' | '1688' | 'wechat';
  sales: number;
}

export interface TaobaoItem {
  title: string;
  pic_url: string;
  promotion_price: number;
  price: number;
  num_iid: string;
  is_tmall: string;
  area: string;
  detail_url: string;
  nick: string;
  shop_url: string;
  recommend?: number | string; // 推荐标识字段
}

export interface TaobaoItems {
  page: string;
  real_total_results: number;
  total_results: number;
  page_size: number;
  pagecount: string;
  _ddf: string;
  item: TaobaoItem[];
}

export interface ApiResponse {
    items: TaobaoItems;
    error_code: string;
    reason: string;
} 


export interface SearchPageProps {
  params: Promise<{
    lng: Locale;
  }>;

  searchParams: Promise<{
    q?: string;
    page?: string;
    imgid?: string;
    type?: string;
    platform?: string;
  }>;
}

export type SearchResponse = ApiResponse;