'use client'

import { useState, useEffect } from 'react';
import { Spin, Empty, message } from 'antd';
import Image from 'next/image';
import { Api } from '@/request/api';

interface ShippingMethod {
  template_id: number;
  name: string;
  logo?: string;
  cycle: string;
  firstfee: string;
  firstweight: string;
  continuefee: string;
  continueweight: string;
  feature?: string;
  ban?: string;
  fee: {
    totalfee: number;
  };
}

interface ShippingMethodsDisplayProps {
  dict: any;
  addressId: number | null;
  weight: number;
  volume: number;
  goodstype: string | any[];
  stotalmoney: number;
  onMethodsLoaded?: (methods: ShippingMethod[]) => void; // 新增回调函数
}

export default function ShippingMethodsDisplay({
  dict,
  addressId,
  weight,
  volume,
  goodstype,
  stotalmoney,
  onMethodsLoaded
}: ShippingMethodsDisplayProps) {
  const [methods, setMethods] = useState<ShippingMethod[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchShippingMethods();
  }, [addressId, weight, volume, goodstype, stotalmoney]);

  const fetchShippingMethods = async () => {
    if (!addressId) return;
    
    setLoading(true);
    try {
      const response = await Api.getEstimates({
        area_id: addressId,
        weight: weight,
        volume: volume,
        goodstype: goodstype,
        stotalmoney: stotalmoney,
        clienttype: 'pc'
      });
      
      if (response.success) {
        setMethods(response.data);
        // 通知父组件运输方式已加载
        if (onMethodsLoaded && response.data.length > 0) {
          onMethodsLoaded(response.data);
        }
      } else {
        message.error(dict?.confirm?.order?.transport?.fetchError);
      }
    } catch (error) {
      console.error('获取国际运输方式错误:', error);
      message.error(dict?.confirm?.order?.transport?.loadError);
    } finally {
      setLoading(false);
    }
  };

  if (!addressId) {
    return (
      <div className="bg-white p-6 rounded-lg">
        <Empty description={dict?.confirm?.order?.transport?.addressFirst} />
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg">
      <Spin spinning={loading}>
        {methods.length > 0 ? (
          <div className="space-y-4">
            {methods.map(method => (
              <div
                key={method.template_id}
                className="border rounded-lg p-4 border-gray-200 bg-gray-50"
              >
                <div className="flex gap-4">
                  {/* 运输方式图片 */}
                  {method.logo && (
                    <div className="flex-shrink-0 flex items-center">
                      <Image
                        src={method.logo.startsWith('//') ? `https:${method.logo}` : method.logo}
                        alt={method.name}
                        width={60}
                        height={60}
                        className="rounded-lg object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                        }}
                      />
                    </div>
                  )}

                  <div className="flex-1">
                    <div className="font-medium text-base">{method.name}</div>
                    <div className="text-sm text-gray-500 mt-1">
                      {dict?.confirm?.order?.transport?.estimate}: {method.cycle}
                    </div>

                    {method.ban && (
                      <div className="text-xs text-gray-400 mt-2">{dict?.confirm?.order?.transport?.restriction}: {method.ban}</div>
                    )}

                    {/* 线路特点 */}
                    {method.feature && (
                      <div className="mt-2">
                        <div className="text-xs text-gray-600">
                          <span className="font-medium">{dict?.confirm?.order?.transport?.feature}:</span>
                        </div>
                        <div className="text-xs text-gray-500 mt-1 leading-relaxed">
                          {method.feature}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          !loading && <Empty description={dict?.confirm?.order?.transport?.noAvailable}/>
        )}
      </Spin>
    </div>
  );
}
