'use client'

import { useEffect, useState, useRef, useCallback } from 'react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import Pagination from '@/components/Pagination'
import { Api } from '@/request/api'
import Loading from '@/components/Loading'
import Tabs from '@/components/Tabs'
import ProductCard from '@/components/ProductCard'
import EmptyState from '@/components/EmptyState'
import axios from 'axios'
import ToastHelper from '@/utils/toastHelper'

interface SearchResultsProps {
  query: string
  currentPage: number
  pageSize: number
  imgid: string
  dict: any
  lng: string
  initialPlatform?: string
}
// 获取本地配置信息
const getConfig = () => {
  if (typeof window === 'undefined') {
    // 服务器端返回默认配置
    return {
      api_list: {
        taobao: 'taobao',
        '1688': '1688',
        jd: 'jd',
        micro: 'micro'
      }
    };
  }
  try {
    const siteData = localStorage.getItem('siteData');
    if (!siteData) {
      // 如果没有本地数据，返回默认配置
      return {
        api_list: {
          taobao: 'taobao',
          '1688': '1688',
          jd: 'jd',
          micro: 'micro'
        }
      };
    }
    const parsedData = JSON.parse(siteData);
    // 确保api_list存在
    if (!parsedData.api_list) {
      return {
        api_list: {
          taobao: 'taobao',
          '1688': '1688',
          jd: 'jd',
          micro: 'micro'
        }
      };
    }
    return parsedData;
  } catch (error) {
    console.error('解析siteData失败:', error);
    // 出错时返回默认配置
    return {
      api_list: {
        taobao: 'taobao',
        '1688': '1688',
        jd: 'jd',
        micro: 'micro'
      }
    };
  }
};

// 定义平台类型
type Platform = 'taobao' | '1688' | 'jd' | 'micro'


interface ProductList {
  items: {
    total_results: number
    item: any[]
  }
}

const fetchData = async (
  platform: Platform,
  query: string,
  currentPage: number,
  pageSize: number,
  imgid: string,
  cancelToken?: any
) => {
  try {
    console.log('发起API请求:', { platform, query, currentPage, pageSize, imgid });

    // 使用原有的API方法，但需要修改以支持cancelToken
    // 由于API装饰器不支持传递config，我们需要直接使用request
    const { request } = await import('@/request/index');

    const params = {
      searchlang: localStorage.getItem('selectedLanguage'),
      keyword: query,
      tp: platform,
      page: currentPage.toString(),
      pagesize: pageSize.toString(),
      imgid: imgid,
    };

    // 选择正确的API端点
    const endpoint = process.env.NEXT_PUBLIC_BACKEND_TYPE === '6'
      ? '/api/v1.0/web/goods/ob/list'
      : '/api/obapi/getItemlist';

    const response = await request.post(endpoint, params, {
      cancelToken
    } as any);

    console.log('API响应:', response);

    // 响应已经被拦截器处理过，直接使用
    console.log('API请求成功，返回数据:', response);
    return {
      success: true,
      data: response,
      error: null,
      isTimeout: false,
      cancelled: false
    }
  } catch (error) {
    // 检查是否是axios取消请求
    if (axios.isCancel(error)) {
      console.log('请求被取消:', platform);
      return {
        success: false,
        error: 'Request was cancelled',
        isTimeout: false,
        cancelled: true,
        data: { items: { total_results: 0, item: [] } }
      }
    }

    // 只有非取消请求的错误才显示错误信息
    console.error(`Error fetching ${platform} data:`, error)

    // 区分不同类型的错误
    const isTimeout = error instanceof Error && (
      error.message.includes('timeout') ||
      error.message.includes('Network Error') ||
      error.message.includes('ECONNABORTED')
    )
    return {
      success: false,
      error: error instanceof Error ? error.message : '请求失败',
      isTimeout,
      cancelled: false,
      data: { items: { total_results: 0, item: [] } }
    }
  }
}


export default function TabResults({
  query,
  currentPage,
  pageSize,
  imgid,
  dict,
  lng,
  initialPlatform
}: SearchResultsProps) {
  const [loading, setLoading] = useState(true)
  const [sortBy, setSortBy] = useState<'default' | 'price-asc' | 'price-desc' | 'sales'>('default')
  const [platforms, setPlatforms] = useState<Platform[]>(['taobao', '1688', 'jd', 'micro'])
  const [selectedPlatform, setSelectedPlatform] = useState<Platform>('taobao')
  const [productLists, setProductLists] = useState<Record<Platform, ProductList>>({
    taobao: { items: { total_results: 0, item: [] } },
    '1688': { items: { total_results: 0, item: [] } },
    jd: { items: { total_results: 0, item: [] } },
    micro: { items: { total_results: 0, item: [] } }
  })
  const [activeTab, setActiveTab] = useState(0)
  const [products, setProducts] = useState<any[]>([])
  const [error, setError] = useState<{message: string, isTimeout: boolean} | null>(null)
  const router = useRouter()

  // 用于管理请求取消的refs
  const currentRequestRef = useRef<any>(null)
  const tabRequestRef = useRef<any>(null)

  // 初始化平台列表和选中平台
  useEffect(() => {
    const config = getConfig()
    if (config.api_list) {
      const availablePlatforms = Object.values(config.api_list) as Platform[]
      setPlatforms(availablePlatforms)

      // 确定初始选中的平台
      let targetPlatform: Platform = 'taobao' // 默认平台
      let shouldUpdateUrl = false

      // 如果URL中有平台参数，优先使用URL中的平台
      if (initialPlatform && availablePlatforms.includes(initialPlatform as Platform)) {
        targetPlatform = initialPlatform as Platform
      } else if (availablePlatforms.length > 0) {
        // 如果URL中没有平台参数或平台不可用，使用第一个可用平台
        targetPlatform = availablePlatforms[0]
        // 如果URL中没有平台参数，需要更新URL
        if (!initialPlatform) {
          shouldUpdateUrl = true
        }
      }

      setSelectedPlatform(targetPlatform)

      // 设置对应的activeTab
      const tabIndex = availablePlatforms.indexOf(targetPlatform)
      if (tabIndex >= 0) {
        setActiveTab(tabIndex)
      }

      // 如果需要更新URL，添加平台参数
      if (shouldUpdateUrl && (query || imgid)) {
        const params = new URLSearchParams()
        if (query) params.set('q', query)
        params.set('page', currentPage.toString())
        params.set('platform', targetPlatform)
        if (imgid) params.set('imgid', imgid)
        router.replace(`/${lng}/search?${params.toString()}`)
      }
    }
  }, [initialPlatform, lng, query, currentPage, imgid, router])

  // 监听URL参数变化，同步tab状态
  useEffect(() => {
    if (initialPlatform && platforms.length > 0) {
      const platformIndex = platforms.indexOf(initialPlatform as Platform)
      if (platformIndex >= 0 && platformIndex !== activeTab) {
        setActiveTab(platformIndex)
        setSelectedPlatform(initialPlatform as Platform)
      }
    }
  }, [initialPlatform, platforms, activeTab])

  useEffect(() => {
    const loadData = async () => {
      // 取消之前的请求
      if (currentRequestRef.current) {
        currentRequestRef.current.cancel('新请求取消了之前的请求');
      }

      // 创建新的取消令牌
      const source = axios.CancelToken.source();
      currentRequestRef.current = source;

      setLoading(true)
      setError(null)

      console.log('开始加载数据:', { selectedPlatform, query, currentPage, pageSize, imgid });

      const res = await fetchData(selectedPlatform, query, currentPage, pageSize, imgid, source.token)

      console.log('数据加载结果:', res);

      // 如果请求被取消，不更新状态
      if (res.cancelled) {
        console.log('请求被取消，不更新状态');
        return;
      }

      if (res.success && res.data) {
        let data = res.data
        console.log('成功获取数据:', data);

        setProductLists(prev => ({
          ...prev,
          [selectedPlatform]: data
        }))

        const items = data?.items?.item || []
        setProducts(items)
        console.log('设置产品数据:', items.length, '个产品');

        // 确保清除错误状态
        setError(null)
      } else {
        console.log('数据加载失败:', res.error || '未知错误');
        // 设置错误状态，不再跳转到手工填单
        setError({
          message: res.error || ToastHelper.getLocalizedMessage('load_failed', lng),
          isTimeout: res.isTimeout || false
        })
        setProducts([])
      }
      setLoading(false)
    }

    // 只有当selectedPlatform有效且(有关键词或有imgid)时才加载数据
    if (selectedPlatform && (query || imgid)) {
      loadData()
    }

    // 清理函数：组件卸载时取消请求
    return () => {
      if (currentRequestRef.current) {
        currentRequestRef.current.cancel('组件卸载取消请求');
      }
    }
  }, [selectedPlatform, query, currentPage, pageSize, imgid])

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams()
    if (query) params.set('q', query)
    params.set('page', page.toString())
    params.set('sort', sortBy)
    params.set('platform', selectedPlatform)
    if (imgid) params.set('imgid', imgid)
    router.push(`/${lng}/search?${params.toString()}`)
  }

  const handleTabChange = async (activeKey: string) => {
    const key = parseInt(activeKey);
    setActiveTab(key);
    const platform = platforms[key] as Platform;

    // 确保platform是有效的
    if (!platform) {
      console.error('Invalid platform selected:', key, platforms);
      return;
    }

    // 取消当前正在进行的请求
    if (currentRequestRef.current) {
      currentRequestRef.current.cancel('切换tab取消了当前请求');
    }

    // 取消之前的tab请求
    if (tabRequestRef.current) {
      tabRequestRef.current.cancel('新的tab请求取消了之前的tab请求');
    }

    setSelectedPlatform(platform);

    // 更新 URL 参数，将页码重置为1
    const params = new URLSearchParams()
    if (query) params.set('q', query)
    params.set('page', '1')
    params.set('sort', sortBy)
    params.set('platform', platform)
    if (imgid) params.set('imgid', imgid)
    router.push(`/${lng}/search?${params.toString()}`);

    // 如果该平台数据为空，则获取数据
    if (!productLists[platform]?.items?.item?.length) {
      // 创建新的取消令牌用于tab请求
      const source = axios.CancelToken.source();
      tabRequestRef.current = source;

      setLoading(true);
      setError(null);
      const res = await fetchData(platform, query, 1, pageSize, imgid, source.token);

      // 如果请求被取消，不更新状态
      if (res.cancelled) {
        console.log('Tab请求被取消，不更新状态');
        return;
      }

      if (res.success && res.data) {
        let data = res.data
        setProductLists(prev => ({
          ...prev,
          [platform]: data
        }));
        setProducts(data?.items?.item || []);
        setError(null); // 确保清除错误状态
      } else {
        setError({
          message: res.error || ToastHelper.getLocalizedMessage('load_failed', lng),
          isTimeout: res.isTimeout || false
        })
        setProducts([]);
      }
      setLoading(false);
    } else {
      // 如果已有数据，直接切换到对应平台的数据
      setProducts(productLists[platform]?.items?.item || []);
      setError(null);
    }
  };

  const currentList = productLists[selectedPlatform]
  const totalResults = currentList?.items?.total_results || 0

  // 平台列表图标
  const platformList = platforms.map((platform) => {
    if (platform === 'taobao') {
      return <div key={platform} className='flex items-center justify-center text-[14px] font-bold'>
        <Image src="/images/taobao.png" alt={dict?.platforms?.taobao || "淘宝"} width={20} height={20} className='mr-2 inline-block' />
        {dict?.search?.taobao || "淘宝"}
      </div>
    } else if (platform === '1688') {
      return <div key={platform} className='flex items-center justify-center text-[14px] font-bold'>
        <Image src="/images/1688.png" alt={dict?.platforms?.['1688'] || "阿里巴巴"} width={20} height={20} className='mr-2 inline-block' />
        {dict?.search?.['1688'] || "阿里巴巴"}
      </div>
    } else if (platform === 'jd') {
      return <div key={platform} className='flex items-center justify-center text-[14px] font-bold'>
        <Image src="/images/jd.png" alt={dict?.platforms?.jd || "京东"} width={20} height={20} className='mr-2 inline-block' />
        {dict?.search?.jd || "京东"}
      </div>
    } else if (platform === 'micro') {
      return <div key={platform} className='flex items-center justify-center text-[14px] font-bold'>
        <Image src="/images/micro.png" alt={dict?.platforms?.micro || "微店"} width={20} height={20} className='mr-2 inline-block' />
        {dict?.search?.micro || "微店"}
      </div>
    }
    return null;
  })
  const platformItems = platforms.map((_, index) => ({
    key: index.toString(),
    label: '',
    icon: platformList[index]
  }))

  const handleRetry = () => {
    const loadData = async () => {
      // 取消之前的请求
      if (currentRequestRef.current) {
        currentRequestRef.current.cancel('重试取消了之前的请求');
      }

      // 创建新的取消令牌
      const source = axios.CancelToken.source();
      currentRequestRef.current = source;

      setLoading(true)
      setError(null)
      const res = await fetchData(selectedPlatform, query, currentPage, pageSize, imgid, source.token)

      // 如果请求被取消，不更新状态
      if (res.cancelled) {
        console.log('重试请求被取消，不更新状态');
        return;
      }

      if (res.success && res.data) {
        let data = res.data
        setProductLists(prev => ({
          ...prev,
          [selectedPlatform]: data
        }))
        setProducts(data?.items?.item || [])
        setError(null) // 确保清除错误状态
      } else {
        setError({
          message: res.error || ToastHelper.getLocalizedMessage('load_failed', lng),
          isTimeout: res.isTimeout || false
        })
        setProducts([])
      }
      setLoading(false)
    }
    loadData()
  }

  const handleManualEntry = () => {
    router.push(`/${lng}/selfservice`)
  }

  const Results = () => {
    if (loading) {
      return <Loading />
    }

    if (error) {
      return (
        <EmptyState
          type={error.isTimeout ? 'timeout' : 'error'}
          showRetry={true}
          onRetry={handleRetry}
          showManualEntry={true}
          onManualEntry={handleManualEntry}
          dict={dict}
        />
      )
    }

    if (products.length === 0) {
      return (
        <EmptyState
          type="no-results"
          showRetry={true}
          onRetry={handleRetry}
          showManualEntry={true}
          onManualEntry={handleManualEntry}
          dict={dict}
        />
      )
    }

    return (
      <>
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap gap-4 mb-8">
            {products.map((product: any) => (
              <div key={product.num_iid} style={{ width: 'calc(20% - 14px)' }} className="mb-2">
                <ProductCard
                  product={{
                    ...product,
                    recommend: product.recommend // 确保传递推荐字段
                  }}
                  platform={selectedPlatform}
                  dict={dict}
                />
              </div>
            ))}
          </div>
        </div>
        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(totalResults / pageSize)}
          pageSize={pageSize}
          onPageChange={handlePageChange}
        />
      </>
    )
  }

  return (
    <div className='container mx-auto px-4'>
      <div className="mb-4 text-2xl text-center mt-10">
        {!error && !loading && products.length > 0 ? (
          dict?.search?.foundProducts
            ? dict.search.foundProducts
              .replace('{platform}', ` "${selectedPlatform}" `)
              .replace('{query}', `"${query}"` || '')
              .replace('{count}', totalResults.toString())
            : ` 找到 ${totalResults} 个商品 `
        ) : (
          dict?.search?.searchResults || '搜索结果'
        )}
      </div>

      <Tabs activeKey={activeTab.toString()} onChange={handleTabChange} size="large" items={platformItems}>
      </Tabs>
      <Results />
    </div>
  )
} 