'use client'

import { useEffect, useState } from 'react'
import Button from '@/components/Button'
import Tabs from '@/components/Tabs'
import Pagination from '@/components/Pagination'
import { Api } from '@/request/api'
import { TabsProps } from 'antd'
import { formatCurrency } from '@/utils/currency';
import { useParams } from 'next/navigation';
import { getDictionary } from "@/dictionaries";
// 定义返佣记录接口
interface ReferralRecord {
  id: number
  user_id: number
  reg_user_id: number
  regemail: string
  regip: string
  regtime: number
  successtime: number
  createtime: string
  status: number
  consume_total: number
}

// 定义邀请记录接口
interface InviteRecord {
  id: string
  regemail: string
  createtime: string
  createtime_text: string
  amount?: number
}

// 定义返佣接口返回值
interface CashbackResponse {
  data: ReferralRecord[]
  last_page: number
  total?: number
  per_page?: number
}



export default function ReferralPage() {
  const [activeTab, setActiveTab] = useState<string>('1')
  const [loading, setLoading] = useState<boolean>(false)
  const [totalAmount, setTotalAmount] = useState<number>(0)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [totalPages, setTotalPages] = useState<number>(1)
  const [recordList, setRecordList] = useState<ReferralRecord[]>([])
  const [unregisteredInvites, setUnregisteredInvites] = useState<InviteRecord[]>([])
  const [registeredInvites, setRegisteredInvites] = useState<InviteRecord[]>([])
  const [sendingEmail, setSendingEmail] = useState<boolean>(false)
  const [emailInput, setEmailInput] = useState<string>('')
  const [showEmailForm, setShowEmailForm] = useState<boolean>(false)
  const { lng } = useParams();
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
  const [dict, setDict] = useState<any>(null); // 添加字典状态
  // 异步获取字典数据
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };

    fetchDictionary();
  }, [lng]);

  // 获取返佣总额
  const fetchCashbackTotal = async () => {
    try {
      // @ts-ignore - API类型不完整
      const response = await Api.getCashbackTotal()
      if (response) {
        // 新API返回的总额字段
        let total = response.data?.total || response.total || 0
        setTotalAmount(total)
      }
    } catch (error) {
      console.error('获取返佣总额失败', error)
    }
  }

  // 获取返佣记录列表
  const fetchCashbackList = async () => {
    setLoading(true)
    try {
      // @ts-ignore - API类型不完整
      const response = await Api.getCashbackList() as CashbackResponse
      if (response) {
        setRecordList(response.data || [])
        setTotalPages(response.last_page || 1)
      }
    } catch (error) {
      console.error('获取返佣列表失败', error)
    } finally {
      setLoading(false)
    }
  }



  // 获取邀请记录（按状态）
  const fetchReferralList = async (status: number) => {
    setLoading(true)
    try {
      const response = await Api.getReferralList({ size: 100, status: status as 0 | 1 | 2 })
      if (response.success) {
        if (status === 0) {
          setUnregisteredInvites(response.data.data)
        } else if (status === 2) {
          setRegisteredInvites(response.data.data)
        }
      }
    } catch (error) {
      console.error(`获取状态 ${status} 的邀请记录失败`, error)
    } finally {
      setLoading(false)
    }
  }

  // 发送邮件邀请
  const handleSendInviteEmail = async () => {
    if (!emailInput || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailInput)) {
      alert(dict?.dashboard?.referral?.emailError)
      return
    }
    
    setSendingEmail(true)
    try {
      // @ts-ignore - API类型不完整
      const response = await Api.emailAdd({ email: emailInput })
      if (response.success) {
        alert(dict?.dashboard?.referral?.sendSuccess)
        setEmailInput('')
        setShowEmailForm(false)
        // 重新获取邀请列表
        fetchReferralList(0)
      }else{
        // alert(dict?.dashboard?.referral?.sendFail)
        alert(response.data)
      }
    } catch (error) {
      console.error('发送邀请邮件失败', error)
      alert(dict?.dashboard?.referral?.sendFail)
    } finally {
      setSendingEmail(false)
    }
  }

  // 重新发送邀请邮件
  const handleResendInvite = async (email: string) => {
    try {
      // @ts-ignore - API类型不完整
      const response = await Api.sendInvitefriendsEmail({ email })
      if (response) {
        alert(`${dict?.dashboard?.referral?.resendSuccess} ${email}`)
      }
    } catch (error) {
      console.error('重新发送邀请邮件失败', error)
      alert(dict?.dashboard?.referral?.sendFail)
    }
  }

  // 复制推广链接
  const handleCopyLink = async () => {
    let registerUrl = `${window.location.origin}/register`
    // 实际项目中应从API获取推广链接
    let promoLink = `${registerUrl}?ref=${Math.random().toString(36).substring(2, 10)}`
    if(!isTp5){
      try {
        let res = await Api.getInviteLink()
        if(res.success){
            promoLink = registerUrl + res.data.link
        }
      }catch (error) {
        console.log(error,'获取推广链接:失败')
      }
    }
    console.log(promoLink,'推广链接')
    navigator.clipboard.writeText(promoLink)
      .then(() => alert(dict?.dashboard?.referral?.copySuccess))
      .catch(() => alert(dict?.dashboard?.referral?.copyFail))      
  

  }

  useEffect(() => {
    // 获取返佣总额
    fetchCashbackTotal()
    
    // 根据当前标签页加载数据
    if (activeTab === '1') {
      fetchCashbackList()
    } else if (activeTab === '2') {
      fetchReferralList(0) // 未注册
    } else if (activeTab === '3') {
      fetchReferralList(2) // 已注册并激活
    }
  }, [activeTab, currentPage])

  const handleTabChange: TabsProps['onChange'] = (key) => {
    setActiveTab(key)
    setCurrentPage(1)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const items = [
    {
      key: '1',
      label: dict?.dashboard?.referral?.recordTitle,
      children: (
        <div className="mt-4">
          {loading ? (
            <div className="flex items-center justify-center py-20">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500"></div>
            </div>
          ) : recordList.length > 0 ? (
            <div>
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gray-50 text-gray-500 text-sm">
                    <th className="py-4 px-6 text-left font-medium">{dict?.dashboard?.referral?.recordId}</th>
                    <th className="py-4 px-6 text-left font-medium">{dict?.dashboard?.referral?.subordinateUsers}</th>
                    <th className="py-4 px-6 text-left font-medium">{dict?.dashboard?.referral?.regTime}</th>
                    <th className="py-4 px-6 text-left font-medium">{dict?.dashboard?.referral?.spendAmount}</th>
                    <th className="py-4 px-6 text-left font-medium">{dict?.dashboard?.referral?.status}</th>
                  </tr>
                </thead>
                <tbody>
                  {recordList.map((record) => (
                    <tr key={record.id} className="border-t border-gray-100">
                      <td className="py-4 px-6">{record.id}</td>
                      <td className="py-4 px-6">{record.regemail}</td>
                      <td className="py-4 px-6 text-gray-500">{record.createtime}</td>
                      <td className="py-4 px-6">{formatCurrency(Number(record.consume_total)).formatValue}</td>
                      <td className="py-4 px-6">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          record.status === 1 ? 'bg-yellow-100 text-yellow-800' :
                          record.status === 2 ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {record.status === 1 ? dict?.dashboard?.referral?.statusActive :
                           record.status === 2 ? dict?.dashboard?.referral?.statusCompleted :
                           dict?.dashboard?.referral?.statusInactive}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              <Pagination 
                currentPage={currentPage} 
                totalPages={totalPages} 
                onPageChange={handlePageChange} 
              />
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-20 text-gray-500">
              <div className="flex justify-center mb-4">
                <div className="i-heroicons:currency-dollar text-6xl text-gray-300"></div>
              </div>
              <p> {dict?.dashboard?.referral?.noRecords}</p>
            </div>
          )}
        </div>
      ),
    },
    {
      key: '2',
      label: dict?.dashboard?.referral?.pendingStatus,
      children: (
        <div className="mt-4">
          {loading ? (
            <div className="flex items-center justify-center py-20">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500"></div>
            </div>
          ) : unregisteredInvites.length > 0 ? (
            <div>
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gray-50 text-gray-500 text-sm">
                    <th className="py-4 px-6 text-left font-medium"> {dict?.dashboard?.referral?.inviteEmail}</th>
                    <th className="py-4 px-6 text-left font-medium"> {dict?.dashboard?.referral?.inviteTime}</th>
                    <th className="py-4 px-6 text-left font-medium"> {dict?.dashboard?.referral?.action}</th>
                  </tr>
                </thead>
                <tbody>
                  {unregisteredInvites.map((invite) => (
                    <tr key={invite.id} className="border-t border-gray-100">
                      <td className="py-4 px-6">{invite.regemail}</td>
                      <td className="py-4 px-6 text-gray-500">{invite.createtime_text || invite.createtime}</td>
                      <td className="py-4 px-6">
                        <Button 
                          type="link" 
                          size="small" 
                          onClick={() => handleResendInvite(invite.regemail)}
                        >
                          {dict?.dashboard?.referral?.resend}
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-20 text-gray-500">
              <div className="flex justify-center mb-4">
                <div className="i-heroicons:user-plus text-6xl text-gray-300"></div>
              </div>
              <p> {dict?.dashboard?.referral?.noPending}</p>
            </div>
          )}
        </div>
      ),
    },
    {
      key: '3',
      label: dict?.dashboard?.referral?.registeredStatus,
      children: (
        <div className="mt-4">
          {loading ? (
            <div className="flex items-center justify-center py-20">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500"></div>
            </div>
          ) : registeredInvites.length > 0 ? (
            <div>
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gray-50 text-gray-500 text-sm">
                    <th className="py-4 px-6 text-left font-medium">{dict?.dashboard?.referral?.userEmail}</th>
                    <th className="py-4 px-6 text-left font-medium">{dict?.dashboard?.referral?.regTime}</th>
                    {/* <th className="py-4 px-6 text-left font-medium">{dict?.dashboard?.referral?.spendAmount}</th> */}
                  </tr>
                </thead>
                <tbody>
                  {registeredInvites.map((invite) => (
                    <tr key={invite.id} className="border-t border-gray-100">
                      <td className="py-4 px-6">{invite.regemail}</td>
                      <td className="py-4 px-6 text-gray-500">{invite.createtime}</td>
                      {/* <td className="py-4 px-6 text-orange-500">{formatCurrency(Number(invite.amount || 0)).formatValue}</td> */}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-20 text-gray-500">
              <div className="flex justify-center mb-4">
                <div className="i-heroicons:user-circle text-6xl text-gray-300"></div>
              </div>
              <p> {dict?.dashboard?.referral?.noRegistered}</p>
            </div>
          )}
        </div>
      ),
    },
  ]

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex flex-wrap items-center gap-6 mb-6">
        <div className="flex items-center">
          <div className="i-ant-design:money-collect-outlined text-4xl text-orange-500 mr-3"></div>
          <div>
            <div className="flex items-baseline">
              <span className="text-2xl font-bold text-orange-500">{formatCurrency(Number(totalAmount)).formatValue}</span>
            </div>
            <div className="text-sm text-gray-500 mt-1">{dict?.dashboard?.referral?.totalCommission}</div>
          </div>
        </div>
        
        <div className="ml-auto flex flex-wrap gap-3">
          <Button type="primary" className="flex items-center" onClick={handleCopyLink}>
            <span className="i-ant-design:link-outlined mr-1"></span>
            {dict?.dashboard?.referral?.copyLink}
          </Button>
          <Button 
            icon={<span className="i-ant-design:mail-outlined"></span>} 
            className="flex items-center" 
            onClick={() => setShowEmailForm(!showEmailForm)}
          >
             {dict?.dashboard?.referral?.sendInvite}
          </Button>
        </div>
      </div>

      {showEmailForm && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex flex-wrap items-center gap-3">
            <div className="flex-grow">
              <input
                type="email"
                value={emailInput}
                onChange={(e) => setEmailInput(e.target.value)}
                placeholder= {dict?.dashboard?.referral?.emailHint}
                className="w-full p-2 border border-gray-200 rounded-md"
              />
            </div>
            <Button 
              type="primary" 
              onClick={handleSendInviteEmail}
              loading={sendingEmail}
            >
             {dict?.dashboard?.referral?.sendBtn}
            </Button>
          </div>
        </div>
      )}

      <Tabs defaultActiveKey="1" items={items} onChange={handleTabChange} />
    </div>
  )
}
  