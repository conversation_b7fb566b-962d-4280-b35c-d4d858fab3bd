'use client'
import { Avatar, Input, Radio, DatePicker, Select, Form, Upload, message as antMessage } from 'antd'
import Tabs from '@/components/Tabs'
import Button from '@/components/Button'
import { LockOutlined, SafetyOutlined, MailOutlined, UploadOutlined, LoadingOutlined, PlusOutlined } from '@ant-design/icons'
import { useState, useEffect, useRef } from 'react'
import dayjs from 'dayjs'
import {Api,AddressItem} from '@/request/api'
import Message from '@/components/CustomMessage'
import Modal from '@/components/Modal'
import Loading from '@/components/Loading'
import { getDictionary } from '@/dictionaries'
import type { Locale } from '@/config'
import { useParams, useRouter } from 'next/navigation'
import Cookies from 'js-cookie'
import { md5 } from 'js-md5'
import { UpperCaseFirst } from '@/utils';
import { useCartStore } from '@/store/cartStore';
import Toast from '@/components/Toast';

import {  Empty, Spin, message } from 'antd';
import AddressCard from './components/AddressCard';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import AddressModal from '@/components/AddressModal';

// 地址数据接口
interface Address {
  id: number;
  user_id: number;
  consignee: string;
  area_id: number;
  address: string;
  zipcode: string;
  telephone: string;
  isdefault: number;
  createtime: number;
  updatetime: number;
  province: {
    id: number;
    pid: number;
    name: string;
    level: number;
    langcode: string;
  };
  country: {
    id: number;
    pid: number;
    name: string;
    level: number;
    langcode: string;
  };
  mergename: string;
}


export default function AccountPage() {
  const params = useParams();
  const router = useRouter();
  const lng = params.lng as Locale || 'zh-cn';
  const [dict, setDict] = useState<any>(null);

  useEffect(() => {
    const loadDict = async () => {
      const dictionary = await getDictionary(lng);
      setDict(dictionary);
    };
    loadDict();
  }, [lng]);

  const [activeTab, setActiveTab] = useState('profile')
  const [user, setUser] = useState<any>(null)
  const [avatar, setAvatar] = useState<string | undefined>(undefined)
  const [form] = Form.useForm()
  const [initialValues, setInitialValues] = useState<any>({})
  const [saveLoading, setSaveLoading] = useState(false)
  const [pwdModalOpen, setPwdModalOpen] = useState(false)
  const [pwdLoading, setPwdLoading] = useState(false)
  const [oldPwd, setOldPwd] = useState('')
  const [newPwd, setNewPwd] = useState('')
  const [renewPwd, setRenewPwd] = useState('')
  const [mobileModalOpen, setMobileModalOpen] = useState(false)
  const [newMobile, setNewMobile] = useState('')
  const [mobileCode, setMobileCode] = useState('')
  const [mobileLoading, setMobileLoading] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [mcodeList, setMcodeList] = useState<Array<{id: number, name: string, code: string}>>([])
  const [selectedMcode, setSelectedMcode] = useState('')
  const [selectedMcodeDisplay, setSelectedMcodeDisplay] = useState('')
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const [emailModalOpen, setEmailModalOpen] = useState(false)
  const [newEmail, setNewEmail] = useState('')
  const [emailCode, setEmailCode] = useState('')
  const [emailLoading, setEmailLoading] = useState(false)
  const [emailCountdown, setEmailCountdown] = useState(0)
  const emailTimerRef = useRef<NodeJS.Timeout | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploading, setUploading] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
  const {  fetchCartCount } = useCartStore();

  // 支付密码相关状态
  const [payPasswordModalOpen, setPayPasswordModalOpen] = useState(false)
  const [payPasswordLoading, setPayPasswordLoading] = useState(false)
  const [hasPayPassword, setHasPayPassword] = useState(false)
  const [payPasswordPluginEnabled, setPayPasswordPluginEnabled] = useState(false)
  const [isCreatePayPassword, setIsCreatePayPassword] = useState(true)
  const [newPayPassword, setNewPayPassword] = useState('')
  const [confirmPayPassword, setConfirmPayPassword] = useState('')
  // 支付密码修改验证码相关状态
  const [payPasswordVerifyCode, setPayPasswordVerifyCode] = useState('')
  const [payPasswordCodeCountdown, setPayPasswordCodeCountdown] = useState(0)
  const payPasswordCodeTimerRef = useRef<NodeJS.Timeout | null>(null)

  // 地址相关状态
  const [loading, setLoading] = useState(true);
  const [showAddressModal, setShowAddressModal] = useState(false);
  const [addresses, setAddresses] = useState<AddressItem[]>([]);
  const [currentAddress, setCurrentAddress] = useState<AddressItem | null>(null);
  // Tab items
  const tabItems = [
    { key: 'profile', label: dict?.dashboard?.account?.title },
    { key: 'security', label: dict?.dashboard?.account?.security },
    { key: 'address', label: dict?.dashboard?.address?.text },
  ]

  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        setPageLoading(true);
        const res = await Api.getUserInfo()
        if (res?.success) {
          const userinfo = res.data
          setUser(userinfo)
          setAvatar(userinfo?.avatar ? (userinfo.avatar.startsWith('http') ? userinfo.avatar : `${process.env.NEXT_PUBLIC_BASE_URL}${userinfo.avatar}`) : undefined)
          const values = {
            username: userinfo?.nickname,
            gender: userinfo?.gender === 1 ? 'male' : 'female',
            phone: userinfo?.mobile,
            email: userinfo?.email,
            birthday: userinfo?.birthday ? dayjs(userinfo.birthday) : null,
          }
          console.log('Initial user info:', userinfo);
          console.log('Initial birthday from API:', userinfo?.birthday);
          console.log('Parsed birthday dayjs object:', userinfo?.birthday ? dayjs(userinfo.birthday) : null);
          console.log('Form initial values:', values);
          setInitialValues(values)
          form.setFieldsValue(values)
        }
      } catch (e) {
        console.error(e)
      } finally {
        setPageLoading(false);
      }
    }
    fetchUserInfo()

    // 检查支付密码插件状态和是否已设置支付密码
    checkPayPasswordPlugin()
  }, [form])

  // 检查支付密码插件状态
  const checkPayPasswordPlugin = async () => {
    try {
      // 使用新的插件管理工具检查插件是否启用
      const { isPluginEnabled } = await import('@/utils/plugin');
      const pluginEnabled = await isPluginEnabled('paypassword');

      if (pluginEnabled) {
        setPayPasswordPluginEnabled(true)
        // 检查是否已设置支付密码
        const hasPasswordRes = await Api.hasPayPassword()
        if (hasPasswordRes.success) {
          setHasPayPassword(hasPasswordRes.data?.has_password || false)
        }
      }
    } catch (error) {
      console.error('检查支付密码插件状态失败:', error)
    }
  }

  // 打开支付密码设置/修改弹窗
  const openPayPasswordModal = (isCreate: boolean) => {
    setIsCreatePayPassword(isCreate)
    setNewPayPassword('')
    setConfirmPayPassword('')
    setPayPasswordVerifyCode('')
    setPayPasswordModalOpen(true)
  }

  // 处理支付密码设置/修改
  const handlePayPasswordSubmit = async () => {
    // 验证密码格式
    const passwordRegex = /^\d{6}$/
    if (!passwordRegex.test(newPayPassword)) {
      Toast.error(dict?.dashboard?.account?.payPassword?.passwordFormat)
      return
    }

    if (newPayPassword !== confirmPayPassword) {
      Toast.error(dict?.dashboard?.account?.payPassword?.passwordMismatch)
      return
    }

    // 修改支付密码时需要验证码
    if (!isCreatePayPassword && !payPasswordVerifyCode) {
      Toast.error(dict?.dashboard?.account?.payPassword?.verifyCodeRequired || '请输入验证码')
      return
    }

    try {
      setPayPasswordLoading(true)
      setPageLoading(true)

      if (isCreatePayPassword) {
        // 创建支付密码
        const res = await Api.createPayPassword({ password: newPayPassword })
        if (res.success) {
          Toast.success(dict?.dashboard?.account?.payPassword?.setSuccess)
          setHasPayPassword(true)
          setPayPasswordModalOpen(false)
        } else {
          Toast.error(res.msg || dict?.dashboard?.account?.payPassword?.setFail)
        }
      } else {
        // 修改支付密码，只需要验证码验证，不需要当前密码
        const res = await Api.changePayPassword({
          password: newPayPassword,
          captcha: payPasswordVerifyCode
        })
        if (res.success) {
          Toast.success(dict?.dashboard?.account?.payPassword?.changeSuccess)
          setPayPasswordModalOpen(false)
          // 清空验证码相关状态
          setPayPasswordVerifyCode('')
          setPayPasswordCodeCountdown(0)
          if (payPasswordCodeTimerRef.current) {
            clearInterval(payPasswordCodeTimerRef.current)
          }
        } else {
          Toast.error(res.msg || dict?.dashboard?.account?.payPassword?.changeFail)
        }
      }
    } catch (error) {
      console.error('支付密码操作失败:', error)
      Toast.error(isCreatePayPassword ? dict?.dashboard?.account?.payPassword?.setFail : dict?.dashboard?.account?.payPassword?.changeFail)
    } finally {
      setPayPasswordLoading(false)
      setPageLoading(false)
    }
  }

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
      if (emailTimerRef.current) {
        clearInterval(emailTimerRef.current)
      }
      if (payPasswordCodeTimerRef.current) {
        clearInterval(payPasswordCodeTimerRef.current)
      }
    }
  }, [])

  const startCountdown = () => {
    setCountdown(60)
    timerRef.current = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          if (timerRef.current) {
            clearInterval(timerRef.current)
          }
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }

  const startEmailCountdown = () => {
    setEmailCountdown(60)
    emailTimerRef.current = setInterval(() => {
      setEmailCountdown((prev) => {
        if (prev <= 1) {
          if (emailTimerRef.current) {
            clearInterval(emailTimerRef.current)
          }
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }

  const startPayPasswordCodeCountdown = () => {
    setPayPasswordCodeCountdown(60)
    payPasswordCodeTimerRef.current = setInterval(() => {
      setPayPasswordCodeCountdown((prev) => {
        if (prev <= 1) {
          if (payPasswordCodeTimerRef.current) {
            clearInterval(payPasswordCodeTimerRef.current)
          }
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }

  // 发送支付密码修改验证码
  const handleSendPayPasswordCode = async () => {
    if (!user?.email) {
      Message.error(dict?.dashboard?.account?.payPassword?.noEmail || '请先绑定邮箱')
      return
    }
    try {
      const res = await Api.sendEmailCode({
        email: user.email,
        event: 'resetpaypassword'
      })
      if (res?.success) {
        Message.success(dict?.dashboard?.account?.codeSent)
        startPayPasswordCodeCountdown()
      } else {
        Message.error(res?.msg || dict?.dashboard?.account?.sendFail)
      }
    } catch (e) {
      console.error(e)
      Message.error(dict?.dashboard?.account?.sendFail)
    }
  }

  const handleSave = async () => {
    try {
      setSaveLoading(true);
      setPageLoading(true);
      const values = await form.validateFields()

      // 调试：打印生日值
      console.log('Form values:', values);
      console.log('Birthday value:', values.birthday);
      console.log('Formatted birthday:', values.birthday ? values.birthday.format('YYYY-MM-DD') : '');

      const requestData = {
        nickname: values.username,
        avatar: avatar || '',
        gender: values.gender === 'male' ? 1 : 2,
        birthday: values.birthday ? values.birthday.format('YYYY-MM-DD') : '',
      };

      console.log('Request data being sent to API:', requestData);

      const res = await Api.editUserInfo(requestData)
      if (res?.success) {
        Message.success(dict?.dashboard?.account?.saveSuccess)
        // 重新获取用户信息
        getUserInfo()
      } else {
        Message.error(res?.msg || dict?.dashboard?.account?.saveFail)
      }
    } catch (e) {
      console.log('Error in handleSave:', e)
    } finally {
      setSaveLoading(false);
      setPageLoading(false);
    }
  }
  const getUserInfo = async () => {
    try {
      const userInfoRes = await Api.getUserInfo()
      if (userInfoRes?.success) {
        const userinfo = userInfoRes.data
        setUser(userinfo)
        setAvatar(userinfo?.avatar)
        const values = {
          username: userinfo?.nickname,
          gender: userinfo?.gender === 1 ? 'male' : 'female',
          phone: userinfo?.mobile,
          email: userinfo?.email,
          birthday: userinfo?.birthday ? dayjs(userinfo.birthday) : null,
          country: '中国',
        }
        setInitialValues(values)
        form.setFieldsValue(values)
      }
    }catch (e) {
      console.log(e)
    }
  }

  const handlePwdChange = async () => {
    // 密码长度为6-16位，必须包含大小写字母和数字
    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[\x21-\x7E]{6,16}$/;
    if (!regex.test(newPwd)) {
      Toast.error('密码长度为6-16位，必须包含大小写字母和数字')
      return;
    }
    if (!oldPwd || !newPwd || !renewPwd) return Message.error(dict?.dashboard?.account?.formError)
    if (newPwd !== renewPwd) return Message.error(dict?.dashboard?.account?.pwdMismatch)
    setPwdLoading(true)
    setPageLoading(true);
    let data = {
        renew_password: md5(newPwd),
    }
    if(isTp5){
       data = Object.assign(data, {  old_password: md5(oldPwd),  new_password: md5(newPwd), })
    }else{
       data = Object.assign(data, {  oldpassword: md5(oldPwd),  newpassword: md5(newPwd), })
    }
    const res = await (Api as any).changePwd(data)
    setPwdLoading(false)
    setPageLoading(false);
    if (res?.success) {
      Message.success(dict?.dashboard?.account?.updateSuccess)
      logout()
      setPwdModalOpen(false)
      setOldPwd('')
      setNewPwd('')
      setRenewPwd('')
    } else {
      Message.error(res?.msg || dict?.dashboard?.account?.updateFail)
    }
  }
  const logout = async () => {
    try {
      await Api.logout();

      // 清理本地存储的用户信息
      if (typeof window !== 'undefined') {
        localStorage.removeItem('info');
        // 根据后端类型清理对应的token cookie
        const tokenName = process.env.NEXT_PUBLIC_BACKEND_TYPE === '6' ? 'access_token' : 'token';
        Cookies.remove(tokenName, { path: '/' });
      }

      // 重置购物车数量
      fetchCartCount();

      // 跳转到登录页面
      const loginUrl = `/${lng}/login`;
      router.push(loginUrl);

      // 建议刷新页面以确保完全清理状态
      setTimeout(() => {
        if (typeof window !== 'undefined') {
          window.location.reload();
        }
      }, 100);

    } catch (error) {
      console.error('退出登录失败:', error);
      // 即使API调用失败，也要清理本地状态并跳转
      if (typeof window !== 'undefined') {
        localStorage.removeItem('info');
        const tokenName = process.env.NEXT_PUBLIC_BACKEND_TYPE === '6' ? 'access_token' : 'token';
        Cookies.remove(tokenName, { path: '/' });
      }

      const loginUrl = `/${lng}/login`;
      router.push(loginUrl);

      // 建议刷新页面
      setTimeout(() => {
        if (typeof window !== 'undefined') {
          window.location.reload();
        }
      }, 100);
    }
  }

  // 获取区号列表
  useEffect(() => {
    const fetchMcode = async () => {
      try {
        const res = isTp5 ? await Api.getMcode() : await Api.getConfigList()
        if (res?.success && res.data) {
          let data = res.data.mobile_area_list || res.data
          setMcodeList(data)
          if (data.length > 0) {
            setSelectedMcode(data[0].code)
            setSelectedMcodeDisplay(data[0].code || data[0].name)
          }
        }
      } catch (e) {
        console.error(e)
      }
    }
    if (mobileModalOpen) {
      fetchMcode()
    }
  }, [mobileModalOpen])

  const handleSendCode = async () => {
    if (!newMobile) {
      return Message.error(dict?.dashboard?.account?.phoneHint)
    }
    try {
      const res = await Api.sendMobileCode({
        mobile: newMobile,
        mobilecode: selectedMcode,
        event: 'changemobile'
      })
      if (res?.success) {
        Message.success(dict?.dashboard?.account?.codeSent)
        startCountdown()
      } else {
        Message.error(res?.msg || dict?.dashboard?.account?.sendFail)
      }
    } catch (e) {
      console.error(e)
    }
  }

  const handleChangeMobile = async () => {
    if (!newMobile || !mobileCode) {
      return Message.error(dict?.dashboard?.account?.formError)
    }
    try {
      setMobileLoading(true)
      setPageLoading(true);
      const res = await Api.changeMobile({
        mobile: newMobile,
        mobilecode: selectedMcode,
        captcha: mobileCode
      })
      if (res?.success) {
        Message.success(dict?.dashboard?.account?.updateSuccess)
        setMobileModalOpen(false)
        setNewMobile('')
        setMobileCode('')
        // 重新获取用户信息
        const userInfoRes = await Api.getUserInfo()
        if (userInfoRes?.success) {
          const userinfo = userInfoRes.data
          setUser(userinfo)
          const values = {
            username: userinfo?.nickname,
            gender: userinfo?.gender === 1 ? 'male' : 'female',
            phone: userinfo?.mobile,
            email: userinfo?.email,
            birthday: userinfo?.birthday ? dayjs(userinfo.birthday) : null,
            country: '中国',
          }
          setInitialValues(values)
          form.setFieldsValue(values)
        }
      } else {
        Message.error(res?.msg || dict?.dashboard?.account?.updateFail)
      }
    } catch (e) {
      console.error(e)
    } finally {
      setMobileLoading(false)
      setPageLoading(false);
    }
  }

  const handleSendEmailCode = async () => {
    if (!newEmail) {
      return Message.error(dict?.dashboard?.account?.emailHint)
    }
    try {
      const res = await Api.sendEmailCode({
        email: newEmail,
        event: 'changeemail'
      })
      if (res?.success) {
        Message.success(dict?.dashboard?.account?.codeSent)
        startEmailCountdown()
      } else {
        Message.error(res?.msg || dict?.dashboard?.account?.sendFail)
      }
    } catch (e) {
      console.error(e)
    }
  }

  const handleChangeEmail = async () => {
    if (!newEmail || !emailCode) {
      return Message.error(dict?.dashboard?.account?.formError)
    }
    try {
      setEmailLoading(true)
      setPageLoading(true);
      const res = await Api.changeEmail({
        email: newEmail,
        captcha: emailCode
      })
      if (res?.success) {
        Message.success(dict?.dashboard?.account?.updateSuccess)
        setEmailModalOpen(false)
        setNewEmail('')
        setEmailCode('')
        // 重新获取用户信息
        const userInfoRes = await Api.getUserInfo()
        if (userInfoRes?.success) {
          const userinfo = userInfoRes.data
          setUser(userinfo)
          const values = {
            username: userinfo?.nickname,
            gender: userinfo?.gender === 1 ? 'male' : 'female',
            phone: userinfo?.mobile,
            email: userinfo?.email,
            birthday: userinfo?.birthday ? dayjs(userinfo.birthday) : null,
            country: '中国',
          }
          console.log('values:', values);
          setInitialValues(values)
          form.setFieldsValue(values)
        }
      } else {
        Message.error(res?.msg || dict?.dashboard?.account?.updateFail)
      }
    } catch (e) {
      console.error(e)
    } finally {
      setEmailLoading(false)
      setPageLoading(false);
    }
  }

  const handleAvatarUpload = (info: any) => {
    console.log('Upload info:', info);
    
    if (info.file.status === 'uploading') {
      setUploading(true);
      return;
    }
    
    if (info.file.status === 'done') {
      setUploading(false);
      setPageLoading(false);
      if (info.file.response?.success || info.file.response?.code === 1) {
        const avatarUrl = info.file.response.data.fullurl || info.file.response.data.url;
        setAvatar(avatarUrl);
        Message.success('头像上传成功');
      } else {
        Message.error(info.file.response?.msg || dict?.dashboard?.account?.uploadFail);
      }
    } else if (info.file.status === 'error') {
      setUploading(false);
      setPageLoading(false);
      Message.error(dict?.dashboard?.account?.uploadFail);
    }
  };

  const beforeUpload = (file: any) => {
    console.log('Before upload file:', file);
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      Message.error(dict?.dashboard?.account?.avatarTypeError);
      return Upload.LIST_IGNORE;
    }
    
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      Message.error(dict?.dashboard?.account?.avatarSizeError);
      return Upload.LIST_IGNORE;
    }
    
    return true;
  };

  // 自定义上传处理
  const customUploadRequest = async (options: any) => {
    const { file, onSuccess, onError } = options;
    setUploading(true);
    setPageLoading(true);

    try {
      // 将文件转换为Base64格式
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = async (e) => {
        const base64String = e.target?.result as string;
        if (base64String) {
          // 使用uploadImage方法上传Base64格式的图片
          // todo 
           const response = isTp5? await Api.uploadImage(base64String): await Api.uploadImage(file);
          if (response.success || response.code === 1) {
            // 处理成功响应
            const imageUrl = response.data?.items?.item?.name || response.data?.path || '';
            setAvatar(imageUrl);
            onSuccess({ ...response, url: imageUrl }, file);
            Message.success(dict?.dashboard?.account?.avatarSuccess);
            
            // 保存头像到用户资料
            try {
              // 获取当前表单的值，确保生日字段正确传递
              const currentFormValues = form.getFieldsValue();
              console.log('Current form values for avatar save:', currentFormValues);

              const saveRes = await Api.editUserInfo({
                nickname: user?.nickname || '',
                avatar: imageUrl,
                gender: user?.gender === 1 ? 1 : 2,
                birthday: currentFormValues.birthday ? currentFormValues.birthday.format('YYYY-MM-DD') : (user?.birthday || ''),
              });

              console.log('Avatar save request data:', {
                nickname: user?.nickname || '',
                avatar: imageUrl,
                gender: user?.gender === 1 ? 1 : 2,
                birthday: currentFormValues.birthday ? currentFormValues.birthday.format('YYYY-MM-DD') : (user?.birthday || ''),
              });

              getUserInfo()
              if (!saveRes?.success) {
                Message.error(saveRes?.msg || dict?.dashboard?.account?.avatarSaveFail);
              }
            } catch (saveError) {
              console.error('保存头像失败:', saveError);
            }
          } else {
            // 处理失败响应
            onError(new Error(response.msg ||dict?.dashboard?.account?.uploadFail));
            Message.error(response.msg || dict?.dashboard?.account?.avatarUploadFail);
          }
        }
      };
    } catch (error) {
      console.error('上传错误:', error);
      onError(error);
      Message.error(dict?.dashboard?.account?.avatarUploadFail);
    } finally {
      setUploading(false);
      setPageLoading(false);
    }
  };

  const uploadButton = (
    <div>
      {uploading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>{dict?.dashboard?.account?.uploadTitle}</div>
    </div>
  );
  // ***处理地址相关逻辑***
  // 获取地址列表
  const fetchAddresses = async () => {
    try {
      setLoading(true);
      const response = await Api.getAddressList({});
      if (response.success) {
        setAddresses(response.data);
      } else {
        message.error(response.msg || '获取地址列表失败');
      }
    } catch (error) {
      console.error('获取地址列表失败:', error);
      message.error('获取地址列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAddresses();
  }, []);
  // 编辑地址
  const handleEdit = (id: number) => {
    const address = addresses.find(addr => addr.id === id);
    if (address) {
      console.log('编辑地址:', address);
      console.log('国家ID:', address.country_id);
      console.log('地区ID:', address.region_id);
      setCurrentAddress(address);
      setShowAddressModal(true);
    }
  };

  // 添加新地址
  const handleAddAddress = () => {
    setCurrentAddress(null);
    setShowAddressModal(true);
  };

  // 格式化地址详情
  const formatAddressDetail = (address: AddressItem) => {
    return `${address.address}, ${address.mergename}${address.zipcode ? `, ${address.zipcode}` : ''}`;
  };

  // 删除地址
  const handleDelete = async (id: number) => {
    const response = await Api.deleteAddress(id);
    if (response.success) {
      message.success(dict?.dashboard?.address?.deleteSuccess);
      fetchAddresses();
    } else {
      message.error(response.msg || dict?.dashboard?.address?.deleteFail);
    }
  };

  return (
    <>
      {pageLoading ? (
        <Loading height="400px" />
      ) : (
        <>
          <Tabs
            defaultActiveKey="profile"
            items={tabItems}
            activeKey={activeTab}
            onChange={setActiveTab}
          />
          {activeTab === 'profile' && (
            <Form
              form={form}
              initialValues={initialValues}
              layout="vertical"
              style={{ maxWidth: 500, margin: '0 auto', marginTop: 40 }}
            >
              <Form.Item>
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <Upload
                    name="file"
                    listType="picture-circle"
                    className="avatar-uploader"
                    showUploadList={false}
                    customRequest={customUploadRequest}
                    beforeUpload={beforeUpload}
                  >
                    {avatar ? (
                      <Avatar 
                        size={80} 
                        src={avatar}
                      />
                    ) : uploadButton}
                  </Upload>
                </div>
              </Form.Item>
              <Form.Item label={dict?.dashboard?.account?.username} name="username">
                <Input />
              </Form.Item>
              <Form.Item label={dict?.dashboard?.account?.gender}  name="gender">
                <Radio.Group>
                  <Radio value="male">{dict?.dashboard?.account?.male}</Radio>
                  <Radio value="female">{dict?.dashboard?.account?.female}</Radio>
                </Radio.Group>
              </Form.Item>
              <Form.Item label={dict?.dashboard?.account?.email}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Form.Item name="email" noStyle>
                    <Input disabled style={{ width: '100%' }} />
                  </Form.Item>
                  <Button type="primary" style={{ marginLeft: 16, flexShrink: 0 }} onClick={() => setEmailModalOpen(true)}>
                   {dict?.dashboard?.account?.modifyEmail}
                  </Button>
                </div>
              </Form.Item>
              {/* <Form.Item label= {dict?.dashboard?.account?.phone} >
                <div style={{ display: 'flex', alignItems: 'center' }}>
                   <Form.Item name="phone" noStyle>
                    <Input disabled style={{ width: '100%' }} />
                  </Form.Item>
                  <Button type="primary" style={{ marginLeft: 16 }} onClick={() => setMobileModalOpen(true)}>
                     {dict?.dashboard?.account?.modifyPhone}
                  </Button>
                </div>
              </Form.Item> */}
              <Form.Item
                label={dict?.dashboard?.account?.birthday}
                name="birthday"
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder={dict?.dashboard?.account?.birthdayPlaceholder || "请选择生日"}
                  format="YYYY-MM-DD"
                />
              </Form.Item>
             
              <Form.Item>
                <Button style={{ marginRight: 16 }} onClick={() => form.resetFields()}> {dict?.dashboard?.account?.reset}</Button>
                <Button type="primary" loading={saveLoading} onClick={handleSave}> {dict?.dashboard?.account?.save}</Button>
              </Form.Item>
            </Form>
          )}
          {activeTab === 'security' && (
            <div style={{ background: '#fff', borderRadius: 12, padding: 32, marginTop: 32, maxWidth: 900, marginLeft: 'auto', marginRight: 'auto' }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 32 }}>
                {
                  avatar ? <Avatar size={55}  src={`${user?.avatar}`} alt="avatar"  /> : <Avatar style={{ backgroundColor: process.env.NEXT_PUBLIC_BASE_COLOR, color: 'white' }} size={55}>{UpperCaseFirst(user?.nickname?.slice(0,1))}</Avatar>
                }
                <div style={{ marginLeft: 16 }}>
                  <div style={{ fontWeight: 600, fontSize: 20 }}>{user?.nickname}</div>
                  <div style={{ color: '#888', fontSize: 14 }}>ID: {user?.id}</div>
                </div>
                <div style={{ marginLeft: 'auto', display: 'flex', alignItems: 'center' }}>
                  <MailOutlined style={{ color: '#888', marginRight: 8 }} />
                  <span style={{ fontSize: 16 }}>{user?.email}</span>
                </div>
              </div>

              {/* 修改密码 */}
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
                <LockOutlined style={{ fontSize: 32, color: '#f97316', marginRight: 24 }} />
                <div style={{ flex: 1 }}>
                  <div style={{ fontWeight: 500 }}> {dict?.dashboard?.account?.changePwd}</div>
                  <div style={{ color: '#888', fontSize: 14 }}> {dict?.dashboard?.account?.pwdTips}</div>
                </div>
                <Button type="primary" onClick={() => setPwdModalOpen(true)}> {dict?.dashboard?.account?.changePwd}</Button>
              </div>

              {/* 支付密码 */}
              {payPasswordPluginEnabled && (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <SafetyOutlined style={{ fontSize: 32, color: '#f97316', marginRight: 24 }} />
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 500 }}>{dict?.dashboard?.account?.payPassword?.title}<div style={{ color: '#888', fontSize: 12, marginLeft: 4,display: 'inline-block' }}>
                      ({hasPayPassword ? dict?.dashboard?.account?.payPassword?.alreadySet : dict?.dashboard?.account?.payPassword?.notSet})
                    </div></div>
                    <div style={{ color: '#888', fontSize: 14 }}>{dict?.dashboard?.account?.payPassword?.tips}</div>
                    
                  </div>
                  <Button
                    type="primary"
                    onClick={() => openPayPasswordModal(!hasPayPassword)}
                  >
                    {hasPayPassword ? dict?.dashboard?.account?.payPassword?.change : dict?.dashboard?.account?.payPassword?.set}
                  </Button>
                </div>
              )}
            </div>
          )}
           {activeTab === 'address' && (
            <div style={{ background: '#fff', borderRadius: 12, padding: 32, marginTop: 32, maxWidth: 900, marginLeft: 'auto', marginRight: 'auto' }}>
              <AntdConfigProvider>
                  <div className="">
                    <div className="flex justify-end items-center mb-4">
                      {/* <h1 className="text-xl font-medium">{dict?.dashboard?.address?.title}</h1> */}
                      <Button 
                        type="primary" 
                        icon={<PlusOutlined />} 
                        onClick={handleAddAddress}
                        style={{ backgroundColor: '#FF6000', borderColor: '#FF6000' }}
                      >
                        {dict?.dashboard?.address?.addNew}
                      </Button>
                    </div>
                    {loading ? (
                      <div className="flex justify-center items-center min-h-[200px]">
                        <Spin size="large" />
                      </div>
                    ) : addresses.length > 0 ? (
                      <div className="grid grid-cols-1 gap-4">
                        {addresses.map((address) => (
                          <AddressCard
                            key={address.id}
                            recipient={address.consignee}
                            phone={address.telephone}
                            addressDetail={formatAddressDetail(address)}
                            isDefaultAddress={address.isdefault === 1}
                            onEdit={() => handleEdit(address.id)}
                            onDelete={() => handleDelete(address.id)}
                            dict={dict}
                          />
                        ))}
                      </div>
                    ) : (
                      <Empty 
                        description= {dict?.dashboard?.address?.empty}
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                      />
                    )}
                  </div>
                  {showAddressModal && (
                    <AddressModal
                      open={showAddressModal}
                      onCancel={() => {
                        setShowAddressModal(false);
                        setCurrentAddress(null);
                      }}
                      onSuccess={fetchAddresses}
                      currentAddress={currentAddress}
                      dict={dict}
                    />
                  )}
                </AntdConfigProvider>
            </div>
          )}
          <Modal
            title= {dict?.dashboard?.account?.changePwd}
            open={pwdModalOpen}
            onCancel={() => setPwdModalOpen(false)}
            onOk={handlePwdChange}
            confirmLoading={pwdLoading}
            centered
            okText={dict?.dashboard?.account?.confirm}
            cancelText={dict?.dashboard?.account?.cancel}
            maskClosable={false}
          >
            <Input.Password
              placeholder= {dict?.dashboard?.account?.oldPwd}
              value={oldPwd}
              onChange={e => setOldPwd(e.target.value)}
              style={{ marginBottom: 12 }}
            />
            <Input.Password
              placeholder= {dict?.dashboard?.account?.newPwd}
              value={newPwd}
              onChange={e => setNewPwd(e.target.value)}
              style={{ marginBottom: 12 }}
            />
            <Input.Password
              placeholder= {dict?.dashboard?.account?.confirmPwd}
              value={renewPwd}
              onChange={e => setRenewPwd(e.target.value)}
            />
          </Modal>

          {/* 修改手机号Modal */}
          <Modal
            title={dict?.dashboard?.account?.changeMobile.title}
            open={mobileModalOpen}
            onCancel={() => setMobileModalOpen(false)}
            onOk={handleChangeMobile}
            confirmLoading={mobileLoading}
            centered
            okText={dict?.dashboard?.account?.confirm}
            cancelText={dict?.dashboard?.account?.cancel}
          >
            <div style={{ display: 'flex', marginBottom: 12 }}>
              <Select
                value={selectedMcodeDisplay}
                onChange={(value, option: any) => {
                  const selectedItem = mcodeList.find(item => item.name === value)
                  if (selectedItem) {
                    setSelectedMcode(selectedItem.code)
                    setSelectedMcodeDisplay(selectedItem.code || selectedItem.name)
                  }
                }}
                style={{ width: 120, marginRight: 12 }}
              >
                {mcodeList.map(item => (
                  <Select.Option key={item.id} value={item.name}>
                    {item.name}
                  </Select.Option>
                ))}
              </Select>
              <Input
                placeholder={dict?.dashboard?.account?.changeMobile?.newMobile}
                value={newMobile}
                onChange={e => setNewMobile(e.target.value)}
                style={{ flex: 1 }}
              />
            </div>
            <div style={{ display: 'flex' }}>
              <Input
                placeholder={dict?.dashboard?.account?.changeMobile?.verificationCode}
                value={mobileCode}
                onChange={e => setMobileCode(e.target.value)}
                style={{ marginRight: 12, flex: 1 }}
              />
              <Button 
                type="primary" 
                disabled={countdown > 0}
                onClick={handleSendCode}
              >
                {countdown > 0 ? `${countdown}s` : dict?.dashboard?.account?.changeMobile?.sendCode}
              </Button>
            </div>
          </Modal>

          {/* 修改邮箱Modal */}
          <Modal
            title={dict?.dashboard?.account?.changeEmail?.title}
            open={emailModalOpen}
            onCancel={() => setEmailModalOpen(false)}
            onOk={handleChangeEmail}
            confirmLoading={emailLoading}
            centered
            okText={dict?.dashboard?.account?.confirm}
            cancelText={dict?.dashboard?.account?.cancel}
          >
            <Input
              placeholder={dict?.dashboard?.account?.changeEmail?.newEmail}
              value={newEmail}
              onChange={e => setNewEmail(e.target.value)}
              style={{ marginBottom: 12 }}
            />
            <div style={{ display: 'flex' }}>
              <Input
                placeholder={dict?.dashboard?.account?.changeEmail?.verificationCode}
                value={emailCode}
                onChange={e => setEmailCode(e.target.value)}
                style={{ marginRight: 12, flex: 1 }}
              />
              <Button
                type="primary"
                disabled={emailCountdown > 0}
                onClick={handleSendEmailCode}
              >
                {emailCountdown > 0 ? `${emailCountdown}s` : dict?.dashboard?.account?.changeEmail?.sendCode}
              </Button>
            </div>
          </Modal>

          {/* 支付密码设置/修改Modal */}
          <Modal
            title={isCreatePayPassword ? dict?.dashboard?.account?.payPassword?.createTitle : dict?.dashboard?.account?.payPassword?.changeTitle}
            open={payPasswordModalOpen}
            onCancel={() => setPayPasswordModalOpen(false)}
            onOk={handlePayPasswordSubmit}
            confirmLoading={payPasswordLoading}
            centered
            okText={dict?.dashboard?.account?.confirm}
            cancelText={dict?.dashboard?.account?.cancel}
            maskClosable={false}
          >
            <Input.Password
              placeholder={dict?.dashboard?.account?.payPassword?.passwordPlaceholder}
              value={newPayPassword}
              onChange={e => setNewPayPassword(e.target.value)}
              style={{ marginBottom: 12 }}
              maxLength={6}
            />
            <Input.Password
              placeholder={dict?.dashboard?.account?.payPassword?.confirmPlaceholder}
              value={confirmPayPassword}
              onChange={e => setConfirmPayPassword(e.target.value)}
              style={{ marginBottom: !isCreatePayPassword ? 12 : 0 }}
              maxLength={6}
            />
            {!isCreatePayPassword && (
              <div style={{ display: 'flex' }}>
                <Input
                  placeholder={dict?.dashboard?.account?.payPassword?.verifyCodePlaceholder || '请输入验证码'}
                  value={payPasswordVerifyCode}
                  onChange={e => setPayPasswordVerifyCode(e.target.value)}
                  style={{ marginRight: 12, flex: 1 }}
                  maxLength={6}
                />
                <Button
                  type="primary"
                  disabled={payPasswordCodeCountdown > 0}
                  onClick={handleSendPayPasswordCode}
                >
                  {payPasswordCodeCountdown > 0 ? `${payPasswordCodeCountdown}s` : (dict?.dashboard?.account?.payPassword?.sendCode || '发送验证码')}
                </Button>
              </div>
            )}
          </Modal>
        </>
      )}
    </>
  )
}
  