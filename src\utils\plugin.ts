/**
 * 插件管理工具
 * 用于检查和管理插件状态
 */

// 插件数据接口定义
export interface PluginInfo {
    id: number;
    code: string;
    name: string;
    version: string;
    status: number;
  }
  
  export interface MerchantPlugin {
    id: number;
    merchant_id: number;
    plugin_id: number;
    status: number;
    version: string;
    createtime: number;
    plugin: PluginInfo;
  }
  
  // 插件状态缓存
  let pluginCache: Map<string, boolean> | null = null;
  let cacheTimestamp: number = 0;
  const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存
  
  /**
   * 获取所有插件列表
   * @param mer_id 商户ID（可选）
   * @returns 插件列表响应
   */
  export const getPluginList = async (mer_id?: string): Promise<{data: MerchantPlugin[],success:boolean}> => {
    if (typeof window === 'undefined') {
      throw new Error('Plugin list can only be fetched on client side');
    }

    try {
      // 动态导入 Api 以避免循环依赖
      const { Api } = await import('@/request/api');

      // 检查是否有getPluginList方法（TP6）
      if (typeof Api.getPluginList === 'function') {
        const response = await Api.getPluginList(mer_id);
        return response;
      } else {
        // TP5不支持批量获取插件列表，返回空列表
        return { data: [], success: true };
      }
    } catch (error) {
      console.error('获取插件列表失败:', error);
      throw error;
    }
  };
  
  /**
   * 刷新插件状态缓存
   * @param mer_id 商户ID（可选）
   */
  export const refreshPluginCache = async (mer_id?: string): Promise<void> => {
    try {
      const response = await getPluginList(mer_id);
      if (response.success) {
        pluginCache = new Map();
        // 将插件状态存入缓存
        
        response.data.forEach((merchantPlugin: MerchantPlugin) => {
          if (merchantPlugin.plugin && merchantPlugin.plugin.code) {
            // status 为 1 表示插件已启用
            const isEnabled = merchantPlugin.status === 1;
            pluginCache!.set(merchantPlugin.plugin.code, isEnabled);
          }
        });

        cacheTimestamp = Date.now();
      }
    } catch (error) {
      console.error('刷新插件缓存失败:', error);
      // 如果刷新失败，清空缓存
      pluginCache = null;
      cacheTimestamp = 0;
    }
  };
  
  /**
   * 检查缓存是否有效
   * @returns 缓存是否有效
   */
  const isCacheValid = (): boolean => {
    return pluginCache !== null && (Date.now() - cacheTimestamp) < CACHE_DURATION;
  };
  
  /**
   * 检查指定插件是否启用
   * @param pluginCode 插件代码
   * @param mer_id 商户ID（可选）
   * @returns 插件是否启用
   */
  export const isPluginEnabled = async (pluginCode: string, mer_id?: string): Promise<boolean> => {
    try {
      // 动态导入 Api 以避免循环依赖
      const { Api } = await import('@/request/api');

      // 检查是否有getPluginList方法（TP6）
      if (typeof Api.getPluginList === 'function') {
        // 检查缓存是否有效
        if (!isCacheValid()) {
          await refreshPluginCache(mer_id);
        }

        // 从缓存中获取插件状态
        if (pluginCache && pluginCache.has(pluginCode)) {
          const enabled = pluginCache.get(pluginCode) || false;
          return enabled;
        }

        // 如果缓存中没有找到，返回 false
        return false;
      } else {
        // TP5环境，直接调用checkAddonStatus
        const response = await Api.checkAddonStatus(pluginCode);

        if (response && response.success) {
          const enabled = response.data === 1 || response.data === true;
          return enabled;
        } else {
          return false;
        }
      }
    } catch (error) {
      console.error(`检查插件 ${pluginCode} 状态失败:`, error);
      return false;
    }
  };
  
  /**
   * 批量检查多个插件状态
   * @param pluginCodes 插件代码数组
   * @param mer_id 商户ID（可选）
   * @returns 插件状态映射
   */
  export const checkMultiplePlugins = async (
    pluginCodes: string[], 
    mer_id?: string
  ): Promise<Record<string, boolean>> => {
    try {
      // 检查缓存是否有效
      if (!isCacheValid()) {
        await refreshPluginCache(mer_id);
      }
      
      const result: Record<string, boolean> = {};
      
      pluginCodes.forEach(code => {
        if (pluginCache && pluginCache.has(code)) {
          result[code] = pluginCache.get(code) || false;
        } else {
          result[code] = false;
        }
      });
      
      return result;
    } catch (error) {
      console.error('批量检查插件状态失败:', error);
      // 返回所有插件都未启用的状态
      const result: Record<string, boolean> = {};
      pluginCodes.forEach(code => {
        result[code] = false;
      });
      return result;
    }
  };
  
  /**
   * 清空插件缓存
   */
  export const clearPluginCache = (): void => {
    pluginCache = null;
    cacheTimestamp = 0;
  };
  
  /**
   * 获取所有已启用的插件代码列表
   * @param mer_id 商户ID（可选）
   * @returns 已启用的插件代码数组
   */
  export const getEnabledPlugins = async (mer_id?: string): Promise<string[]> => {
    try {
      // 检查缓存是否有效
      if (!isCacheValid()) {
        await refreshPluginCache(mer_id);
      }
      
      if (!pluginCache) {
        return [];
      }
      
      const enabledPlugins: string[] = [];
      pluginCache.forEach((isEnabled, pluginCode) => {
        if (isEnabled) {
          enabledPlugins.push(pluginCode);
        }
      });
      
      return enabledPlugins;
    } catch (error) {
      console.error('获取已启用插件列表失败:', error);
      return [];
    }
  };
  
