'use client';

import { DatePicker as AntDatePicker, DatePickerProps } from 'antd';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import React, { ReactNode } from 'react';

// 获取AntDesign的所有DatePicker相关组件
const { 
  RangePicker: AntRangePicker, 
  MonthPicker: AntMonthPicker,
  QuarterPicker: AntQuarterPicker,
  WeekPicker: AntWeekPicker,
  YearPicker: AntYearPicker,
} = AntDatePicker;

function DatePickerComponent({ children, ...props }: DatePickerProps & { children?: ReactNode }) {
  return (
    <AntdConfigProvider>
      <AntDatePicker {...props}>
        {children && children}
      </AntDatePicker>
    </AntdConfigProvider>
  );
}

// 定义RangePicker组件
function RangePicker(props: any) {
  return (
    <AntdConfigProvider>
      <AntRangePicker {...props} />
    </AntdConfigProvider>
  );
}

// 定义MonthPicker组件
function MonthPicker(props: any) {
  return (
    <AntdConfigProvider>
      <AntMonthPicker {...props} />
    </AntdConfigProvider>
  );
}

// 定义QuarterPicker组件
function QuarterPicker(props: any) {
  return (
    <AntdConfigProvider>
      <AntQuarterPicker {...props} />
    </AntdConfigProvider>
  );
}

// 定义WeekPicker组件
function WeekPicker(props: any) {
  return (
    <AntdConfigProvider>
      <AntWeekPicker {...props} />
    </AntdConfigProvider>
  );
}

// 定义YearPicker组件
function YearPicker(props: any) {
  return (
    <AntdConfigProvider>
      <AntYearPicker {...props} />
    </AntdConfigProvider>
  );
}

// 添加所有子组件到DatePickerComponent
DatePickerComponent.RangePicker = RangePicker;
DatePickerComponent.MonthPicker = MonthPicker;
DatePickerComponent.QuarterPicker = QuarterPicker;
DatePickerComponent.WeekPicker = WeekPicker;
DatePickerComponent.YearPicker = YearPicker;

export default DatePickerComponent;