/* Toast动画样式 */

/* 进度条收缩动画 */
@keyframes shrink {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Toast进入动画 */
@keyframes toast-enter {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Toast退出动画 */
@keyframes toast-exit {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
}

/* Toast悬停效果 */
@keyframes toast-hover {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.02);
  }
}

/* 脉冲效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Toast容器样式 */
.toast-container {
  position: fixed;
  z-index: 9999;
  pointer-events: none;
}

.toast-container > * {
  pointer-events: auto;
}

/* Toast项目样式 */
.toast-item {
  animation: toast-enter 0.3s ease-out forwards;
  will-change: transform, opacity;
}

.toast-item.leaving {
  animation: toast-exit 0.3s ease-in forwards;
}

.toast-item:hover {
  animation: toast-hover 0.2s ease-out forwards;
}

/* 进度条样式 */
.toast-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px 2px 0 0;
  overflow: hidden;
}

.toast-progress-bar {
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  transition: width linear;
  border-radius: 2px 2px 0 0;
}

.toast-progress-bar.paused {
  animation-play-state: paused;
}

/* 响应式设计 */
@media (max-width: 640px) {
  /* 对于居中位置的Toast，在移动端保持居中但调整宽度 */
  .toast-container {
    left: 1rem !important;
    right: 1rem !important;
  }

  /* 对于top-center和bottom-center，保持居中 */
  .toast-container:has(.toast-item) {
    left: 50% !important;
    right: auto !important;
    transform: translateX(-50%) !important;
    width: calc(100vw - 2rem);
    max-width: 400px;
  }

  .toast-item {
    max-width: none;
    width: 100%;
    margin: 0 auto;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .toast-item {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .toast-item,
  .toast-item.leaving,
  .toast-item:hover,
  .toast-progress-bar {
    animation: none;
    transition: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .toast-item {
    border: 2px solid;
  }
}

/* 自定义滚动条（如果需要） */
.toast-container::-webkit-scrollbar {
  display: none;
}

.toast-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
