.stepsContainer {
  display: flex;
  justify-content: center;
  padding: 2rem 1rem;
  width: 100%;
}

/* 步骤条样式 - 使用更高优先级覆盖全局样式 */
.stepsContainer :global(.ant-steps) {
  max-width: 960px;
  width: 100%;
  display: flex;
  justify-content: space-between;
}

/* 当前步骤图标样式 */
.stepsContainer :global(.ant-steps-item-process .ant-steps-item-icon),
.stepsContainer :global(.ant-steps-item-process) :global(.ant-steps-item-icon) {
  background-color: #f97316 !important;
  border-color: #f97316 !important;
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.stepsContainer :global(.ant-steps-item-process .ant-steps-item-icon .ant-steps-icon),
.stepsContainer :global(.ant-steps-item-process) :global(.ant-steps-item-icon) :global(.ant-steps-icon) {
  color: white !important;
}

/* 已完成步骤图标样式 */
.stepsContainer :global(.ant-steps-item-finish .ant-steps-item-icon),
.stepsContainer :global(.ant-steps-item-finish) :global(.ant-steps-item-icon) {
  background-color: #f97316 !important;
  border-color: #f97316 !important;
}

.stepsContainer :global(.ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon),
.stepsContainer :global(.ant-steps-item-finish) :global(.ant-steps-item-icon) :global(.ant-steps-icon) {
  color: white !important;
}

/* 连接线样式 */
.stepsContainer :global(.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after),
.stepsContainer :global(.ant-steps-item-finish) > :global(.ant-steps-item-container) > :global(.ant-steps-item-tail)::after {
  background-color: #f97316 !important;
  height: 2px;
}

.stepsContainer :global(.ant-steps-item-wait > .ant-steps-item-container > .ant-steps-item-tail::after),
.stepsContainer :global(.ant-steps-item-wait) > :global(.ant-steps-item-container) > :global(.ant-steps-item-tail)::after {
  height: 2px;
  background-color: #e5e7eb !important;
}

/* 标题颜色 */
.stepsContainer :global(.ant-steps-item-finish .ant-steps-item-title),
.stepsContainer :global(.ant-steps-item-finish) :global(.ant-steps-item-title) {
  color: #f97316 !important;
  font-weight: 500;
}

.stepsContainer :global(.ant-steps-item-process .ant-steps-item-title),
.stepsContainer :global(.ant-steps-item-process) :global(.ant-steps-item-title) {
  color: #f97316 !important;
  font-weight: 500;
}

/* 描述文字颜色 */
.stepsContainer :global(.ant-steps-item-process .ant-steps-item-description),
.stepsContainer :global(.ant-steps-item-process) :global(.ant-steps-item-description) {
  color: rgba(249, 115, 22, 0.8) !important;
}

.stepsContainer :global(.ant-steps-item-wait .ant-steps-item-title) {
  color: #9ca3af;
}

.stepsContainer :global(.ant-steps-item-wait .ant-steps-item-description) {
  color: #d1d5db;
}

.stepsContainer :global(.ant-steps-item-wait .ant-steps-item-icon) {
  background-color: #f1f1f1;
  border-color: #d9d9d9;
}

.stepsContainer :global(.ant-steps-item-title) {
  font-size: 15px;
  padding-inline-end: 0 !important;
}

.stepsContainer :global(.ant-steps-item-description) {
  font-size: 13px;
  margin-top: 2px;
  font-weight: normal;
}



.stepsContainer :global(.ant-steps-item) {
  padding-left: 8px;
  padding-right: 8px;
  margin-top: 6px;
  flex: 1;
}

.stepsContainer :global(.ant-steps-item-container) {
  display: flex;
  align-items: flex-start;
}

.stepsContainer :global(.ant-steps-item-content) {
  /* margin-top: 25px; */
  text-align: center;
}

.stepsContainer :global(.ant-steps-item-tail) {
  top: 20px;
  padding: 0;
}

.stepsContainer :global(.ant-steps-item-title::after) {
  background-color: #e5e7eb;
  height: 2px;
}

.stepsContainer :global(.ant-steps-item-finish .ant-steps-item-title::after) {
  background-color: #f97316;
}

/* 表单标题和必填项 */
.formRequiredStyles :global(.ant-form-item-required::before) {
  display: inline-block;
  margin-right: 4px;
  color: #f97316;
  font-size: 16px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
  position: relative;
  top: 1px;
}

.formRequiredStyles :global(.ant-form-item-label > label) {
  font-size: 15px;
  height: 32px;
  color: #374151;
  font-weight: normal;
}

/* 输入框样式 */
.formRequiredStyles :global(.ant-input) {
  height: 48px;
  border-radius: 4px;
  border-color: #e5e7eb;
  padding: 12px 16px;
  font-size: 15px;
  background-color: white;
}

.formRequiredStyles :global(.ant-input::placeholder) {
  color: #9ca3af;
}

.formRequiredStyles :global(.ant-input:focus),
.formRequiredStyles :global(.ant-input-focused),
.formRequiredStyles :global(.ant-input:hover) {
  border-color: #f97316;
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.1);
}

/* 数字输入框样式 */
.formRequiredStyles :global(.ant-input-number) {
  width: 100%;
  border-radius: 4px;
  background-color: white;
}

.formRequiredStyles :global(.ant-input-number-input) {
  height: 46px;
  padding: 12px 16px;
  font-size: 15px;
  background-color: white;
}

.formRequiredStyles :global(.ant-input-number-handler-wrap) {
  border-radius: 0 4px 4px 0;
  background-color: #f9fafb;
}

.formRequiredStyles :global(.ant-input-number:focus),
.formRequiredStyles :global(.ant-input-number-focused),
.formRequiredStyles :global(.ant-input-number:hover) {
  border-color: #f97316;
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.1);
}

.formRequiredStyles :global(.ant-input-number-group-addon) {
  background-color: #f9fafb;
  color: #6b7280;
  border-color: #e5e7eb;
  font-size: 14px;
  padding: 0 12px;
}

/* 上传组件样式 */
.formRequiredStyles :global(.ant-upload-drag) {
  border-radius: 4px;
  border: 1px dashed #d1d5db;
  background: white;
  transition: all 0.3s;
}

.formRequiredStyles :global(.ant-upload-drag:hover) {
  border-color: #f97316;
}

.formRequiredStyles :global(.ant-upload-list-item) {
  border-radius: 4px;
  margin-top: 8px;
}

/* 文本域样式 */
.formRequiredStyles :global(.ant-input-textarea) {
  border-radius: 4px;
}

.formRequiredStyles :global(.ant-input-textarea textarea) {
  border-radius: 4px;
  border-color: #e5e7eb;
  min-height: 112px;
  padding: 12px 16px;
  font-size: 15px;
  resize: none;
  background-color: white;
}

.formRequiredStyles :global(.ant-input-textarea textarea::placeholder) {
  color: #9ca3af;
}

.formRequiredStyles :global(.ant-input-textarea textarea:focus),
.formRequiredStyles :global(.ant-input-textarea textarea:hover) {
  border-color: #f97316;
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.1);
}

/* 错误提示 */
.formRequiredStyles :global(.ant-form-item-explain-error) {
  color: #f97316;
  margin-top: 2px;
  font-size: 13px;
}

/* 复选框样式 */
.formRequiredStyles :global(.ant-checkbox-wrapper) {
  font-size: 14px;
}

.formRequiredStyles :global(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #f97316;
  border-color: #f97316;
}

.formRequiredStyles :global(.ant-checkbox-wrapper:hover .ant-checkbox-inner),
.formRequiredStyles :global(.ant-checkbox:hover .ant-checkbox-inner),
.formRequiredStyles :global(.ant-checkbox-input:focus + .ant-checkbox-inner) {
  border-color: #f97316;
}

/* 总计样式 */
.totalPriceContainer {
  background-color: #f9fafb;
  border-radius: 6px;
  padding: 16px 20px;
  margin-bottom: 24px;
}

.totalPriceLabel {
  color: #374151;
  font-size: 15px;
}

.totalPriceValue {
  font-size: 24px;
  font-weight: 600;
  color: #f97316;
}

.totalPriceCurrency {
  font-size: 14px;
  color: #6b7280;
  margin-left: 4px;
  font-weight: normal;
}

.totalPriceDescription {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
}

/* 数量输入框样式 */
.formRequiredStyles :global(.ant-input-number-group-wrapper) {
  width: 100%;
}

.formRequiredStyles :global(.ant-input-number.flex-1) {
  border-left: none;
  border-right: none;
  height: 48px;
}

.formRequiredStyles :global(.ant-input-number.flex-1 .ant-input-number-input) {
  text-align: center;
  height: 48px;
  padding: 0;
  font-size: 16px;
  font-weight: 500;
}

.formRequiredStyles :global(.ant-input-number.flex-1:focus),
.formRequiredStyles :global(.ant-input-number.flex-1-focused),
.formRequiredStyles :global(.ant-input-number.flex-1:hover) {
  z-index: 1;
  border-color: #f97316;
} 