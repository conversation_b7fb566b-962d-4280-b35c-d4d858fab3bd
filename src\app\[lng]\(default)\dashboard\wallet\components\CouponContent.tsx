import { useState, useEffect, useMemo } from 'react';
import { Card, Empty, Input } from 'antd';
import Pagination from '@/components/Pagination'
import { Api } from '@/request/api';
import type { TabsProps } from 'antd';
import Button from '@/components/Button';
import TabsComponent from '@/components/Tabs';
import { formatCurrency } from '@/utils/currency';
import Toast from '@/components/Toast';
interface CouponItem {
  id: number;
  money: string;
  status: string;
  status_text: string;
  endtime_text: string;
  type_text: string;
  usetype_text: string;
  coupon_rule_id: number;
  sn: string;
  user_id: number;
  getway: number;
  getway_text: string;
  usetype: number;
  createtime: string;
  createtime_text: string;
  endtime: number;
  updatetime: string;
  totalmoeny: string;
  use_money: string;
  coupon_rule: any;
  couponRule: any;
}

interface CouponResponse {
  code: number;
  msg: string;
  time: string;
  data: {
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
    data: CouponItem[];
  };
}

export default function CouponContent({dict}:  {dict:any}) {
  const [activeTab, setActiveTab] = useState('all');
  const [couponCode, setCouponCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [allCoupons, setAllCoupons] = useState<CouponItem[]>([]); // 存储所有优惠券
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(15); // 每页显示15个优惠券

  // 获取所有优惠券数据
  const fetchAllCoupons = async () => {
    try {
      setLoading(true);
      // 获取所有优惠券数据，设置一个较大的size以获取全部数据
      const response = await Api.getCouponList({ page: 1, size: 10000 });
      if (response.success) {
        setAllCoupons(response.data.data || []);
      } else {
        Toast.error(response.msg || '获取优惠券列表失败');
      }
    } catch (error) {
      Toast.error(dict?.dashboard?.wallet?.getFail);
    } finally {
      setLoading(false);
    }
  };

  // 激活优惠券
  const handleActivateCoupon = async () => {

    if (!couponCode.trim()) {
      Toast.warning(dict?.dashboard?.wallet?.inputTip);
      return;
    }

    try {
      const res = await Api.couponActivate({ code: couponCode.trim() });
      if (res.success) {
        Toast.success(dict?.dashboard?.wallet?.activeOk);
        setCouponCode('');
        // 激活成功后重新获取所有数据
        setCurrentPage(1);
        fetchAllCoupons();
      } else {
        Toast.error(res.msg || dict?.dashboard?.wallet?.activeFail);
      }
    } catch (error) {
      Toast.error(dict?.dashboard?.wallet?.activeFail);
    }
  };

  // 根据状态过滤优惠券
  const getFilteredCoupons = () => {
    let filtered = allCoupons;

    // 根据选中的tab筛选
    if (activeTab !== 'all') {
      filtered = allCoupons.filter(coupon => {
        // 将status转换为字符串进行比较，兼容数字和字符串类型
        const status = String(coupon.status);
        switch (activeTab) {
          case 'usable':
            return status === '1';  // 未使用
          case 'used':
            return status === '2';  // 已使用
          case 'expired':
            return status === '3';  // 已过期
          default:
            return true;
        }
      });
    }

    return filtered;
  };

  // 获取当前页面要显示的优惠券（分页后的数据）
  const getCurrentPageCoupons = () => {
    const filteredCoupons = getFilteredCoupons();
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredCoupons.slice(startIndex, endIndex);
  };

  // 使用useMemo优化计算各状态优惠券数量，确保实时更新
  const counts = useMemo(() => {
    const counts = {
      all: allCoupons.length,
      usable: 0,
      used: 0,
      expired: 0
    };

    allCoupons.forEach(coupon => {
      // 将status转换为字符串进行比较，兼容数字和字符串类型
      const status = String(coupon.status);
      if (status === '1') counts.usable++;      // 未使用
      else if (status === '2') counts.used++;   // 已使用
      else if (status === '3') counts.expired++; // 已过期
    });



    return counts;
  }, [allCoupons]); // 依赖allCoupons，当优惠券数据变化时重新计算

  // 动态生成tab项目，确保数量实时更新
  const items: TabsProps['items'] = [
    {
      key: 'all',
      label: `${dict?.dashboard?.wallet?.all} (${counts.all})`,
    },
    {
      key: 'usable',
      label: `${dict?.dashboard?.wallet?.canUse} (${counts.usable})`,
    },
    {
      key: 'used',
      label: `${dict?.dashboard?.wallet?.used} (${counts.used})`,
    },
    {
      key: 'expired',
      label: `${dict?.dashboard?.wallet?.expired} (${counts.expired})`,
    },
  ];

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 处理tab切换时重置到第一页
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setCurrentPage(1);
  };

  useEffect(() => {
    fetchAllCoupons();
  }, []);

  // 渲染优惠券卡片
  const renderCouponCard = (coupon: CouponItem) => {
    const isUsable = coupon.status === '1';
    const isUsed = coupon.status === '2';

    return (
      <div 
        key={coupon.id}
        className={`relative rounded-lg overflow-hidden shadow-lg ${
          isUsable ? 'bg-gradient-to-r from-orange-500 to-orange-400' :
          'bg-gradient-to-r from-gray-400 to-gray-300'
        }`}
      >
        <div className="flex p-6">
          <div className="flex-1">
            <div className="text-white text-3xl font-extrabold mb-2">
              {formatCurrency(Number(coupon.money)).formatValue}
            </div>
            <div className="text-white/90 text-base">
              {coupon.type_text}
            </div>
            <div className="text-white/70 text-sm mt-2">
              {dict?.dashboard?.wallet?.validDate}：{coupon.endtime !== 0 ? coupon.endtime_text : (dict?.dashboard?.wallet?.noExpiry || '无')}
            </div>
          </div>
          <div className="flex items-center">
            <div className={`px-4 py-2 rounded-full text-base font-semibold ${
              isUsable ? 'bg-white text-orange-500' :
              isUsed ? 'bg-gray-600 text-white' :
              'bg-gray-600 text-white'
            }`}>
              {coupon.status_text}
            </div>
          </div>
        </div>
        <div className="bg-black/10 px-6 py-3">
          <div className="text-white/70 text-sm">
            {coupon.getway_text} | {coupon.usetype_text}
          </div>
        </div>
      </div>
    );
  };

  const currentPageCoupons = getCurrentPageCoupons();
  const filteredCoupons = getFilteredCoupons();
  const totalFilteredPages = Math.ceil(filteredCoupons.length / pageSize);

  return (
    <div className="mx-auto px-4">
      <Card className="shadow-sm border-0">
        {/* 激活优惠券区域 */}
        <div className="flex gap-4 mb-6">
          <Input
            placeholder={dict?.dashboard?.wallet?.inputTitle}
            value={couponCode}
            onChange={(e) => setCouponCode(e.target.value)}
            className="flex-1"
          />
          <Button
            type="primary"
            onClick={handleActivateCoupon}
            className="bg-orange-500 hover:bg-orange-600"
          >
          {  dict?.dashboard?.wallet?.activeBtn}
          </Button>
        </div>

        {/* 优惠券列表 */}
        <TabsComponent
          activeKey={activeTab}
          items={items}
          onChange={handleTabChange}
          className="mb-4"
        />

        {loading ? (
          <div className="py-8 text-center text-gray-500">{dict?.dashboard?.wallet?.loading}</div>
        ) : currentPageCoupons.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              {currentPageCoupons.map(renderCouponCard)}
            </div>
            {/* 分页组件 */}
            {totalFilteredPages > 1 && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalFilteredPages}
                pageSize={pageSize}
                onPageChange={handlePageChange}
                prevText={dict?.dashboard?.wallet?.prevPage || '上一页'}
                nextText={dict?.dashboard?.wallet?.nextPage || '下一页'}
              />
            )}
          </>
        ) : (
          <Empty description={dict?.dashboard?.wallet?.noData} />
        )}
      </Card>
    </div>
  );
}