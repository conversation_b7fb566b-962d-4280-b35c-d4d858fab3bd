'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { useRouter, usePathname } from 'next/navigation';
import { Api } from '@/request/api';

interface SearchBarProps {
  topNav?: boolean;
  dict?: {
    search?: {
      taobao?: string;
      wechat?: string;
      placeholder?: string;
      searchButton?: string;
    };
  };
}

const SearchBar = ({ dict = {}, topNav = false }: SearchBarProps) => {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [isSearchByImgPluginEnabled, setIsSearchByImgPluginEnabled] = useState(false);
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];
  const fileInputRef = useRef<HTMLInputElement>(null);
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'

  // Safely get image search configuration to avoid localStorage unavailability during SSR
  const getConfig = () => {
    if (typeof window === 'undefined') {
      return 0; // Server-side default return 0
    }
    try {
      const siteData = localStorage.getItem('siteData');
      if (!siteData) return 0;
      const parsedData = JSON.parse(siteData);
      return parsedData
    } catch (error) {
      console.error('Failed to parse siteData:', error);
      return 0;
    }
  };

  const imgSearch = Number(getConfig().api_image_search) || 0;

  // 检查 search_by_img 插件是否启用
  useEffect(() => {
    const checkSearchByImgPlugin = async () => {
      try {
        // 使用新的插件管理工具检查插件是否启用
        const { isPluginEnabled } = await import('@/utils/plugin');
        const pluginEnabled = await isPluginEnabled('search_by_img');

        console.log('search_by_img插件状态:', pluginEnabled);
        setIsSearchByImgPluginEnabled(pluginEnabled);
      } catch (error) {
        console.error('检查search_by_img插件状态失败:', error);
        setIsSearchByImgPluginEnabled(false);
      }
    };

    checkSearchByImgPlugin();
  }, []);
  // Function to detect if it's a link - more lenient version
  const isValidUrl = (string: string) => {
    // 基本URL检测
    try {
      new URL(string);
      return true;
    } catch (_) {
      // Try adding protocol and detect again
      try {
        new URL('https://' + string);
        return true;
      } catch (_) {
        // If the above methods fail, use regex for more lenient detection
        const urlPattern = /^(https?:\/\/)?[\w.-]+(\.[\w.-]+)+[\w\-._~:/?#[\]@!$&'()*+,;=]+$/i;
        return urlPattern.test(string);
      }
    }
  };

  // Extract product ID and platform type from link - enhanced version
  const extractProductInfo = (url: string) => {
    try {
      let urlObject;
      
      if (url.startsWith('http') || url.startsWith('https')) {
        urlObject = new URL(url);
      } else {
        urlObject = new URL('https://' + url);
      }
      
      const hostname = urlObject.hostname.toLowerCase();
      const pathname = urlObject.pathname;
      const searchParams = new URLSearchParams(urlObject.search);
      let platform = null;
      let productId = null;
      
      // Taobao/Tmall link processing
      if (hostname.includes('taobao') || hostname.includes('tmall') || hostname.includes('tb.cn')) {
        platform = 'taobao';
        // Try different parameter names
        productId = searchParams.get('id') || searchParams.get('item_id');
        
        // If no ID parameter found, try extracting from path
        if (!productId && pathname.includes('item/')) {
          const matches = pathname.match(/item\/(\d+)/);
          productId = matches ? matches[1] : null;
        }
        
        // Handle short links
        if (!productId && hostname.includes('tb.cn')) {
          // For short links, backend parsing may be needed, so we mark as Taobao link but don't provide ID
          return { platform, productId: null };
        }
      }
      
      // JD link processing
      else if (hostname.includes('jd.com') || hostname.includes('jingxi.com') || hostname.includes('jd.hk')) {
        platform = 'jd';
        // Standard JD product link
        let matches = pathname.match(/\/(\d+)\.html/);
        if (matches) {
          productId = matches[1];
        }
        
        // Other possible JD link formats
        if (!productId) {
          productId = searchParams.get('wareId') || searchParams.get('sku') || searchParams.get('id');
        }
        
        // Handle complex paths
        if (!productId && pathname.includes('product/')) {
          matches = pathname.match(/product\/(\d+)/);
          productId = matches ? matches[1] : null;
        }
      }
      
      // Pinduoduo link processing
      else if (hostname.includes('pinduoduo') || hostname.includes('yangkeduo') || hostname.includes('pdd.cn')) {
        platform = 'micro'; // Pinduoduo corresponds to micro directory
        productId = searchParams.get('goods_id') || searchParams.get('gid');
        
        // If there's no explicit product ID parameter in the link
        if (!productId && pathname.includes('goods/')) {
          const matches = pathname.match(/goods\/(\d+)/);
          productId = matches ? matches[1] : null;
        }
      }
      
      // 1688 link processing
      else if (hostname.includes('1688.com') || hostname.includes('alibaba')) {
        platform = '1688';
        
        // Standard 1688 product link
        let matches = pathname.match(/\/offer\/(\d+)\.html/);
        if (matches) {
          productId = matches[1];
        }
        
        // Handle other possible 1688 link formats
        if (!productId) {
          productId = searchParams.get('offerId') || searchParams.get('id');
        }
        
        // Handle detail page links
        if (!productId && pathname.includes('detail/')) {
          matches = pathname.match(/detail\/(\d+)/);
          productId = matches ? matches[1] : null;
        }
      }
      
      // Try to extract any possible numeric ID from URL as fallback option
      if (!productId && !platform) {
        // Try to extract any possible number from URL as ID
        const idMatch = url.match(/\b\d{5,}\b/); // Assume product ID has at least 5 digits
        if (idMatch) {
          productId = idMatch[0];
          // Roughly guess platform based on domain name
          if (hostname.includes('taobao') || hostname.includes('tmall') || hostname.includes('tb')) {
            platform = 'taobao';
          } else if (hostname.includes('jd')) {
            platform = 'jd';
          } else if (hostname.includes('pdd') || hostname.includes('pinduoduo')) {
            platform = 'micro';
          } else if (hostname.includes('1688') || hostname.includes('alibaba')) {
            platform = '1688';
          }
        }
      }
      
      return { platform, productId };
    } catch (error) {
      console.error('Error parsing URL:', error);
      return { platform: null, productId: null };
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const query = searchQuery.trim();
    if (!query) return;

    // Check if it's a link
    if (isValidUrl(query)) {
      // Try to extract product ID and platform
      const { platform, productId } = extractProductInfo(query);
      
      if (platform && productId) {
        // Have platform and product ID, directly jump to corresponding platform's detail page
        router.push(`/${currentLocale}/detail/${platform}?url=${query}`);
      } else if (platform && !productId) {
        // Identified platform but no ID, might be short link or other special cases
        router.push(`/${currentLocale}/search?q=${encodeURIComponent(query)}&page=1&type=${platform}`);
      } else {
        // Cannot identify platform or product ID, but confirmed as link
        // Pass entire link to search page, let backend handle it
        router.push(`/${currentLocale}/search?q=${encodeURIComponent(query)}&page=1&type=link`);
      }
    } else {
      // Normal search - don't pass type parameter, let search page decide based on default config
      router.push(`/${currentLocale}/search?q=${encodeURIComponent(query)}&page=1`);
    }
  };

  const handleUpload = async (e: React.MouseEvent) => {
    e.preventDefault();
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    try {
      const reader = new FileReader();
      reader.onload = async (event) => {
        const base64String = event.target?.result as string;
        if (base64String) {
          const uploadResult = isTp5? await Api.uploadImage(base64String): await Api.uploadImage(file);
          if (uploadResult.success) {
            const imgUrl = uploadResult?.data?.items?.item?.name|| uploadResult.data?.url || '';
            router.push(`/${currentLocale}/search?q=${encodeURIComponent(searchQuery.trim())}&page=1&imgid=${imgUrl}`);
          } else {
            console.error('Upload failed', uploadResult.error);
          }
        }
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error processing file:', error);
    } finally {
      setIsUploading(false);
    }
  };


  // Ensure dict.search exists
  const searchDict = dict?.search || {};
  
  const api_list = getConfig()?.api_list || {};
  const searchIcon = (
    <div
      className="items-center pl-4 bg-white h-full"
      style={{ display: topNav ? 'none' : 'flex' }}
    >
      {!topNav && api_list && (
        <>
          {Object.entries(api_list).map(([key]) => {
            // Define image mapping relationship
            const imageMap:any = {
              taobao: {
                src: "/images/taobao.png",
                alt: searchDict.taobao || "Taobao"
              },
              '1688': {
                src: "/images/1688.png",
                alt: "1688"
              },
              micro: {
                src: "/images/micro.png",
                alt: searchDict.wechat || "Pinduoduo"
              },
              jd: {
                src: "/images/jd.png",
                alt: "JD"
              }
            };

            // Dynamically calculate icon style
            const getIconStyle = () => {
              if (isFocused) {
                // When input gets focus, icons become more faded
                return { filter: 'grayscale(100%)', opacity: 0.3 };
              } else if (isHovered) {
                // On mouse hover, icons highlight but remain gray
                return { filter: 'grayscale(100%)', opacity: 0.8 };
              } else {
                // Default state
                return { filter: 'grayscale(100%)', opacity: 0.5 };
              }
            };

            // Only render if api_list has corresponding key
            if (imageMap[key]) {
              return (
                <Image
                  key={key}
                  draggable={false}
                  src={imageMap[key].src}
                  alt={imageMap[key].alt}
                  width={32}
                  height={32}
                  className="mr-1 transition-all duration-300"
                  style={getIconStyle()}
                  priority
                />
              );
            }
            return null;
          })}
        </>
      )}
    </div>
  );

  // Dynamically calculate placeholder style
  const getPlaceholderStyle = () => {
    if (isFocused) {
      // When input gets focus, placeholder becomes more faded
      return 'placeholder:text-gray-300';
    } else if (isHovered) {
      // On mouse hover, placeholder highlights
      return 'placeholder:text-gray-600';
    } else {
      // Default state
      return 'placeholder:text-gray-400';
    }
  };

  return (
    <form
      onSubmit={handleSearch}
      className={`${topNav ? "w-auto" : "max-w-3xl w-full"} bg-[var(--base-color)] rounded-full flex items-center overflow-hidden duration-300 border-2 border-[var(--base-color)] transition-all`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {searchIcon}
      <input
        type="text"
        placeholder={searchDict.placeholder || "Enter product name / link"}
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            e.preventDefault();
            handleSearch(e);
          }
        }}
        className={`pr-5 font-medium flex-1 outline-none text-gray-700 transition-all duration-300 ${getPlaceholderStyle()}
           ${topNav ? "px-3 py-2 text-base" : "px-6 py-4 text-lg"}`}
      />
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
      />
      <div className={` bg-white px-5 flex items-center justify-center ${topNav ? 'h-[40px]' : 'h-full'}`}>
        {imgSearch && isSearchByImgPluginEnabled ? (
          <span
          onClick={handleUpload}
          className={`i-material-symbols:android-camera text-2xl text-[var(--base-color)]  cursor-pointer ${isUploading ? 'opacity-50' : ''} ${topNav ? '' : 'h-full py-5'}`}
         >
          {isUploading && <span className="i-material-symbols:loading text-sm ml-1"></span>}
        </span>
        ):<span></span>}
      </div>
      <button type="submit" className={`bg-[var(--base-color)] text-white hover:bg-[var(--base-color-hover)] transition-colors duration-300 flex items-center justify-center ${topNav ? "px-3 py-2 text-sm" : "px-8 py-4 text-lg font-medium"}`} style={{borderTopRightRadius: '280px',borderBottomRightRadius: '280px'}}>
        <span className={topNav ? `i-material-symbols:search text-2xl ` : `i-material-symbols:search text-2xl mr-2`} />
        <span className='min-w-[40px] text-center'>{searchDict.searchButton || "Search"}</span>
      </button>
    </form>
  );
};

export default SearchBar; 