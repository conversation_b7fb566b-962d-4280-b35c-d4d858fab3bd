'use client';

import { useMemo, useCallback } from 'react';
import Image from 'next/image';
import useEmblaCarousel from 'embla-carousel-react';
import Autoplay from 'embla-carousel-autoplay';
import { ShoppingTimeProduct } from "@/types/product";
import { prepareImageForNextJs } from '@/utils/imageUtils';
import { formatCurrency } from '@/utils/currency';
import styles from './ShoppingTimeCarousel.module.css';

interface ShoppingTimeCarouselProps {
  products: ShoppingTimeProduct[];
  dict: any;
}

interface UserGroup {
  user: {
    username: string;
    nickname: string;
  };
  products: ShoppingTimeProduct[];
  latestTime: string;
  totalItems: number;
  cloneId?: string;
  isClone?: boolean;
}

export default function ImprovedEmblaCarousel({ products, dict }: ShoppingTimeCarouselProps) {
  // 按用户nickname进行分组聚合商品数据，并复制用于无限轮播
  const userGroups = useMemo(() => {
    const groupMap = new Map<string, UserGroup>();

    products.forEach(product => {
      // 优先使用nickname作为分组键，如果没有nickname则使用username，最后使用user_id
      const groupKey = product.user?.nickname || product.user?.username || `user_${product.user_id}` || 'unknown';

      if (groupMap.has(groupKey)) {
        const group = groupMap.get(groupKey)!;
        group.products.push(product);
        group.totalItems += product.goodsnum;
        // 更新最新时间
        if (new Date(product.createtime) > new Date(group.latestTime)) {
          group.latestTime = product.createtime;
        }
      } else {
        // 创建新的用户组
        groupMap.set(groupKey, {
          user: product.user,
          products: [product],
          latestTime: product.createtime,
          totalItems: product.goodsnum
        });
      }
    });

    // 按最新购买时间排序，最新的在前面
    const baseGroups = Array.from(groupMap.values()).sort((a, b) =>
      new Date(b.latestTime).getTime() - new Date(a.latestTime).getTime()
    );

    // 为了实现无缝无限轮播，复制分组数据
    // 如果分组数量少于5个，复制多份以确保无缝循环效果
    if (baseGroups.length > 0 && baseGroups.length < 5) {
      const multiplier = Math.ceil(10 / baseGroups.length); // 确保至少有10个分组用于轮播
      const duplicatedGroups: any[] = [];

      for (let i = 0; i < multiplier; i++) {
        baseGroups.forEach((group, index) => {
          duplicatedGroups.push({
            ...group,
            // 为复制的分组添加唯一标识
            cloneId: i > 0 ? `clone-${i}-${index}` : undefined,
            isClone: i > 0
          });
        });
      }

      return duplicatedGroups;
    }

    // 如果分组数量足够，也复制一份用于更好的无限轮播效果
    return [
      ...baseGroups.map(group => ({ ...group, isClone: false })),
      ...baseGroups.map((group, index) => ({
        ...group,
        cloneId: `clone-1-${index}`,
        isClone: true
      }))
    ];
  }, [products]);

  // Embla Carousel 配置 - 基于复制分组数据实现真正的无缝无限轮播
  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop: true,
      align: 'start',
      skipSnaps: false,
      dragFree: true, // 启用自由拖拽，支持动量滚动
      containScroll: false, // 禁用容器滚动限制
      slidesToScroll: 1, // 每次滚动一个slide
      duration: 30, // 平滑的滚动速度
      startIndex: 0, // 从第一个开始
    },
    [
      Autoplay({
        delay: 2500, // 2.5秒自动播放，稍快一些
        stopOnInteraction: false, // 交互后继续自动播放
        stopOnMouseEnter: true, // 鼠标悬停时暂停
        stopOnFocusIn: true, // 获得焦点时暂停
        playOnInit: true, // 初始化后立即开始播放
      })
    ]
  );

  // 导航按钮处理
  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  // 渲染用户卡片 - 改进版本，显示用户信息和商品，支持克隆分组
  const renderUserCard = (userGroup: UserGroup, index: number) => {
    const keyPrefix = userGroup.cloneId
      ? `${userGroup.cloneId}-${userGroup.user.username}-${index}`
      : `user-${userGroup.user.username}-${index}`;

    return (
      <div
        key={keyPrefix}
        className="embla__slide flex-shrink-0"
        style={{ flex: '0 0 auto', minWidth: 'fit-content', width: 'auto' }}
      >
        {(() => {
          const productsToShow = userGroup.products.slice(0, 3);
          const productCount = productsToShow.length;
          const cardWidth = productCount * 288 + (productCount - 1) * 16 + 32; // 商品宽度 + 间距 + 内边距

          return (
            <div className="bg-white rounded-xl p-5 shadow-lg border-2 border-orange-100 h-full mr-6 relative overflow-hidden" style={{
              width: `${cardWidth}px`,
              minWidth: `${cardWidth}px`
            }}>

              {/* 用户信息头部 - 极简版：更矮更紧凑 */}
              <div className="flex items-center space-x-2 mb-2 pb-1 border-b border-gray-50 relative">
                <div className="w-6 h-6 bg-gradient-to-br from-gray-300 to-gray-400 rounded-full flex items-center justify-center text-white font-medium text-xs">
                  {(userGroup.user.nickname || userGroup.user.username || 'U').charAt(0).toUpperCase()}
                </div>
                <div className="flex-1 min-w-0 flex items-center justify-between">
                  <h3 className="font-normal text-gray-500 text-xs truncate">
                    {userGroup.user.nickname || userGroup.user.username}
                  </h3>
                  <p className="text-xs text-gray-400 ml-2 flex-shrink-0">
                    {userGroup.products.length > 3 ? `${productCount}+` : productCount} {dict?.warehouse?.pieces || '件'}
                  </p>
                </div>
              </div>

              {/* 商品网格 - 根据实际商品数量动态调整列数和宽度 */}
              <div
                className="grid gap-4"
                style={{
                  gridTemplateColumns: `repeat(${productCount}, 288px)`
                }}
              >
                {productsToShow.map((item, idx) => {
                  const titleLength = item.goodsname?.length || 0;
                  const titleLengthClass = titleLength <= 10 ? 'short' : titleLength <= 20 ? 'medium' : 'long';
                  const detailUrl = item.goodsurl
                    ? `/detail/${item.goodssite || 'taobao'}/?url=${encodeURIComponent(item.goodsurl)}`
                    : '#';

                  return (
                    <a
                      key={`${keyPrefix}-product-${item.id || item.goodsimg || idx}`}
                      href={detailUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="custom-card flex flex-col h-full relative"
                      data-title-length={titleLengthClass}
                    >
                      {/* 商品图片 - 与ProductCard保持一致的正方形比例 */}
                      <div className="relative aspect-square bg-white">
                        <Image
                          src={prepareImageForNextJs(item.goodsimg)}
                          alt={item.goodsname || '商品图片'}
                          fill
                          sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
                          className="object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/images/default.jpg';
                          }}
                        />
                        {/* 推荐标签 - 与ProductCard保持一致的样式 */}
                        {item.recommend === 1 && (
                          <div style={{
                            position: 'absolute',
                            top: '0',
                            right: '0',
                            zIndex: 49,
                            width: '50px',
                            height: '50px',
                            overflow: 'hidden'
                          }}>
                            {/* 三角形背景 */}
                            <div style={{
                              position: 'absolute',
                              top: '0',
                              right: '0',
                              width: '0',
                              height: '0',
                              borderTop: `50px solid var(--base-color, #ff6b6b)`,
                              borderLeft: '50px solid transparent'
                            }}></div>
                            {/* 文字 */}
                            <div style={{
                              position: 'absolute',
                              top: '8px',
                              right: '8px',
                              color: 'white',
                              fontSize: '10px',
                              fontWeight: '700',
                              textShadow: '0 1px 2px rgba(0,0,0,0.4)',
                              letterSpacing: '0.5px',
                              transform: 'rotate(45deg)',
                              transformOrigin: 'center',
                              width: '30px',
                              textAlign: 'center'
                            }}>
                              {dict?.home?.hotProducts || '推荐'}
                            </div>
                          </div>
                        )}
                      </div>
                      {/* 商品信息 - 与ProductCard保持一致的样式 */}
                      <div className="p-3 flex-1 flex flex-col">
                        <h3 className="text-md text-gray-800 line-clamp-2 mb-2 custom-h-48px">
                          {item.goodsname}
                        </h3>
                        <div className="flex items-center justify-between mt-auto">
                          <span className="text-[#FF6B00] font-medium">
                            <span className='text-sm sm:text-base md:text-lg lg:text-xl font-bold'>
                              {formatCurrency(Number(item.goodsprice)).formatValue}
                            </span>
                          </span>
                        </div>
                      </div>
                    </a>
                  );
                })}
              </div>
            </div>
          );
        })()}
      </div>
    );
  };

  return (
    <div className="relative w-full group px-4">
      {userGroups.length > 0 ? (
        <>
          <div className="embla overflow-hidden" ref={emblaRef}>
            <div className="embla__container flex" style={{ gap: '0px' }}>
              {userGroups.map((userGroup, index) => renderUserCard(userGroup, index))}
            </div>
          </div>

          {/* 导航按钮 - 仅在hover时显示 */}
          <button
            className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-gray-700 hover:text-orange-600 p-3 rounded-full shadow-lg transition-all duration-300 z-30 border border-gray-200 opacity-0 group-hover:opacity-100"
            onClick={scrollPrev}
            aria-label="上一个"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-gray-700 hover:text-orange-600 p-3 rounded-full shadow-lg transition-all duration-300 z-30 border border-gray-200 opacity-0 group-hover:opacity-100"
            onClick={scrollNext}
            aria-label="下一个"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      ) : (
        <div className="text-center text-gray-500 py-12">
          {dict?.home?.emptyList || '列表为空'}
        </div>
      )}
    </div>
  );
}
