'use client';

import React, { useEffect, useState } from 'react'
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { Checkbox, Input, Select, Divider } from 'antd';
import { Api } from '@/request/api';
import message from '@/components/CustomMessage';
import Button from '@/components/Button';
import { formatCurrency, formatUSDPrice } from '@/utils/currency';

// Extend Window interface for custom properties
declare global {
  interface Window {
    selectedShippingMethod?: {
      template_id: number;
      fee: {
        totalfee: number;
        sendfee: string;
        customsfee: string;
        serverfee: string;
        fuelfee: string;
        [key: string]: any;
      };
      name: string;
    };
  }
}

interface Goods {
  id: number;
  goodsimg: string;
  goodsname: string;
  skuname: string;
  goodsprice: string;
  goodsnum: number;
  weight?: number;
  volume?: number;
  goodstype?: number;
  [key: string]: any;
}

interface Product {
  cartlist: Goods[];
  serverfee: number;
  sendmoney: number;
}

interface CouponItem {
  id: number;
  money: string;
  status: string;
  status_text: string;
  endtime_text: string | number;
  type_text: string;
  usetype_text: string;
  usetype: number; // Usage scope: 1=specific, 2=all
  coupon_rule: {
    name: string;
    code: string;
  };
}

interface ServiceOption {
  id: string;
  name: string;
  price: number;
  selected: boolean;
  description?: string;
}

interface OnePayOrderInfo {
  order_serve: {
    [key: string]: {
      name: string;
      title: string;
      value: string;
      surcharge: number;
      feetype: string;
      base: string;
    };
  };
  sendorder_server?: {
    [key: string]: {
      name: string;
      title: string;
      value: string;
      surcharge: number;
      feetype: string;
      base: string;
    };
  };
  serve_discount: number;
  freight_discount: number;
  totalweight: number;
  totalvolume: number;
  totalfreight: number; // 新增：到中国仓运费
  cart_ids: string[];
  shoplist: Array<{
    goodsmoney: number;
    sendmoney: string;
    totalmoney: number;
    goodsseller: string;
    sellerurl: string;
    goodslist: any[];
  }>;
  totalmoney: string;
}

interface OnePayOrderSummaryProps {
  dict: any;
  products: Product;
  itemData?: any;
  cart_ids: string;
  lng: string;
}

// 价格显示组件，支持多货币
const PriceDisplay = ({ value, className = "", inline = false }: { value: number; className?: string; inline?: boolean }) => {
  const [usdPrice, setUsdPrice] = useState<any>(null);

  useEffect(() => {
    const loadUSDPrice = async () => {
      const usdPriceData = await formatUSDPrice(value);
      setUsdPrice(usdPriceData);
    };
    loadUSDPrice();
  }, [value]);

  const mainPrice = formatCurrency(value);

  if (inline) {
    return (
      <span className={className}>
        <span className="font-medium text-[#FF6000]">
          {mainPrice.formatValue}
        </span>
        {usdPrice && (
          <span className="text-xs text-gray-500 ml-2">
            ({usdPrice.formatValue})
          </span>
        )}
      </span>
    );
  }

  return (
    <div className={className}>
      <div className="font-medium text-[#FF6000]">
        {mainPrice.formatValue}
      </div>
      {usdPrice && (
        <div className="text-xs text-gray-500 mt-1">
          {usdPrice.formatValue}
        </div>
      )}
    </div>
  );
};

export default function OnePayOrderSummary({
  dict,
  products,
  itemData,
  cart_ids,
  lng
}: OnePayOrderSummaryProps) {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [selectedCouponId, setSelectedCouponId] = useState<number | null>(null);
  const [selectedCoupon, setSelectedCoupon] = useState<CouponItem | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [serviceFee, setServiceFee] = useState(0);
  const [shippingFee, setShippingFee] = useState(0);
  const [shippingFeeDetails, setShippingFeeDetails] = useState<any>(null);
  const [siteConfig, setSiteConfig] = useState<any>(null);
  const [serviceOptions, setServiceOptions] = useState<ServiceOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [orderServerFeeEnabled, setOrderServerFeeEnabled] = useState(true);
  const [onePayOrderInfo, setOnePayOrderInfo] = useState<OnePayOrderInfo | null>(null);

  // Get cart_ids list
  const cart_ids_list = cart_ids ? cart_ids.split(',') : [];

  // Validate products data
  const validProducts = products?.cartlist && Array.isArray(products.cartlist)
    ? products.cartlist.filter(product =>
        product &&
        product.goodsname &&
        product.goodsprice !== undefined &&
        product.goodsprice !== null
      )
    : [];

  // Calculate product subtotal
  // 商品金额：该订单所有商品的金额总和
  const subtotal = validProducts.reduce((sum, product) => {
    const goodsnum = itemData?.goodsnum || product.goodsnum || 1;
    return sum + (parseFloat(String(product.goodsprice)) || 0) * goodsnum;
  }, 0);

  // Calculate coupon discount
  const couponDiscount = selectedCoupon ? parseFloat(selectedCoupon.money) : 0;

  // Calculate dynamic service fees from window.orderServices
  const getDynamicServiceFee = () => {
    if (typeof window !== 'undefined' && window.orderServices?.getServiceData) {
      const serviceData = window.orderServices.getServiceData();
      let totalFee = 0;

      Object.entries(serviceData).forEach(([serviceKey, service]: [string, any]) => {
        if (service && typeof service === 'object') {
          Object.entries(service).forEach(([productId, serviceInfo]: [string, any]) => {
            if (serviceInfo && serviceInfo.fee) {
              totalFee += parseFloat(serviceInfo.fee || 0);
            }
          });
        }
      });
      return totalFee;
    }
    return 0;
  };

  // Get one-time payment order information from client-side API
  useEffect(() => {
    const fetchOnePayOrderInfo = async () => {
      if (!cart_ids) return;

      try {
        const cart_ids_array = cart_ids.split(',');
        const response = await Api.getOnePayOrderInfo({ cart_ids: cart_ids_array });

        if (response.success && response.data) {
          setOnePayOrderInfo(response.data);
        }
      } catch (error) {
        console.error('Failed to get order info from client:', error);
        // Continue without order info if API fails
      }
    };

    fetchOnePayOrderInfo();
  }, [cart_ids]);

  // Get site configuration and initialize settings
  useEffect(() => {
    const getSiteConfig = async () => {
      try {
        if (typeof window !== 'undefined') {
          const siteData = localStorage.getItem('siteData');
          if (siteData) {
            const config = JSON.parse(siteData);
            setSiteConfig(config);
            // Set order server fee enabled status
            const orderFeeEnabled = config?.orderfee === '1';
            setOrderServerFeeEnabled(orderFeeEnabled);



            return config;
          }
        }
        // Fallback to API if localStorage is not available
        const res = await Api.getConfigList();
        const config = res.data?.site || {};
        setSiteConfig(config);
        const orderFeeEnabled = config?.orderfee === '1';
        setOrderServerFeeEnabled(orderFeeEnabled);



        return config;
      } catch (error) {
        console.error('Failed to get site config:', error);
        return null;
      }
    };

    getSiteConfig();
  }, []);

  // Fetch service options from API or use onePayOrderInfo
  useEffect(() => {
    const fetchServiceOptions = async () => {
      try {
        // First try to use service options from onePayOrderInfo.sendorder_server (for package services)
        if (onePayOrderInfo?.sendorder_server) {
          const apiServices = Object.entries(onePayOrderInfo.sendorder_server).map(([key, service]: [string, any]) => ({
            id: key,
            name: service.title || service.name || key,
            price: parseFloat(service.value || 0),
            selected: false, // Default to not selected
            description: service.value ? formatCurrency(parseFloat(service.value)).formatValue : service.name
          }));

          setServiceOptions(apiServices);
          return;
        }

        // Fallback to order_serve if sendorder_server is not available
        if (onePayOrderInfo?.order_serve) {
          const apiServices = Object.entries(onePayOrderInfo.order_serve).map(([key, service]: [string, any]) => ({
            id: key,
            name: service.title || service.name || key,
            price: parseFloat(service.value || 0),
            selected: false, // Default to not selected
            description: service.value ? formatCurrency(parseFloat(service.value)).formatValue : service.name
          }));

          setServiceOptions(apiServices);
          return;
        }

        // Fallback to API call if onePayOrderInfo is not available
        const response = await Api.getServerList('sendorder'); // Use 'sendorder' for package services
        if (response.success && response.data) {
          const apiServices = Object.entries(response.data).map(([key, service]: [string, any]) => ({
            id: key,
            name: service.name || key,
            price: parseFloat(service.value || 0),
            selected: false, // Default to not selected
            description: service.value ? formatCurrency(parseFloat(service.value)).formatValue : service.name
          }));

          setServiceOptions(apiServices);
        } else {
          // Fallback to hardcoded options if API fails
          setServiceOptions([
            {
              id: 'vacuum',
              name: dict?.confirm?.onepayorder?.services?.vacuum || 'Vacuum Packaging',
              price: 2.00,
              selected: false,
              description: '$2.00'
            },
            {
              id: 'photo',
              name: dict?.confirm?.onepayorder?.services?.photo || 'Photo Service',
              price: 1.00,
              selected: false,
              description: '$1.00'
            },
            {
              id: 'insurance',
              name: dict?.confirm?.onepayorder?.services?.insurance || 'Insurance',
              price: 0,
              selected: false,
              description: dict?.confirm?.onepayorder?.services?.insuranceDesc || 'Product Total * 2%'
            }
          ]);
        }
      } catch (error) {
        console.error('Failed to fetch service options:', error);
        // Fallback to hardcoded options
        setServiceOptions([
          {
            id: 'vacuum',
            name: dict?.confirm?.onepayorder?.services?.vacuum || 'Vacuum Packaging',
            price: 2.00,
            selected: false,
            description: '$2.00'
          },
          {
            id: 'photo',
            name: dict?.confirm?.onepayorder?.services?.photo || 'Photo Service',
            price: 1.00,
            selected: false,
            description: '$1.00'
          },
          {
            id: 'insurance',
            name: dict?.confirm?.onepayorder?.services?.insurance || 'Insurance',
            price: 0,
            selected: false,
            description: dict?.confirm?.onepayorder?.services?.insuranceDesc || 'Product Total * 2%'
          }
        ]);
      }
    };

    fetchServiceOptions();
  }, [onePayOrderInfo]);

  // Update service fees periodically and force re-render when services change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const updateServiceFee = () => {
        const currentFee = getDynamicServiceFee();
        setServiceFee(currentFee);

        // Force component re-render by updating a dummy state
      };

      updateServiceFee();
      const interval = setInterval(updateServiceFee, 1000); // Increased to 1 second for better debugging
      return () => clearInterval(interval);
    }
  }, []);

  // Also update when onePayOrderInfo changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const currentFee = getDynamicServiceFee();
      setServiceFee(currentFee);

    }
  }, [onePayOrderInfo]);



  // Listen for coupon selection changes from CouponSection
  useEffect(() => {
    const handleCouponChange = (event: CustomEvent) => {
      const selectedCoupon = event.detail;
      setSelectedCoupon(selectedCoupon);
      setSelectedCouponId(selectedCoupon?.id || null);
    };

    // Load initial selected coupon from localStorage
    if (typeof window !== 'undefined') {
      const savedCoupon = localStorage.getItem('selectedOnePayCoupon');
      if (savedCoupon) {
        try {
          const coupon = JSON.parse(savedCoupon);
          setSelectedCoupon(coupon);
          setSelectedCouponId(coupon.id);
        } catch (error) {
          console.error('Failed to parse saved coupon:', error);
        }
      }

      // Listen for coupon changes
      window.addEventListener('onePayCouponChanged', handleCouponChange as EventListener);

      return () => {
        window.removeEventListener('onePayCouponChanged', handleCouponChange as EventListener);
      };
    }
  }, []);

  // Listen for shipping method changes from OnePayShippingMethods component
  useEffect(() => {
    const handleShippingMethodChange = (event: CustomEvent) => {
      const methodInfo = event.detail;
      if (methodInfo && methodInfo.fee) {
        setShippingFee(methodInfo.fee.totalfee);
        setShippingFeeDetails(methodInfo.fee);
      }
    };

    // Check if there's already a selected shipping method in window
    if (typeof window !== 'undefined' && window.selectedShippingMethod) {

      const methodInfo = window.selectedShippingMethod;
      if (methodInfo.fee) {
        setShippingFee(methodInfo.fee.totalfee);
        setShippingFeeDetails(methodInfo.fee);
      }
    }

    // Listen for shipping method changes
    if (typeof window !== 'undefined') {
      window.addEventListener('shippingMethodChanged', handleShippingMethodChange as EventListener);

      return () => {
        window.removeEventListener('shippingMethodChanged', handleShippingMethodChange as EventListener);
      };
    }
  }, []);

  // Fallback: Calculate shipping fee based on selected method if not available from shipping component
  useEffect(() => {
    const templateId = searchParams.get('template_id');
    const addressId = searchParams.get('address_id');

    // Only use fallback if we don't have shipping fee from the shipping component
    if (templateId && addressId && shippingFee === 0 && (onePayOrderInfo || validProducts.length > 0)) {

      fetchShippingFee(Number(templateId), Number(addressId));
    }
  }, [searchParams.get('template_id'), searchParams.get('address_id'), onePayOrderInfo, validProducts.length, shippingFee]);

  const fetchShippingFee = async (templateId: number, addressId: number) => {
    try {


      // First, get the address details to obtain the area_id
      const addressResponse = await Api.getAddressList();
      if (!addressResponse.success || !addressResponse.data) {
        console.error('Failed to get address list');
        return;
      }

      // Find the selected address by its ID
      const selectedAddress = addressResponse.data.find((addr: any) => addr.id === addressId);
      if (!selectedAddress) {
        console.error('Selected address not found');
        return;
      }



      // Calculate shipping based on onePayOrderInfo or fallback to cart items
      let totalWeight = 0;
      let totalVolume = 0;
      let goodsType = 0;
      let totalPrice = 0;

      // 优先使用一次性付款API返回的总重量和总体积
      if (onePayOrderInfo) {
        totalWeight = onePayOrderInfo.totalweight || 0;
        totalVolume = onePayOrderInfo.totalvolume || 0;
        totalPrice = onePayOrderInfo.shoplist?.reduce((sum, shop) => sum + shop.goodsmoney, 0) || 0;

        // 获取商品类型（使用第一个商品的类型）
        if (onePayOrderInfo.shoplist?.[0]?.goodslist?.[0]?.goodstype !== undefined) {
          goodsType = Number(onePayOrderInfo.shoplist[0].goodslist[0].goodstype) || 0;
        }


      } else {
        // 回退到从商品列表计算
        const productsToUse = validProducts;



        if (productsToUse && Array.isArray(productsToUse)) {
          productsToUse.forEach((item: any) => {
            const quantity = itemData?.goodsnum || item.goodsnum || 1;
            totalWeight += (item.weight || 0.5) * quantity; // Default 0.5kg if no weight
            totalVolume += (item.volume || 100) * quantity; // Default 100cm³ if no volume
            // Use the first product's goodstype
            if (item.goodstype !== undefined && goodsType === 0) {
              goodsType = Number(item.goodstype) || 0;
            }
            // Calculate price from the item
            const price = parseFloat(String(item.goodsprice)) || 0;
            totalPrice += price * quantity;
          });
        }

        // Use subtotal as fallback for total price
        totalPrice = totalPrice || subtotal;
      }

      const finalTotalPrice = totalPrice;



      // Use the area_id from the selected address for estimates API
      const response = await Api.getEstimates({
        area_id: selectedAddress.area_id, // Use area_id instead of address id
        weight: totalWeight || 1,
        volume: totalVolume || 1,
        goodstype: goodsType, // Use single number like shipping methods component
        stotalmoney: finalTotalPrice,
        clienttype: 'pc'
      });

      if (response.success) {
        const selectedMethod = response.data.find((method: any) => method.template_id === templateId);

        if (selectedMethod) {
          setShippingFee(selectedMethod.fee.totalfee);
          setShippingFeeDetails(selectedMethod.fee);
        }
      } else {
        console.error('Estimates API failed:', response);
      }
    } catch (error) {
      console.error('Error fetching shipping fee:', error);
    }
  };

  // Calculate totals with dynamic data from onePayOrderInfo
  // 商品金额：该订单所有商品的金额总和
  const goodsMoney = onePayOrderInfo?.shoplist?.reduce((sum, shop) => sum + shop.goodsmoney, 0) || subtotal;

  // 到中国仓运费：优先使用新的 totalfreight 字段，否则使用商品的运费总和
  const chinaWarehouseShippingFee = (() => {
    // 优先使用新的 totalfreight 字段
    if (onePayOrderInfo?.totalfreight !== undefined) {
      return onePayOrderInfo.totalfreight;
    }

    // 备用方案：使用 shoplist 中的 sendmoney 总和
    if (onePayOrderInfo?.shoplist) {
      const shoplistTotal = onePayOrderInfo.shoplist.reduce((sum, shop) => {
        const sendmoney = parseFloat(shop.sendmoney || '0');
        return sum + sendmoney;
      }, 0);
      return shoplistTotal;
    }

    // 最后备用方案：使用 products 中的 sendmoney
    const fallbackFee = parseFloat(String(products?.sendmoney || '0'));
    return fallbackFee;
  })();



  // 订单附加服务费：该订单所有商品的附加服务费用总和（用户选择的动态服务费）
  const orderAdditionalServiceFee = orderServerFeeEnabled ? getDynamicServiceFee() : 0;



  // 商品服务费：商品总金额 * 订单服务费计算值 ÷ 100
  // 从站点配置中获取订单服务费计算值（这个值在会员等级中设置）
  const getOrderServiceFeeRate = () => {
    if (siteConfig && siteConfig.orderfee_value) {
      return parseFloat(siteConfig.orderfee_value) || 0;
    }
    return 0;
  };

  const orderServiceFeeRate = getOrderServiceFeeRate();
  const productServiceFee = (goodsMoney * orderServiceFeeRate) / 100;



  // 折扣率：用户的运费折扣
  const freightDiscountRate = onePayOrderInfo?.freight_discount || 100; // 默认100%（无折扣）

  // 运单服务费计算值：从会员等级中设置
  const waybillServiceFeeRate = onePayOrderInfo?.serve_discount || 0;

  // Calculate additional service fees from selected options
  const additionalServiceFee = serviceOptions.reduce((total, option) => {
    if (option.selected) {
      if (option.id === 'insurance') {
        // Insurance is 2% of product total
        return total + (subtotal * 0.02);
      }
      return total + option.price;
    }
    return total;
  }, 0);

  // 国际运费：显示折扣后的国际运费
  const discountedInternationalShippingFee = shippingFee * (freightDiscountRate / 100);

  // 包裹服务费：国际运费 * 运单服务费计算值 ÷ 100
  const packageServiceFee = (discountedInternationalShippingFee * waybillServiceFeeRate) / 100;



  // 包裹附加服务费：包裹的附加服务费用总和（从sendorder_server或order_serve获取）
  const packageAdditionalServiceFee = onePayOrderInfo?.sendorder_server ?
    Object.values(onePayOrderInfo.sendorder_server).reduce((sum, service) => {
      return sum + parseFloat(service.value || '0');
    }, 0) : (onePayOrderInfo?.order_serve ?
    Object.values(onePayOrderInfo.order_serve).reduce((sum, service) => {
      return sum + parseFloat(service.value || '0');
    }, 0) : additionalServiceFee);

  // 已节省：显示运费优惠的金额，计算方式：折扣前的国际运费减去折扣后的国际运费
  const savedAmount = shippingFee - discountedInternationalShippingFee;

  // Calculate package total
  const packageTotal = packageServiceFee + discountedInternationalShippingFee + packageAdditionalServiceFee;

  // Calculate grand total
  // 商品金额 = 该订单所有商品的金额总和
  // 到中国仓运费 = 商品的运费总和
  // 订单附加服务费 = 该订单所有商品的附加服务费用总和
  // 商品服务费 = 商品总金额 * 订单服务费计算值 ÷ 100
  const productTotal = goodsMoney + chinaWarehouseShippingFee + orderAdditionalServiceFee + productServiceFee;
  const grandTotal = Math.max(0, productTotal + packageTotal - couponDiscount);

  // Handle service option toggle
  const handleServiceToggle = (serviceId: string) => {
    setServiceOptions(prev =>
      prev.map(option =>
        option.id === serviceId
          ? { ...option, selected: !option.selected }
          : option
      )
    );
  };



  // Submit one-time payment order
  const handleSubmitOrder = async () => {
    const addressId = searchParams.get('address_id');
    const templateId = searchParams.get('template_id');

    // Validation
    if (!addressId || !templateId) {
      message.error(dict?.confirm?.onepayorder?.validation?.selectAddressAndShipping || 'Please select shipping address and shipping method');
      return;
    }

    if (!cart_ids || cart_ids_list.length === 0) {
      message.error(dict?.confirm?.onepayorder?.validation?.cartInfoMissing || 'Cart information is missing');
      return;
    }

    // Check if terms are agreed (if checkbox exists)
    const agreeTermsCheckbox = document.getElementById('agree-terms') as HTMLInputElement;
    if (agreeTermsCheckbox && !agreeTermsCheckbox.checked) {
      message.error(dict?.confirm?.onepayorder?.validation?.agreeTermsFirst || 'Please read and agree to the terms first');
      return;
    }

    setSubmitting(true);

    try {
      // Get service data from window.orderServices
      const serve = typeof window !== 'undefined' && window.orderServices?.getServiceData ?
        window.orderServices.getServiceData() : {};

      // Validate photo service remarks if present and actually selected
      if (serve.photo) {
        let photo: any = Object.values(serve.photo);
        try {
          // Check if photo service is actually selected (num > 0)
          const hasSelectedPhoto = photo.some((photoService: any) => photoService.num > 0);
          if (hasSelectedPhoto) {
            let remarks = JSON.parse(photo[0].remark);
            const hasEmpty = remarks.some((item: any) => item.trim() === '');
            if (hasEmpty) {
              message.error(dict?.confirm?.onepayorder?.validation?.fillPhotoRemarks || 'Please fill in photo remarks');
              return;
            }
          }
        } catch (e) {
          console.warn('Photo remark validation error:', e);
        }
      }

      // Prepare API parameters
      const params = {
        cart_ids: cart_ids_list.map(id => Number(id)),
        selectedaid: Number(addressId),
        did: Number(templateId),
        clienttype: 'pc',
        isrefill: '',
        server: serve,
        order_serve: ''
      };



      const res = await Api.createOnePayOrder(params);

      // 添加详细调试日志
      console.log('🔍 createOnePayOrder API 完整响应:', res);
      console.log('🔍 res.success:', res.success);
      console.log('🔍 res.data:', res.data);
      console.log('🔍 res.msg:', res.msg);
      console.log('🔍 res 的所有属性:', Object.keys(res));

      // 检查API响应：
      // 成功时：响应拦截器返回data.data，@catchError装饰器包装为{success: true, data: 实际数据}
      // 失败时：响应拦截器抛出错误，@catchError装饰器捕获并返回{success: false, msg: 错误信息}
      if (res.success) {
        console.log('✅ 订单提交成功，准备跳转');
        // 订单提交成功，清理保存的附加服务状态
        if (typeof window !== 'undefined' && window.clearOnePayOrderServiceState) {
          window.clearOnePayOrderServiceState();
        }

        message.success(dict?.confirm?.onepayorder?.messages?.orderSubmitSuccess || 'Order submitted successfully');
        // Redirect to one-time payment order list page
        router.push(`/${lng}/dashboard/onepayorders`);
      } else {
        console.log('❌ 订单提交失败，进入错误处理');
        console.log('🔍 失败原因 - res.success:', res.success);
        console.log('🔍 失败原因 - res.msg:', res.msg);
        console.log('🔍 失败原因 - res.code:', res.code);
        // 显示具体的错误信息
        const errorMsg = res.msg || res.message || dict?.confirm?.onepayorder?.messages?.orderSubmitFailed || 'Order submission failed';
        console.log('🔍 最终错误消息:', errorMsg);
        message.error(errorMsg);
        setSubmitting(false);
      }
    } catch (error) {
      console.error('Order submission error:', error);
      message.error(dict?.confirm?.onepayorder?.messages?.orderSubmitRetry || 'Order submission failed, please try again');
      setSubmitting(false);
    }
  };

  return (
    <div className="bg-white">
      {/* Three-column layout for bottom section */}
      <div className="grid grid-cols-3 gap-6">
        {/* Left column - Product Summary */}
        <div className="space-y-3">
          <h3 className="text-base font-medium text-gray-900 mb-4">{dict?.confirm?.onepayorder?.summary?.productSummary || 'Product Summary'}</h3>

          <div className="space-y-2 text-sm">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">{dict?.confirm?.onepayorder?.summary?.productAmount || 'Product Amount'}</span>
              <PriceDisplay value={goodsMoney} inline={true} />
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-600 flex items-center">
              {dict?.confirm?.onepayorder?.summary?.shippingToChina || 'Shipping to China'}
              </span>
              <PriceDisplay value={chinaWarehouseShippingFee} inline={true} />
            </div>


              <div className="flex justify-between items-center">
                <span className="text-gray-600 flex items-center">
                {dict?.confirm?.onepayorder?.summary?.orderAdditionalService || 'Order Additional Service Fee'}
                </span>
                <PriceDisplay value={orderAdditionalServiceFee} inline={true} />
              </div>


            <div className="flex justify-between items-center">
              <span className="text-gray-600 flex items-center">
              {dict?.confirm?.onepayorder?.summary?.productServiceFee || 'Product Service Fee'}
              </span>
              <PriceDisplay value={productServiceFee} inline={true} />
            </div>

            {couponDiscount > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600 text-[#FF6000]">{dict?.confirm?.onepayorder?.summary?.couponDiscount || 'Coupon Discount'}</span>
                <span className="text-[#FF6000]">-<PriceDisplay value={couponDiscount} inline={true} /></span>
              </div>
            )}

            <div className="border-t border-gray-200 pt-2 mt-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-900">{dict?.confirm?.onepayorder?.summary?.productPayable || 'Product Payable Amount'}</span>
                <PriceDisplay value={productTotal - couponDiscount} className="text-sm font-medium" />
              </div>
            </div>
          </div>
        </div>

        {/* Middle column - Package Additional Services */}
        <div className="space-y-3">
          <h3 className="text-base font-medium text-gray-900 mb-4">{dict?.confirm?.onepayorder?.summary?.packageAdditionalServices || 'Package Additional Services'}</h3>

          {/* Service options with checkboxes */}
          <div className="space-y-3 mb-4">
            {serviceOptions.map((option) => (
              <div key={option.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                 
                  <span className="text-sm text-gray-700">
                    {option.name} ({option.description})
                  </span>
                </div>
                <div
                  className={`w-4 h-4 border border-gray-300 cursor-pointer flex items-center justify-center ${
                    option.selected ? 'bg-gray-100' : ''
                  }`}
                  onClick={() => handleServiceToggle(option.id)}
                >
                  {option.selected && (
                    <span className="text-xs text-gray-600">✓</span>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Package summary section */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-900">{dict?.confirm?.onepayorder?.summary?.packageSummary || 'Package Summary'}</h4>

            <div className="space-y-1 text-sm">
              {freightDiscountRate < 100 && (
                <div className="flex justify-between items-center">
                  <span className="text-orange-500">{dict?.confirm?.onepayorder?.summary?.discountRate || 'Discount Rate'}</span>
                  <span className="text-orange-500">{freightDiscountRate}%</span>
                </div>
              )}

              {/* 运费详细分解 */}
              {shippingFeeDetails && (
                <>
                  {parseFloat(shippingFeeDetails.sendfee || '0') > 0 && (
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">{dict?.confirm?.onepayorder?.summary?.deliveryFee || 'Delivery Fee'}</span>
                      <PriceDisplay value={parseFloat(shippingFeeDetails.sendfee)} inline={true} />
                    </div>
                  )}

                  {parseFloat(shippingFeeDetails.customsfee || '0') > 0 && (
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">{dict?.confirm?.onepayorder?.summary?.customsFee || 'Customs Fee'}</span>
                      <PriceDisplay value={parseFloat(shippingFeeDetails.customsfee)} inline={true} />
                    </div>
                  )}

                  {parseFloat(shippingFeeDetails.serverfee || '0') > 0 && (
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">{dict?.confirm?.onepayorder?.summary?.transportServiceFee || 'Transport Service Fee'}</span>
                      <PriceDisplay value={parseFloat(shippingFeeDetails.serverfee)} inline={true} />
                    </div>
                  )}

                  {parseFloat(shippingFeeDetails.fuelfee || '0') > 0 && (
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">{dict?.confirm?.onepayorder?.summary?.fuelFee || 'Fuel Fee'}</span>
                      <PriceDisplay value={parseFloat(shippingFeeDetails.fuelfee)} inline={true} />
                    </div>
                  )}
                </>
              )}

              {/* 显示折扣后的国际运费 */}
              <div className="flex justify-between items-center">
                <span className="text-gray-600">{dict?.confirm?.onepayorder?.summary?.internationalShipping || 'International Shipping'}</span>
                <PriceDisplay value={discountedInternationalShippingFee} inline={true} />
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600">{dict?.confirm?.onepayorder?.summary?.packageServiceFee || 'Package Service Fee'}</span>
                <PriceDisplay value={packageServiceFee} inline={true} />
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600">{dict?.confirm?.onepayorder?.summary?.packageAdditionalServiceFee || 'Package Additional Service Fee'}</span>
                <PriceDisplay value={packageAdditionalServiceFee} inline={true} />
              </div>
            </div>

            <div className="border-t border-gray-200 pt-2 mt-3">
              <div className="flex justify-between items-center mb-1">
                <span className="text-sm font-medium text-gray-900">{dict?.confirm?.onepayorder?.summary?.packagePayable || 'Package Payable Amount'}</span>
                <PriceDisplay value={packageTotal} className="text-sm font-medium" />
              </div>

              {savedAmount > 0 && (
                <div className="space-y-1 text-xs text-gray-500">
                  <div className="flex justify-between">
                    <span>{dict?.confirm?.onepayorder?.summary?.saved || 'Saved'}</span>
                    <PriceDisplay value={savedAmount} inline={true} className="text-[#FF6000]" />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right column - Total */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-base font-medium text-gray-900">{dict?.confirm?.onepayorder?.summary?.total || 'Total'}</h3>
            <PriceDisplay value={grandTotal} className="text-xl font-bold" />
          </div>

          {/* Service tags */}
          <div className="flex flex-wrap text-xs">
            <span className="text-gray-600 rounded">{dict?.confirm?.onepayorder?.summary?.serviceTerms || 'Onebuy Service Terms'}</span>
            <span className="text-gray-600 rounded">{dict?.confirm?.onepayorder?.summary?.returnPolicy || 'Return & Exchange Policy'}</span>
            <span className="text-gray-600 rounded">{dict?.confirm?.onepayorder?.summary?.termsOfService || 'Terms of Service'}</span>
            <span className="text-gray-600 rounded">{dict?.confirm?.onepayorder?.summary?.privacyPolicy || 'Privacy Policy'}</span>
            <span className="text-gray-600 rounded">{dict?.confirm?.onepayorder?.summary?.faq || 'FAQ'}</span>
          </div>

          {/* Checkbox option */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="agree-terms"
              className="w-4 h-4 text-[#FF6000] border-gray-300 rounded focus:ring-[#FF6000]"
            />
            <label htmlFor="agree-terms" className="text-sm text-gray-700">
              {dict?.confirm?.onepayorder?.summary?.agreeTerms || 'I have read and agree to the terms'}
            </label>
          </div>



          {/* Submit button */}
          <Button
            type="primary"
            size="large"
            block
            loading={submitting}
            onClick={handleSubmitOrder}
            disabled={!searchParams.get('address_id') || !searchParams.get('template_id')}
            className="bg-[#FF6000] hover:bg-[#E55A00] border-[#FF6000] h-12 text-base font-medium rounded disabled:bg-gray-400 disabled:border-gray-400"
          >
            {submitting ? (dict?.confirm?.onepayorder?.summary?.submitting || 'Submitting...') : (dict?.confirm?.onepayorder?.summary?.submit || 'Submit')}
          </Button>

          {/* Notice section */}
          <div className="bg-orange-50 border border-orange-200 p-3 rounded">
            <div className="text-orange-700 font-medium text-sm mb-2">{dict?.confirm?.onepayorder?.summary?.notice || 'Notice'}</div>
            <div className="text-orange-600 text-xs leading-relaxed">
            {dict?.confirm?.onepayorder?.summary?.noticeText || 'After submitting the order and payment, we will purchase and ship for you. Please wait patiently. After shipping, you can track the package through the international tracking number in the package.'}
            </div>
          </div>

          {/* Loading state */}
          {loading && (
            <div className="text-center text-gray-500 text-sm">
              {dict?.confirm?.onepayorder?.summary?.loading || 'Loading data...'}
            </div>
          )}
        </div>
      </div>


    </div>
  );
}
