import { useRouter } from 'next/navigation';
import Toast from '@/components/Toast';

/**
 * 标准化错误码定义
 */
export const ERROR_CODES = {
  SUCCESS: 200,
  API_NOT_FOUND: 9000,
  DB_WRITE_ERROR: 9001,
  THIRD_PARTY_API_ERROR: 9002,
  REDIS_ERROR: 9003,
  EMAIL_SEND_ERROR: 9004,
  SMS_SEND_ERROR: 9005,
  SYSTEM_ERROR: 9999,
  TOKEN_INVALID: 10000,
  FUNCTION_NOT_OPEN: 10001,
  PERMISSION_DENIED: 10002,
  MISSING_REQUIRED_PARAMS: 13000,
  PARAM_TYPE_ERROR: 13001,
  TIMESTAMP_ERROR: 13002,
  SIGN_VERIFICATION_FAILED: 13003,
  PARAM_FORMAT_ERROR: 13004,
  PARAM_VALUE_NOT_ALLOWED: 13005,
  PARAM_LENGTH_ERROR: 13006
} as const;

/**
 * 错误码对应的默认消息
 */
export const ERROR_MESSAGES = {
  [ERROR_CODES.SUCCESS]: '成功',
  [ERROR_CODES.API_NOT_FOUND]: '接口不存在',
  [ERROR_CODES.DB_WRITE_ERROR]: '数据库写入错误',
  [ERROR_CODES.THIRD_PARTY_API_ERROR]: '请求第三方接口错误',
  [ERROR_CODES.REDIS_ERROR]: 'Redis错误',
  [ERROR_CODES.EMAIL_SEND_ERROR]: '邮件发送错误',
  [ERROR_CODES.SMS_SEND_ERROR]: '短信发送错误',
  [ERROR_CODES.SYSTEM_ERROR]: '服务器内部错误',
  [ERROR_CODES.TOKEN_INVALID]: 'TOKEN失效或错误',
  [ERROR_CODES.FUNCTION_NOT_OPEN]: '功能未开放',
  [ERROR_CODES.PERMISSION_DENIED]: '权限不足',
  [ERROR_CODES.MISSING_REQUIRED_PARAMS]: '缺少必须参数',
  [ERROR_CODES.PARAM_TYPE_ERROR]: '参数类型错误',
  [ERROR_CODES.TIMESTAMP_ERROR]: 'timestamp异常',
  [ERROR_CODES.SIGN_VERIFICATION_FAILED]: '业务参数sign校验失败',
  [ERROR_CODES.PARAM_FORMAT_ERROR]: '业务参数格式错误',
  [ERROR_CODES.PARAM_VALUE_NOT_ALLOWED]: '参数不是可选值',
  [ERROR_CODES.PARAM_LENGTH_ERROR]: '参数内容长度不符'
} as const;

/**
 * 统一的错误处理工具函数
 */
export class ErrorHandler {
  /**
   * 检查是否是需要登录的错误
   */
  static isAuthError(error: any): boolean {
    // 检查HTTP状态码
    if (error?.response?.status === 401) {
      return true;
    }

    // 检查响应中的错误码 - 使用标准化错误码
    if (error?.response?.data?.code === ERROR_CODES.TOKEN_INVALID ||
        error?.code === ERROR_CODES.TOKEN_INVALID) {
      return true;
    }

    // 检查错误消息
    const message = error?.response?.data?.msg || error?.message || '';
    if (message.includes('登录') ||
        message.includes('未授权') ||
        message.includes('token') ||
        message.includes('unauthorized') ||
        message.includes('login')) {
      return true;
    }

    return false;
  }

  /**
   * 根据错误码获取默认错误消息
   */
  static getErrorMessage(code: number): string {
    return ERROR_MESSAGES[code as keyof typeof ERROR_MESSAGES] || '未知错误';
  }

  /**
   * 检查是否是权限相关错误
   */
  static isPermissionError(error: any): boolean {
    const code = error?.response?.data?.code || error?.code;
    return code === ERROR_CODES.PERMISSION_DENIED || code === ERROR_CODES.FUNCTION_NOT_OPEN;
  }

  /**
   * 检查是否是参数相关错误
   */
  static isParamError(error: any): boolean {
    const code = error?.response?.data?.code || error?.code;
    return code >= 13000 && code <= 13006;
  }

  /**
   * 检查是否是系统错误
   */
  static isSystemError(error: any): boolean {
    const code = error?.response?.data?.code || error?.code;
    return (code >= 9000 && code <= 9005) || code === ERROR_CODES.SYSTEM_ERROR;
  }

  /**
   * 获取国际化错误信息
   */
  static getLocalizedMessage(key: string, lng: string, fallback: string): string {
    // 这里可以根据语言返回对应的错误信息
    const messages: Record<string, Record<string, string>> = {
      'zh-cn': {
        'auth_required': '请登录后重试',
        'permission_denied': '权限不足',
        'param_error': '参数错误',
        'system_error': '系统错误，请稍后重试',
        'network_error': '网络错误，请稍后重试',
        'operation_failed': '操作失败，请重试',
        'add_cart_failed': '添加失败',
        'fetch_points_failed': '获取积分记录失败',
        'buy_failed': '购买失败，请重试'
      },
      'en': {
        'auth_required': 'Please login and try again',
        'permission_denied': 'Permission denied',
        'param_error': 'Parameter error',
        'system_error': 'System error, please try again later',
        'network_error': 'Network error, please try again later',
        'operation_failed': 'Operation failed, please try again',
        'add_cart_failed': 'Failed to add',
        'fetch_points_failed': 'Failed to fetch points',
        'buy_failed': 'Purchase failed, please try again'
      },
      'ja': {
        'auth_required': 'ログイン後に再試行してください',
        'permission_denied': '権限が不足しています',
        'param_error': 'パラメータエラー',
        'system_error': 'システムエラー、後でもう一度お試しください',
        'network_error': 'ネットワークエラー、後でもう一度お試しください',
        'operation_failed': '操作に失敗しました、再試行してください',
        'add_cart_failed': '追加に失敗しました',
        'fetch_points_failed': 'ポイント記録の取得に失敗しました',
        'buy_failed': '購入に失敗しました、再試行してください'
      }
    };

    return messages[lng]?.[key] || fallback;
  }

  /**
   * 处理认证错误，跳转到登录页面
   */
  static handleAuthError(lng: string, router: any, errorMessage?: string) {
    const defaultMessage = this.getLocalizedMessage('auth_required', lng, '请登录后重试');
    Toast.error(errorMessage || defaultMessage);

    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname + window.location.search;
      const loginUrl = `/${lng}/login?callback=${encodeURIComponent(currentPath)}`;
      router.push(loginUrl);
    }
  }

  /**
   * 统一的API错误处理
   */
  static handleApiError(error: any, lng: string, router: any, defaultMessage?: string) {
    console.error('API Error:', error);

    // 获取错误码和消息
    const errorCode = error?.response?.data?.code || error?.code;
    const errorMsg = error?.response?.data?.msg || error?.message;

    // 处理认证错误
    if (this.isAuthError(error)) {
      this.handleAuthError(lng, router, errorMsg);
      return;
    }

    // 处理权限错误
    if (this.isPermissionError(error)) {
      const message = errorMsg || this.getErrorMessage(errorCode) || this.getLocalizedMessage('permission_denied', lng, '权限不足');
      Toast.error(message);
      return;
    }

    // 处理参数错误
    if (this.isParamError(error)) {
      const message = errorMsg || this.getErrorMessage(errorCode) || this.getLocalizedMessage('param_error', lng, '参数错误');
      Toast.error(message);
      return;
    }

    // 处理系统错误
    if (this.isSystemError(error)) {
      const message = errorMsg || this.getErrorMessage(errorCode) || this.getLocalizedMessage('system_error', lng, '系统错误，请稍后重试');
      Toast.error(message);
      return;
    }

    // 处理其他错误
    const errorMessage = errorMsg ||
                        (errorCode ? this.getErrorMessage(errorCode) : '') ||
                        defaultMessage ||
                        this.getLocalizedMessage('operation_failed', lng, '操作失败，请重试');
    Toast.error(errorMessage);
  }

  /**
   * 检查响应是否成功，如果是认证错误则处理
   */
  static checkResponseAuth(response: any, lng: string, router: any): boolean {
    if (!response?.success && response?.code === ERROR_CODES.TOKEN_INVALID) {
      this.handleAuthError(lng, router, response?.msg);
      return false;
    }
    return true;
  }
}

/**
 * React Hook 版本的错误处理器
 */
export function useErrorHandler(lng: string) {
  const router = useRouter();
  
  const handleError = (error: any, defaultMessage?: string) => {
    ErrorHandler.handleApiError(error, lng, router, defaultMessage);
  };
  
  const handleAuthError = (errorMessage?: string) => {
    ErrorHandler.handleAuthError(lng, router, errorMessage);
  };
  
  const checkResponseAuth = (response: any): boolean => {
    return ErrorHandler.checkResponseAuth(response, lng, router);
  };
  
  return {
    handleError,
    handleAuthError,
    checkResponseAuth,
    isAuthError: ErrorHandler.isAuthError
  };
}
