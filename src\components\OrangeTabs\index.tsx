'use client';

import { Tabs, TabsProps } from 'antd';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import React, { ReactNode } from 'react';
import styles from './index.module.css';

interface OrangeTabsProps extends TabsProps {
  children?: ReactNode;
}

export default function OrangeTabs({ children, className, ...props }: OrangeTabsProps) {
  const tabsClassName = `${styles.orangeTabs} ${className || ''}`.trim();

  return (
    <AntdConfigProvider>
      <Tabs {...props} className={tabsClassName}>
        {children && children}
      </Tabs>
    </AntdConfigProvider>
  );
}
