'use client';

import React from 'react';
import { createRoot } from 'react-dom/client';
import styles from './styles.module.css';

type MessageType = 'success' | 'error';

interface MessageProps {
  content: string;
  type: MessageType;
}

const MessageComponent: React.FC<MessageProps> = ({ content, type }) => {
  return (
    <div className={`${styles.messageContainer} ${styles[type]}`}>
      {content}
    </div>
  );
};

let messageContainer: HTMLDivElement | null = null;

const createContainer = () => {
  const container = document.createElement('div');
  container.id = 'message-container';
  document.body.appendChild(container);
  return container;
};

const show = (content: string, type: MessageType, duration: number = 3000) => {
  if (!messageContainer) {
    messageContainer = createContainer();
  }

  const messageElement = document.createElement('div');
  messageContainer.appendChild(messageElement);

  const root = createRoot(messageElement);
  root.render(<MessageComponent content={content} type={type} />);

  setTimeout(() => {
    if (messageElement && messageContainer) {
      root.unmount();
      messageContainer.removeChild(messageElement);
    }
  }, duration);
};

const Message = {
  success: (content: string, duration?: number) => show(content, 'success', duration),
  error: (content: string, duration?: number) => show(content, 'error', duration),
};

export default Message; 