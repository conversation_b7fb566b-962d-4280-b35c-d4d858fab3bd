import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetWind3
} from 'unocss'

export default defineConfig({
  rules: [
    ['custom-h-48px', { height: '48px' }],
  ],
  shortcuts: {
    'custom-btn': 'py-2 px-4 bg-blue-500 text-white rounded hover:bg-blue-600',
  },
  safelist: [
    'i-material-symbols:favorite',
    'i-material-symbols:package-2-sharp',
    'i-material-symbols:account-balance-wallet',
    'i-material-symbols:android-messages',
    'i-material-symbols:tv-options-input-settings',
    'i-material-symbols:3p',
    'i-material-symbols:order-approve-rounded',
    'i-material-symbols:warehouse',
    'i-material-symbols:person',
    'i-material-symbols:location-on',
    'i-material-symbols:question-mark',
    'i-material-symbols:payments'
  ],
  presets: [
    presetWind3({
      // dark: 'media',
    }),
    presetAttributify(),
    presetIcons({
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle',
      },
    }),
  ],
})