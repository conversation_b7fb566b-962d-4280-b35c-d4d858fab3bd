'use client'
import React, { useState, useEffect } from 'react'
import { Api, AddressItem } from '@/request/api'
import { message, App } from 'antd'
import AddressModal from '@/components/AddressModal'
import { useParams } from 'next/navigation';
import { getDictionary } from "@/dictionaries";
export default function AddressCard() {
  const [showAddressModal, setShowAddressModal] = useState(false)
  const [loading, setLoading] = useState(true)
  const [addressList, setAddressList] = useState<AddressItem[]>([])
  const [activeIndex, setActiveIndex] = useState(0)
  const [currentAddress, setCurrentAddress] = useState<AddressItem | null>(null)
  const [messageApi, contextHolder] = message.useMessage()

  const { lng } = useParams();
  const [dict, setDict] = useState<any>(null); // 添加字典状态
   // 异步获取字典数据
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };

    fetchDictionary();
  }, [lng]);

  useEffect(() => {
    const fetchAddressList = async () => {
      const { data } = await Api.getAddressList();
      if (data.length > 0) {
        let list = data.sort((a: any, b: any) => b.isdefault - a.isdefault)
        setAddressList(list)
      }
      setLoading(false);
    };
    fetchAddressList();
  }, []);

  const handleEdit = (item: AddressItem, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止冒泡，避免触发选中效果
    setCurrentAddress(item);
    setShowAddressModal(true);
  };

  const handleCloseModal = () => {
    setShowAddressModal(false);
    setCurrentAddress(null);
  };

  if (loading) {
    return <div className="flex justify-center items-center h-full">
      <div className="w-10 h-10 border-t-transparent border-solid rounded-full animate-spin border-t-gray-900 border-8"></div>
    </div>
  }

  return (
    <App>
      {contextHolder}
      <div className="p-0.5 relative space-y-3">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-2">
            <h2 className="text-[15px] font-medium">{dict?.dashboard?.address?.text || '地址'}</h2>
            <span className="text-gray-400 text-xs">({addressList.length})</span>
          </div>
          <button
            onClick={() => {
              setShowAddressModal(true);
              setCurrentAddress(null);
            }}
            className="text-[var(--base-color)] border border-[var(--base-color)] px-4 py-1.5 rounded-full flex items-center gap-1.5 hover:bg-[var(--base-color)] hover:text-white transition-all duration-300 text-xs font-medium shadow-sm hover:shadow-md"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            新增地址
          </button>
        </div>

        <AddressModal
          open={showAddressModal}
          onCancel={handleCloseModal}
          onSuccess={() => {
            handleCloseModal();
            const fetchAddressList = async () => {
              const { data } = await Api.getAddressList();
              if (data.length > 0) {
                let list = data.sort((a: any, b: any) => b.isdefault - a.isdefault)
                setAddressList(list)
              }
            };
            fetchAddressList();
          }}
          currentAddress={currentAddress}
          dict={dict}
        />

        {addressList.map((item: any, index: number) => (
          <div
            key={item.id}
            onClick={() => setActiveIndex(index)}
            className={`
              relative bg-white rounded-lg transition-all duration-300 ease-in-out
              hover:shadow-[0_4px_20px_rgb(0,0,0,0.03)] cursor-pointer p-4
              ${activeIndex === index
                ? 'border-[var(--base-color)] border shadow-[0_4px_20px_rgb(0,0,0,0.06)]'
                : 'border border-gray-100'
              }
            `}
          >
            {/* 选中状态圆圈 - 右上方 */}
            {activeIndex === index && (
              <div className="absolute top-3 right-3 w-6 h-6 bg-[var(--base-color)] rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}

            {/* 默认地址标签 - 左下方 */}
            {item.isdefault ? (
              <div
                style={{ backgroundColor: 'var(--base-color)' }}
                className="absolute bottom-3 left-3 text-white px-2 py-0.5 text-[10px] rounded-full font-medium shadow-sm backdrop-blur-sm bg-opacity-90"
              >
                {dict?.dashboard?.address?.default || '默认'}
              </div>
            ) : null}

            <div className="flex flex-col gap-2 pr-8 pb-8">
              <div className="flex items-center gap-3">
                <span className="font-medium text-sm tracking-wide">{item.consignee}</span>
                <span className="text-gray-500 text-sm tracking-wide">{item.telephone}</span>
              </div>
              <div className="text-gray-600 text-xs leading-relaxed tracking-wide">
                {item.address}
                {item.door_number && <span className="ml-1 text-gray-400">#{item.door_number}</span>}
              </div>
              {item.zipcode && (
                <div className="text-gray-400 text-[10px]">
                  {dict?.dashboard?.address?.postcode || '邮编'}: {item.zipcode}
                </div>
              )}
            </div>
            <button
              onClick={(e) => handleEdit(item, e)}
              className="absolute bottom-3 right-3 text-[var(--base-color)] hover:bg-[var(--base-color)] hover:text-white px-3 py-1 rounded-full text-xs transition-all duration-300 border border-[var(--base-color)] flex items-center gap-1.5 font-medium hover:shadow group"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 transition-transform duration-300 group-hover:rotate-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
              <span>编辑</span>
            </button>
          </div>
        ))}

        {addressList.length === 0 && (
          <div className="relative bg-white rounded-lg p-8 border border-gray-100 shadow-sm">
            <div className="flex flex-col items-center gap-3">
              <div className="text-gray-400 text-sm mb-1">{dict?.dashboard?.address?.empty || '暂无收货地址'}</div>
              <button
                onClick={() => setShowAddressModal(true)}
                className="text-[var(--base-color)] hover:bg-[var(--base-color)] hover:text-white px-4 py-1.5 rounded-full text-xs transition-all duration-300 border border-[var(--base-color)] flex items-center gap-1.5 font-medium hover:shadow group"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 transition-transform duration-300 group-hover:scale-110" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                <span>{dict?.dashboard?.address?.addNew || '添加地址'}</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </App>
  )
}