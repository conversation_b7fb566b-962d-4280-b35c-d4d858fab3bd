// middleware.ts
import { NextRequest, NextResponse } from 'next/server'
import { match } from '@formatjs/intl-localematcher'
import Negotiator from 'negotiator'
import { locales, defaultLocale } from '@/config'
// console.log(process.env);
let currentLocale = defaultLocale;
function getLocale(request: NextRequest) {
  const acceptLanguage = request.headers.get('accept-language') || '';
  const headers = { 'accept-language': acceptLanguage };

  // console.log('Accept-Language Header:', acceptLanguage);

  const languages = new Negotiator({ headers }).languages();
  // console.log('Negotiator Languages:', languages);

  const matchedLocale = match(languages, locales, defaultLocale);
  // console.log('Matched Locale:', matchedLocale, 'Default Locale:', defaultLocale);
  return matchedLocale;
}

let loginPages = [`dashboard/home`, `dashboard/cart`, `dashboard/favorites`, `dashboard/warehouse`, `dashboard/orders`, `dashboard/packages`, `dashboard/wallet`, `dashboard/messages`, `dashboard/account`, `dashboard/referral`];

loginPages = loginPages.map(page => `/${currentLocale}/${page}`)

export function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl
  
  // 检查是否为静态资源路径
  const isStaticResource = /\.(svg|png|jpg|jpeg|gif|ico|css|js)$/.test(pathname)
  
  // 如果是静态资源请求，直接返回，不做重定向
  if (isStaticResource) {
    return NextResponse.next()
  }
  
  // 判断请求路径中是否已存在语言，已存在语言则跳过
  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  )
  
  // 从路径中提取当前语言代码
  let currentPathLocale = ''
  if (pathnameHasLocale) {
    const pathSegments = pathname.split('/')
    if (pathSegments.length > 1 && locales.includes(pathSegments[1])) {
      currentPathLocale = pathSegments[1]
    }
  }

  // 获取匹配的 locale - 只有在路径中不存在语言代码时才需要
  const locale = pathnameHasLocale ? currentPathLocale : getLocale(request)
  
  // 处理货币参数
  const currencyParam = searchParams.get('currency')
  let response: NextResponse

  if (pathnameHasLocale) {
    let tokenName = 'token'
    if(process.env.NEXT_PUBLIC_BACKEND_TYPE === '6'){
      tokenName = 'access_token'
    }
    // 如果已经包含语言代码，检查用户是否登录
    const token = request.cookies.get(tokenName)?.value
    const loginPages = [
      `dashboard/home`, `dashboard/cart`, `dashboard/favorites`,
      `dashboard/warehouse`, `dashboard/orders`, `dashboard/packages`,
      `dashboard/wallet`, `dashboard/messages`, `dashboard/account`,
      `dashboard/referral`
    ];

    // 定义不需要登录验证的页面
    const publicPages = [
      `help`, `sizecomparison`, `estimate`, `sharepromotion`
    ];

    const localizedLoginPages = loginPages.map(page => `/${currentPathLocale}/${page}`)
    const localizedPublicPages = publicPages.map(page => `/${currentPathLocale}/${page}`)

    // 检查当前路径是否为公开页面
    const isPublicPage = localizedPublicPages.some(publicPage =>
      pathname === publicPage || pathname.startsWith(`${publicPage}/`)
    )

    if (!token && localizedLoginPages.includes(pathname) && !isPublicPage) {
      const loginUrl = new URL(`/${currentPathLocale}/login`, request.url)

      // 添加callback参数保存原始页面路径（包含查询参数）
      const originalUrl = pathname + (request.nextUrl.search ? request.nextUrl.search : '')
      loginUrl.searchParams.set('callback', originalUrl)

      // 保留所有URL参数（除了callback，避免重复）
      searchParams.forEach((value, key) => {
        if (key !== 'callback') {
          loginUrl.searchParams.set(key, value)
        }
      })

      response = NextResponse.redirect(loginUrl)
    } else {
      response = NextResponse.next()
    }
  } else {
    // 重定向，如 /products 重定向到 /zh-cn/products
    const url = new URL(`/${locale}${pathname === '/' ? '' : pathname}`, request.url)
    
    // 保留所有URL参数
    searchParams.forEach((value, key) => {
      url.searchParams.set(key, value)
    })
    
    response = NextResponse.redirect(url)
  }
  
  // 如果URL中包含货币参数，则将其存入cookie
  if (currencyParam) {
    // 设置30天过期的cookie
    response.cookies.set({
      name: 'currency',
      value: currencyParam,
      path: '/',
      maxAge: 60 * 60 * 24 * 30, // 30天
      sameSite: 'lax'
    })
  }
  
  // 将当前路径的语言代码添加到请求头中
  if (currentPathLocale) {
    response.headers.set('x-path-locale', currentPathLocale)
  }
  
  // 将当前完整路径添加到请求头中
  response.headers.set('x-original-pathname', pathname)
  
  return response
}

export const config = {
  matcher: [
    // 排除 API 路由、Next.js 内部路由和 web 路径
    '/((?!api|_next|web|other|favicon.ico).*)'
  ],
}

