'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react'
import Image from 'next/image'
import { useParams, useSearchParams } from 'next/navigation'
import { fixedUrl } from '@/utils';
import { Checkbox, InputNumber, Input, Collapse } from 'antd';
import { Api } from '@/request/api';
import message from '@/components/CustomMessage';
import { formatCurrency } from '@/utils/currency';
interface Product {
    id: number;
    goodsimg: string;
    goodsname: string;
    skuname: string;
    goodsprice: string;
    goodsnum: number;
    [key: string]: any;
}

interface OrderItemProps {
    supportReturn?: boolean;
    products?: Product[];
    dict?: any;
    showAccessorialService?: boolean;
    onServiceChange?: (productId: number, services: any) => void;
}

// 声明全局对象用于存储服务数据
declare global {
    interface Window {
        orderServices: {
            serverList: any;
            productServices: Record<number, {
                selectedServices: Record<string, boolean>,
                photoCount: number,
                photoRemarks: string[]
            }>;
            getServiceData?: () => any;
            clearServiceState?: () => void;
        };
    }
}

export default function OrderItem({
    supportReturn = false,
    products = [],
    dict = {},
    showAccessorialService = true,
    onServiceChange
}: OrderItemProps) {
    // 获取当前语言
    const params = useParams();
    const lng = params?.lng || 'zh-cn';

    // 获取URL参数
    const searchParams = useSearchParams();
    const [itemData, setItemData] = useState<any>(null);

    // 解析URL中的itemData参数
    useEffect(() => {
        const itemDataParam = searchParams.get('itemData');
        if (itemDataParam && itemDataParam.trim() !== '') {
            try {
                // 先尝试解码URL参数
                let decodedParam;
                try {
                    decodedParam = decodeURIComponent(itemDataParam);
                } catch (decodeError) {
                    console.warn('Failed to decode itemData URL parameter, trying direct parse:', decodeError);
                    decodedParam = itemDataParam;
                }

                // 然后尝试解析JSON
                const decodedData = JSON.parse(decodedParam);
                setItemData(decodedData);
                console.log('Successfully parsed itemData:', decodedData);
            } catch (error) {
                console.error('Failed to parse itemData from URL:', error);
                console.log('Raw itemData parameter:', itemDataParam);
                // 设置为null，让组件使用默认数据
                setItemData(null);
            }
        }
    }, [searchParams]);
    
    // 服务列表和选择状态
    const [serverList, setServerList] = useState<any>({});
    const [productServices, setProductServices] = useState<Record<number, {
        selectedServices: Record<string, boolean>,
        photoCount: number,
        photoRemarks: string[]
    }>>({});

    // 从localStorage恢复服务选择状态
    const loadServiceStateFromStorage = useCallback(() => {
        if (typeof window !== 'undefined') {
            try {
                const savedState = localStorage.getItem('orderServiceState');
                if (savedState) {
                    const parsedState = JSON.parse(savedState);
                    console.log('恢复附加服务选择状态:', parsedState);
                    setProductServices(parsedState);
                    return parsedState;
                }
            } catch (error) {
                console.error('恢复附加服务状态失败:', error);
            }
        }
        return {};
    }, []);

    // 保存服务选择状态到localStorage
    const saveServiceStateToStorage = useCallback((services: typeof productServices) => {
        if (typeof window !== 'undefined') {
            try {
                localStorage.setItem('orderServiceState', JSON.stringify(services));
                console.log('保存附加服务选择状态:', services);
            } catch (error) {
                console.error('保存附加服务状态失败:', error);
            }
        }
    }, []);

    // 初始化全局对象
    useEffect(() => {
        if (typeof window !== 'undefined') {
            window.orderServices = {
                serverList: {},
                productServices: {}
            };

            // 恢复之前保存的状态
            loadServiceStateFromStorage();
        }
    }, [loadServiceStateFromStorage]);

    // 更新全局对象 - 使用 useCallback 来避免不必要的更新
    const updateGlobalServices = useCallback(() => {
        if (typeof window !== 'undefined') {
            window.orderServices.serverList = serverList;
            window.orderServices.productServices = productServices;
        }
    }, [serverList, productServices]);

    useEffect(() => {
        updateGlobalServices();
    }, [updateGlobalServices]);

    // 使用 useMemo 来避免 products 数组的不必要重新创建
    const memoizedProducts = useMemo(() => products, [JSON.stringify(products.map(p => ({ id: p.id, goodsname: p.goodsname })))]);

    // 初始化每个产品的服务选择状态
    useEffect(() => {
        if (!memoizedProducts || memoizedProducts.length === 0) return;

        // 先尝试从localStorage恢复状态
        const savedState = loadServiceStateFromStorage();

        const initialServices: Record<number, {
            selectedServices: Record<string, boolean>,
            photoCount: number,
            photoRemarks: string[]
        }> = {};

        memoizedProducts.forEach(product => {
            // 如果有保存的状态且包含当前产品，使用保存的状态
            if (savedState[product.id]) {
                initialServices[product.id] = savedState[product.id];
            } else {
                // 否则使用默认状态
                initialServices[product.id] = {
                    selectedServices: {},
                    photoCount: 1,
                    photoRemarks: ['']
                };
            }
        });

        setProductServices(initialServices);
    }, [memoizedProducts, loadServiceStateFromStorage]);

    // 获取附加服务列表
    const fetchServerList = async () => {
        try {
            const res = await Api.getServerList('goods')
            if (res.success) {
                setServerList(res.data)
            }
        } catch (error) {
            message.error('获取附加服务失败')
        }
    }

    useEffect(() => {
        fetchServerList()
    }, []);

    // 处理服务选择 - 使用 useCallback 避免不必要的重新创建
    const handleServiceSelect = useCallback((productId: number, key: string) => {
        setProductServices(prev => {
            const productService = prev[productId] || {
                selectedServices: {},
                photoCount: 1,
                photoRemarks: ['']
            };

            const isCurrentlySelected = productService.selectedServices[key] || false;

            const updatedService = {
                ...productService,
                selectedServices: {
                    ...productService.selectedServices,
                    [key]: !isCurrentlySelected
                }
            };

            // 如果选择了照片服务，初始化照片数量和备注
            if (key === 'photo') {
                if (!isCurrentlySelected) {
                    updatedService.photoCount = 1;
                    updatedService.photoRemarks = [''];
                } else {
                    updatedService.photoCount = 0;
                    updatedService.photoRemarks = [];
                }
            }

            const result = {
                ...prev,
                [productId]: updatedService
            };

            // 保存状态到localStorage
            saveServiceStateToStorage(result);

            // 通知父组件服务变化
            if (onServiceChange) {
                onServiceChange(productId, result[productId]);
            }

            return result;
        });
    }, [onServiceChange, saveServiceStateToStorage]);

    // 处理照片数量变化 - 使用 useCallback 避免不必要的重新创建
    const handlePhotoCountChange = useCallback((productId: number, count: number) => {
        setProductServices(prev => {
            const productService = prev[productId];
            if (!productService) return prev;

            let newRemarks = [...productService.photoRemarks];
            if (count > productService.photoRemarks.length) {
                // 如果新增照片，添加空备注
                newRemarks.push(...Array(count - productService.photoRemarks.length).fill(''));
            } else {
                // 如果减少照片，截取数组
                newRemarks = newRemarks.slice(0, count);
            }

            const result = {
                ...prev,
                [productId]: {
                    ...productService,
                    photoCount: count,
                    photoRemarks: newRemarks
                }
            };

            // 保存状态到localStorage
            saveServiceStateToStorage(result);

            if (onServiceChange) {
                onServiceChange(productId, result[productId]);
            }

            return result;
        });
    }, [onServiceChange, saveServiceStateToStorage]);

    // 处理照片备注变化 - 使用 useCallback 避免不必要的重新创建
    const handlePhotoRemarkChange = useCallback((productId: number, index: number, remark: string) => {
        setProductServices(prev => {
            const productService = prev[productId];
            if (!productService) return prev;

            const newRemarks = [...productService.photoRemarks];
            newRemarks[index] = remark;

            const result = {
                ...prev,
                [productId]: {
                    ...productService,
                    photoRemarks: newRemarks
                }
            };

            // 保存状态到localStorage
            saveServiceStateToStorage(result);

            if (onServiceChange) {
                onServiceChange(productId, result[productId]);
            }

            return result;
        });
    }, [onServiceChange]);

    // 获取服务数据用于提交订单 - 使用 useCallback 避免不必要的重新创建
    const getServiceData = useCallback(() => {
        const serve: any = {};

        // 遍历所有产品
        Object.entries(productServices).forEach(([productId, service]) => {
            const pid = Number(productId);

            // 处理照片服务
            if (service.selectedServices['photo'] && service.photoCount > 0) {
                if (!serve.photo) {
                    serve.photo = {};
                }
                serve.photo[pid] = {
                    num: service.photoCount,
                    remark: JSON.stringify(service.photoRemarks)
                };
            }

            // 处理其他服务
            Object.entries(service.selectedServices).forEach(([key, selected]) => {
                if (selected && key !== 'photo') {
                    if (!serve[key]) {
                        serve[key] = [];
                    }
                    serve[key].push(pid);
                }
            });
        });

        return serve;
    }, [productServices]);

    // 清理保存的服务状态
    const clearServiceState = useCallback(() => {
        if (typeof window !== 'undefined') {
            try {
                localStorage.removeItem('orderServiceState');
                console.log('已清理附加服务选择状态');
            } catch (error) {
                console.error('清理附加服务状态失败:', error);
            }
        }
    }, []);

    // 添加到window对象，以便OrderSummary可以访问
    useEffect(() => {
        if (typeof window !== 'undefined') {
            window.orderServices.getServiceData = getServiceData;
            window.orderServices.clearServiceState = clearServiceState;
        }
    }, [getServiceData, clearServiceState]);

    return (
        <div>
            {memoizedProducts.map((product) => {
                const productService = productServices[product.id] || {
                    selectedServices: {},
                    photoCount: 1,
                    photoRemarks: ['']
                };
                
                return (
                    <div key={product.id} className="flex flex-col border-b pb-4 mb-2">
                        {/* 产品信息部分 */}
                        <div className="flex justify-between items-start mb-4">
                            <div className="flex items-start">
                                <div className="flex-shrink-0 mr-4">
                                    <Image draggable={false} src={fixedUrl(product.goodsimg)} alt={product.goodsname} width={80} height={80} className="border rounded-md object-cover" />
                                </div>
                                <div className="flex flex-col flex-1">
                                    <div className="text-sm font-medium">{product.goodsname}</div>
                                    <div className="text-xs text-gray-500 mt-1">{product.skuname}</div>
                                    {/* 货架号显示 */}
                                    {product.warehouse && (
                                        <div className="text-xs text-gray-500 mt-1">
                                            {dict?.dashboard?.previewPackages?.detail?.warehouse || '货架号'}: {product.warehouse}
                                        </div>
                                    )}
                                    {/* 商品编号、重量和体积信息在同一行 */}
                                    {(() => {
                                        // 调试：显示所有可能的编号字段
                                        console.log('Product data for ID check:', {
                                            goodssn: product.goodssn,
                                            goods_sn: product.goods_sn,
                                            sn: product.sn,
                                            num_iid: product.num_iid,
                                            id: product.id,
                                            goods_id: product.goods_id
                                        });

                                        const productNumber = product.goodssn || product.goods_sn || product.sn || product.num_iid || product.goods_id || product.id;
                                        const hasProductNumber = productNumber && productNumber !== '' && productNumber !== '0';
                                        const hasWeight = product.goodsweight !== undefined && product.goodsweight !== null && product.goodsweight > 0;
                                        const hasVolume = product.goodsvolume !== undefined && product.goodsvolume !== null && product.goodsvolume > 0;

                                        // 只有当至少有一个信息需要显示时才渲染这一行
                                        if (!hasProductNumber && !hasWeight && !hasVolume) {
                                            return null;
                                        }

                                        return (
                                            <div className="text-xs text-gray-400 mt-1 flex justify-between items-center">
                                                <div>
                                                    {hasProductNumber && (
                                                        <span>{dict?.dashboard?.previewPackages?.detail?.productNumber || '商品编号'}: {productNumber}</span>
                                                    )}
                                                </div>
                                                <div className="flex gap-3">
                                                    {hasWeight && (
                                                        <span>{dict?.dashboard?.previewPackages?.detail?.weight || '重量'}: {product.goodsweight}g</span>
                                                    )}
                                                    {hasVolume && (
                                                        <span>{dict?.dashboard?.previewPackages?.detail?.volume || '体积'}: {Math.round(product.goodsvolume)}cm³</span>
                                                    )}
                                                </div>
                                            </div>
                                        );
                                    })()}
                                </div>
                            </div>
                            <div className="flex flex-col items-end w-1/4">
                                <div className="font-medium"> {formatCurrency(Number(product.goodsprice)).formatValue}</div>
                                <div className="text-gray-500 text-sm">x {itemData?.goodsnum || product.goodsnum}</div>
                            </div>
                        </div>
                         {/* 附加服务部分 */}
                        {showAccessorialService  && 
                        (<div className="ml-4">
                            <Collapse 
                                ghost 
                                items={[
                                    {
                                        key: '1',
                                        label: <span className="text-sm font-medium">{dict?.confirm?.order?.addition?.service}</span>,
                                        children: (
                                            <div className="space-y-2">
                                                {Object.entries(serverList).map(([key, service]: [string, any]) => (
                                                    <div key={key}>
                                                        <div 
                                                            className="flex justify-between items-center text-sm cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors"
                                                            onClick={() => handleServiceSelect(product.id, key)}
                                                        >
                                                            <div className="flex items-center gap-2">
                                                                <Checkbox 
                                                                    checked={productService.selectedServices[key] || false}
                                                                />
                                                                <span>{dict?.confirm?.server?.[key] || key}</span>
                                                            </div>
                                                            <span>{formatCurrency(Number(service.value)).formatValue}</span>
                                                        </div>
                                                        
                                                        {/* 照片相关选项 */}
                                                        {key === 'photo' && productService.selectedServices[key] && (
                                                            <div className="pl-6 space-y-2 mt-2">
                                                                <div className="flex items-center gap-2">
                                                                    <span>{dict.confirm.order.addition.photoCount}:</span>
                                                                    <InputNumber
                                                                        min={1}
                                                                        value={productService.photoCount}
                                                                        onChange={(value) => handlePhotoCountChange(product.id, value || 0)}
                                                                        controls
                                                                        className="w-24"
                                                                    />
                                                                </div>
                                                                {productService.photoCount > 0 && (
                                                                    <div className="space-y-2">
                                                                        {Array.from({ length: productService.photoCount }).map((_, index) => (
                                                                            <div key={`photo-remark-${index}`} className="flex items-center gap-2">
                                                                                <span className="whitespace-nowrap">{dict.confirm.order.addition.photo} {index + 1} {dict.confirm.order.addition.remark}：</span>
                                                                                <Input.TextArea
                                                                                    value={productService.photoRemarks[index] || ''}
                                                                                    onChange={(e) => handlePhotoRemarkChange(product.id, index, e.target.value)}
                                                                                    placeholder={dict.confirm.order.addition.remarkPlaceholder}
                                                                                    autoSize={{ minRows: 1, maxRows: 3 }}
                                                                                    className="flex-1"
                                                                                />
                                                                            </div>
                                                                        ))}
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        )
                                    }
                                ]}
                            />
                        </div>)
                        }
                    </div>
                );
            })}
        </div>
    )
}
