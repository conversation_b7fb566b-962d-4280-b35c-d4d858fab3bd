'use client'

import { useEffect, useState } from "react"
import { Api } from "@/request/api"
import { useSearchParams, useRouter, useParams } from 'next/navigation'
import { Button, Radio, Input } from 'antd'
import ModalComponent from '@/components/Modal'
import StepsComponent from '@/components/Steps'
import Toast from '@/components/Toast'
import Loading from '@/components/Loading'
import { getDictionary } from '@/dictionaries'
import type { Locale } from '@/config'
import { formatCurrency } from '@/utils/currency'
import { useErrorHandler } from '@/utils/errorHandler'
export default function PayPage() {
    const searchParams = useSearchParams()
    const router = useRouter()
    const orderId = searchParams.get('order_id')
    const orderIds = orderId?.split(',')
    const type = searchParams.get('type')
    const onePayOrder = searchParams.get('onepayorder') === '1'
    const [paymentTypeList, setPaymentTypeList] = useState<any>({})
    const [invoice, setInvoice] = useState<any>(null)
    const [selectedPayment, setSelectedPayment] = useState()
    const [loading, setLoading] = useState(false)
    const [pageLoading, setPageLoading] = useState(true)
    const [currentStep, setCurrentStep] = useState(2)
    const [activePayObj, setActivePayObj] = useState<any>({})
    const params = useParams()
    const lng = params.lng as Locale || 'zh-cn'
    const [dict, setDict] = useState<any>(null)
    const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
    const [tradeSn, setTradeSn] = useState('');
    const [couponCode, setCouponCode] = useState(''); // 输入框内的优惠券码
    const [selectedCoupon, setSelectedCoupon] = useState<any | null>(null);

    // 优惠券分类相关状态 - 现在只显示可用优惠券
    const [couponFilter, setCouponFilter] = useState<'available'>('available'); // 优惠券筛选状态
    const [showAllCoupons, setShowAllCoupons] = useState(false); // 是否显示所有优惠券

    // 支付密码相关状态
    const [payPasswordModalOpen, setPayPasswordModalOpen] = useState(false)
    const [payPassword, setPayPassword] = useState('')
    const [payPasswordLoading, setPayPasswordLoading] = useState(false)
    const [payPasswordPluginEnabled, setPayPasswordPluginEnabled] = useState(false)
    const [hasPayPassword, setHasPayPassword] = useState(false)
    const [balanceMoney, setBalanceMoney] = useState(0)
    const [setPasswordModalOpen, setSetPasswordModalOpen] = useState(false)
    const { handleError, checkResponseAuth } = useErrorHandler(lng)
    useEffect(() => {
        const loadDictionary = async () => {
            const dictionary = await getDictionary(lng)
            setDict(dictionary)
        }

        loadDictionary()
    }, [lng])

    useEffect(() => {
        if (searchParams.get('type') === 'package') {
            setCurrentStep(5)
        } else if (searchParams.get('type') === 'packageview' || searchParams.get('type') === 'packageviewpay') {
            setCurrentStep(4) // 预演包裹支付停留在第5步（索引4）
        }
        async function getPaymentTypeList() {
            try {
                setPageLoading(true)
                let tradesn: string | null = null
                if (type != 'cz') {
                    let invoiceType = 'OD' // 默认订单类型
                    if (searchParams.get('type') === 'package') {
                        invoiceType = 'PG'
                    } else if (searchParams.get('type') === 'packageview') {
                        invoiceType = 'SRE' // 预演包裹类型
                    }else if (searchParams.get('type') === 'packageviewpay') {
                        invoiceType = 'SRER' // 预演包裹类型
                    } else if (onePayOrder) {
                        invoiceType = 'OP' // 一次付款订单类型
                    }

                    const res = await Api.getInvoice({ ids: orderIds?.map(item => Number(item)), type: invoiceType })
                    // 检查是否返回18008错误码，表示订单需要审核
                    if (!res.success) {
                        // 跳转到对应页面
                        if (searchParams.get('type') === 'package') {
                            router.push(`/${lng}/dashboard/packages`)
                        } else if (searchParams.get('type') === 'packageview' || searchParams.get('type') === 'packageviewpay') {
                            router.push(`/${lng}/dashboard/preview-packages`)
                        } else if (onePayOrder) {
                            router.push(`/${lng}/dashboard/onepayorders`)
                        } else {
                            router.push(`/${lng}/dashboard/orders`)
                        }
                        return
                    }

                    const sn = isTp5 ? res.data : res.data.tradesn
                    setInvoice(sn)
                    tradesn = sn
                } else if (type == 'cz') {
                    tradesn = searchParams.get('tradesn')
                    setInvoice(tradesn)
                } else if (onePayOrder && searchParams.get('tradesn')) {
                    // 一次付款订单直接使用传入的 tradesn
                    tradesn = searchParams.get('tradesn')
                    setInvoice(tradesn)
                }
                if (tradesn) {
                    getPayList(tradesn)
                }
            } catch (error) {
                handleError(error, dict?.pay?.getOrderFail)
            } finally {
                setPageLoading(false)
            }
        }
        getPaymentTypeList()

        // 检查支付密码插件状态
        checkPayPasswordPlugin()
    }, [orderId, type, searchParams])

    // 检查支付密码插件状态
    const checkPayPasswordPlugin = async () => {
        try {
            // 使用新的插件管理工具检查插件是否启用
            const { isPluginEnabled } = await import('@/utils/plugin');
            const pluginEnabled = await isPluginEnabled('paypassword');

            if (pluginEnabled) {
                setPayPasswordPluginEnabled(true)
                // 检查是否已设置支付密码
                const hasPasswordRes = await Api.hasPayPassword()
                if (hasPasswordRes.success) {
                    setHasPayPassword(hasPasswordRes.data?.has_password || false)
                }
            } else {
                setPayPasswordPluginEnabled(false)
            }
        } catch (error) {
            console.error('检查支付密码插件状态失败:', error)
        }
    }

    useEffect(() => {
        if (paymentTypeList.onlineList) {
            getCurrentPay(paymentTypeList.onlineList[0].paycode)
            setSelectedPayment(paymentTypeList.onlineList[0].paycode)
        }
    }, [paymentTypeList])

    const getPayList = async (tradesn: string) => {
        setTradeSn(tradesn)
        const res2 = await Api.getPaymentList(tradesn)

        // 处理支付方式排序：余额支付永远排第一（充值除外）
        let processedData = { ...res2.data }
        if (processedData.onlineList && type !== 'cz') {
            const onlineList = [...processedData.onlineList]

            // 找到余额支付
            const balanceIndex = onlineList.findIndex(item => item.paycode === 'balance')

            // 如果找到余额支付且不在第一位，则移动到第一位
            if (balanceIndex > 0) {
                const balancePayment = onlineList.splice(balanceIndex, 1)[0]
                onlineList.unshift(balancePayment)
                processedData.onlineList = onlineList
            }
        }

        // 单独获取可用优惠券，替换原有的优惠券列表
        try {
            const couponRes = await Api.getCouponList({ status: 1 }); // 只获取未使用的优惠券
            if (couponRes.success && couponRes.data && couponRes.data.data) {
                processedData.couponList = couponRes.data.data;
            } else {
                // 如果获取失败，保留原有的优惠券列表但进行过滤
                processedData.couponList = (processedData.couponList || []).filter((coupon: any) =>
                    coupon.status && String(coupon.status) === '1'
                );
            }
        } catch (error) {
            console.error('获取优惠券列表失败:', error);
            // 如果获取失败，保留原有的优惠券列表但进行过滤
            processedData.couponList = (processedData.couponList || []).filter((coupon: any) =>
                coupon.status && String(coupon.status) === '1'
            );
        }

        setPaymentTypeList(processedData)
        // 设置余额信息
        if (res2.data.balanceMoney !== undefined) {
            setBalanceMoney(res2.data.balanceMoney)
        }
    }
    const getCurrentPay = (payment: string) => {
        const activePay = paymentTypeList.onlineList.find((item: any) => item.paycode == payment)
        activePay.calculatePrice = activePay.currencyMoneyAndFee
        setActivePayObj(activePay)
        if (selectedCoupon) {
            calculateTotalPrice(selectedCoupon, activePay)
        }
    }
    const calculateTotalPrice = (coupon: any, activePay: any) => {
        console.log('coupon:', coupon)
        console.log('activePay:', activePay)
        let price = activePay.currencyMoneyAndFee
        if (coupon) {
            // 计算汇率后的优惠券金额
            let couponMoney = coupon.money * activePay.rate
            price = coupon.type == 2 ? (Number(activePay.currencyMoneyAndFee) * coupon.money) : (Number(activePay.currencyMoneyAndFee) - couponMoney)

            // 确保支付金额不能低于0
            price = Math.max(0, price)
            price = price.toFixed(2)
        }
        setActivePayObj({ ...activePay, calculatePrice: price })
    };

    // 优惠券分类逻辑
    const getFilteredCoupons = () => {
        if (!paymentTypeList.couponList) return [];

        // 由于我们已经在接口层面过滤了优惠券（只获取status=1的），
        // 这里的couponList应该都是可用的优惠券
        if (couponFilter === 'available') {
            return paymentTypeList.couponList; // 显示所有可用优惠券
        } else {
            // 对于不可用优惠券，返回空数组，因为我们不再获取过期/已使用的优惠券
            return [];
        }
    };

    // 计算优惠券数量
    const getCouponCounts = () => {
        if (!paymentTypeList.couponList) return { available: 0, unavailable: 0 };

        // 由于我们已经在接口层面过滤了优惠券（只获取status=1的），
        // 这里的couponList都是可用的优惠券
        const available = paymentTypeList.couponList.length;
        const unavailable = 0; // 不再显示不可用优惠券

        return { available, unavailable };
    };
    const handlePayment = async () => {
        if (!selectedPayment) {
            Toast.warning(dict?.pay?.selectMethodHint)
            return
        }

        if (!invoice) {
            Toast.error(dict?.pay?.orderNotExist)
            return
        }

        // 验证支付金额不能小于0
        const payAmount = parseFloat(activePayObj?.calculatePrice || '0')
        if (payAmount < 0) {
            Toast.error(dict?.pay?.invalidAmount || '支付金额不能小于0')
            return
        }

        // 如果启用了支付密码插件
        if (payPasswordPluginEnabled) {
            if (hasPayPassword) {
                // 用户已设置支付密码，需要验证支付密码
                setPayPasswordModalOpen(true)
                return
            } else {
                // 用户未设置支付密码，提醒用户去设置密码
                setSetPasswordModalOpen(true)
                return
            }
        }

        await processPayment()
    }

    // 处理支付密码验证
    const handlePayPasswordVerify = async () => {
        if (!payPassword) {
            Toast.error(dict?.pay?.payPasswordVerify?.required)
            return
        }

        try {
            setPayPasswordLoading(true)
            const validateRes = await Api.validatePayPassword({ password: payPassword })
            if (validateRes.success) {
                setPayPasswordModalOpen(false)
                setPayPassword('')
                await processPayment()
            } else {
                Toast.error(dict?.pay?.payPasswordVerify?.verifyFail)
            }
        } catch (error) {
            Toast.error(dict?.pay?.payPasswordVerify?.verifyFail)
        } finally {
            setPayPasswordLoading(false)
        }
    }

    // 处理设置支付密码提醒
    const handleSetPasswordReminder = () => {
        setSetPasswordModalOpen(false)
        // 跳转到账户设置页面
        router.push(`/${lng}/dashboard/account`)
    }

    // 实际的支付处理逻辑
    const processPayment = async () => {
        setLoading(true)
        try {
            if (selectedPayment === 'balance') {
                const res = await Api.payBalance({
                    tradesn: invoice,
                    coupon_id: selectedCoupon?.id || 0
                })
                if (res.success) {
                    Toast.success(dict?.pay?.success)
                    if (searchParams.get('type') === 'package') {
                        router.replace(`/${lng}/dashboard/packages`)
                    } else if (searchParams.get('type') === 'packageview' || searchParams.get('type') === 'packageviewpay') {
                        router.replace(`/${lng}/dashboard/preview-packages`)
                    } else if (onePayOrder) {
                        // 一次付款成功后的处理，可以跳转到订单详情或成功页面
                        router.replace(`/${lng}/dashboard/onepayorders`)
                    } else {
                        router.replace(`/${lng}/dashboard/orders`)
                    }
                } else {
                    // 检查是否是认证错误
                    if (!checkResponseAuth(res)) {
                        return; // 认证错误已被处理，直接返回
                    }
                    Toast.error(res.msg || dict?.pay?.fail)
                }
            } else {
                // 查找选中的支付方式
                const selectedOnlinePayment = paymentTypeList.onlineList?.find(
                    (payment: any) => payment.paycode === selectedPayment
                )

                const selectedOfflinePayment = paymentTypeList.offlineList?.find(
                    (payment: any) => payment.paycode === selectedPayment
                )

                const selectedPaymentInfo = selectedOnlinePayment || selectedOfflinePayment

                if (selectedPaymentInfo && selectedPaymentInfo.payUrl) {
                    // 跳转到对应的支付链接
                    window.location.href = selectedPaymentInfo.payUrl
                } else {
                    Toast.error(dict?.pay?.getLinkFail)
                }
            }
        } catch (error) {
            handleError(error, dict?.pay?.fail)
        } finally {
            setLoading(false)
        }
    }
    // 线下支付
    const handleOfflinePay = (pay: any) => {
        if (pay.paycode === 'offline') { // 线下支付
            router.push(`/${lng}/pay/offline?tradesn=${invoice}`)
        }
    }
    // 激活优惠券
    const handleActivateCoupon = async () => {
        if (!couponCode.trim()) {
            Toast.warning(dict?.dashboard?.wallet?.inputTip);
            return;
        }
        try {
            const res = await Api.couponActivate({ code: couponCode.trim() });
            if (res.success) {
                Toast.success(dict?.dashboard?.wallet?.activeOk);
                setCouponCode('');
                getPayList(tradeSn)
            } else {
                Toast.error(res.msg || dict?.dashboard?.wallet?.activeFail);
            }
        } catch (error) {
            Toast.error(dict?.dashboard?.wallet?.activeFail);
        }
    }
    return (
        <div className="min-h-screen px-6 py-4">
            <div className="max-w-[1200px] mx-auto">

                <StepsComponent
                    current={currentStep}
                    labelPlacement="vertical"
                    dict={dict}
                    stepOverrides={
                        (searchParams.get('type') === 'packageview' || searchParams.get('type') === 'packageviewpay') ? {
                            4: dict?.pay?.payPreviewPackage || '支付预演包裹'
                        } : undefined
                    }
                    style={{
                        display: type == 'cz' ? 'none' : 'block'
                    }}
                />
                {pageLoading ? (
                    <Loading />
                ) : (
                    <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
                        {type != 'cz' ? <h1 className="text-2xl font-medium mb-6">{dict?.pay?.title}</h1>
                            : <h1 className="text-2xl font-medium mb-6">{dict?.pay?.titleCZ}</h1>}
                        {/* 优惠券 */}
                        <div>
                            <div className="flex justify-between items-center mb-4">
                                <div className="text-lg font-medium">{dict?.pay?.coupon}</div>
                                <div className="flex gap-4 mb-6">
                                    <Input
                                        placeholder={dict?.dashboard?.wallet?.inputTitle}
                                        value={couponCode}
                                        onChange={(e) => setCouponCode(e.target.value)}
                                        className="flex-1"
                                    />
                                    <Button
                                        type="primary"
                                        onClick={handleActivateCoupon}
                                        className="!bg-orange-500 !hover:bg-orange-600"
                                    >
                                        {dict?.dashboard?.wallet?.activeBtn}
                                    </Button>
                                </div>
                            </div>

                            {/* 优惠券标题 - 只显示可用优惠券 */}
                            <div className="flex justify-between items-center mb-4">
                                <div className="text-lg font-medium text-gray-800">
                                    {dict?.pay?.availableCoupons || '可用优惠券'} ({getCouponCounts().available})
                                </div>
                                {getFilteredCoupons().length > 4 && (
                                    <button
                                        onClick={() => setShowAllCoupons(!showAllCoupons)}
                                        className="text-orange-500 text-sm hover:text-orange-600 transition-colors"
                                    >
                                        {showAllCoupons ? (dict?.pay?.collapse || '收起') : (dict?.pay?.viewAll || '查看全部')}
                                    </button>
                                )}
                            </div>

                            {/* 优惠券网格布局 */}
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-4">
                                {(showAllCoupons ? getFilteredCoupons() : getFilteredCoupons().slice(0, 4)).map((coupon: any) => {
                                    // 由于我们只获取可用优惠券，所以这里的优惠券都是可用的
                                    return (
                                    <div
                                        key={coupon.id}
                                        className={`border rounded-lg p-4 relative overflow-hidden cursor-pointer transition-all duration-200 ${
                                            selectedCoupon?.id === coupon.id
                                                ? 'border-orange-500 bg-orange-50 shadow-md'
                                                : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                                        }`}
                                        onClick={() => {
                                            setSelectedCoupon(selectedCoupon?.id === coupon.id ? null : coupon);
                                            calculateTotalPrice(selectedCoupon?.id === coupon.id ? null : coupon, activePayObj)
                                        }}
                                    >
                                        {/* 选中状态指示器 */}
                                        {selectedCoupon?.id === coupon.id && (
                                            <div className="absolute top-2 right-2 bg-orange-500 text-white p-1 w-6 h-6 flex items-center justify-center rounded-full text-xs">
                                                ✓
                                            </div>
                                        )}

                                        <div className="mb-2">
                                            <div className={`text-xl font-bold mb-1 ${
                                                selectedCoupon?.id === coupon.id ? 'text-orange-600' : 'text-[var(--base-color)]'
                                            }`}>
                                                {coupon.type == 2 ? `${coupon.money * 10} ${dict?.pay?.discount}` : formatCurrency(coupon.money).formatValue}
                                            </div>
                                            <div className="text-sm text-gray-600">{coupon.type_text}</div>
                                        </div>

                                        <div className="text-xs text-gray-500">
                                            {dict?.pay?.validity}：{coupon.endtime !== 0 ? coupon.endtime_text : (dict?.pay?.noExpiry || '无')}
                                        </div>
                                    </div>
                                    );
                                })}
                            </div>

                            {/* 空状态显示 */}
                            {getFilteredCoupons().length === 0 && (
                                <div className="text-center py-8 text-gray-500">
                                    <div className="text-lg mb-2">
                                        {dict?.pay?.noAvailableCoupons || '暂无可用优惠券'}
                                    </div>
                                    <div className="text-sm">
                                        {dict?.pay?.noAvailableCouponsDesc || '您当前没有可用的优惠券'}
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* 订单信息 */}
                        <div className="mb-8 p-4 bg-gray-50 rounded-lg">
                            <div className="flex justify-between">
                                <span className="text-gray-600">{dict?.pay?.orderNo}：{invoice}</span>
                                <div>
                                    {activePayObj.currencyFee > 0 && (<div className="text-md text-gray-600" >{dict?.pay?.currencyFee}：{activePayObj.currencyFee}</div>)}
                                    <div className="text-lg font-medium">{dict?.pay?.actually}：{activePayObj?.currencyoptions?.symbol + activePayObj?.calculatePrice || formatCurrency(paymentTypeList.currencyMoneyAndFee).formatValue}</div>
                                    {/* 当使用优惠券且支付金额为0时显示提示 */}
                                    {selectedCoupon && parseFloat(activePayObj?.calculatePrice || '0') === 0 && (
                                        <div className="text-sm text-green-600 mt-1">
                                            {dict?.pay?.couponFullDiscount || '优惠券已完全抵扣订单金额'}
                                        </div>
                                    )}
                                </div>

                            </div>
                        </div>

                        {/* 支付方式 */}
                        <div className="mb-8">
                            <h2 className="text-lg font-medium mb-4">{dict?.pay?.selectMethod}</h2>
                            <Radio.Group
                                value={selectedPayment}
                                onChange={e => {
                                    setSelectedPayment(e.target.value)
                                    getCurrentPay(e.target.value)
                                }}
                                className="w-full"
                            >
                                <div className="flex flex-col gap-4">
                                    {/* 在线支付 */}
                                    {paymentTypeList.onlineList?.map((payment: any) => (
                                        <Radio
                                            key={payment.paycode}
                                            value={payment.paycode}
                                            className="!flex items-center border rounded-lg hover:border-[#FF6000] transition-all duration-200 [&_.ant-radio-wrapper]:!p-4 !px-4 !py-2 w-full"
                                        >
                                            <div className="flex items-center gap-3">
                                                <img src={payment.logo} alt={payment.name} className="w-8 h-8 object-contain" />
                                                <div className="flex-1">
                                                    <div className="font-medium">
                                                        {payment.name}
                                                        {payment.paycode === 'balance' && (
                                                            <span className="ml-2 text-sm text-gray-600">
                                                                ({dict?.dashboard?.home?.balance?.title || 'Balance'}: {formatCurrency(balanceMoney).formatValue})
                                                            </span>
                                                        )}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {payment.remark && <div dangerouslySetInnerHTML={{ __html: payment.remark }} />}
                                                    </div>
                                                </div>
                                            </div>
                                        </Radio>

                                    ))}

                                    {/* 支付按钮 */}
                                    <div className="flex justify-end">
                                        <Button
                                            type="primary"
                                            size="large"
                                            loading={loading}
                                            onClick={handlePayment}
                                            style={{
                                                backgroundColor: '#FF6000',
                                                borderColor: '#FF6000',
                                                width: '200px'
                                            }}
                                            className="hover:opacity-90 transition-opacity"
                                        >
                                            {dict?.pay?.payNow}
                                        </Button>
                                    </div>

                                </div>
                            </Radio.Group>
                            {/* 线下支付 */}
                            {paymentTypeList.offlineList?.map((payment: any) => (
                                <div
                                    className="!flex items-center border rounded-lg hover:border-[#FF6000] transition-all duration-200 [&_.ant-radio-wrapper]:!p-4 !px-4 !py-2 w-full relative mt-4"
                                >
                                    <div className="flex items-center gap-3 w-full">
                                        <img src={payment.logo} alt={payment.name} className="w-8 h-8 object-contain" />
                                        <div className="flex-1">
                                            <div className="font-medium">{payment.name}</div>
                                            <div className="text-sm text-gray-500">
                                                {payment.remark && <div dangerouslySetInnerHTML={{ __html: payment.remark }} />}
                                            </div>
                                        </div>
                                        <div className="absolute right-4 top-4 text-[var(--base-color)] hover:text-[#fe8a39] cursor-pointer" onClick={() => handleOfflinePay(payment)}>{dict?.pay?.goUse}</div>
                                    </div>
                                </div>
                            ))}
                        </div>

                    </div>
                )}

                {/* 支付密码验证Modal */}
                <ModalComponent
                    title={dict?.pay?.payPasswordVerify?.title}
                    open={payPasswordModalOpen}
                    onCancel={() => {
                        setPayPasswordModalOpen(false)
                        setPayPassword('')
                    }}
                    onOk={handlePayPasswordVerify}
                    confirmLoading={payPasswordLoading}
                    centered
                    okText={dict?.pay?.payPasswordVerify?.confirm}
                    cancelText={dict?.pay?.payPasswordVerify?.cancel}
                    maskClosable={false}
                >
                    <div style={{ marginBottom: 16 }}>
                        <div style={{ color: '#666', fontSize: 14, marginBottom: 12 }}>
                            {dict?.pay?.payPasswordVerify?.tips}
                        </div>
                        <Input.Password
                            placeholder={dict?.pay?.payPasswordVerify?.placeholder}
                            value={payPassword}
                            onChange={e => setPayPassword(e.target.value)}
                            maxLength={6}
                            onPressEnter={handlePayPasswordVerify}
                        />
                    </div>
                </ModalComponent>

                {/* 设置支付密码提醒Modal */}
                <ModalComponent
                    title={dict?.pay?.setPasswordReminder?.title || '设置支付密码'}
                    open={setPasswordModalOpen}
                    onCancel={() => setSetPasswordModalOpen(false)}
                    onOk={handleSetPasswordReminder}
                    centered
                    okText={dict?.pay?.setPasswordReminder?.goToSet || '去设置'}
                    cancelText={dict?.pay?.setPasswordReminder?.cancel || '取消'}
                    maskClosable={false}
                >
                    <div style={{ marginBottom: 16 }}>
                        <div style={{ color: '#666', fontSize: 14, marginBottom: 12 }}>
                            {dict?.pay?.setPasswordReminder?.tips || '为了您的账户安全，请先设置支付密码后再进行支付。'}
                        </div>
                    </div>
                </ModalComponent>
            </div>
        </div>
    )
}
