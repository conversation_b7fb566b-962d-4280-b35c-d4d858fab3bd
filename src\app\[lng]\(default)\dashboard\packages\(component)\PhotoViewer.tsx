'use client'

import React, { useState, useEffect } from 'react'
import ModalComponent from '@/components/Modal'

interface PhotoViewerProps {
    photos: Array<{ id: string | number; image: string }>
    isOpen: boolean
    onClose: () => void
    initialIndex?: number
}

export default function PhotoViewer({ photos, isOpen, onClose, initialIndex = 0 }: PhotoViewerProps) {
    const [selectedIndex, setSelectedIndex] = useState(initialIndex)

    useEffect(() => {
        setSelectedIndex(initialIndex)
    }, [initialIndex, isOpen])

    const handlePrevPhoto = () => {
        setSelectedIndex((prev) => 
            prev > 0 ? prev - 1 : photos.length - 1
        )
    }

    const handleNextPhoto = () => {
        setSelectedIndex((prev) => 
            prev < photos.length - 1 ? prev + 1 : 0
        )
    }

    // 键盘导航
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (!isOpen) return
            
            switch (event.key) {
                case 'ArrowLeft':
                    event.preventDefault()
                    handlePrevPhoto()
                    break
                case 'ArrowRight':
                    event.preventDefault()
                    handleNextPhoto()
                    break
                case 'Escape':
                    event.preventDefault()
                    onClose()
                    break
            }
        }

        document.addEventListener('keydown', handleKeyDown)
        return () => document.removeEventListener('keydown', handleKeyDown)
    }, [isOpen, photos.length])

    if (!photos.length) return null

    return (
        <ModalComponent
            title={`照片查看 (${selectedIndex + 1}/${photos.length})`}
            open={isOpen}
            onCancel={onClose}
            centered
            footer={null}
            width="90vw"
            style={{ maxWidth: '1200px' }}
        >
            <div className="relative">
                <div className="flex items-center justify-center">
                    <img 
                        src={photos[selectedIndex]?.image} 
                        alt={`运单照片 ${selectedIndex + 1}`}
                        className="max-w-full max-h-[70vh] object-contain"
                    />
                </div>
                
                {/* 导航按钮 */}
                {photos.length > 1 && (
                    <>
                        <button
                            onClick={handlePrevPhoto}
                            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all z-10"
                            aria-label="上一张照片"
                        >
                            <i className="fas fa-chevron-left"></i>
                        </button>
                        <button
                            onClick={handleNextPhoto}
                            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all z-10"
                            aria-label="下一张照片"
                        >
                            <i className="fas fa-chevron-right"></i>
                        </button>
                    </>
                )}
                
                {/* 缩略图导航 */}
                {photos.length > 1 && (
                    <div className="flex justify-center mt-4 gap-2 overflow-x-auto pb-2">
                        {photos.map((item, index) => (
                            <img
                                key={item.id}
                                src={item.image}
                                alt={`缩略图 ${index + 1}`}
                                className={`w-16 h-16 object-cover rounded cursor-pointer transition-all ${
                                    index === selectedIndex 
                                        ? 'border-2 border-orange-500 opacity-100' 
                                        : 'border border-gray-300 opacity-70 hover:opacity-100'
                                }`}
                                onClick={() => setSelectedIndex(index)}
                            />
                        ))}
                    </div>
                )}
            </div>
        </ModalComponent>
    )
}
