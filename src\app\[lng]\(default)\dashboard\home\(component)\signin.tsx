
'use client'
import React, { useState, useEffect } from 'react'
import { Api } from '@/request/api'
import message from '@/components/CustomMessage';
interface SigninProps {
    dict:any
}
export default function Signin({ dict }: SigninProps) {
    const [signinStatus, setSigninStatus] = useState<'loading' | 'can_signin' | 'already_signed'>('loading');
    const [isSubmitting, setIsSubmitting] = useState(false);

    // 检查签到状态
    const checkSigninStatus = async () => {
        try {
            const response = await Api.getSigninCheck();
            console.log('Signin check response:', response);

            // 根据API响应判断状态
            if (response.success || response.code === 200) {
                // 检查 signed 字段来判断是否已签到
                if (response.data && response.data.signed === true) {
                    setSigninStatus('already_signed');
                } else if (response.signed === true) {
                    // 有时 signed 字段可能直接在 response 根级别
                    setSigninStatus('already_signed');
                } else {
                    // 如果 signed 不为 true，说明可以签到
                    setSigninStatus('can_signin');
                }
            } else {
                // API返回失败，可能是还未签到
                setSigninStatus('can_signin');
            }
        } catch (error) {
            console.log('Check signin status error:', error);
            // 出错时默认显示可签到状态
            setSigninStatus('can_signin');
        }
    };

    useEffect(() => {
        checkSigninStatus();
    }, []);

    const handleSignin = async () => {
        if (signinStatus === 'already_signed' || isSubmitting) return;

        setIsSubmitting(true);
        try  {
            const response = await Api.setSignin();
            console.log(response);
            if(response.success || response.code === 200){
                message.success(response.msg || dict.dashboard.home.profile.signinSuccessMsg);
                setSigninStatus('already_signed');
            }else{
                message.error(response.data || response.msg);
            }

        } catch (error) {
            console.log(error, 'Signin failed');
            message.error(dict.dashboard.home.profile.signinErrorMsg);
        } finally {
            setIsSubmitting(false);
        }
    }
  const getButtonContent = () => {
    if (signinStatus === 'loading') {
      return (
        <>
          <i className='fas fa-spinner fa-spin mr-1'></i>
          <span>{dict.dashboard?.home?.profile?.loading || '加载中...'}</span>
        </>
      );
    }

    if (signinStatus === 'already_signed') {
      return (
        <>
          <i className='fas fa-check-circle mr-1 text-green-200'></i>
          <span>{dict.dashboard?.home?.profile?.alreadySigned || '今日已签到'}</span>
        </>
      );
    }

    return (
      <>
        <i className='fas fa-calendar-check mr-1'></i>
        <span>{isSubmitting ? (dict.dashboard?.home?.profile?.signingIn || '签到中...') : (dict.dashboard?.home?.profile?.signin || '签到')}</span>
      </>
    );
  };

  const getButtonClassName = () => {
    // 基础类名，包含360浏览器兼容的样式
    const baseClass = 'btn-signin group px-3 h-9 mb-2 text-white text-xs font-medium border border-white';

    if (signinStatus === 'already_signed') {
      return `${baseClass} signed`;
    }

    if (signinStatus === 'loading' || isSubmitting) {
      return `${baseClass} loading`;
    }

    return baseClass;
  };

  return (
    <button
      onClick={handleSignin}
      disabled={signinStatus === 'already_signed' || signinStatus === 'loading' || isSubmitting}
      className={getButtonClassName()}
    >
      {getButtonContent()}
    </button>
  );
}