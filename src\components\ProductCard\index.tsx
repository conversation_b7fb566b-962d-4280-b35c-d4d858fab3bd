'use client'
import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { formatCurrency } from '@/utils/currency'
import { prepareImageForNextJs } from '@/utils/imageUtils'

interface ProductCardProps {
  product: any
  platform?: 'taobao' | '1688' | 'jd' | 'micro' | 'obmall'
  dict: any
}

export default function ProductCard({ product, platform, dict }: ProductCardProps) {
  const [hasImageError, setHasImageError] = useState(false)

  // 简单判断：如果 recommend 为 1，就显示推荐标签
  const isRecommended = product.recommend === 1

  const CardContent = () => (
    <>
      <div className="relative aspect-square">
        {hasImageError ? (
          <div className="w-full h-full flex items-center justify-center bg-gray-100">
            <svg
              className="w-12 h-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
        ) : (
          <Image
            src={prepareImageForNextJs(product.pic_url)}
            alt={product.title}
            fill
            sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
            className="object-cover"
            onError={() => setHasImageError(true)}
            draggable="false"
          />
        )}

        {/* 推荐标签 - 右上角三角横幅（使用系统主色） */}
        {isRecommended && (
          <div style={{
            position: 'absolute',
            top: '0',
            right: '0',
            zIndex: 49,
            width: '50px',
            height: '50px',
            overflow: 'hidden'
          }}>
            {/* 三角形背景 */}
            <div style={{
              position: 'absolute',
              top: '0',
              right: '0',
              width: '0',
              height: '0',
              borderTop: `50px solid var(--base-color, #ff6b6b)`,
              borderLeft: '50px solid transparent'
            }}></div>
            {/* 文字 */}
            <div style={{
              position: 'absolute',
              top: '8px',
              right: '8px',
              color: 'white',
              fontSize: '10px',
              fontWeight: '700',
              textShadow: '0 1px 2px rgba(0,0,0,0.4)',
              letterSpacing: '0.5px',
              transform: 'rotate(45deg)',
              transformOrigin: 'center',
              width: '30px',
              textAlign: 'center'
            }}>
              推荐
            </div>
          </div>
        )}

        {/* 平台标识 */}
        {platform && platform!='obmall' && (
          <div className="absolute top-2 left-2">
            <Image
              src={`/images/${platform}.png`}
              alt={platform}
              width={24}
              height={24}
              className="rounded-full"
              onError={(e) => {
                e.currentTarget.src = '/images/default-platform.png'
              }}
              draggable="false"
            />
          </div>
        )}
      </div>

      <div className="p-3 flex-1 flex flex-col">
        <h3 className="text-md text-gray-800 line-clamp-2 mb-2 group-hover:text-[#FF6B00] transition-colors custom-h-48px">
          {product.title}
        </h3>
        <div className="flex items-center justify-between mt-auto">
          <span className="text-[#FF6B00] font-medium">
            <span className='text-xs sm:text-sm md:text-base lg:text-lg font-bold whitespace-nowrap'>{formatCurrency(Number(product.promotion_price || product.price)).formatValueNoSpace}</span>
          </span>
          {/* <span className="text-xs text-gray-500">
            {dict?.search?.sold?.replace('{count}', '0') || '已售 0'}
          </span> */}
        </div>
      </div>
    </>
  )

  // 检查是否是"Buy Yourself"、"OneBuy"或"By yourself"店铺，如果是则不显示链接
  const isBuyYourselfShop = product.nick === 'Buy Yourself' || product.goodsseller === 'Buy Yourself' ||
                           product.nick === 'OneBuy' || product.goodsseller === 'OneBuy' ||
                           product.nick === 'By yourself' || product.goodsseller === 'By yourself';

  if (isBuyYourselfShop) {
    return (
      <div className="custom-card flex flex-col h-full relative">
        <CardContent />


      </div>
    );
  }

  return (
    <Link
      href={platform === 'obmall'
        ? product.detail_url  // obmall已经包含完整的路径，如 /en/detail/obmall?id=123
        : platform
          ? `/detail/${platform}/?url=${product.detail_url}`
          : `/detail/taobao/?url=${product.detail_url}`
      }
      target="_blank"
      className="custom-card flex flex-col h-full relative"
    >
      <CardContent />

      {/* 推荐标签 - Link版本三角横幅（使用系统主色） */}
      {isRecommended && (
        <div style={{
          position: 'absolute',
          top: '0',
          right: '0',
          zIndex: 49,
          width: '50px',
          height: '50px',
          overflow: 'hidden'
        }}>
          {/* 三角形背景 */}
          <div style={{
            position: 'absolute',
            top: '0',
            right: '0',
            width: '0',
            height: '0',
            borderTop: `50px solid var(--base-color, #ff6b6b)`,
            borderLeft: '50px solid transparent'
          }}></div>
          {/* 文字 */}
          <div style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            color: 'white',
            fontSize: '10px',
            fontWeight: '700',
            textShadow: '0 1px 2px rgba(0,0,0,0.4)',
            letterSpacing: '0.5px',
            transform: 'rotate(45deg)',
            transformOrigin: 'center',
            width: '30px',
            textAlign: 'center'
          }}>
            推荐
          </div>
        </div>
      )}
    </Link>
  )
} 