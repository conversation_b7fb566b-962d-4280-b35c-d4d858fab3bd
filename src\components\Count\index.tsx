'use client'
import CountUp from 'react-countup'
import { useInView } from 'react-intersection-observer'
import { useEffect, useState } from 'react'
import { formatCurrency } from '@/utils/currency'

export default function MoneyCounter({ value }: { value: number }) {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1
  })
  
  const [currencyInfo, setCurrencyInfo] = useState(formatCurrency(value))

  useEffect(() => {
    setCurrencyInfo(formatCurrency(value))
  }, [value])

  return (
    <div ref={ref} className="text-3xl font-bold">
      {inView && <CountUp 
        end={currencyInfo.value} 
        duration={1.5}
        separator=","
        decimals={2}
        prefix={currencyInfo.symbol}
      />}
    </div>
  )
}