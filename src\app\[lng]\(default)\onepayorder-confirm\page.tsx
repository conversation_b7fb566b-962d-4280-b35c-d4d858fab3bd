import StepsComponent from '@/components/Steps'
import OnePayOrderSummary from './(component)/OnePayOrderSummary'
import AddressSection from './(component)/AddressSection'
import OnePayShippingMethods from './(component)/OnePayShippingMethods'
import CouponSection from './(component)/CouponSection'
import OnePayOrderClientWrapper from './(component)/OnePayOrderClientWrapper'
import { getServerData, checkServerPluginStatus } from '@/request/server';
import { getDictionary } from '@/dictionaries';
import { Locale } from '@/config';
import { redirect } from 'next/navigation';

interface CartItem {
    id: number;
    mall_goods_id: number;
    goods_id: string;
    goodsname: string;
    goodsimg: string;
    goodsprice: string;
    goodsnum: number;
    skuname: string;
    [key: string]: any;
}

interface ProductListResponse {
    cartlist: CartItem[];
    serverfee: number;
    sendmoney: number;
}



export default async function OnePayOrderConfirmPage({
    params,
    searchParams
}: {
    params: Promise<{ lng: Locale }>,
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
    const { lng } = await params;
    const resolvedSearchParams = await searchParams;
    const dict = await getDictionary(lng);

    // Check if onepayorder plugin is enabled
    const onePayOrderEnabled = await checkServerPluginStatus('onepayorder');
    if (!onePayOrderEnabled) {
        redirect(`/${lng}/`);
    }

    // Get cart data
    const cart_ids = resolvedSearchParams.cart_ids as string;

    // Don't parse itemData on server side - let client components handle it safely
    // This prevents "URI malformed" errors when the URL parameter is corrupted
    let itemData = null;
    try {
        if (resolvedSearchParams.itemData) {
            itemData = JSON.parse(decodeURIComponent(resolvedSearchParams.itemData as string));
        }
    } catch (error) {
        console.error('Failed to parse itemData from URL parameters:', error);
        // Continue without itemData - client components will handle this gracefully
    }

    if (!cart_ids) {
        redirect(`/${lng}/cart`);
    }

    // Use the same API call pattern as the regular confirm page
    const isTp6 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '6';
    const cart_list = await getServerData<ProductListResponse>(
        isTp6 ? '/web/goods/cart/list' : '/api/cart/getList',
        'POST',
        { cart_ids }
    );

    // Validate cart data structure (same as confirm page expects)
    if (!cart_list || !cart_list.cartlist || !Array.isArray(cart_list.cartlist) || cart_list.cartlist.length === 0) {

    }




    return (
        <div className="min-h-screen bg-white">
            {/* Header with steps */}
            <div className="bg-white border-b">
                <div className="max-w-[1200px] mx-auto px-4 py-4">
                    <StepsComponent current={1} labelPlacement="vertical" dict={dict} />
                </div>
            </div>

            <div className="max-w-[1200px] mx-auto px-4 py-6">
                {/* Available Coupons Section */}
                <CouponSection dict={dict} />

                {/* Product Information Section */}
                <div className="mb-8">
                    <h3 className="text-base font-medium text-gray-900 mb-4">{dict?.confirm?.onepayorder?.productSummary || 'Product Information'}</h3>
                    <OnePayOrderClientWrapper
                        fallbackProducts={cart_list.cartlist}
                        dict={dict}
                        itemData={itemData}
                        cart_ids={cart_ids}
                    />
                </div>

                {/* Package Information Section */}
                <div className="mb-8">
                    <h3 className="text-base font-medium text-gray-900 mb-6">{dict?.confirm?.onepayorder?.productInfo?.packageInfo || 'Package Information'}</h3>

                    {/* Shipping Address Subsection */}
                    <AddressSection dict={dict} />

                    {/* Shipping Method Subsection */}
                    <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-4">{dict?.confirm?.onepayorder?.shipping?.shippingMethod || 'Shipping Method'}</h4>
                        <OnePayShippingMethods dict={dict} cartData={cart_list} />
                    </div>
                </div>

                {/* 订单汇总区域 - 独占一排 */}
                <div>
                    <OnePayOrderSummary
                        dict={dict}
                        products={cart_list}
                        itemData={itemData}
                        cart_ids={cart_ids}
                        lng={lng}
                    />
                </div>
            </div>
        </div>
    )
}
