'use client'

import { Api } from '@/request/api'
import { useState } from 'react'
import { message } from 'antd'
import ModalComponent from '@/components/Modal'
import ButtonComponent from '@/components/Button'
import { useRouter } from 'next/navigation'

export default function ClearFavorite({style,dict}:{style:React.CSSProperties,dict:any}) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false)

  const handleClear = async () => {
    setLoading(true)
    await Api.clearFavorite()
    setOpen(false)
    setLoading(false)
    router.refresh()
  }

  return (
    <div style={style}>
      <ButtonComponent color="danger" variant="link" onClick={() => setOpen(true)}>
        {dict.dashboard.favorites.clearAll}
      </ButtonComponent>
      <ModalComponent
        title={dict.dashboard.favorites.confirmClear}
        open={open}
        onOk={handleClear}
        onCancel={() => setOpen(false)}
        okText={dict.dashboard.favorites.confirm}
        cancelText={dict.dashboard.favorites.cancel}
        confirmLoading={loading}
      >
        <p>{dict.dashboard.favorites.clearWarning}</p>
      </ModalComponent>
    </div>
  )
}