'use client';

import React, { ReactNode, useEffect, useState } from 'react';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import enUS from 'antd/locale/en_US';
import jaJP from 'antd/locale/ja_JP';
import { usePathname } from 'next/navigation';
import { defaultLocale, locales } from '@/config';
import type { Locale } from '@/config';
console.log(' 主题色：',process.env.NEXT_PUBLIC_BASE_COLOR);
// 导入 dayjs 和本地化支持
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/en';
import 'dayjs/locale/ja';

interface AntdConfigProviderProps {
  children: ReactNode;
}

// 根据应用语言获取对应的 Ant Design 语言包
const getAntdLocale = (locale: Locale) => {
  // 设置 dayjs 的 locale
  if (locale === 'zh-cn') {
    dayjs.locale('zh-cn');
    return zhCN;
  } else if (locale === 'en') {
    dayjs.locale('en');
    return enUS;
  } else if (locale === 'ja') {
    dayjs.locale('ja');
    return jaJP;
  }
  
  // 默认使用中文
  dayjs.locale('zh-cn');
  return zhCN;
};

/**
 * Ant Design 配置提供者组件
 * 提供全局的主题和语言配置
 */
const AntdConfigProvider: React.FC<AntdConfigProviderProps> = ({ children }) => {
  const [currentLocale, setCurrentLocale] = useState<Locale>(defaultLocale);
  const pathname = usePathname();
  
  // 组件初始化时从路径中提取语言代码
  useEffect(() => {
    if (pathname) {
      const pathSegments = pathname.split('/');
      // 检查第一个路径段是否是支持的语言
      if (pathSegments.length > 1 && locales.includes(pathSegments[1] as Locale)) {
        setCurrentLocale(pathSegments[1] as Locale);
      } else {
        // 如果路径中没有语言代码，尝试从浏览器获取
        const browserLanguage = navigator.language;
        // 简单匹配，实际应用中可能需要更复杂的逻辑
        if (browserLanguage.startsWith('zh')) {
          setCurrentLocale('zh-cn');
        } else if (browserLanguage.startsWith('ja')) {
          setCurrentLocale('ja');
        } else {
          setCurrentLocale('en'); // 默认使用英语
        }
      }
    }
  }, [pathname]);
  
  // 根据当前语言获取 Ant Design 语言包
  const antdLocale = getAntdLocale(currentLocale);
  
  // 设置 dayjs 的 locale
  useEffect(() => {
    if (currentLocale === 'zh-cn') {
      dayjs.locale('zh-cn');
    } else if (currentLocale === 'en') {
      dayjs.locale('en');
    } else if (currentLocale === 'ja') {
      dayjs.locale('ja');
    }
  }, [currentLocale]);
  
  return (
    <ConfigProvider
      locale={antdLocale}
      theme={{
        components: {
          Tabs: {
            itemColor: 'rgba(0, 0, 0, 0.35)',
          },
        },
        token: {
          colorPrimary: process.env.NEXT_PUBLIC_BASE_COLOR,
        },
      }}
    >
      {children}
    </ConfigProvider>
  );
};

export default AntdConfigProvider; 