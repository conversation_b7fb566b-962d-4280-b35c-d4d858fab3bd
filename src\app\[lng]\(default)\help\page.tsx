'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { getDictionary } from '@/dictionaries';
import type { Locale } from '@/config';
import { Api } from '@/request/api';
import { Input, Modal, Empty, Skeleton } from 'antd';
import { FileTextOutlined, FolderOutlined } from '@ant-design/icons';
import Pagination from '@/components/Pagination';
import './help.css';

const { Search } = Input;

interface HelpCategory {
  id: number;
  category_id: number;
  pid: number;
  name: string;
  nickname: string;
  keywords: string;
  description: string;
  image: string;
  weigh: number;
  status: string;
  langcode: string;
}

interface HelpArticle {
  id: number;
  category_id: number;
  title: string;
  keywords: string;
  description: string;
  content: string;
  createtime: string;
  updatetime: string;
  clickcount: number;
  category: HelpCategory[];
}

export default function HelpCenterPage() {
  const params = useParams();
  const lng = params.lng as Locale;
  const [dict, setDict] = useState<any>(null);
  const [categories, setCategories] = useState<HelpCategory[]>([]);
  const [articles, setArticles] = useState<HelpArticle[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<HelpCategory | null>(null);
  const [selectedArticle, setSelectedArticle] = useState<HelpArticle | null>(null);
  const [loading, setLoading] = useState(true);
  const [articlesLoading, setArticlesLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [showArticleModal, setShowArticleModal] = useState(false);

  // 获取语言代码映射
  const getLangCode = (locale: Locale): string => {
    const langMap: Record<Locale, string> = {
      'zh-cn': 'zh-cn',
      'en': 'en',
      'ja': 'ja'
    };
    return langMap[locale] || 'zh-cn';
  };

  useEffect(() => {
    const loadDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };
    loadDictionary();
  }, [lng]);

  // 加载帮助分类
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const response = await Api.getHelpCategories({
          langcode: getLangCode(lng),
          size: '100'
        });

        if (response.success && response.data) {
          console.log('Categories API response:', response.data);

          // 检查返回的数据结构
          let categoriesData = response.data;
          if (response.data.data && Array.isArray(response.data.data)) {
            categoriesData = response.data.data;
          }

          // 按权重排序分类
          const sortedCategories = categoriesData.sort((a: HelpCategory, b: HelpCategory) => b.weigh - a.weigh);

          // 调试信息：显示分类结构
          console.log('Total categories:', sortedCategories.length);
          console.log('Root categories (pid=0):', sortedCategories.filter((cat: HelpCategory) => cat.pid === 0).length);
          console.log('Child categories (pid>0):', sortedCategories.filter((cat: HelpCategory) => cat.pid > 0).length);
          console.log('Categories data:', sortedCategories.map((cat: HelpCategory) => ({
            id: cat.id,
            category_id: cat.category_id,
            pid: cat.pid,
            name: cat.name
          })));

          setCategories(sortedCategories);
        } else {
          console.warn('Failed to fetch categories:', response);
        }
      } catch (error) {
        console.error('Failed to fetch categories:', error);
      } finally {
        setLoading(false);
      }
    };

    if (dict) {
      fetchCategories();
    }
  }, [lng, dict]);

  // 加载文章列表
  const fetchArticles = async (categoryId: string, keyword: string = '') => {
    try {
      setArticlesLoading(true);
      const response = await Api.getHelpArticles({
        category_id: categoryId,
        langcode: getLangCode(lng),
        size: 100,
        keyword: keyword.trim()
      });

      if (response.success && response.data) {
        console.log('Articles API response:', response.data);

        // 检查返回的数据结构，可能有分页信息
        let articlesData = response.data;

        // 如果返回的数据有分页结构，提取实际的文章数据
        if (response.data.data && Array.isArray(response.data.data)) {
          articlesData = response.data.data;
          console.log('Using paginated articles data:', articlesData);
        }

        // 按更新时间排序文章
        const sortedArticles = articlesData.sort((a: HelpArticle, b: HelpArticle) =>
          new Date(b.updatetime).getTime() - new Date(a.updatetime).getTime()
        );
        setArticles(sortedArticles);
        setCurrentPage(1);
      } else {
        console.warn('Failed to fetch articles:', response);
        setArticles([]);
      }
    } catch (error) {
      console.error('Failed to fetch articles:', error);
      setArticles([]);
    } finally {
      setArticlesLoading(false);
    }
  };

  // 获取文章详情
  const fetchArticleDetail = async (articleId: number) => {
    try {
      const response = await Api.getHelpArticleDetail({ id: articleId });

      if (response.success && response.data) {
        console.log('Article detail API response:', response.data);
        let articleData = null;

        // 检查返回的数据结构
        if (Array.isArray(response.data) && response.data.length > 0) {
          articleData = response.data[0];
          console.log('Using direct array data:', articleData);
        } else if (response.data.data && Array.isArray(response.data.data) && response.data.data.length > 0) {
          articleData = response.data.data[0];
          console.log('Using nested array data:', articleData);
        } else if (typeof response.data === 'object' && response.data.id) {
          articleData = response.data;
          console.log('Using direct object data:', articleData);
        }

        if (articleData) {
          setSelectedArticle(articleData);
          setShowArticleModal(true);
        } else {
          console.warn('Article not found or empty response:', response);
        }
      } else {
        console.warn('Article not found or empty response:', response);
      }
    } catch (error) {
      console.error('Failed to fetch article detail:', error);
    }
  };

  // 处理分类点击
  const handleCategoryClick = (category: HelpCategory) => {
    setSelectedCategory(category);
    fetchArticles(category.category_id.toString(), searchKeyword);
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    const trimmedValue = value.trim();
    setSearchKeyword(trimmedValue);
    if (selectedCategory) {
      fetchArticles(selectedCategory.category_id.toString(), trimmedValue);
    } else if (trimmedValue) {
      // 如果没有选择分类但有搜索关键词，可以在所有分类中搜索
      // 这里可以实现全局搜索功能
      console.log('Global search for:', trimmedValue);
    }
  };

  // 处理文章点击
  const handleArticleClick = (article: HelpArticle) => {
    fetchArticleDetail(article.id);
    currentPageArticles.map(item =>{
      if(item.id == article.id){
        item.clickcount ++;
      }
    })
  };

  // 返回分类列表
  const handleBackToCategories = () => {
    setSelectedCategory(null);
    setArticles([]);
    setSearchKeyword('');
    setCurrentPage(1);
  };

  // 获取根分类（pid为0的分类）
  const getRootCategories = () => {
    return categories.filter(cat => cat.pid === 0);
  };

  // 获取子分类
  const getChildCategories = (parentId: number) => {
    return categories.filter(cat => cat.pid === parentId);
  };

  // 分页处理
  const totalPages = Math.ceil(articles.length / pageSize);
  const currentPageArticles = articles.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部横幅区域 */}
      <div className="w-full min-h-[300px] md:min-h-[400px] bg-cover bg-center py-12 md:py-16 relative flex justify-center"
        style={{
          backgroundImage: 'url(/images/help_bg.jpg), linear-gradient(135deg, #f97316 0%, #ea580c 100%)',
        }}>
        {/* 橙色遮罩 */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#f97316cc] to-[#ea580ccc] opacity-90"></div>

        <div className="container mx-auto px-4 lg:px-8 relative z-10 flex flex-col justify-center items-center gap-6 md:gap-10">
          <div className="text-center text-white">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6">
              {dict?.help?.title || 'Help Center'}
            </h1>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="container mx-auto px-4 py-8 md:py-12">
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
            {[...Array(6)].map((_, i) => (
              <Skeleton key={i} active paragraph={{ rows: 3 }} />
            ))}
          </div>
        ) : selectedCategory ? (
          // 显示文章列表
          <div>
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleBackToCategories}
                  className="flex items-center gap-2 text-[#FF6B00] hover:text-[#e55a00] transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  {dict?.help?.articles?.backToCategories || 'Back to Categories'}
                </button>
                <h2 className="text-2xl font-bold text-gray-800">{selectedCategory.name}</h2>
              </div>
            </div>

            {articlesLoading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} active paragraph={{ rows: 2 }} />
                ))}
              </div>
            ) : currentPageArticles.length > 0 ? (
              <>
                <div className="space-y-4 mb-8">
                  {currentPageArticles.map((article) => (
                    <div
                      key={article.id}
                      onClick={() => handleArticleClick(article)}
                      className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:p-6 hover:shadow-md transition-shadow cursor-pointer article-card"
                    >
                      <div className="flex items-start gap-3 md:gap-4">
                        <div className="flex-shrink-0 w-10 h-10 md:w-12 md:h-12 bg-[#FF6B00] bg-opacity-10 rounded-lg flex items-center justify-center">
                          <FileTextOutlined className="text-[#FF6B00] text-lg md:text-xl" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="text-base md:text-lg font-semibold text-gray-800 mb-2 hover:text-[#FF6B00] transition-colors">
                            {article.title}
                          </h3>

                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs text-gray-500">
                            <span>{dict?.help?.articles?.lastUpdated || 'Last Updated'}: {article.updatetime}</span>
                            <span className="hidden sm:inline">•</span>
                            <span>{article.clickcount} {dict?.help?.views || '次查看'}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* 分页 */}
                {totalPages > 1 && (
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    pageSize={pageSize}
                    onPageChange={handlePageChange}
                    prevText={dict?.help?.pagination?.prev || '上一页'}
                    nextText={dict?.help?.pagination?.next || '下一页'}
                  />
                )}
              </>
            ) : (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={dict?.help?.empty || 'No content available'}
              />
            )}
          </div>
        ) : (
          // 显示分类列表
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-8 text-center">
              {dict?.help?.categories?.title || 'Help Categories'}
            </h2>

            {getRootCategories().length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                {getRootCategories().map((category) => {
                  const childCategories = getChildCategories(category.category_id);
                  return (
                    <div key={category.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow category-card">
                      <div className="p-4 md:p-6">
                        <div className="flex items-center gap-4 mb-4">
                          <div className="flex-shrink-0 w-16 h-16 bg-[#FF6B00] bg-opacity-10 rounded-lg flex items-center justify-center">
                            {category.image ? (
                              <img src={category.image} alt={category.name} className="w-8 h-8 object-contain" />
                            ) : (
                              <FolderOutlined className="text-[#FF6B00] text-2xl" />
                            )}
                          </div>
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-gray-800 mb-1">{category.name}</h3>
                            <p className="text-sm text-gray-600">{category.description}</p>
                          </div>
                        </div>

                        {/* 子分类 */}
                        {childCategories.length > 0 && (
                          <div className="space-y-2">
                            {childCategories.slice(0, 5).map((child) => (
                              <button
                                key={child.id}
                                onClick={() => handleCategoryClick(child)}
                                className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:text-[#FF6B00] hover:bg-gray-50 rounded transition-colors"
                              >
                                {child.name}
                              </button>
                            ))}
                            {childCategories.length > 5 && (
                              <button
                                onClick={() => handleCategoryClick(category)}
                                className="w-full text-left px-3 py-2 text-sm text-[#FF6B00] hover:bg-gray-50 rounded transition-colors"
                              >
                                {dict?.help?.categories?.viewAll || 'View All'} (+{childCategories.length-5} {dict?.help?.categories?.articles || 'more'})
                              </button>
                            )}
                          </div>
                        )}

                        {/* 如果没有子分类，直接点击查看文章 */}
                        {/* {childCategories.length === 0 && (
                          <button
                            onClick={() => handleCategoryClick(category)}
                            className="w-full mt-4 px-4 py-2 bg-[#FF6B00] text-white rounded-lg hover:bg-[#e55a00] transition-colors"
                          >
                            {dict?.help?.articles?.readMore || 'View Articles'}
                          </button>
                        )} */}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={dict?.help?.empty || 'No categories available'}
              />
            )}
          </div>
        )}
      </div>

      {/* 文章详情模态框 */}
      <Modal
        title={selectedArticle?.title}
        open={showArticleModal}
        onCancel={() => setShowArticleModal(false)}
        footer={null}
        width="90%"
        style={{ maxWidth: '800px' }}
        className="help-article-modal"
        centered
      >
        {selectedArticle && (
          <div className="prose max-w-none">
            <div className="mb-4 text-sm text-gray-500">
              {dict?.help?.articles?.lastUpdated || 'Last Updated'}: {selectedArticle.updatetime}
            </div>
            <div
              className="article-content"
              dangerouslySetInnerHTML={{ __html: selectedArticle.content }}
            />
          </div>
        )}
      </Modal>
    </div>
  );
}
