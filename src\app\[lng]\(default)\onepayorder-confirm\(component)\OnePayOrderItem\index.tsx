'use client'
import React, { useState, useEffect, useCallback } from 'react'
import Image from 'next/image'
import { Collapse, Checkbox, Input } from 'antd'
import { formatCurrency } from '@/utils/currency'
import { Api } from '@/request/api'
import message from '@/components/CustomMessage'
import { prepareImageForNextJs } from '@/utils/imageUtils'

interface CartItem {
  id: number;
  mall_goods_id: number;
  goods_id: string;
  goodsname: string;
  goodsimg: string;
  goodsprice: string;
  sendprice?: string;
  goodsnum: number;
  skuname: string;
  weight?: number;
  volume?: number;
  [key: string]: any;
}

interface OnePayOrderItemProps {
  products: CartItem[];
  dict: any;
  itemData?: any;
}

// 声明全局对象用于存储服务数据
declare global {
  interface Window {
    clearOnePayOrderServiceState?: () => void;
  }
}

export default function OnePayOrderItem({ products, dict, itemData }: OnePayOrderItemProps) {
  // 附加服务相关状态
  const [serverList, setServerList] = useState<any>({});
  const [productServices, setProductServices] = useState<Record<number, {
    selectedServices: Record<string, boolean>,
    photoCount: number,
    photoRemarks: string[]
  }>>({});
  const [showAccessorialService, setShowAccessorialService] = useState(true);

  // 从localStorage恢复服务选择状态
  const loadServiceStateFromStorage = useCallback(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedState = localStorage.getItem('onePayOrderServiceState');
        if (savedState) {
          const parsedState = JSON.parse(savedState);
          console.log('恢复一次付款订单附加服务选择状态:', parsedState);
          setProductServices(parsedState);
          return parsedState;
        }
      } catch (error) {
        console.error('恢复一次付款订单附加服务状态失败:', error);
      }
    }
    return {};
  }, []);

  // 保存服务选择状态到localStorage
  const saveServiceStateToStorage = useCallback((services: typeof productServices) => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('onePayOrderServiceState', JSON.stringify(services));
        console.log('保存一次付款订单附加服务选择状态:', services);
      } catch (error) {
        console.error('保存一次付款订单附加服务状态失败:', error);
      }
    }
  }, []);

  // 清理保存的服务状态
  const clearServiceState = useCallback(() => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem('onePayOrderServiceState');
        console.log('已清理一次付款订单附加服务选择状态');
      } catch (error) {
        console.error('清理一次付款订单附加服务状态失败:', error);
      }
    }
  }, []);

  const fixedUrl = (url: string) => {
    return prepareImageForNextJs(url);
  };

  // 初始化每个产品的服务选择状态
  useEffect(() => {
    // 检查 products 是否存在且为数组
    if (!products || !Array.isArray(products)) {
      return;
    }

    // 先尝试从localStorage恢复状态
    const savedState = loadServiceStateFromStorage();

    const initialServices: Record<number, {
      selectedServices: Record<string, boolean>,
      photoCount: number,
      photoRemarks: string[]
    }> = {};

    products.forEach(product => {
      if (product && product.id) {
        // 如果有保存的状态且包含当前产品，使用保存的状态
        if (savedState[product.id]) {
          initialServices[product.id] = savedState[product.id];
        } else {
          // 否则使用默认状态
          initialServices[product.id] = {
            selectedServices: {},
            photoCount: 1,
            photoRemarks: ['']
          };
        }
      }
    });

    setProductServices(initialServices);
  }, [products, loadServiceStateFromStorage]);

  // 获取附加服务列表
  const fetchServerList = async () => {
    try {
      const res = await Api.getServerList('goods')
      if (res.success) {
        setServerList(res.data)
      }
    } catch (error) {
      message.error(dict?.confirm?.onepayorder?.messages?.orderSubmitFailed || 'Failed to fetch additional services')
    }
  }

  useEffect(() => {
    fetchServerList()
  }, []);

  // 将清理函数添加到全局对象
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.clearOnePayOrderServiceState = clearServiceState;
    }
  }, [clearServiceState]);

  // 处理服务选择
  const handleServiceSelect = (productId: number, key: string) => {
    setProductServices(prev => {
      const productService = prev[productId] || {
        selectedServices: {},
        photoCount: 1,
        photoRemarks: ['']
      };

      const isCurrentlySelected = productService.selectedServices[key] || false;

      const updatedService = {
        ...productService,
        selectedServices: {
          ...productService.selectedServices,
          [key]: !isCurrentlySelected
        }
      };

      // 如果选择了照片服务，初始化照片数量和备注
      if (key === 'photo') {
        if (!isCurrentlySelected) {
          updatedService.photoCount = 1;
          updatedService.photoRemarks = [''];
        } else {
          updatedService.photoCount = 0;
          updatedService.photoRemarks = [];
        }
      }

      const result = {
        ...prev,
        [productId]: updatedService
      };

      // 保存状态到localStorage
      saveServiceStateToStorage(result);

      // 更新全局window对象
      if (typeof window !== 'undefined') {
        window.orderServices = {
          serverList,
          productServices: result,
          getServiceData: () => {
            const serviceData: any = {};
            Object.entries(result).forEach(([productId, service]) => {
              Object.entries(service.selectedServices).forEach(([serviceKey, selected]) => {
                if (selected) {
                  if (!serviceData[serviceKey]) {
                    serviceData[serviceKey] = {};
                  }

                  if (serviceKey === 'photo') {
                    serviceData[serviceKey][productId] = {
                      fee: parseFloat(serverList[serviceKey]?.value || '0') * service.photoCount,
                      num: service.photoCount,
                      remark: JSON.stringify(service.photoRemarks)
                    };
                  } else {
                    serviceData[serviceKey][productId] = {
                      fee: parseFloat(serverList[serviceKey]?.value || '0')
                    };
                  }
                }
              });
            });
            return serviceData;
          }
        };
      }

      return result;
    });
  };

  const getProductQuantity = (product: CartItem) => {
    // For one-time payment, prioritize itemData quantity if available
    return itemData?.goodsnum || product.goodsnum || 1;
  };

  const calculateItemTotal = (product: CartItem) => {
    const quantity = getProductQuantity(product);
    const price = parseFloat(String(product.goodsprice)) || 0;
    return price * quantity;
  };

  const calculateGrandTotal = () => {
    if (!products || !Array.isArray(products)) {
      return 0;
    }
    return products.reduce((sum, product) => {
      return sum + calculateItemTotal(product);
    }, 0);
  };

  const calculateTotalShippingFee = () => {
    if (!products || !Array.isArray(products)) {
      return 0;
    }
    return products.reduce((sum, product) => {
      const sendprice = parseFloat(product.sendprice || '0');
      return sum + sendprice;
    }, 0);
  };

  // Handle empty or invalid products
  if (!products || !Array.isArray(products) || products.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>{dict?.confirm?.order?.noProducts || 'No product information'}</p>
        <p className="text-xs mt-2">{dict?.confirm?.order?.returnToCart || 'Please return to cart and select products again'}</p>
      </div>
    );
  }

  // Validate product data structure
  const validProducts = (products || []).filter(product =>
    product &&
    product.goodsname &&
    product.goodsprice !== undefined &&
    product.goodsprice !== null
  );

  if (validProducts.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>{dict?.confirm?.order?.invalidData || 'Invalid product data'}</p>
        <p className="text-xs mt-2">{dict?.confirm?.order?.refreshPage || 'Please refresh the page or select products again'}</p>
      </div>
    );
  }

  return (
    <div>
      {/* 商家分组 */}
      <div className="mb-6">
        {/* 商家信息 */}
        <div className="flex items-center mb-4">
          <div className="w-4 h-4 mr-2 flex items-center justify-center">
            <svg className="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6z"/>
            </svg>
          </div>
          <span className="text-sm text-gray-700">
            {(products && products[0])?.goodsseller || (dict?.confirm?.order?.officialStore || 'Official Store')}
          </span>
        </div>
        <div className="text-xs text-gray-500 mb-4">
        {dict?.confirm?.onepayorder?.productInfo?.shippingToChina || 'Shipping to China'}：{formatCurrency(calculateTotalShippingFee()).formatValue}
        </div>

        {/* 商品列表 */}
        {validProducts.map((product, index) => {
          const quantity = getProductQuantity(product);
          const itemTotal = calculateItemTotal(product);
          const productService = productServices[product.id] || {
            selectedServices: {},
            photoCount: 1,
            photoRemarks: ['']
          };

          return (
            <div key={product.id || index} className="py-4">
              <div className="flex items-start space-x-4">
                {/* 商品图片 */}
                <div className="flex-shrink-0 w-16 h-16 relative">
                  <Image
                    src={fixedUrl(product.goodsimg)}
                    alt={product.goodsname}
                    fill
                    className="object-cover rounded"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/images/default.jpg';
                    }}
                  />
                </div>

                {/* 商品信息 */}
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-gray-900 mb-2 leading-tight">
                    {product.goodsname}
                  </h3>

                  {product.skuname && (
                    <div className="text-xs text-gray-500 mb-2">
                      {dict?.confirm?.onepayorder?.productInfo?.specifications || 'Specifications'}：{product.skuname}
                    </div>
                  )}
                </div>

                {/* 数量和价格 */}
                <div className="flex-shrink-0 text-right">
                  <div className="text-sm font-medium text-gray-900 mb-2">
                    × {quantity}
                  </div>
                  <div className="text-lg font-bold text-gray-900">
                    {formatCurrency(itemTotal).formatValue}
                  </div>
                </div>
              </div>

              {/* 附加服务部分 */}
              {showAccessorialService && (
                <div className="ml-4 mt-3">
                  <Collapse
                    ghost
                    items={[
                      {
                        key: '1',
                        label: <span className="text-sm font-medium">{dict?.confirm?.onepayorder?.productInfo?.additionalServices || 'Additional Services'}</span>,
                        children: (
                          <div className="space-y-2">
                            {Object.entries(serverList).map(([key, service]: [string, any]) => (
                              <div key={key}>
                                <div
                                  className="flex justify-between items-center text-sm cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors"
                                  onClick={() => handleServiceSelect(product.id, key)}
                                >
                                  <div className="flex items-center gap-2">
                                    <Checkbox
                                      checked={productService.selectedServices[key] || false}
                                    />
                                    <span>{dict?.confirm?.server?.[key] || service.name || key}</span>
                                  </div>
                                  <span className="text-gray-500">
                                    {formatCurrency(parseFloat(service.value || 0)).formatValue}
                                  </span>
                                </div>

                                {/* 照片服务的额外选项 */}
                                {key === 'photo' && productService.selectedServices[key] && (
                                  <div className="ml-6 mt-2 space-y-2">
                                    <div className="flex items-center gap-2">
                                      <span className="text-xs text-gray-600">{dict?.confirm?.addition?.photoCount || 'Photo Quantity'}:</span>
                                      <Input
                                        type="number"
                                        min={1}
                                        max={10}
                                        value={productService.photoCount}
                                        onChange={(e) => {
                                          const count = parseInt(e.target.value) || 1;
                                          setProductServices(prev => ({
                                            ...prev,
                                            [product.id]: {
                                              ...prev[product.id],
                                              photoCount: count,
                                              photoRemarks: Array(count).fill('').map((_, i) =>
                                                prev[product.id]?.photoRemarks[i] || ''
                                              )
                                            }
                                          }));
                                        }}
                                        className="w-16 text-xs"
                                      />
                                    </div>

                                    {Array.from({ length: productService.photoCount }, (_, i) => (
                                      <div key={i} className="flex items-center gap-2">
                                        <span className="text-xs text-gray-600">{dict?.confirm?.addition?.remark || 'Remark'}{i + 1}:</span>
                                        <Input
                                          placeholder={`${dict?.confirm?.addition?.photo || 'Photo'}${i + 1} ${dict?.confirm?.addition?.remark || 'Remark'}`}
                                          value={productService.photoRemarks[i] || ''}
                                          onChange={(e) => {
                                            const newRemarks = [...productService.photoRemarks];
                                            newRemarks[i] = e.target.value;
                                            setProductServices(prev => ({
                                              ...prev,
                                              [product.id]: {
                                                ...prev[product.id],
                                                photoRemarks: newRemarks
                                              }
                                            }));
                                          }}
                                          className="text-xs"
                                        />
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        )
                      }
                    ]}
                  />
                </div>
              )}
            </div>
          );
        })}

      </div>
    </div>
  );
}
