'use client'

import { Api } from '@/request/api'
import { useState } from 'react'
import { message } from 'antd'
import { useRouter } from 'next/navigation'

export default function DeleteFavorite({ favorite }: { favorite: number }) {
  const router = useRouter()

  const handleDeleteFavorite = async () => {
      await Api.deleteFavorite(favorite)
      router.refresh()
  }

  return (
    <>
     <div className="absolute top-2 right-2 z-10 i-heroicons-heart-solid text-red-500 text-2xl cursor-pointer" onClick={handleDeleteFavorite}>
     </div>
    </>
  )
}