/**
 * Simple error tracking utility for production debugging
 */

interface ErrorInfo {
  message: string;
  stack?: string;
  url?: string;
  userAgent?: string;
  timestamp: string;
  userId?: string;
  sessionId?: string;
}

export const trackError = (error: Error, context?: Record<string, any>) => {
  if (typeof window === 'undefined') return;
  
  const errorInfo: ErrorInfo = {
    message: error.message,
    stack: error.stack,
    url: window.location.href,
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString(),
    ...context
  };
  
  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.error('Tracked Error:', errorInfo);
  }
  
  // Store in localStorage for debugging (keep last 10 errors)
  try {
    const existingErrors = JSON.parse(localStorage.getItem('error_log') || '[]');
    const updatedErrors = [errorInfo, ...existingErrors].slice(0, 10);
    localStorage.setItem('error_log', JSON.stringify(updatedErrors));
  } catch (e) {
    console.error('Failed to store error log:', e);
  }
  
  // In production, you could send this to your error tracking service
  // Example: sendToErrorService(errorInfo);
};

export const getErrorLog = (): ErrorInfo[] => {
  if (typeof window === 'undefined') return [];
  
  try {
    return JSON.parse(localStorage.getItem('error_log') || '[]');
  } catch (e) {
    console.error('Failed to retrieve error log:', e);
    return [];
  }
};

export const clearErrorLog = () => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem('error_log');
  } catch (e) {
    console.error('Failed to clear error log:', e);
  }
};
