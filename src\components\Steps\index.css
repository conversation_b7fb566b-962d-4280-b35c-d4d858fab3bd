/* 统一Steps组件宽度 - 强制样式 - 使用更高优先级选择器 */
.ant-steps-icon{
    width: 52px !important;
    height: 52px !important;
}

/* 全局统一Steps容器宽度 - 覆盖所有可能的样式 */
.ant-steps,
div .ant-steps,
div[class*="steps"] .ant-steps,
div[class*="Steps"] .ant-steps,
.stepsContainer .ant-steps {
    max-width: 960px !important;
    width: 100% !important;
    margin: 0 auto !important;
    display: flex !important;
    justify-content: space-between !important;
}

/* 统一Steps包装器宽度 */
.ant-steps-horizontal,
div .ant-steps-horizontal,
div[class*="steps"] .ant-steps-horizontal,
div[class*="Steps"] .ant-steps-horizontal {
    max-width: 960px !important;
    width: 100% !important;
}

/* 统一每个步骤项的宽度分配 - 覆盖所有可能的样式 */
.ant-steps-item,
div .ant-steps-item,
div[class*="steps"] .ant-steps-item,
div[class*="Steps"] .ant-steps-item,
.stepsContainer .ant-steps-item {
    flex: 1 !important;
    min-width: 0 !important;
    max-width: none !important;
    padding-left: 8px !important;
    padding-right: 8px !important;
}

/* 确保步骤项容器居中对齐 */
.ant-steps-item-container,
div .ant-steps-item-container,
div[class*="steps"] .ant-steps-item-container,
div[class*="Steps"] .ant-steps-item-container,
.stepsContainer .ant-steps-item-container {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    width: 100% !important;
}

/* 统一步骤标题的最大宽度 */
.ant-steps-item-title,
div .ant-steps-item-title,
div[class*="steps"] .ant-steps-item-title,
div[class*="Steps"] .ant-steps-item-title,
.stepsContainer .ant-steps-item-title {
    max-width: 120px !important;
    text-align: center !important;
    word-wrap: break-word !important;
    margin: 0 auto !important;
    font-size: 14px !important;
}

/* 统一步骤内容位置 */
.ant-steps-item-content,
div .ant-steps-item-content,
div[class*="steps"] .ant-steps-item-content,
div[class*="Steps"] .ant-steps-item-content,
.stepsContainer .ant-steps-item-content {
    margin-top: 25px !important;
    text-align: center !important;
}

/* 统一步骤颜色 - 橙色主题 */
/* 当前步骤和已完成步骤的图标 */
.ant-steps-item-process .ant-steps-item-icon,
div .ant-steps-item-process .ant-steps-item-icon,
div[class*="steps"] .ant-steps-item-process .ant-steps-item-icon,
div[class*="Steps"] .ant-steps-item-process .ant-steps-item-icon,
.stepsContainer .ant-steps-item-process .ant-steps-item-icon,
.ant-steps-item-finish .ant-steps-item-icon,
div .ant-steps-item-finish .ant-steps-item-icon,
div[class*="steps"] .ant-steps-item-finish .ant-steps-item-icon,
div[class*="Steps"] .ant-steps-item-finish .ant-steps-item-icon,
.stepsContainer .ant-steps-item-finish .ant-steps-item-icon {
    background-color: #f97316 !important;
    border-color: #f97316 !important;
}

/* 图标内的文字和符号 */
.ant-steps-item-process .ant-steps-item-icon .ant-steps-icon,
div .ant-steps-item-process .ant-steps-item-icon .ant-steps-icon,
div[class*="steps"] .ant-steps-item-process .ant-steps-item-icon .ant-steps-icon,
div[class*="Steps"] .ant-steps-item-process .ant-steps-item-icon .ant-steps-icon,
.stepsContainer .ant-steps-item-process .ant-steps-item-icon .ant-steps-icon,
.ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon,
div .ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon,
div[class*="steps"] .ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon,
div[class*="Steps"] .ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon,
.stepsContainer .ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon {
    color: white !important;
}

/* 步骤标题颜色 */
.ant-steps-item-process .ant-steps-item-title,
div .ant-steps-item-process .ant-steps-item-title,
div[class*="steps"] .ant-steps-item-process .ant-steps-item-title,
div[class*="Steps"] .ant-steps-item-process .ant-steps-item-title,
.stepsContainer .ant-steps-item-process .ant-steps-item-title,
.ant-steps-item-finish .ant-steps-item-title,
div .ant-steps-item-finish .ant-steps-item-title,
div[class*="steps"] .ant-steps-item-finish .ant-steps-item-title,
div[class*="Steps"] .ant-steps-item-finish .ant-steps-item-title,
.stepsContainer .ant-steps-item-finish .ant-steps-item-title {
    color: #f97316 !important;
    font-weight: 500 !important;
}

/* 步骤描述颜色 */
.ant-steps-item-process .ant-steps-item-description,
div .ant-steps-item-process .ant-steps-item-description,
div[class*="steps"] .ant-steps-item-process .ant-steps-item-description,
div[class*="Steps"] .ant-steps-item-process .ant-steps-item-description,
.stepsContainer .ant-steps-item-process .ant-steps-item-description {
    color: rgba(249, 115, 22, 0.8) !important;
}

/* 连接线颜色 */
.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after,
div .ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after,
div[class*="steps"] .ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after,
div[class*="Steps"] .ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after,
.stepsContainer .ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after {
    background-color: #f97316 !important;
}

/* 响应式调整 - 移动端 */
@media (max-width: 768px) {
    .ant-steps,
    div .ant-steps,
    div[class*="steps"] .ant-steps,
    div[class*="Steps"] .ant-steps,
    .stepsContainer .ant-steps {
        max-width: 100% !important;
        padding: 0 10px !important;
    }

    .ant-steps-item-title,
    div .ant-steps-item-title,
    div[class*="steps"] .ant-steps-item-title,
    div[class*="Steps"] .ant-steps-item-title,
    .stepsContainer .ant-steps-item-title {
        max-width: 70px !important;
        font-size: 12px !important;
    }
}

/* 响应式调整 - 小屏幕 */
@media (max-width: 480px) {
    .ant-steps-item-title,
    div .ant-steps-item-title,
    div[class*="steps"] .ant-steps-item-title,
    div[class*="Steps"] .ant-steps-item-title,
    .stepsContainer .ant-steps-item-title {
        max-width: 60px !important;
        font-size: 11px !important;
    }
}