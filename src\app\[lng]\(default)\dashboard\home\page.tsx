import Image from 'next/image'
import { getServerData } from '@/request/server'
import Nick<PERSON><PERSON> from '@/components/NickName'
import MoneyCounter from '@/components/Count'
import ProductCard from '@/components/ProductCard'
import { UserInfo } from '@/types'
import RechargeButton from './RechargeButton'
import { getDictionary } from '@/dictionaries'
import ClearHistory from "./(component)/clearHistory"
import { UpperCaseFirst } from '@/utils';
import Signin from "./(component)/signin"
import Announcement from "./(component)/announcement"
import { prepareImageForNextJs } from '@/utils/imageUtils'
import './home.css'


interface HistoryItem {
  id: number;
  user_id: number;
  goods_id: string;
  goodsurl: string;
  goodsname: string;
  goodsprice: string;
  goodsimg: string;
  goodsseller: string;
  sellerurl: string;
  goodssite: string;
  mall_goods_id: number;
  langcode: string;
  createtime: string;
  deletetime: string | null;
  favorited: number;
  recommend?: number | string; // 推荐标识字段
}

interface HistoryResponse {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  data: HistoryItem[];
}


export default async function MemberPage({
  params,
}: {
  params: Promise<{ lng: string }>;
}) {
  const { lng } = await params;
  const dict = await getDictionary(lng);
  const isTp6 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '6'

  const [userInfoResult, historyResult] = await Promise.allSettled([
    getServerData(isTp6 ? '/web/member/user/info':'/api/user/info',isTp6 ? 'POST':'GET') as Promise<UserInfo>,
    getServerData(isTp6 ? '/web/member/user/web_history':'/api/history/lists',"POST",{size:20}) as Promise<HistoryResponse>
  ]);

  const defaultUserInfo: UserInfo = {
    "id": 1,
    "username": "",
    "nickname": "",
    "email": "",
    "mobilecode": "1",
    "mobile": "",
    "avatar": "",
    "level": 1,
    "gender": 2,
    "birthday": "",
    "bio": "",
    "money": "",
    "score": 1,
    "status": "normal",
    "rebate": "0",
    "spread_id": 0,
    "experience": 0,
    "qrcode_url": null,
    "token": "",
    "user_id": 1,
    "createtime": 1,
    "expiretime": 1,
    "expires_in": 2511313
  };

  const userInfo = userInfoResult.status === 'fulfilled' && userInfoResult.value ? userInfoResult.value : defaultUserInfo;
  const history = historyResult.status === 'fulfilled' && historyResult.value ? historyResult.value : { data: [] };
  const { nickname = '', id = '', money = 0, avatar = '' } = userInfo || defaultUserInfo;

  return (
    <div>

      <div className="bg-white rounded-lg mb-4 flex items-center" data-tg-profile>
        <div className="relative">
          <div className=" absolute -right-1 -top-1 bg-[#ffa96d] rounded-full px-2 py-1 text-xs text-white flex items-center z-10" style={{
            boxShadow: "0px 0px 10px 1px #ffd94f, 0px 0px 10px 1px #ffe895"
          }}>
            <span className="i-material-symbols:crown text-yellow-300 mr-1"></span>
            <span>LV {userInfo.level || 0}</span>
          </div>
          <div className="w-32 h-32 rounded-full p-1 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500">
            <div className="w-full h-full bg-white rounded-full p-0.5">
              {avatar ? (
                <Image
                  src={prepareImageForNextJs(avatar)}
                  alt="Onebuy"
                  width={1200}
                  height={120}
                  className="w-full h-full object-cover rounded-full"
                  draggable="false"
                />
              ) : (
                <div className="w-full h-full bg-orange-500 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                  {nickname ? UpperCaseFirst(nickname.slice(0, 1)) : ''}
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="ml-10">
          <NickName nickName={nickname} />
          <Signin dict={dict} />
          <div className="text-gray-500 text-sm">ID: {id}</div>
        </div>
        <div className="ml-auto flex items-center gap-4" data-tg-balance>
          <div>
            <div className="text-right pb-1">
              <span className='i-material-symbols:account-balance-wallet text-2xl mr-2'></span>
              <span className="text-sm">{dict.dashboard.home.balance.title}</span>
            </div>
            <MoneyCounter value={Number(money)} /></div>
          <RechargeButton dict={dict} />
          {/* <button className="border border-[1px] border-[#1E293B] px-12 py-2 rounded-md hover:bg-gray-100 ">提现</button> */}
        </div>
      </div>

      {/* <div className="relative h-[120px] mb-4" data-tg-vip>
        <Image
          src="/images/home-banner.png"
          draggable="false"
          alt="VIP Banner"
          width={2156}
          height={176}
          className="w-full h-full rounded-lg object-cover"
        />
      </div> */}
      
      <Announcement dict={dict}  />
      <div className="flex justify-between items-center mb-6">
        <div className="text-2xl font-bold pt-10">{dict.dashboard.home.history.title}</div>
       {isTp6 &&  <ClearHistory dict={dict} style={{ display:history.data?.length === 0 ? 'none' : 'block' }} />}
      </div>
     
    
      <div className="bg-white rounded-lg p-6" data-tg-history>
        {history.data && history.data.length > 0 ? (
          <div className="grid grid-cols-5 gap-4">
            {history.data.map((item) => (
              <ProductCard
                key={item.id}
                product={{
                  num_iid: item.id.toString(),
                  title: item.goodsname,
                  pic_url: item.goodsimg,
                  price: item.goodsprice,
                  detail_url: item.goodsurl,
                  nick: item.goodsseller,
                  recommend: item.recommend // 传递推荐字段
                }}
                platform="taobao"
                dict={{
                  search: {
                    sold: '已售 {count}'
                  }
                }}
              />
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-[400px]">
            <div className="w-32 h-32 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-16 h-16 text-gray-400" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 5H7C5.89543 5 5 5.89543 5 7V19C5 20.1046 5.89543 21 7 21H17C18.1046 21 19 20.1046 19 19V7C19 5.89543 18.1046 5 17 5H15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                <path d="M9 5C9 3.89543 9.89543 3 11 3H13C14.1046 3 15 3.89543 15 5C15 6.10457 14.1046 7 13 7H11C9.89543 7 9 6.10457 9 5Z" stroke="currentColor" strokeWidth="2" />
              </svg>
            </div>
            <div className="text-gray-500">{dict.dashboard.home.history.empty}</div>
          </div>
        )}
      </div>
    </div >
  )
}
