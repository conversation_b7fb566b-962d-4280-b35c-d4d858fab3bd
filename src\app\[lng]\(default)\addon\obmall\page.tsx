'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import { Api } from '@/request/api';
import Loading from '@/components/Loading';
import ProductCard from '@/components/ProductCard';
import RecommendedProducts from '@/components/RecommendedProducts';
import Toast from '@/components/Toast';
import { getDictionary } from '@/dictionaries';
import type { Locale } from '@/config';
import FloorTabs from '@/components/FloorTabs';
import '@/styles/floor-tabs.css';

// 定义接口类型
interface MallProduct {
  id: number;
  goodsname: string;
  image: string;
  images?: string;
  price: string;
  orginal_price?: string;
  sales?: number;
  date_available_text: string;
  date_available: number;
  recommend?: number | string; // 推荐标识字段
}

interface MallGoodsList {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  data: MallProduct[];
}

interface MallCategory {
  id: number;
  pid: number;
  flag: string;
  createtime: string;
  updatetime: string;
  weigh: number;
  status: string;
  mall_category_id: number;
  langcode: string;
  name: string;
  image: string;
  spacer: string;
  childlist: any[];
}

interface MallFloor {
  id: number;
  pid: number;
  flag: string;
  createtime: string;
  updatetime: string;
  weigh: number;
  status: string;
  mall_category_id: number;
  langcode: string;
  name: string;
  image: string;
  spacer: string;
  childlist: MallCategory[];
  goodslist: MallGoodsList;
}

interface MallFloorResponse {
  code: number;
  msg: string;
  success: boolean;
  data: MallFloor[];
  time: number;
}



export default function MallPage() {
  const params = useParams();
  const router = useRouter();
  const lng = params.lng as Locale;

  // 状态管理
  const [dict, setDict] = useState<any>(null);
  const [floors, setFloors] = useState<MallFloor[]>([]);
  const [recommendedItems, setRecommendedItems] = useState<MallFloor[]>([]);
  const [loading, setLoading] = useState(true);
  const [addonEnabled, setAddonEnabled] = useState<boolean | null>(null);
  // 管理每个楼层的当前选中标签页
  const [activeFloorTabs, setActiveFloorTabs] = useState<Record<number, string>>({});
  // 管理每个楼层标签页的商品数据
  const [floorTabProducts, setFloorTabProducts] = useState<Record<string, MallGoodsList>>({});

  // 检查插件状态
  useEffect(() => {
    const checkAddonStatus = async () => {
      try {
        // 暂时跳过插件检查，直接启用用于测试
        console.log('暂时跳过插件检查，直接启用自营商城');
        setAddonEnabled(true);
        return;

        // 使用新的插件管理工具检查插件是否启用
        const { isPluginEnabled } = await import('@/utils/plugin');
        const pluginEnabled = await isPluginEnabled('obmall');

        console.log('Obmall插件状态:', pluginEnabled);

        // 检查插件是否启用
        if (pluginEnabled) {
          setAddonEnabled(true);
          console.log('Obmall插件已启用，继续加载页面');
        } else {
          // 插件未启用，跳转到首页
          console.log('Obmall addon is not enabled, redirecting to homepage');
          setAddonEnabled(false);
          // 暂时注释掉跳转，用于调试
          // router.push(`/${lng}`);
          return;
        }
      } catch (error) {
        console.error('Failed to check addon status:', error);
        // 检查失败时也跳转到首页
        router.push(`/${lng}`);
        return;
      }
    };

    checkAddonStatus();
  }, [lng, router]);

  // 初始化字典
  useEffect(() => {
    const initDict = async () => {
      try {
        const dictionary = await getDictionary(lng);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };

    // 只有在插件启用时才加载字典
    if (addonEnabled) {
      initDict();
    }
  }, [lng, addonEnabled]);

  // 加载楼层数据
  useEffect(() => {
    const loadFloors = async () => {
      try {
        const response: MallFloorResponse = await Api.getMallFloor({ size: '10' });
        console.log('Floor response:', response);
        if (response.success) {
          console.log('Setting floors:', response.data);

          // 分离推荐商品和普通楼层
          const regularFloors = response.data.filter(item => item.flag === 'floor');
          const recommendedProducts = response.data.filter(item => item.flag === 'recommend');

          setFloors(regularFloors);
          setRecommendedItems(recommendedProducts);

          // 初始化每个楼层的默认标签页（默认选中楼层本身）
          const initialTabs: Record<number, string> = {};
          regularFloors.forEach(floor => {
            // 默认选中楼层本身的分类ID
            initialTabs[floor.id] = floor.mall_category_id.toString();
          });
          setActiveFloorTabs(initialTabs);

          console.log('Regular floors:', regularFloors);
          console.log('Recommended items:', recommendedProducts);
        } else {
          console.log('Floor response error:', response);
          Toast.error(response.msg || dict?.mall?.error?.loadFloors || '加载楼层失败');
        }
      } catch (error) {
        console.error('Failed to load floors:', error);
        Toast.error(dict?.mall?.error?.loadFloors || '加载楼层失败');
      } finally {
        setLoading(false);
      }
    };

    // 只有在插件启用且字典加载完成后才加载楼层数据
    if (addonEnabled && dict) {
      loadFloors();
    }
  }, [dict, addonEnabled]);

  // 加载特定分类的商品数据
  const loadCategoryProducts = async (categoryId: string) => {
    try {
      const response = await Api.getMallProductList({
        category_id: categoryId,
        page: 1,
        size: '10',
        sort: 'default',
        recommend: '1',
      });

      if (response.success) {
        setFloorTabProducts(prev => ({
          ...prev,
          [categoryId]: response.data
        }));
      }
    } catch (error) {
      console.error('Failed to load category products:', error);
    }
  };

  // 处理标签页切换
  const handleTabChange = (floorId: number, activeKey: string) => {
    setActiveFloorTabs(prev => ({
      ...prev,
      [floorId]: activeKey
    }));

    // 获取当前楼层信息
    const currentFloor = floors.find(f => f.id === floorId);
    if (!currentFloor) return;

    // 如果选中的不是楼层本身，且该分类的商品数据还没有加载，则加载
    if (activeKey !== currentFloor.mall_category_id.toString() && !floorTabProducts[activeKey]) {
      loadCategoryProducts(activeKey);
    }
  };

  console.log('Component state:', { loading, dict: !!dict, floors: floors.length, addonEnabled });

  // 如果插件状态未确定或插件未启用，显示加载状态
  if (addonEnabled === null || !addonEnabled) {
    return <Loading />;
  }

  // 如果插件启用但字典或楼层数据还在加载中
  if (loading || !dict) {
    return <Loading />;
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="max-w-[1200px] mx-auto p-4">
        {/* 推荐商品展示 */}
        <RecommendedProducts
          recommendedItems={recommendedItems}
          dict={dict}
          lng={lng}
        />

        {/* 楼层展示 */}
        {floors.length > 0 ? (
          <div className="space-y-8">
            {floors.map((floor) => (
              <div key={floor.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
                {/* 没有子分类时显示传统楼层标题 */}
                {(!floor.childlist || floor.childlist.length === 0) && (
                  <div className="bg-gradient-to-r from-orange-500 to-orange-600 px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        {floor.image && (
                          <div className="w-12 h-12 rounded-lg overflow-hidden bg-white/20">
                            <Image
                              src={floor.image.startsWith('http')
                                ? floor.image
                                : `${process.env.NEXT_PUBLIC_BASE_URL || 'https://api.v6.daigouxt.com'}${floor.image}`
                              }
                              alt={floor.name}
                              width={48}
                              height={48}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        )}
                        <h2 className="text-xl font-bold text-white">{floor.name}</h2>
                      </div>
                      <div className="flex items-center space-x-4">
                        <button
                          onClick={() => router.push(`/${lng}/addon/obmall/list?category_id=${floor.mall_category_id}&name=${encodeURIComponent(floor.name)}`)}
                          className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center space-x-1"
                        >
                          <span>{dict?.mall?.more || '更多'}</span>
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* 分类标签页 */}
                {floor.childlist && floor.childlist.length > 0 && (
                  <div className="bg-white">
                    <div
                      className="floor-tabs-container"
                      style={{
                        '--tabs-nav-bg': 'linear-gradient(to right, #f97316, #ea580c)'
                      } as React.CSSProperties}
                    >
                      <FloorTabs
                        activeKey={activeFloorTabs[floor.id] || floor.mall_category_id.toString()}
                        onChange={(activeKey) => handleTabChange(floor.id, activeKey)}
                        tabBarExtraContent={
                        <button
                          onClick={() => router.push(`/${lng}/addon/obmall/list?category_id=${floor.mall_category_id}&name=${encodeURIComponent(floor.name)}`)}
                          className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center space-x-1"
                        >
                          <span>{dict?.mall?.more || '更多'}</span>
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </button>
                        }
                        items={[
                        // 楼层本身作为第一个标签页
                        {
                          key: floor.mall_category_id.toString(),
                          label: floor.name,
                          children: (
                            <div className="p-6">
                              {floor.goodslist?.data && floor.goodslist.data.length > 0 ? (
                                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                                  {floor.goodslist.data.map((product: MallProduct) => {
                                    // 处理图片URL
                                    let imageUrl = product.image;
                                    if (imageUrl && !imageUrl.startsWith('http')) {
                                      if (imageUrl.startsWith('//')) {
                                        imageUrl = `https:${imageUrl}`;
                                      } else if (imageUrl.startsWith('/')) {
                                        imageUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'https://api.v6.daigouxt.com'}${imageUrl}`;
                                      }
                                    }

                                    return (
                                      <ProductCard
                                        key={product.id}
                                        product={{
                                          title: product.goodsname,
                                          pic_url: imageUrl,
                                          price: parseFloat(product.price),
                                          promotion_price: product.orginal_price ? parseFloat(product.orginal_price) : undefined,
                                          detail_url: `/${lng}/detail/obmall?id=${product.id}`,
                                          nick: '',
                                          recommend: product.recommend // 传递推荐字段
                                        }}
                                        platform={'obmall'}
                                        dict={dict}
                                      />
                                    );
                                  })}
                                </div>
                              ) : (
                                <div className="text-center py-12 text-gray-500">
                                  <div className="text-lg mb-2">{dict?.mall?.empty?.noProducts || '暂无商品'}</div>
                                  <div className="text-sm">{dict?.mall?.empty?.floorEmpty || '该楼层暂时没有商品'}</div>
                                </div>
                              )}
                            </div>
                          )
                        },
                        // 子分类作为其他标签页
                        ...floor.childlist.map(category => ({
                          key: category.mall_category_id.toString(),
                          label: category.name,
                          children: (
                            <div className="p-6">
                              {(() => {
                                const categoryProducts = floorTabProducts[category.mall_category_id.toString()];
                                return categoryProducts?.data && categoryProducts.data.length > 0 ? (
                                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                                    {categoryProducts.data.map((product: MallProduct) => {
                                      // 处理图片URL
                                      let imageUrl = product.image;
                                      if (imageUrl && !imageUrl.startsWith('http')) {
                                        if (imageUrl.startsWith('//')) {
                                          imageUrl = `https:${imageUrl}`;
                                        } else if (imageUrl.startsWith('/')) {
                                          imageUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'https://api.v6.daigouxt.com'}${imageUrl}`;
                                        }
                                      }

                                      return (
                                        <ProductCard
                                          key={product.id}
                                          product={{
                                            title: product.goodsname,
                                            pic_url: imageUrl,
                                            price: parseFloat(product.price),
                                            promotion_price: product.orginal_price ? parseFloat(product.orginal_price) : undefined,
                                            detail_url: `/${lng}/detail/obmall?id=${product.id}`,
                                            nick: '',
                                            recommend: product.recommend // 传递推荐字段
                                          }}
                                          platform={'obmall'}
                                          dict={dict}
                                        />
                                      );
                                    })}
                                  </div>
                                ) : (
                                  <div className="text-center py-12 text-gray-500">
                                    <div className="text-lg mb-2">{dict?.mall?.empty?.noProducts || '暂无商品'}</div>
                                    <div className="text-sm">{dict?.mall?.empty?.categoryEmpty || '该分类暂时没有商品'}</div>
                                  </div>
                                );
                              })()}
                            </div>
                          )
                        }))
                        ]}
                      />
                    </div>
                  </div>
                )}

                {/* 没有子分类时的商品货架 */}
                {(!floor.childlist || floor.childlist.length === 0) && (
                  <div className="p-6">
                    {floor.goodslist?.data && floor.goodslist.data.length > 0 ? (
                      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                        {floor.goodslist.data.map((product: MallProduct) => {
                          // 处理图片URL - 优先使用image字段，如果是相对路径则添加域名前缀
                          let imageUrl = product.image;
                          if (imageUrl && !imageUrl.startsWith('http')) {
                            if (imageUrl.startsWith('//')) {
                              imageUrl = `https:${imageUrl}`;
                            } else if (imageUrl.startsWith('/')) {
                              imageUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'https://api.v6.daigouxt.com'}${imageUrl}`;
                            }
                          }

                          return (
                            <ProductCard
                              key={product.id}
                              product={{
                                title: product.goodsname,
                                pic_url: imageUrl,
                                price: parseFloat(product.price),
                                promotion_price: product.orginal_price ? parseFloat(product.orginal_price) : undefined,
                                detail_url: `/${lng}/detail/obmall?id=${product.id}`,
                                nick: '',
                                recommend: product.recommend // 传递推荐字段
                              }}
                              platform={'obmall'}
                              dict={dict}
                            />
                          );
                        })}
                      </div>
                    ) : (
                      <div className="text-center py-12 text-gray-500">
                        <div className="text-lg mb-2">{dict?.mall?.empty?.noProducts || '暂无商品'}</div>
                        <div className="text-sm">{dict?.mall?.empty?.floorEmpty || '该楼层暂时没有商品'}</div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 text-gray-500">
            <div className="text-lg mb-2">{dict?.mall?.empty?.noFloors || '暂无楼层数据'}</div>
            <div className="text-sm">{dict?.mall?.empty?.tryLater || '请稍后再试'}</div>
          </div>
        )}
      </div>
    </div>
  );
}