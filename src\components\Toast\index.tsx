'use client'

import React, { useState, useEffect, useRef, useCallback, createContext, useContext, useReducer } from 'react';
import { createPortal } from 'react-dom';
import './toast.css';

type ToastType = 'info' | 'success' | 'warning' | 'error';
type ToastPosition = 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';

interface ToastItem {
  id: string;
  content: string;
  type: ToastType;
  duration?: number;
  position?: ToastPosition;
  createdAt: number;
}

interface ToastOptions {
  duration?: number;
  position?: ToastPosition;
  maxToasts?: number;
  preventDuplicates?: boolean;
}

interface ToastState {
  toasts: ToastItem[];
  maxToasts: number;
  preventDuplicates: boolean;
}

type ToastAction =
  | { type: 'ADD_TOAST'; payload: ToastItem }
  | { type: 'REMOVE_TOAST'; payload: string }
  | { type: 'CLEAR_ALL' }
  | { type: 'SET_CONFIG'; payload: Partial<Pick<ToastState, 'maxToasts' | 'preventDuplicates'>> };

// Toast状态管理
const toastReducer = (state: ToastState, action: ToastAction): ToastState => {
  switch (action.type) {
    case 'ADD_TOAST': {
      const newToast = action.payload;
      let toasts = [...state.toasts];

      // 防重复逻辑
      if (state.preventDuplicates) {
        const duplicateIndex = toasts.findIndex(
          toast => toast.content === newToast.content && toast.type === newToast.type
        );
        if (duplicateIndex !== -1) {
          // 如果找到重复的，移除旧的，添加新的
          toasts.splice(duplicateIndex, 1);
        }
      }

      // 添加新toast
      toasts.push(newToast);

      // 限制最大数量
      if (toasts.length > state.maxToasts) {
        toasts = toasts.slice(-state.maxToasts);
      }

      return { ...state, toasts };
    }
    case 'REMOVE_TOAST':
      return {
        ...state,
        toasts: state.toasts.filter(toast => toast.id !== action.payload)
      };
    case 'CLEAR_ALL':
      return { ...state, toasts: [] };
    case 'SET_CONFIG':
      return { ...state, ...action.payload };
    default:
      return state;
  }
};

// Toast Context
const ToastContext = createContext<{
  state: ToastState;
  dispatch: React.Dispatch<ToastAction>;
} | null>(null);

// 单个Toast组件
interface ToastItemProps {
  toast: ToastItem;
  index: number;
  onRemove: (id: string) => void;
}

const ToastItemComponent: React.FC<ToastItemProps> = ({ toast, index, onRemove }) => {
  const [isLeaving, setIsLeaving] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(Date.now());
  const remainingTimeRef = useRef<number>(toast.duration || 3000);

  // 启动定时器
  const startTimer = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      handleClose();
    }, remainingTimeRef.current);
  }, []);

  // 暂停定时器
  const pauseTimer = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
      const elapsed = Date.now() - startTimeRef.current;
      remainingTimeRef.current = Math.max(0, remainingTimeRef.current - elapsed);
    }
  }, []);

  // 恢复定时器
  const resumeTimer = useCallback(() => {
    startTimeRef.current = Date.now();
    startTimer();
  }, [startTimer]);

  // 处理关闭
  const handleClose = useCallback(() => {
    setIsLeaving(true);
    setTimeout(() => {
      onRemove(toast.id);
    }, 300); // 等待退出动画完成
  }, [toast.id, onRemove]);

  // 自动关闭逻辑
  useEffect(() => {
    if (toast.duration && toast.duration > 0 && !isPaused) {
      startTimer();
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [toast.duration, isPaused, startTimer]);

  // 鼠标悬停暂停
  const handleMouseEnter = () => {
    setIsPaused(true);
    pauseTimer();
  };

  const handleMouseLeave = () => {
    setIsPaused(false);
    resumeTimer();
  };

  // 获取样式配置
  const getToastConfig = () => {
    switch (toast.type) {
      case 'success':
        return {
          bgColor: 'bg-green-500',
          textColor: 'text-white',
        };
      case 'warning':
        return {
          bgColor: 'bg-yellow-500',
          textColor: 'text-white',
        };
      case 'error':
        return {
          bgColor: 'bg-red-500',
          textColor: 'text-white',
        };
      case 'info':
      default:
        return {
          bgColor: 'bg-blue-500',
          textColor: 'text-white',
        };
    }
  };

  const config = getToastConfig();

  return (
    <div
      className={`
        toast-item ${isLeaving ? 'leaving' : ''}
        relative mb-3 max-w-md w-full mx-auto
        ${config.bgColor} ${config.textColor}
        rounded-lg shadow-md
        transform transition-all duration-300 ease-out
        hover:shadow-lg
        cursor-pointer group
      `}
      style={{
        zIndex: 1000 + index,
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClose}
    >
      {/* 进度条 */}
      {toast.duration && toast.duration > 0 && (
        <div className="absolute top-0 left-0 h-1 bg-white/30 rounded-t-lg overflow-hidden w-full">
          <div
            className={`h-full bg-white/80 transition-all ease-linear ${isPaused ? 'paused' : ''}`}
            style={{
              animationDuration: `${remainingTimeRef.current}ms`,
              animationName: isPaused ? 'none' : 'shrink',
            }}
          />
        </div>
      )}

      <div className="flex items-center justify-between p-4 pt-5">
        {/* 内容 */}
        <div className="flex-1 min-w-0 pr-4">
          <p className="font-medium text-sm leading-relaxed break-words">
            {toast.content}
          </p>
        </div>

        {/* 关闭按钮 */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleClose();
          }}
          className="flex-shrink-0 p-1 rounded-full hover:bg-white/20 transition-colors duration-200 opacity-60 hover:opacity-100"
        >
          <span className="text-lg leading-none">×</span>
        </button>
      </div>
    </div>
  );
};

// Toast容器组件
const ToastContainer: React.FC = () => {
  const context = useContext(ToastContext);
  if (!context) return null;

  const { state, dispatch } = context;
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);

  // 创建Portal容器
  useEffect(() => {
    if (typeof document !== 'undefined') {
      let container = document.getElementById('toast-container');
      if (!container) {
        container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'fixed inset-0 pointer-events-none z-[9999]';
        document.body.appendChild(container);
      }
      setPortalContainer(container);
    }

    return () => {
      // 清理时不移除容器，因为可能有其他Toast实例在使用
    };
  }, []);

  const handleRemoveToast = useCallback((id: string) => {
    dispatch({ type: 'REMOVE_TOAST', payload: id });
  }, [dispatch]);

  if (!portalContainer || state.toasts.length === 0) return null;

  // 按位置分组Toast
  const groupedToasts = state.toasts.reduce((groups, toast) => {
    const position = toast.position || 'top-right';
    if (!groups[position]) {
      groups[position] = [];
    }
    groups[position].push(toast);
    return groups;
  }, {} as Record<ToastPosition, ToastItem[]>);

  // 获取位置样式
  const getPositionStyles = (position: ToastPosition) => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4 items-end';
      case 'top-left':
        return 'top-4 left-4 items-start';
      case 'bottom-right':
        return 'bottom-4 right-4 items-end';
      case 'bottom-left':
        return 'bottom-4 left-4 items-start';
      case 'top-center':
        return 'top-4 left-1/2 -translate-x-1/2 items-center';
      case 'bottom-center':
        return 'bottom-4 left-1/2 -translate-x-1/2 items-center';
      default:
        return 'top-4 left-1/2 -translate-x-1/2 items-center';
    }
  };

  return createPortal(
    <>
      {Object.entries(groupedToasts).map(([position, toasts]) => (
        <div
          key={position}
          className={`fixed flex flex-col pointer-events-none ${getPositionStyles(position as ToastPosition)}`}
        >
          {toasts.map((toast, index) => (
            <div key={toast.id} className="pointer-events-auto">
              <ToastItemComponent
                toast={toast}
                index={index}
                onRemove={handleRemoveToast}
              />
            </div>
          ))}
        </div>
      ))}
    </>,
    portalContainer
  );
};

// Toast Provider组件
interface ToastProviderProps {
  children: React.ReactNode;
  maxToasts?: number;
  preventDuplicates?: boolean;
  defaultPosition?: ToastPosition;
  defaultDuration?: number;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({
  children,
  maxToasts = 5,
  preventDuplicates = true,
}) => {
  const [state, dispatch] = useReducer(toastReducer, {
    toasts: [],
    maxToasts,
    preventDuplicates,
  });

  // 更新配置
  useEffect(() => {
    dispatch({
      type: 'SET_CONFIG',
      payload: { maxToasts, preventDuplicates }
    });
  }, [maxToasts, preventDuplicates]);

  // 自动设置全局dispatch
  useEffect(() => {
    toastManager.setDispatch(dispatch);
  }, [dispatch]);

  return (
    <ToastContext.Provider value={{ state, dispatch }}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  );
};

// 全局Toast管理器
class ToastManager {
  private static instance: ToastManager;
  private dispatch: React.Dispatch<ToastAction> | null = null;
  private toastCounter = 0;
  private isInitializing = false;
  private defaultOptions: ToastOptions = {
    duration: 3000,
    position: 'top-center',
    maxToasts: 5,
    preventDuplicates: true,
  };

  static getInstance(): ToastManager {
    if (!ToastManager.instance) {
      ToastManager.instance = new ToastManager();
    }
    return ToastManager.instance;
  }

  setDispatch(dispatch: React.Dispatch<ToastAction>) {
    this.dispatch = dispatch;
  }

  setDefaultOptions(options: Partial<ToastOptions>) {
    this.defaultOptions = { ...this.defaultOptions, ...options };
  }

  private async ensureInitialized(): Promise<void> {
    if (this.dispatch || this.isInitializing) return;

    if (typeof document === 'undefined') {
      console.warn('Toast can only be used in browser environment');
      return;
    }

    this.isInitializing = true;

    try {
      await this.initializeToastSystem();
    } catch (error) {
      console.error('Failed to initialize Toast system:', error);
    } finally {
      this.isInitializing = false;
    }
  }

  private async initializeToastSystem(): Promise<void> {
    let container = document.getElementById('toast-root');
    if (!container) {
      container = document.createElement('div');
      container.id = 'toast-root';
      document.body.appendChild(container);

      const { createRoot } = await import('react-dom/client');
      const React = await import('react');

      const root = createRoot(container);

      const ToastApp = React.createElement(ToastProvider, {
        children: React.createElement('div', { style: { display: 'none' } })
      });

      root.render(ToastApp);

      // 等待一小段时间让Provider挂载
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  private addToast(content: string, type: ToastType, options?: ToastOptions): string {
    const id = `toast-${++this.toastCounter}-${Date.now()}`;

    // 异步初始化，但立即返回ID
    this.ensureInitialized().then(() => {
      if (!this.dispatch) {
        console.warn('Toast system not ready.');
        return;
      }

      const mergedOptions = { ...this.defaultOptions, ...options };

      const toast: ToastItem = {
        id,
        content,
        type,
        duration: mergedOptions.duration,
        position: mergedOptions.position,
        createdAt: Date.now(),
      };

      this.dispatch({ type: 'ADD_TOAST', payload: toast });
    });

    return id;
  }



  info(content: string, options?: ToastOptions): string {
    return this.addToast(content, 'info', options);
  }

  success(content: string, options?: ToastOptions): string {
    return this.addToast(content, 'success', options);
  }

  warning(content: string, options?: ToastOptions): string {
    return this.addToast(content, 'warning', options);
  }

  error(content: string, options?: ToastOptions): string {
    return this.addToast(content, 'error', options);
  }

  remove(id: string): void {
    if (this.dispatch) {
      this.dispatch({ type: 'REMOVE_TOAST', payload: id });
    }
  }

  clear(): void {
    if (this.dispatch) {
      this.dispatch({ type: 'CLEAR_ALL' });
    }
  }
}

// 全局Toast实例
const toastManager = ToastManager.getInstance();

// Hook for using toast in components
export const useToast = () => {
  const context = useContext(ToastContext);

  useEffect(() => {
    if (context) {
      toastManager.setDispatch(context.dispatch);
    }
  }, [context]);

  return {
    info: (content: string, options?: ToastOptions) => toastManager.info(content, options),
    success: (content: string, options?: ToastOptions) => toastManager.success(content, options),
    warning: (content: string, options?: ToastOptions) => toastManager.warning(content, options),
    error: (content: string, options?: ToastOptions) => toastManager.error(content, options),
    remove: (id: string) => toastManager.remove(id),
    clear: () => toastManager.clear(),
  };
};

// 全局Toast API (向后兼容)
const Toast = {
  info: (content: string, options?: ToastOptions) => toastManager.info(content, options),
  success: (content: string, options?: ToastOptions) => toastManager.success(content, options),
  warning: (content: string, options?: ToastOptions) => toastManager.warning(content, options),
  error: (content: string, options?: ToastOptions) => toastManager.error(content, options),
  remove: (id: string) => toastManager.remove(id),
  clear: () => toastManager.clear(),
  setDefaultOptions: (options: Partial<ToastOptions>) => toastManager.setDefaultOptions(options),
};

// Toast系统现在使用按需初始化，无需预先初始化

export default Toast;
