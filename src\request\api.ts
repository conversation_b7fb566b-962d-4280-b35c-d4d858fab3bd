import { request } from '.';
import { catchError } from './decorators';
import type { UserInfo, LoginParams, ResponseData, RegisterParams, AddressParams } from '@/types';

// 地址相关类型定义
export interface AddressRegion {
  id: number;
  pid: number;
  name: string;
  level: number;
  langcode: string;
}

export interface AddressItem {
  id: number;
  area_id: number;
  user_id: number;
  consignee: string;
  country_id: number;
  region_id: number;
  address: string;
  zipcode: string;
  telephone: string;
  telephone2?: string;
  door_number?: string;
  tax_number?: string;
  address_tag?: string;
  isdefault: number;
  custom_field: null | any;
  createtime: number;
  updatetime: number;
  deletetime: null | number;
  province: AddressRegion;
  country: AddressRegion;
  mergename: string;
  custom_field_values: any[];
}
export interface GoodsHistory{
    "goodsname": string,
    "goodsprice": number,
    "goodsurl": string,
    "goodsimg": string,
    "goodsseller": string,
    "sellerurl": string,
    "goodssite": string,
    "goods_id": number
}


class Api5 {

  /**
   * 用户登录
   * @param email 邮箱
   * @param password 密码 , 需要 md5 加密
   * @returns 登录结果
   */
  @catchError()
  static async login(params: LoginParams) {
    const data = await request.post<ResponseData<UserInfo>>('/api/user/login', params);
    return data;
  }

  @catchError()
  static async logout() {
    return await request.post('/api/user/logout');
  }

  @catchError()
  static async register(params: RegisterParams) {
    return await request.post('/api/user/register', params);
  }

  @catchError()
  static async resetPassword(params: {
    type: 'mobile' | 'email';
    mobile?: string;
    mobilecode?: string;
    email?: string;
    newpassword: string;
    captcha: string;
  }) {
    return await request.post('/api/user/resetpwd', params);
  }

  @catchError()
  static async getUserInfo() {
    return await request.get('/api/user/info');
  }

  @catchError()
  static async editUserInfo(params: {
    custom_field?: string[];
    nickname: string;
    avatar: string;
    bio?: string;
    gender: number;
    birthday: string;
  }) {
    return await request.post('/api/user/profile', params);
  }

  @catchError()
  static async changeMobile(params: {
    mobile: string;
    mobilecode: string;
    captcha: string;
  }) {
    return await request.post('/api/user/changemobile', params);
  }

  @catchError()
  static async sendMobileCode(params: {
    mobile: string;
    mobilecode: string;
    event: 'register' | 'changemobile' | 'changepwd' | 'resetpwd';
  }) {
    return await request.post('/api/sms/send', params);
  }

  @catchError()
  static async getMcode() {
    return await request.post('/api/other/getmcode');
  }

  @catchError()
  static async changePwd(params: {
    old_password: string;
    new_password: string;
    renew_password: string;
  }) {
    return await request.post('/api/user/changepwd', params);
  }

  // 上传图片
  @catchError()
  static async uploadImage(base64Image: string) {
    const data = await request.post('/api/obapi/uploadImg', {
      imgcode: base64Image
    });
    return data;
  }

  @catchError()
  static async getItemList(params: any) {
    return await request.post('/api/obapi/getItemlist', params);
  }

  @catchError()
  static async getItemDetail(params: any) {
    return await request.post('/api/obapi/getItem', params);
  }


  @catchError()
  static async getGoodsType() {
    return await request.get('/api/other/getGoodsType');
  }

  @catchError()
  static async getFeedbackList(params: any) {
    return await request.get('/api/feedback/lists', params);
  }

  @catchError()
  static async addFeedback(params: { content: string; type?: number; imgs?: string }) {
    return await request.post('/api/feedback/add', params);
  }

  @catchError()
  static async getBankList() {
    return await request.post('/api/cash/bankLists');
  }

  @catchError()
  static async addCash(params: {
    transfer_account: string;
    transfer_card: string;
    transfer_bank_selected: string;
    transfer_amount: number;
    captcha?: string;
  }) {
    return await request.post('/api/cash/addCash', params);
  }

  @catchError()
  static async getCashList() {
    return await request.post('/api/cash/lists');
  }
  
  @catchError()
  static async getPointList() {
    return await request.get('/api/score/lists');
  }

  @catchError()
  static async exchangeCoupon(params: {
    coupon_id: number;
    num: number;
  }) {
    return await request.post('/api/integralcoupon/exchange', params);
  }
  

  //收藏功能接口
  @catchError()
  static async addFavorite(params: any) {
    return await request.post('/api/favorite/add', params);
  }

  @catchError()
  static async isFavorite(goodsurl: string) {
    return await request.get(`/api/favorite/checkFavorite?goodsurl=${goodsurl}`);
  }

  @catchError()
  static async deleteFavorite(favorite_id: number) {
    return await request.post('/api/favorite/delete', { favorite_id });
  }

  @catchError()
  static async clearFavorite() {
    return await request.post('/api/favorite/clear');
  }

  @catchError()
  static async getCouponList(params?: { page?: number; size?: number }) {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.size) queryParams.append('size', params.size.toString());
    const queryString = queryParams.toString();
    return await request.get(`/api/coupon/lists${queryString ? '?' + queryString : ''}`);
  }

  @catchError()
  static async couponActivate(params: { code: string }) {
    return await request.post('/api/couponrule/activate', params);
  }

  @catchError()
  static async getOrderList(params: any) {
    return await request.post('/api/order/lists', params);
  }

  @catchError()
  static async cancelOrder(params: { order_id: number }) {
    return await request.post('/api/order/cancel', params);
  }

  @catchError()
  static async getOrderDetail({ order_id }: { order_id: number }) {
    return await request.get(`/api/order/detail?order_id=${order_id}`);
  }

  @catchError()
  static async getWalletList({ action, starttime, endtime }: { action: string; starttime: string; endtime: string }) {
    return await request.post(`/api/balance/lists`, { action, starttime, endtime });
  }

  /**
   * 地址相关接口
  */

  @catchError()
  static async getAddressList(params: any = {}) {
    return await request.get<ResponseData<AddressItem[]>>('/api/address/lists', params);
  }

  @catchError()
  static async setDefaultAddress(id: number) {
    return await request.post('/api/address/defaultAddress', { id });
  }

  @catchError()
  static async deleteAddress(address_id: number) {
    return await request.post('/api/address/deleteAddress', { address_id });
  }

  @catchError()
  static async addAddress(params: AddressParams) {
    return await request.post('/api/address/addAddress', params);
  }

  @catchError()
  static async updateAddress(params: any) {
    return await request.post('/api/address/editAddress', params);
  }

  @catchError()
  static async getAreaList(params?: { country?: number }) {
    return await request.get(`/api/address/getAreaList${params?.country ? `?country=${params.country}` : ''}`);
  }

  /**
   * 购物车相关接口
  */

  @catchError()
  static async getServerList(type: 'goods' | 'sendorder') {
    return await request.get(`/api/other/server?type=${type}`);
  }

  @catchError()
  static async getCartNums() {
    return await request.post('/api/cart/cartNums');
  }

  @catchError()
  static async getCartList() {
    return await request.post('/api/cart/getList');
  }


  @catchError()
  static async addCart(params: {
    goodsname: string;
    goodsprice: number;
    sendprice: number;
    goodsnum: number;
    goodsurl: string;
    goodsimg: string;
    goodsseller: string;
    sellerurl: string;
    goodssite: string;
    sku_id: string;
    skuname: string;
    goodsweight: number;
    goodsvolume: number;
    isspec: number;
    goodstype: number;
    goodsremark: string;
    mall_goods_id: number;
    flash_sale_id: string;
    originalprice: number;
    clienttype: string;
    spare_sku_name: string;
    spare_sku_id: string;
    quickbuy: number;
    secret_key: string;
    cid: string;
    checked: boolean;
  }) {
    return await request.post('/api/cart/add', params);
  }

  
  @catchError()
  static async batchAddCart(params: {
    goodsname: string;
    goodsprice: number;
    sendprice: number;
    goodsnum: number;
    goodsurl: string;
    goodsimg: string;
    goodsseller: string;
    sellerurl: string;
    goodssite: string;
    sku_id: string;
    skuname: string;
    goodsweight: number;
    goodsvolume: number;
    isspec: number;
    goodstype: number;
    goodsremark: string;
    mall_goods_id: number;
    flash_sale_id: string;
    originalprice: number;
    clienttype: string;
    spare_sku_name: string;
    spare_sku_id: string;
    quickbuy: number;
    secret_key: string;
    cid: string;
    checked: boolean;
  }) {
    return await request.post('/api/cart/batchAdd', params);
  }


  @catchError()
  static async deleteCart(params: { cart_ids: number[] }) {
    return await request.post('/api/cart/delete', params);
  }

  @catchError()
  static async clearCart() {
    return await request.post('/api/cart/clear');
  }

  @catchError()
  static async carttoorder(params: any) {
    return await request.post('/api/cart/carttoorder', params);
  }

  @catchError()
  static async countmoney(params: any) {
    return await request.post('/api/cart/countmoney', params);
  }


  @catchError()
  static async updateCartQuantity(params: {
    cart_id: number;
    goodsnum: number;
    goodsremark: string;
  }) {
    return await request.post('/api/cart/edit', params);
  }

  /**
   * 支付相关接口
  */

  @catchError()
  static async getPaymentTypeList() {
    return await request.get(`/api/paywallet/typeLists`);
  }

  @catchError()
  static async getPaymentList(tradesn: string) {//交易单号
    return await request.post(`/api/paywallet/getPaymentList`, { tradesn });
  }

  @catchError()
  static async getInvoice(params: { ids: number[], type: string }) {
    return await request.post(`/api/paywallet/getInvoice`, params);
  }

  @catchError()
  static async recharge(params: { money: number, currency: string }) {
    return await request.post(`/api/balance/recharge`, params);
  }

  @catchError()
  static async checkPay(tradesn: string) {//交易单号
    return await request.post(`/api/paywallet/checkPay`, { tradesn });
  }

  @catchError()
  static async payBalance(params: { tradesn: string, coupon_id: number }) {
    return await request.post(`/api/paywallet/payBalance`, params);
  }

  /**
   * 支付密码相关接口 - TP5
   */
  @catchError()
  static async createPayPassword(params: { password: string }) {
      return await request.post('/api/plugin/paypassword/create', params);
  }

  @catchError()
  static async changePayPassword(params: { password: string; captcha?: string }) {
      return await request.post('/api/plugin/paypassword/change', params);
  }

  @catchError()
  static async validatePayPassword(params: { password: string }) {
      return await request.post('/api/plugin/paypassword/validate', params);
  }

  @catchError()
  static async hasPayPassword() {
      return await request.post('/api/plugin/paypassword/has_password');
  }

  @catchError()
  static async checkAddonStatus(name: string) {
      return await request.post('/api/addon/checkStatus', { name });
  }

  //仓库接口（旧版本，已废弃）
  @catchError()
  static async getWarehouseListOld(goods_name: string, page: number = 1, pageSize: number = 10) {
    return await request.post('/api/mywarehouse/lists', {
      goods_name,
      page,
      size: pageSize
    });
  }

  @catchError()
  static async toPackage(params: {
    template_id: number;      // 运费模板ID (必需)
    address_id: number;       // 收货地址ID (必需)
    order_goods_ids: number[]; // 订单商品ID组 (必需)
    server: {
      pack?: {
        [key: string]: number[]; // 打包服务与对应商品ID
      };
      photo?: {
        [key: string]: {
          num: number;        // 照片数量
          remark: string;     // 照片备注 (JSON字符串)
        };
      };
      insurance?: number[];   // 保险服务对应商品ID
      [key: string]: any;     // 其他可能的服务
    };                        // 附加服务对象 (必需)
  }) {
    return await request.post(`/api/order/toPackage`, params);
  }



  @catchError()
  static async getEstimates(params: {
    area_id: number;
    weight: number;
    volume: number;
    goodstype: number | number[];
    stotalmoney: number;
    clienttype: string;
  }) {
    return await request.post(`/api/other/estimates`, params)
  }

  /**
   * 运单管理相关接口
  */
  @catchError()
  static async getSendOrderStatusList() {
    return await request.get(`/api/sendorder/statusLists`)
  }

  @catchError()
  static async getSendOrderList(params: { 
    status_id: number, 
    keywords: string,
    page?: number,
    size?: number 
  }) {
    return await request.post(`/api/sendorder/lists`, params)
  }

  @catchError()
  static async OrderReceipt(params: { sendorder_id: number }) {
    return await request.post(`/api/sendorder/receipt`, params)
  }

  @catchError()
  static async sendOrderReply(params: {
    sendorder_id: number;
  }) {
    return await request.post(`/api/sendorder/sendorderReply`, params)
  }

  @catchError()
  static async createOrderReply(params: {
    order_goods_id: number;
    content: string;
  }) {
    return await request.post(`/api/order/addReply`, params)
  }

  @catchError()
  static async OrderMessage(order_goods_id:number) {
    return await request.post(`/api/order/orderReply`, { order_goods_id })
  }

  @catchError()
  static async cancelSendOrder(params: { sendorder_id: number }) {
    return await request.post(`/api/sendorder/cancel`, params)
  }

  @catchError()
  static async sendEmailCode(params: {
    email: string;
    event: 'register' | 'changeemail' | 'changepwd' | 'resetpwd' | 'withdraw' | 'resetpaypassword' | 'login';
  }) {
    return await request.post('/api/ems/send', params);
  }

  @catchError()
  static async changeEmail(params: {
    email: string;
    captcha: string;
  }) {
    return await request.post('/api/user/changeemail', params);
  }

  @catchError()
  static async verifyEmail(params: {
    email: string;
    captcha: string;
  }) {
    return await request.post('/api/user/verifyemail', params);
  }

  /***公共接口*/
  @catchError()
  static async uploadImg(params: {
    file: Blob;
  }) {
    const formData = new FormData();
    formData.append('file', params.file);
    return await request.post('/api/common/upload', formData);
  }

  /**
   * 邀请相关接口
  */

  @catchError()//发送邀请邮件
  static async emailAdd(params: {
    email: string;
  }) {
    return await request.post('/api/invitefriends/emailAdd', params);
  }

  @catchError()//重新发送邀请邮件
  static async sendInvitefriendsEmail(params: {
    email: string;
  }) {
    return await request.post('/api/invitefriends/sendInvitefriendsEmail', params);
  }

  @catchError()//邀请记录列表
  static async getReferralList(params: {
    size: number;
    status: 0 | 1 | 2;//0:邀请人未注册;1:邀请人已注册未激活;2:邀请人已注册已激活
  }) {
    return await request.post('/api/invitefriends/lists', params);
  }

  @catchError()//邀请列表
  static async getInviteList() {
    return await request.post('/api/invitefriends/inviteList');
  }

  @catchError()//返佣记录列表
  static async getCashbackList() {
    return await request.post('/web/promotion/cashback/list');
  }

  @catchError()//返佣记录总额度
  static async getCashbackTotal() {
    return await request.post('/web/promotion/cashback/total');
  }

  @catchError()//积分记录列表
  static async getScoreList(params?: {
    size: number;
    page?: number;
    type?: -1 | -2 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;
  }) {
    return await request.post('/web/member/score/lists', params);
  }

  @catchError()//经验记录列表
  static async getExperienceList() {
    return await request.post('/api/invitefriends/experienceList');
  }

  /**
   * 消息相关接口
  */

  @catchError()
  static async getMessageList({ size, is_read }: {
    size: number, is_read?: 1
    | 0
  }) {
    return await request.post('/api/message/lists', { size, is_read });
  }

  @catchError()
  static async markMessageRead({ message_id }: { message_id: number }) {
    return await request.post('/api/message/read', { message_id });
  }

  /**
   * 首页导航
   **/

  @catchError()
  static async getNavList(params: { type: 'top' | 'foot' | 'side' }) {
    return await request.post('/api/nav/lists', params);
  }

  @catchError()
  static async getHotRecommend(size: number) {
    return await request.post('/api/hotrecommend/search', { size });
  }

  @catchError()
  static async getHotProductskeywords(size: number = 10) {
    return await request.post('/api/hotrecommend/keywords', { size });
  }
  
  @catchError()
  static async getHotProducts(size: number = 10) {
    return await request.post('/api/hotrecommend/goods_list', { size });
  }

  @catchError()
  static async getLanguageList() {
    return await request.get('/api/language/lists');
  }


  @catchError()
  static async getCurrencyList() {
    return await request.get('/api/currency/lists');
  }

  @catchError()
  static async getOrderReply(order_goods_id: number) {
    return await request.post(`/api/order/orderReply`, { order_goods_id });
  }

  @catchError()
  static async claimSharePromotion(params: { uc?: string; n?: string }) {
    // Manually construct the query string to avoid type issues with the request helper
    const filteredParams: Record<string, string> = {};
    if (params.uc) filteredParams.uc = params.uc;
    if (params.n) filteredParams.n = params.n;
    const queryString = new URLSearchParams(filteredParams).toString();
    return await request.get(`/api/sharepromotion?${queryString}`);
  }

  // 商城货架相关接口
  @catchError()
  static async getMallFloor(params?: {
    size?: string;
  }) {
    return await request.post('/api/obmall/mall_floor', params);
  }

  @catchError()
  static async getMallProductList(params: {
    size: string;
    sort: string;
    category_id?: string;
    page?: number;
  }) {
    return await request.post('/api/obmall/mall_product_lists', params);
  }

  @catchError()
  static async withdrawCommission(params: {
    email: string;
    captcha: string;
    money: number;
  }) {
    return await request.post('/web/plugin/invitefriendspro/to_withdraw', params);
  }

  /**
   * 猜你喜欢相关接口
   */
  @catchError()
  static async getGuessGoodsList(params?: { size?: number }) {
    return await request.post('/web/plugin/guessgoods/list', params || {});
  }

  /**
   * AI比价相关接口
   */
  @catchError()
  static async getPriceParityPlatforms() {
    return await request.post('/web/plugin/priceparity/platforms');
  }

  @catchError()
  static async getPriceParityList(params: {
    keyword?: string;
    api_type?: string;
    page?: number;
    page_size?: number;
  }) {
    // 创建FormData对象以支持multipart/form-data格式
    const formData = new FormData();
    if (params.keyword) formData.append('keyword', params.keyword);
    if (params.api_type) formData.append('api_type', params.api_type);
    if (params.page) formData.append('page', params.page.toString());
    if (params.page_size) formData.append('page_size', params.page_size.toString());

    return await request.post('/web/plugin/priceparity/list', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      } as any,
    });
  }

}

class Api6 {
  /**
 * 用户登录
 * @param email 邮箱
 * @param password 密码 , 需要 md5 加密
 * @returns 登录结果
 */
  @catchError()
  static async login(params: LoginParams) {
    const data = await request.post<ResponseData<UserInfo>>('/api/v1.0/web/member/auth/login', params);
    return data;
  }

  @catchError()
  static async logout() {
    return await request.post('/api/v1.0/web/member/auth/logout');
  }

  @catchError()
  static async register(params: RegisterParams) {
    return await request.post('/api/v1.0/web/member/auth/register', params);
  }

  @catchError()
  static async resetPassword(params: {
    email: string;
    password: string;
    captcha: string;
  }) {
    return await request.post('/api/v1.0/web/member/user/forgot_password', params);
  }

  @catchError()
  static async getUserInfo() {
    return await request.post('/api/v1.0/web/member/user/info');
  }
  // 第三方登录
  @catchError()
  static async facebookLogin(params : any) {
    return await request.post('/api/v1.0/web/member/auth/facebook_login',params);
  }
  
  @catchError()
  static async googleLogin(params : any) {
    return await request.post('/api/v1.0/web/member/auth/google_login',params);
  }
  @catchError()
  static async editUserInfo(params: {
    custom_field?: string[];
    nickname: string;
    avatar: string;
    bio?: string;
    gender: number;
    birthday: string;
  }) {
    return await request.post('/api/v1.0/web/member/user/modify_profile', params);
  }

  @catchError()
  static async changeMobile(params: {
    mobile: string;
    mobilecode: string;
    captcha: string;
  }) {
    return await request.post('/api/v1.0/web/member/user/change_mobile', params);
  }

  @catchError()
  static async sendMobileCode(params: {
    mobile: string;
    mobilecode: string;
    event: 'register' | 'changemobile' | 'changepwd' | 'resetpwd' | 'resetpaypassword';
  }) {
    return await request.post('/api/v1.0/web/verify/code/mobile', params);
  }
  // 获取区号列表
  @catchError()
  static async getConfigList() { 
    return await request.post('/api/v1.0/other/common/config/configList');
  }

  @catchError()
  static async changePwd(params: {
    oldpassword: string;
    newpassword: string;
    renew_password: string;
  }) {
    return await request.post('/api/v1.0/web/member/user/change_password', params);
  }

  // 上传图片
  @catchError()
  static async uploadImage(file: File | FormData) {
   const formData = file instanceof FormData ? file : new FormData();
    if (file instanceof File) {
      formData.append('file', file);  
    }
    return await request.post('/api/v1.0/other/common/attachment/upload', formData,
       {
      headers: {
        'Content-Type': 'multipart/form-data', // 确保正确设置 Content-Type
      } as any,
    }
  );
  }

  @catchError()
  static async getItemList(params: any) {
    return await request.post('/api/v1.0/web/goods/ob/list', params);
  }

  @catchError()
  static async getItemDetail(params: any) {
    return await request.post('/api/v1.0/web/goods/ob/detail', params);
  }


  @catchError()
  static async getGoodsType() {
    return await request.post('/api/v1.0/web/goods/tool/lists_goods_type');
  }

  @catchError()
  static async getFeedbackList(params: any) {
    return await request.post('/api/v1.0/web/cms/feedback/list', params);
  }

  @catchError()
  static async addFeedback(params: { content: string; type?: number; imgs?: string }) {
    return await request.post('/api/v1.0/web/cms/feedback/create', params);
  }

  @catchError()
  static async getBankList() { 
    return await request.post('/api/v1.0/web/member/withdraw/bank_lists');
  }

  @catchError()
  static async addCash(params: {
    transfer_account: string;
    transfer_card: string;
    transfer_bank_selected: string;
    transfer_amount: number;
    captcha?: string;
  }) {
    return await request.post('/api/v1.0/web/member/account/withdraw_apply', params);
  }

  @catchError()
  static async getCashList() { 
    return await request.post('/api/v1.0/web/member/withdraw/withdraw_lists');
  }
  
  @catchError()
  static async getPointList(params:{page:number,type?:string}) {
    return await request.post('/api/v1.0/web/member/score/lists',params);
  }

  @catchError()//积分记录列表
  static async getScoreList(params?: {
    size: number;
    page?: number;
    type?: -1 | -2 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;
  }) {
    return await request.post('/api/v1.0/web/member/score/lists', params);
  }

  @catchError()
  static async exchangeCoupon(params: {
    coupon_id: number;
    num: number;
  }) {
    return await request.post('/api/v1.0/web/order/coupon/exchange_scores', params);
  }
  
  // 商品浏览历史
  @catchError()
  static async addHistoryRecord(params: GoodsHistory) {
    return await request.post('/api/v1.0/web/member/history/add',params);
  }
  @catchError()
  static async clearHistoryRecord() {
    return await request.post('/api/v1.0/web/member/history/clear');
  }

  //收藏功能接口
  @catchError()
  static async addFavorite(params: any) {
      return await request.post('/api/v1.0/web/goods/favorite/favoriteAdd', params);
  }

  @catchError()
  static async isFavorite(goodsurl: string) {
    // TP5模式使用旧的API端点，TP6模式使用新的API端点
    const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5';
    const endpoint = isTp5 ? '/api/favorite/checkFavorite' : '/api/v1.0/web/goods/favorite/checkFavorite';
    return await request.post(endpoint, { goodsurl });
  }

  @catchError()
  static async deleteFavorite(id: number) {
    return await request.post('/api/v1.0/web/goods/favorite/favoriteDel', { id });
  }

  @catchError()
  static async clearFavorite() {
    return await request.post('/api/v1.0/web/goods/favorite/clearFavorite',{clear:true});
  }
  // 优惠券相关接口
  @catchError()
  static async getCouponList(params?: { page?: number; size?: number; status?: number }) {
    return await request.post('/api/v1.0/web/order/coupon/page_user', params || {});
  }

  @catchError()
  static async couponActivate(params: { code: string }) {
    return await request.post('/api/v1.0/web/order/coupon/activate', params);
  }

  @catchError()
  static async getOrderList(params: any) {
    return await request.post('/api/v1.0/web/order/order/page', params);
  }
  
  @catchError()
  static async cancelOrder(params: { order_id: number }) {
    return await request.post('/api/v1.0/web/order/order/cancel', params);
  }

  @catchError()
  static async getOrderDetail({ order_id }: { order_id: number }) {
    return await request.post(`/api/v1.0/web/order/order/detail`, { order_id });
  }

  @catchError()
  static async getOrderStatusList() {
    return await request.post(`/api/v1.0/web/order/order/lists_status`);
  }

  @catchError()
  static async getWalletList({ action, starttime, endtime, page = 1, size = 10 }: { action: string; starttime: string; endtime: string; page?: number; size?: number }) {
    return await request.post(`/api/v1.0/web/member/account/statement`, { type:action, starttime, endtime, page, size }); // todo:
  }

  /**
   * 地址相关接口
  */

  @catchError()
  static async getAddressList(params: any = {}) {
      return await request.post<ResponseData<AddressItem[]>>('/api/v1.0/web/member/address/lists_address', params);
  }

  @catchError()
  static async setDefaultAddress(id: number) {
    return await request.post('/api/v1.0/web/member/address/set_default', { id });
  }

  @catchError()
  static async deleteAddress(address_id: number) {
    return await request.post('/api/v1.0/web/member/address/delete_address', { address_id });
  }

  @catchError()
  static async addAddress(params: AddressParams) {
    return await request.post('/api/v1.0/web/member/address/add_address', params);
  }

  @catchError()
  static async updateAddress(params: any) {
    return await request.post('/api/v1.0/web/member/address/edit_address', params);
  }

  @catchError()
  static async getAreaList(params?: { country?: number }) {
    const p = params?.country ? { country: params.country } : {};
    return await request.post(`/api/v1.0/web/member/address/area_list`,p);
  }

  /**
   * 购物车相关接口
  */

  @catchError()
  static async getServerList(type: 'goods' | 'sendorder') {
    return await request.post(`/api/v1.0/web/order/order/additional_services`,{type});
  }

  // @catchError()
  // static async getCartNums() {
  //   return await request.post('/api/cart/cartNums');
  // }

  @catchError()
  static async getCartList() {
    return await request.post('/api/v1.0/web/goods/cart/list');
  }


  @catchError()
  static async addCart(params: {
    goodsname: string;
    goodsprice: number;
    sendprice: number;
    goodsnum: number;
    goodsurl: string;
    goodsimg: string;
    goodsseller: string;
    sellerurl: string;
    goodssite: string;
    sku_id: string;
    skuname: string;
    goodsweight: number;
    goodsvolume: number;
    isspec: number;
    goodstype: number;
    goodsremark: string;
    mall_goods_id: number;
    flash_sale_id: string;
    originalprice: number;
    clienttype: string;
    spare_sku_name: string;
    spare_sku_id: string;
    quickbuy: number;
    secret_key: string;
    cid: string;
    checked: boolean;
  }) {
    return await request.post('/api/v1.0/web/goods/cart/Add', params);
  }

  
  @catchError()
  static async batchAddCart(params: {
    goodsname: string;
    goodsprice: number;
    sendprice: number;
    goodsnum: number;
    goodsurl: string;
    goodsimg: string;
    goodsseller: string;
    sellerurl: string;
    goodssite: string;
    sku_id: string;
    skuname: string;
    goodsweight: number;
    goodsvolume: number;
    isspec: number;
    goodstype: number;
    goodsremark: string;
    mall_goods_id: number;
    flash_sale_id: string;
    originalprice: number;
    clienttype: string;
    spare_sku_name: string;
    spare_sku_id: string;
    quickbuy: number;
    secret_key: string;
    cid: string;
    checked: boolean;
  }) {
    return await request.post('/api/v1.0/web/goods/cart/batchAdd', params);
  }


  @catchError()
  static async deleteCart(params: { cart_ids: number[] | string }) {
    return await request.post('/api/v1.0/web/goods/cart/Delete', params);
  }

  @catchError()
  static async clearCart() {
    return await request.post('/api/v1.0/web/goods/cart/clear');
  }

  @catchError()
  static async carttoorder(params: any) {
    return await request.post('/api/v1.0/web/order/order/add', params);
  }

  // @catchError()
  // static async countmoney(params: any) {
  //   return await request.post('/api/cart/countmoney', params);
  // }


  @catchError()
  static async updateCartQuantity(params: {
    cart_id: number;
    goodsnum: number;
    goodsremark: string;
  }) {
      return await request.post('/api/v1.0/web/goods/cart/edit', params);
  }

  /**
   * 支付相关接口
  */

  @catchError()
  static async getPaymentTypeList() {
    return await request.get(`/api/v1.0/web/order/pay/lists_type`);
  }

  @catchError()
  static async getPaymentList(tradesn: string) {//交易单号
    return await request.post(`/api/v1.0/web/order/pay/lists_payment`, { tradesn });
  }

  @catchError()
  static async getInvoice(params: { ids: number[], type: string }) {
    return await request.post(`/api/v1.0/web/order/pay/invoice`, params);
  }

  @catchError()
  static async recharge(params: { money: number, currency: string }) {
    return await request.post(`/api/v1.0/web/member/account/recharge_key`, params);
  }
  @catchError()
  static async checkPay(tradesn: string) {//交易单号
    return await request.post(`/api/v1.0/web/order/pay/check`, { tradesn });
  }
  @catchError()
  static async getGoodList(tradesn: string) {//订单商品列表
    return await request.post(`/api/v1.0/web/order/order/goods_list`, { tradesn });
  }

  @catchError()
  static async getGoodServiceImg(order_goods_id: string) {// 订单服务图片列表
    return await request.post(`/api/v1.0/web/order/order/lists_photo`, { order_goods_id });
  }

  @catchError()
  static async payBalance(params: { tradesn: string, coupon_id: number }) {
    return await request.post(`/api/v1.0/web/order/pay/balance_pay`, params);
  }

  @catchError()
  static async getOfflineAreaList() { // 线下支付地区列表
    return await request.post(`/api/v1.0/web/order/pay/lists_offline_area`);
  }
  @catchError()
  static async getOfflinePayList(params: { area_id: number }) { // 线下支付列表（对应地区的）
    return await request.post(`/api/v1.0/web/order/pay/page_offline`, params);
  }

  @catchError()
  static async getOfflineDetail(params: { tradesn: string, payid: number }) { // 线下支付信息（收款人信息
    return await request.post(`/api/v1.0/web/order/pay/detail_offline`, params);
  }
  

  @catchError()
  static async getOfflineRecord(params: { starttime: string, endtime:string }) { // 线下支付记录
    return await request.post(`/api/v1.0/web/order/pay/page_offline_record`, params);
  }
  @catchError()
  static async addRemit( // 添加转账汇款
    params: { 
      accountname: string,
      bankname: string,
      txn: string, 
      pic: string, 
      currency: string, 
      money: number, 
      paytype: string,
      tradesn: string, 
      secretkey: string, 
      }) {
    return await request.post(`/api/v1.0/web/order/pay/add_remit`, params);
  }

  //仓库接口
  @catchError()
  static async getWarehouseList(goods_name: string, page: number = 1, pageSize: number = 10) {
    return await request.post('/api/v1.0/web/order/warehouse_order/list', {
      goods_name,
      page,
      size: pageSize
    });
  }

  @catchError()
  static async toPackage(params: {
    template_id: number;      // 运费模板ID (必需)
    address_id: number;       // 收货地址ID (必需)
    order_goods_ids: number[] | string; // 订单商品ID组 (必需)
    server: {
      pack?: {
        [key: string]: number[]; // 打包服务与对应商品ID
      };
      photo?: {
        [key: string]: {
          num: number;        // 照片数量
          remark: string;     // 照片备注 (JSON字符串)
        };
      };
      insurance?: number[];   // 保险服务对应商品ID
      [key: string]: any;     // 其他可能的服务
    };                        // 附加服务对象 (必需)
  }) {
    return await request.post(`/api/v1.0/web/order/sendorder/create`, params);
  }



  @catchError()
  static async getEstimates(params: {
    area_id: number;
    weight: number;
    volume: number;
    goodstype: number | number[];
    stotalmoney: number;
    clienttype: string;
  }) {
    return await request.post(`/api/v1.0/web/goods/tool/estimates`, params)
  }

  /**
   * 运单管理相关接口
  */
  @catchError()
  static async getSendOrderStatusList() {
    return await request.post(`/api/v1.0/web/order/sendorder/status_list`)
  }

  @catchError()
  static async getSendOrderList(params: {
    sendorder_state?: number,
    status_id?: number,
    keywords: string,
    page?: number,
    size?: number
  }) {
    return await request.post(`/api/v1.0/web/order/sendorder/list`, params)
  }

  @catchError()
  static async OrderReceipt(params: { sendorder_id: number }) {
    return await request.post(`/api/v1.0/web/order/sendorder/receipt`, params)
  }

  @catchError()
  static async sendOrderReply(params: {
    sendorder_id: number;
    content: string;
  }) {
    return await request.post(`/api/v1.0/web/order/sendorder/reply_create`, params);
  }


  @catchError()
  static async createOrderReply(params: {
    order_goods_id: number;
    content: string;
  }) {
    return await request.post(`/api/v1.0/web/order/reply/add_order`, params);
  }
  
  @catchError()
  static async OrderMessage(order_goods_id:number) {
    return await request.post(`/api/v1.0/web/order/reply/lists_order`, { order_goods_id })
  }
 
  @catchError()
  static async cancelSendOrder(params: { sendorder_id: number }) {
    return await request.post(`/api/v1.0/web/order/sendorder/cancle`, params)
  }

  @catchError()
  static async sendEmailCode(params: {
    email: string;
    event: 'register' | 'changeemail' | 'changepwd' | 'resetpwd' | 'withdraw' | 'resetpaypassword' | 'login';
  }) {
    return await request.post('/api/v1.0/other/common/code/email', params);
  }

  @catchError()
  static async changeEmail(params: {
    email: string;
    captcha: string;
  }) {
    return await request.post('/api/v1.0/web/member/user/change_email', params);
  }

  @catchError()
  static async verifyEmail(params: { 
    email: string;
    captcha: string;
  }) {
    return await request.post('/api/v1.0/web/user/verifyemail', params);
  }

  /***公共接口*/
  // @catchError()
  // static async uploadImg(params: { 
  //   file: Blob;
  // }) {
  //   const formData = new FormData();
  //   formData.append('file', params.file);
  //   return await request.post('/api/common/upload', formData);
  // }

  /**
   * 邀请相关接口
  */
  @catchError() //获取邀请链接
  static async getInviteLink() {
    return await request.post('/api/v1.0/web/promotion/invite/link');
  }
  @catchError() //发送邀请邮件
  static async emailAdd(params: {
    email: string;
  }) {
    return await request.post('/api/v1.0/web/promotion/invite/email', params);
  }

  @catchError() //重新发送邀请邮件
  static async sendInvitefriendsEmail(params: {
    email: string;
  }) {
    return await request.post('/api/v1.0/web/promotion/invite/resend_email', params);
  }

  @catchError() //邀请记录列表
  static async getReferralList(params: {
    size: number;
    page?: number;
    status?: 0 | 1 | 2;//0:邀请人未注册;1:邀请人已注册未激活;2:邀请人已注册已激活
    start_date?: string;
    end_date?: string;
  }) {
    return await request.post('/api/v1.0/web/promotion/invite/list', params);
  }



  @catchError()//返佣记录列表
  static async getCashbackList() {
    return await request.post('/api/v1.0/web/promotion/cashback/list');
  }

  @catchError()//返佣记录总额度
  static async getCashbackTotal() {
    return await request.post('/api/v1.0/web/promotion/cashback/total');
  }

  @catchError()//推广详情
  static async getPromotionDetail() {
    return await request.post('/api/v1.0/web/plugin/invitefriendspro/detail_promotion');
  }

  @catchError()//经验列表
  static async getExperienceList(params?: {
    page?: number;
    size?: number;
    start_time?: string;
    end_time?: string;
  }) {
    return await request.post('/api/v1.0/web/plugin/invitefriendspro/page_experience', params);
  }

  @catchError()//邀请详情
  static async getInviteDetail(params?: { page?: number; size?: number }) {
    return await request.post('/api/v1.0/web/plugin/invitefriendspro/detail_invite', params);
  }

  @catchError()//活跃用户列表
  static async getActiveUserList(params?: { page?: number; size?: number }) {
    return await request.post('/api/v1.0/web/plugin/invitefriendspro/page_active_user', params);
  }

  @catchError()//包裹奖金详情
  static async getAmountDetail() {
    return await request.post('/api/v1.0/web/plugin/invitefriendspro/detail_amount');
  }

  @catchError()//佣金变动记录列表
  static async getAmountList(params?: {
    page?: number;
    size?: number;
    type?: number;
    start_time?: string;
    end_time?: string;
    ways?: number;
  }) {
    return await request.post('/api/v1.0/web/plugin/invitefriendspro/page_amount', params);
  }

  @catchError()//推广规则列表
  static async getPromotionRules() {
    return await request.post('/api/v1.0/web/plugin/invitefriendspro/lists_rule');
  }

  @catchError()//佣金类型列表
  static async getAmountTypeList() {
    return await request.post('/api/v1.0/web/plugin/invitefriendspro/lists_ways_amount');
  }

  @catchError()//积分类型列表
  static async getScoreTypeList() {
    return await request.post('/api/v1.0/web/member/score/types');
  }

  @catchError()//冻结类型列表
  static async getFrozenTypeList() {
    return await request.post('/api/v1.0/web/plugin/invitefriendspro/lists_ways_freeze');
  }

  @catchError()//冻结金额变动详情
  static async getFrozenAmountList(params?: {
    page?: number;
    size?: number;
    type?: number;
    start_time?: string;
    end_time?: string;
    ways?: number;
  }) {
    return await request.post('/api/v1.0/web/plugin/invitefriendspro/page_freeze', params);
  }

  @catchError()//结算记录列表
  static async getSettlementList(params?: {
    page?: number;
    size?: number;
    start_time?: string;
    end_time?: string;
  }) {
    return await request.post('/api/v1.0/web/plugin/invitefriendspro/page_settlement', params);
  }

  // @catchError()//积分记录列表 
  // static async getScoreList() {
  //   return await request.post('/api/invitefriends/scoreList');
  // }

  // @catchError()//经验记录列表 
  // static async getExperienceList() {
  //   return await request.post('/api/invitefriends/experienceList');
  // }
  /**
   * 信息管理
   */
  @catchError()
  static async getInformationDetail(information_id: number) {
    return await request.post(`/api/v1.0/web/cms/information/detail`,{information_id});
  }

  /**
   * 文章详情 - 用于协议和服务页面
   */
  @catchError()
  static async getArticleDetail(id: number) {
    return await request.post(`/api/v1.0/web/cms/article/detail`, { id });
  }

  /**
   * 帮助中心相关接口
   */
  @catchError()
  static async getHelpCategories(params: {
    langcode: string;
    category_id?: string[];
    size?: string;
    keyword?: string;
  }) {
    return await request.post('/api/v1.0/web/cms/article/category', params);
  }

  @catchError()
  static async getHelpArticles(params: {
    keyword?: string;
    category_id: string;
    langcode: string;
    size: number;
  }) {
    return await request.post('/api/v1.0/web/cms/article/list', params);
  }

  @catchError()
  static async getHelpArticleDetail(params: {
    id: number;
  }) {
    return await request.post('/api/v1.0/web/cms/article/detail', params);
  }

  /**
   * 消息相关接口
  */

  @catchError()
  static async getMessageList({ size, is_read }: {
    size: number, is_read?: 1
    | 0
  }) {
    return await request.post('/api/v1.0/web/member/message/lists_message', { size, is_read });
  }

  @catchError()
  static async markMessageRead({ message_id }: { message_id: number }) {
    return await request.post('/api/v1.0/web/member/message/read_message', { message_id });
  }

  /**
   * 仓库-创建转运订单
  */

  @catchError()
  static async createTransshipmentOrder(params:any) {
    return await request.post('/api/v1.0/web/order/buyself/add', params);
  }
  /**
   * 首页导航
   **/

  // @catchError()
  // static async getNavList(params: { type: 'top' | 'foot' | 'side' }) { 
  //   return await request.post('/api/nav/lists', params);
  // }

  @catchError()
  static async getHotRecommend(size: number) {
    return await request.post('/api/v1.0/web/goods/hot/searchHot', { size });
  }

  @catchError()
  static async getHotProductskeywords(size: number = 10) {
    return await request.post('/api/v1.0/web/goods/hot/keywords', { size });
  }
  
  @catchError()
  static async getHotProducts(size: number = 10) {
    return await request.post('/api/v1.0/web/goods/hot/goodsList', { size });
  }

  @catchError()
  static async getGoodsRecommendList(params?: { keyword?: string; size?: number }) {
    return await request.post('/api/v1.0/web/plugin/recommend/goodsRecommendList', params || {});
  }

  @catchError()
  static async getShoppingTimeList(size: number = 10) {
      return await request.post('/api/v1.0/web/plugin/shoppingtime/list', { size });
  }

  @catchError()
  static async checkAddonStatus(name: string) {
      return await request.post('/api/v1.0/other/common/addon/check_status', { name });
  }

  /**
   * 支付密码相关接口
   */
  @catchError()
  static async createPayPassword(params: { password: string }) {
      return await request.post('/api/v1.0/web/plugin/paypassword/create', params);
  }

  @catchError()
  static async changePayPassword(params: { password: string; captcha?: string }) {

      return await request.post('/api/v1.0/web/plugin/paypassword/change', params);

  }

  @catchError()
  static async validatePayPassword(params: { password: string }) {
    
      return await request.post('/api/v1.0/web/plugin/paypassword/validate', params);
    
  }

  @catchError()
  static async hasPayPassword() {
   
      return await request.post('/api/v1.0/web/plugin/paypassword/has_password');
    
  }

  // @catchError()
  // static async getLanguageList() { 
  //   return await request.get('/api/language/lists');
  // }


  // @catchError()
  // static async getCurrencyList() {
  //   return await request.get('/api/currency/lists');
  // }

   @catchError()
  static async getOrderReply(order_goods_id: number) {
    return await request.post(`/api/v1.0/web/order/reply/lists_order`,{order_goods_id});
  }
  @catchError()
  static async getAdList(params:any) {
    return await request.post(`/api/v1.0/web/cms/ads/ads_list`, params);
  }
  // 签到
  @catchError()
  static async setSignin() {
    return await request.post(`/api/v1.0/web/member/signin/add`);
  }
  // 签到状态检查
  @catchError()
  static async getSigninCheck() {
    return await request.post(`/api/v1.0/web/member/signin/check`);
  }
  // 签到列表
   @catchError()
  static async getSigninList(params: any) {
    return await request.post(`/api/v1.0/web/member/signin/lists`,params);
  }
  // 签到排名
   @catchError()
  static async getSigninRank(params: any) {
    return await request.post(`/api/v1.0/web/member/signin/rank`,params);
  }
  // 补签
   @catchError()
  static async appendSignin(params: any) {
    return await request.post(`/api/v1.0/web/member/signin/append`, params);
  }
   @catchError()
  static async sendorderReplylist(sendorder_id: number) {
    return await request.post(`/api/v1.0/web/order/sendorder/reply_list`,{sendorder_id});
  }
   @catchError()
  static async newsList() {
    return await request.post(`/api/v1.0/web/cms/news/list`);
  }

  /**
   * 包裹评价相关接口
   */
  @catchError()
  static async getCommentList(params: { size: string }) {
    return await request.post('/api/v1.0/web/plugin/comment/lists', params);
  }

  @catchError()
  static async addComment(params: {
    sendorder_id: string;
    pid: string;
    content: string;
    starlevel: string;
    img?: string;
  }) {
    return await request.post('/api/v1.0/web/plugin/comment/add', params);
  }

  @catchError()
  static async replyComment(params: {
    sendorder_id: string;
    pid: string | number;
    content: string;
  }) {
    return await request.post('/api/v1.0/web/plugin/comment/reply', params);
  }
   @catchError()
  static async sendorderPhoto(sendorder_id: number) {
    return await request.post(`/api/v1.0/web/order/sendorder/photo_list`,{sendorder_id});
  }
   @catchError()
  static async change_shipping(params: {
    template_id:number,
    order_goods_ids: Array<number>
  }) {
    return await request.post(`/api/v1.0/web/plugin/packagepreview/change_shipping`,{...params});
  }

  // 普通运单运费计算接口（区别于预演包裹）
  @catchError()
  static async changeSendOrderShipping(params: {
    template_id:number,
    order_goods_ids: Array<number>
  }) {
    return await request.post(`/api/v1.0/web/order/order/change_shipping`,{...params});
  }
  //1688运费接口
  @catchError()
  static async estimate1688(params: {
    iid:number,
    num: number
  }) {
    return await request.post(`/api/v1.0/web/goods/ob/estimate`,{...params});
  }
  
  @catchError()
  static async getPluginList(mer_id?: string) {
      return await request.post('/api/v1.0/web/plugin/merchant/list', mer_id ? { mer_id } : {});
  }


  /**
   * AI比价相关接口
   */
  @catchError()
  static async getPriceParityPlatforms() {
    return await request.post('/api/v1.0/web/plugin/priceparity/api_type');
  }

  @catchError()
  static async getPriceParityList(params: {
    keyword?: string;
    api_type?: string;
    page?: number;
    page_size?: number;
  }) {
    return await request.post('/api/v1.0/web/plugin/priceparity/list', params);
  }

  /**
   * 一次付款相关接口
   */
  @catchError()
  static async createOnePayOrder(params: {
    cart_ids: number[];
    selectedaid: number;  // 地址ID
    did: number;          // 模板ID
    clienttype: string;
    isrefill?: string;
    server?: any;         // 运单服务
    order_serve?: any;    // 订单服务
  }) {
    return await request.post('/api/v1.0/web/plugin/onepayorder/create', params);
  }

  @catchError()
  static async getOnePayOrderList(params?: { keyword?: string; status_id?: number }) {
    return await request.post('/api/v1.0/web/plugin/onepayorder/list', params || {});
  }

  @catchError()
  static async getOnePayOrderStatusList() {
    return await request.post('/api/v1.0/web/plugin/onepayorder/status_list');
  }

  @catchError()
  static async getOnePayOrderDetail(params: { orderssn: string }) {
    return await request.post('/api/v1.0/web/plugin/onepayorder/info', params);
  }

  @catchError()
  static async cancelOnePayOrder(params: { orders_id: number }) {
    return await request.post('/api/v1.0/web/plugin/onepayorder/cancle', params);
  }

  @catchError()
  static async deleteOnePayOrder(params: { orderssn: string }) {
    return await request.post('/api/v1.0/web/plugin/onepayorder/delete', params);
  }

  @catchError()
  static async getOnePayOrderInfo(params: { cart_ids: string[] }) {
    return await request.post('/api/v1.0/web/plugin/onepayorder/index', params);
  }

  @catchError()
  static async confirmOnePayOrderReceipt(params: { orders_id: string }) {
    return await request.post('/api/v1.0/web/plugin/onepayorder/receipt', params);
  }

  // 商城货架相关接口
  @catchError()
  static async getMallFloor(params?: {
    size?: string;
  }) {
    return await request.post('/api/v1.0/web/plugin/obmall/mall_floor', params);
  }

  @catchError()
  static async getMallProductList(params: {
    size: string;
    sort: string;
    category_id?: string;
    page?: number;
    recommend?: string;
  }) {
    return await request.post('/api/v1.0/web/plugin/obmall/mall_product_lists', params);
  }

  @catchError()
  static async getMallDetail(params: {
    tid: number;
  }) {
    return await request.post('/api/v1.0/web/plugin/obmall/mall_detail', params);
  }

  @catchError()
  static async withdrawCommission(params: {
    email: string;
    captcha: string;
    money: number;
  }) {
    return await request.post('/api/v1.0/web/plugin/invitefriendspro/to_withdraw', params);
  }

  /**
   * 猜你喜欢相关接口
   */
  @catchError()
  static async getGuessGoodsList(params?: { size?: number }) {
    return await request.post('/api/v1.0/web/plugin/guessgoods/list', params || {});
  }
  
  @catchError()
  static async toPreviewPackage(params: {
    order_goods_ids: number[];  // 订单商品ID数组 (必需)
    address_id: number;         // 收货地址ID (必需)
    template_id: number;        // 运费模板ID (必需)
    server: string[];           // 附加服务数组 (必需)
  }) {
    return await request.post(`/api/v1.0/web/plugin/packagepreview/to_preview`, params);
  }

  @catchError()
  static async getPreviewPackageConfig() {
    return await request.get(`/api/v1.0/web/plugin/packagepreview/config`);
  }

  @catchError()
  static async getPreviewPackageDetail(sn: string) {
    return await request.post(`/api/v1.0/web/plugin/packagepreview/detail_rehearsal`, { sn });
  }

  @catchError()
  static async getPreviewPackageInvoice(params: {
    sn: string;
  }) {
    return await request.post(`/api/v1.0/web/plugin/packagepreview/invoice`, params);
  }

  @catchError()
  static async getPreviewPackageList(params?: {
    status?: number;    // 预演包裹状态（可选）
    keywords?: string;  // 关键词：运单编号或预演包裹编号（可选）
  }) {
    return await request.post('/api/v1.0/web/plugin/packagepreview/page_rehearsal', params || {});
  }

  @catchError()
  static async getPreviewPackageStatusList() {
    return await request.get('/api/v1.0/web/plugin/packagepreview/lists_status');
  }

  @catchError()
  static async cancelPreviewPackage(params: {
    rehearsal_id: number;
  }) {
    return await request.post('/api/v1.0/web/plugin/packagepreview/cancel', params);
  }

  @catchError()
  static async submitPreviewPackageRefund(params: {
    rehearsal_id: number;
    content: string;
  }) {
    return await request.post('/api/v1.0/web/plugin/packagepreview/refund', params);
  }

  @catchError()
  static async submitFreightPayment(params: {
    id: number;
  }) {
    return await request.post('/api/v1.0/web/plugin/packagepreview/to_freight', params);
  }

  @catchError()
  static async changePreviewPackageShipping(params: {
    template_id: number;
    rehearsal_id: number;
  }) {
    return await request.post('/api/v1.0/web/plugin/packagepreview/change_shipping', params);
  }

  @catchError()
  static async verifyPayPassword(params: {
    password: string;
  }) {
    return await request.post(`/api/v1.0/web/plugin/paypassword/verify`, params);
  }

}

let Api: typeof Api5 | any = Api5;
if (process.env.NEXT_PUBLIC_BACKEND_TYPE === '6') {
  Api = Api6;
}

export { Api };