'use client';

import { useState, useEffect, useRef } from 'react';
import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import type { Locale } from '@/config';
import { getDictionary } from '@/dictionaries';
import {Api} from '@/request/api';
import { md5 } from 'js-md5';
import Button from '@/components/Button'
import Message from '@/components/CustomMessage'
import  BackgroundImg  from '../(component)/BackgroundImg';

export default function ForgotPage({
  params,
}: {
  params: Promise<{ lng: Locale }>;
}) {
  // 在Next.js 14中，需要使用React.use()来解包params
  const resolvedParams = React.use(params);
  const { lng } = resolvedParams;

  return (
    <ClientForgotPage lng={lng} />
  );
}

function ClientForgotPage({ lng }: { lng: Locale }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [dict, setDict] = useState<any>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [config, setConfig] = useState<any>({logo:'',name:''});
  const [step, setStep] = useState(1); // 1: 输入邮箱, 2: 验证验证码, 3: 设置新密码
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
  const [emailCountdown, setEmailCountdown] = useState(0)
  const emailTimerRef = useRef<NodeJS.Timeout | null>(null)
  
  useEffect(()=>{
    const getConfig = async () => {
      try {
          const res =  await Api.getConfigList()
          if (res?.success && res.data) {
            let data =  res.data.site
            setConfig({logo: data.logo , name: data.name})
          }
        } catch (e) {
          console.error(e)
        }
    }
    getConfig()
  },[])
  useEffect(() => {
    async function loadDictionary() {
      try {
        const dictionary = await getDictionary(lng);
        setDict(dictionary);
      } catch (err) {
        console.error('Failed to load dictionary', err);
      }
    }

    loadDictionary();
  }, [lng]);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  useEffect(() => {
    return () => {
      if (emailTimerRef.current) {
        clearInterval(emailTimerRef.current)
      }
    }
  }, [])
  const startCountdown = () => {
    setCountdown(60);
    timerRef.current = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          if (timerRef.current) {
            clearInterval(timerRef.current);
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleSendCode = async () => {
    if (!email) {
      setError(dict?.forgot?.emailRequired || '请输入邮箱');
      return;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setError(dict?.forgot?.invalidEmail || '请输入有效的邮箱地址');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await Api.sendEmailCode({
        email,
        event: 'resetpwd'
      });

      if (response?.success) {
        startCountdown();
        setStep(2);
      } else {
        setError(response?.msg || dict?.forgot?.sendCodeError || '发送验证码失败，请稍后重试');
      }
    } catch (err) {
      console.error('Failed to send verification code', err);
      setError(err instanceof Error ? err.message : dict?.forgot?.sendCodeError || '发送验证码失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyCode = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!verificationCode) {
      setError(dict?.forgot?.codeRequired || '请输入验证码');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await Api.verifyEmail({
        email,
        captcha: verificationCode
      });

      if (response?.success) {
        // 验证码验证成功，进入设置新密码步骤
        setStep(3);
      } else {
        setError(response?.msg || dict?.forgot?.codeError || '验证码错误，请重新输入');
      }
    } catch (err) {
      console.error('Verification code validation failed', err);
      setError(err instanceof Error ? err.message : dict?.forgot?.codeVerifyError || '验证码验证失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleResetPassword = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!password) {
      setError(dict?.forgot?.passwordRequired || '请输入新密码');
      return;
    }
    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[\x21-\x7E]{6,16}$/;
    if (!regex.test(password)) {
      setError(dict?.forgot?.passwordRule || '密码长度为6-16位，必须包含大小写字母和数字');
      return;
    }

    if (password !== confirmPassword) {
      setError(dict?.forgot?.passwordMismatch || '两次输入的密码不一致');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // 使用正确的API参数结构，并对密码进行md5加密
      let data = isTp5? {
        type: 'email',
        email,
        captcha: verificationCode,
        newpassword: md5(password)
      } :{
        email,
        captcha: verificationCode,
        password: md5(password)
      }
      const response = await Api.resetPassword(data);
      if (response.success) {
        // 重置成功，跳转到登录页，保留callback参数
        const callback = searchParams.get('callback');
        const loginUrl = callback ? `/${lng}/login?callback=${encodeURIComponent(callback)}` : `/${lng}/login`;
        router.push(loginUrl);
      } else {
        setError(response?.msg || dict?.forgot?.resetError || '重置密码失败，请稍后重试');
      }
    } catch (err) {
      console.error('Password reset failed', err);
      setError(err instanceof Error ? err.message : dict?.forgot?.resetError || '重置密码失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };
  const startEmailCountdown = () => {
    setEmailCountdown(60)
    emailTimerRef.current = setInterval(() => {
      setEmailCountdown((prev) => {
        if (prev <= 1) {
          if (emailTimerRef.current) {
            clearInterval(emailTimerRef.current)
          }
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }
  const handleSendEmailCode = async () => {
    if (!email) {
      setError(dict?.forgot?.emailRequired || '请输入邮箱');
      return false
    }
    try {
      const res = await Api.sendEmailCode({
        email,
        event: 'resetpwd'
      })
      if (res?.success) {
        Message.success(dict?.forgot?.emailSent || '邮件已发送')
        startEmailCountdown()
      } else {
         setError(res?.msg || dict?.forgot?.emailSendError || '邮件发送失败');
      }
    } catch (e) {
      console.error(e)
    }
  }
  return (
    <div className="min-h-screen flex">
      {/* 左侧图片部分 */}
      <div className="hidden lg:block lg:w-1/2 relative">
        <Link href={`/${lng}`} className="absolute top-12 left-12 text-white z-10 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          {dict?.nav?.backToHome || '返回首页'}
        </Link>
        <div className="absolute inset-0 bg-white">
            <BackgroundImg/>
        </div>
      </div>

      {/* 右侧表单部分 */}
      <div className="w-full lg:w-1/2 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {config?.logo ? (<img src={config?.logo} alt="Logo" className="w-[130px] h-[40px] mx-auto" />):<h1 className="text-4xl font-bold text-[#FF6B00] text-center">onebuy</h1>}
          <div className="text-center">
            <h2 className="mt-6 text-2xl font-bold text-gray-900">{dict?.forgot?.title || '忘记密码'}</h2>
            {
              isTp5 && (<div>
                <p className="mt-2 text-sm text-gray-600">
                {step === 1 
                  ? (dict?.forgot?.enterEmail || '请输入您的邮箱，我们将发送验证码') 
                  : (step === 2 
                    ? (dict?.forgot?.enterCode || '请输入验证码') 
                    : (dict?.forgot?.enterPassword || '请输入新密码'))}
              </p>
              
                {/* 步骤指示器 */}
                <div className="flex justify-center items-center mt-4">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-[#FF6B00] text-white' : 'bg-gray-200 text-gray-500'}`}>1</div>
                  <div className={`h-1 w-8 ${step >= 2 ? 'bg-[#FF6B00]' : 'bg-gray-200'}`}></div>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-[#FF6B00] text-white' : 'bg-gray-200 text-gray-500'}`}>2</div>
                  <div className={`h-1 w-8 ${step >= 3 ? 'bg-[#FF6B00]' : 'bg-gray-200'}`}></div>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 3 ? 'bg-[#FF6B00] text-white' : 'bg-gray-200 text-gray-500'}`}>3</div>
                </div>
              </div>
            )}
          </div>
          {error && (
                <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded">
                  {error}
                </div>
            )}
          {
            isTp5 ? (
              <form className="mt-8 space-y-6" onSubmit={step === 1 ? (e => { e.preventDefault(); handleSendCode(); }) : (step === 2 ? handleVerifyCode : handleResetPassword)}>
    

            <div className="space-y-4">
              {/* 邮箱输入 */}
              <div className={step !== 1 ? 'opacity-70' : ''}>
                <div className="relative">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={step !== 1}
                    className="appearance-none rounded-md relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#FF6B00] focus:border-[#FF6B00] focus:z-10 sm:text-sm"
                    placeholder={dict?.forgot?.emailPlaceholder || '邮箱'}
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* 步骤2: 验证码输入 */}
              {step >= 2 && (
                <div className={step !== 2 ? 'opacity-70' : ''}>
                  <div className="relative">
                    <input
                      id="verification-code"
                      name="verification-code"
                      type="text"
                      required
                      value={verificationCode}
                      onChange={(e) => setVerificationCode(e.target.value)}
                      disabled={step !== 2}
                      className="appearance-none rounded-md relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#FF6B00] focus:border-[#FF6B00] focus:z-10 sm:text-sm"
                      placeholder={dict?.forgot?.codePlaceholder || '验证码'}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v-1l1-1 1-1 .257-.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
              )}

              {/* 步骤3: 新密码输入 */}
              {step >= 3 && (
                <>
                  <div>
                    <div className="relative">
                      <input
                        id="password"
                        name="password"
                        type={showPassword ? "text" : "password"}
                        autoComplete="new-password"
                        required
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="appearance-none rounded-md relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#FF6B00] focus:border-[#FF6B00] focus:z-10 sm:text-sm"
                        placeholder={dict?.forgot?.passwordPlaceholder || '新密码'}
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button 
                          type="button" 
                          onClick={() => setShowPassword(!showPassword)}
                          className="text-gray-400 hover:text-gray-500 focus:outline-none"
                        >
                          {showPassword ? (
                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                              <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                            </svg>
                          ) : (
                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="relative">
                      <input
                        id="confirm-password"
                        name="confirm-password"
                        type={showConfirmPassword ? "text" : "password"}
                        autoComplete="new-password"
                        required
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        className="appearance-none rounded-md relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#FF6B00] focus:border-[#FF6B00] focus:z-10 sm:text-sm"
                        placeholder={dict?.forgot?.confirmPasswordPlaceholder || '确认新密码'}
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button 
                          type="button" 
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="text-gray-400 hover:text-gray-500 focus:outline-none"
                        >
                          {showConfirmPassword ? (
                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                              <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                            </svg>
                          ) : (
                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>

            <div>
              <button
                type="submit"
                disabled={loading || (step === 1 && countdown > 0)}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-[#FF6B00] hover:bg-[#E55A00] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B00]"
              >
                {loading ? (
                  <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </span>
                ) : null}
                {step === 1
                  ? (countdown > 0
                    ? `${countdown}${dict?.forgot?.countdownText || '秒后可重新发送'}`
                    : (dict?.forgot?.sendCodeButton || '发送验证码'))
                  : (step === 2
                    ? (dict?.forgot?.verifyButton || '验证')
                    : (dict?.forgot?.resetButton || '重置密码'))}
              </button>
            </div>

            {step > 1 && (
              <div className="text-center">
                <button 
                  type="button" 
                  onClick={() => {
                    if (step === 3) {
                      setStep(2);
                    } else if (step === 2 && countdown === 0) {
                      setStep(1);
                    }
                  }}
                  disabled={step === 2 && countdown > 0}
                  className="text-[#FF6B00] hover:text-[#E55A00] text-sm font-medium"
                >
                  {step === 3
                    ? (dict?.forgot?.backToVerify || '返回验证')
                    : (countdown > 0
                      ? `${countdown}${dict?.forgot?.countdownReturn || '秒后可返回'}`
                      : (dict?.forgot?.backToEmail || '返回修改邮箱'))}
                </button>
              </div>
            )}
              </form>
            ) :
            (
              <form className="mt-8 space-y-6" onSubmit={handleResetPassword}>
                <div className="space-y-4">
                  {/* tp6 邮箱输入 */}
                  <div>
                     <div className="relative">
                      <input
                        id="email"
                        name="email"
                        type="email"
                        autoComplete="email"
                        required
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="appearance-none rounded-md relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#FF6B00] focus:border-[#FF6B00] focus:z-10 sm:text-sm"
                        placeholder={dict?.forgot?.emailPlaceholder || '邮箱'}
                      />
                     </div>
   
                  </div>
                  <div>
                    <div  className="relative flex">
                    {/* tp6 验证码输入 */}
                       <input
                        id="verification-code"
                        name="verification-code"
                        type="text"
                        required
                        value={verificationCode}
                        onChange={(e) => setVerificationCode(e.target.value)}
                        className="appearance-none rounded-md relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#FF6B00] focus:border-[#FF6B00] focus:z-10 sm:text-sm"
                        placeholder={dict?.forgot?.codePlaceholder || '验证码'}
                      />
                      <button 
                        className={`group relative w-30 m-l-2 py-3 flex justify-center border border-transparent text-sm font-medium rounded-md text-white bg-[#FF6B00] hover:bg-[#E55A00] sm:text-sm ${
                            emailCountdown > 0 ? 'opacity-50' : ''
                          }`}
                        disabled={emailCountdown > 0}
                        onClick={handleSendEmailCode}
                      >
                        {emailCountdown > 0 ? `${emailCountdown}s` : dict?.dashboard?.account?.changeEmail?.sendCode}
                      </button>
                    </div>
                  </div>  
                  {/* tp6 新密码输入 */}
                  <div>
                    <div className="relative">
                      <input
                        id="password"
                        name="password"
                        type={showPassword ? "text" : "password"}
                        autoComplete="new-password"
                        required
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="appearance-none rounded-md relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#FF6B00] focus:border-[#FF6B00] focus:z-10 sm:text-sm"
                        placeholder={dict?.forgot?.passwordPlaceholder || '新密码'}
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button 
                          type="button" 
                          onClick={() => setShowPassword(!showPassword)}
                          className="text-gray-400 hover:text-gray-500 focus:outline-none"
                        >
                          {showPassword ? (
                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                              <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                            </svg>
                          ) : (
                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* tp6 确认新密码 */}
                  <div>
                    <div className="relative">
                      <input
                        id="confirm-password"
                        name="confirm-password"
                        type={showConfirmPassword ? "text" : "password"}
                        autoComplete="new-password"
                        required
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        className="appearance-none rounded-md relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#FF6B00] focus:border-[#FF6B00] focus:z-10 sm:text-sm"
                        placeholder={dict?.forgot?.confirmPasswordPlaceholder || '确认新密码'}
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button 
                          type="button" 
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="text-gray-400 hover:text-gray-500 focus:outline-none"
                        >
                          {showConfirmPassword ? (
                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                              <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                            </svg>
                          ) : (
                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <button
                    type="submit"
                    disabled={loading}
                    className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-[#FF6B00] hover:bg-[#E55A00] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B00]"
                  >
                    {loading ? (
                      <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                        <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </span>
                    ) : null}
                    {(dict?.forgot?.resetButton || '重置密码')}
                  </button>
                </div>

                </form>
            )
          }
          <div className="text-center text-sm">
              <span className="text-gray-500">{dict?.forgot?.rememberPassword || '记得密码？'}</span>{' '}
              <Link href={`/${lng}/login`} className="font-medium text-[#FF6B00] hover:text-[#E55A00]">
                {dict?.forgot?.loginLink || '登录'}
              </Link>
          </div>

        </div>
      </div>
    </div>
  );
} 