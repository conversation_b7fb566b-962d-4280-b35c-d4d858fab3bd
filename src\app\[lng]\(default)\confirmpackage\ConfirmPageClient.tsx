'use client'
import React, { useState, useEffect, useMemo } from 'react'
import OrderItem from '../confirm/(component)/OrderItem'
import OrderSummary from './(components)/OrderSummary'
import AddressSelect from './(components)/AddressSelect'
import ShippingMethods from './(components)/ShippingMethods'
import { App } from 'antd'
import { Locale } from '@/config'
import { Api } from '@/request/api'

interface CartItem {
    id: number;
    mall_goods_id: number;
    goods_id: string;
    goodsname: string;
    goodsimg: string;
    goodsprice: string;
    goodsnum: number;
    skuname: string;
    [key: string]: any;
}

interface ConfirmPageClientProps {
    lng: Locale;
    dict: any;
    searchKey: string;
}
interface FeeInfo {
    freightdiscount:number,
    freight:number,
    oldfreight:number
}

export default function ConfirmPageClient({ lng, dict, searchKey }: ConfirmPageClientProps) {
    const [selectedAddressId, setSelectedAddressId] = useState<number | null>(null);
    const [selectedAddress_Id, setSelectedAddress_Id] = useState<number | null>(null);
    const [selectedShippingMethod, setSelectedShippingMethod] = useState<number | null>(null);
    const [productList, setProductList] = useState<CartItem[]>([]);
    const [shippingFee, setShippingFee] = useState<number>(0);
    const [shippingFeeInfo, setShippingFeeInfo] = useState<FeeInfo>();
    const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
    useEffect(() => {
        // 从localStorage中读取数据，这必须在客户端环境中执行
        let rawItem = window.localStorage.getItem(searchKey);
        if (rawItem) {
            let items = JSON.parse(rawItem);
            setProductList(items);
        }
    }, [searchKey]);

    // 计算商品总重量和体积
    const totalWeight = useMemo(() => {
        return productList.reduce((sum: number, item: any) => sum + (item.goodsweight || 0), 0);
    }, [productList]);

    const totalVolume = useMemo(() => {
        return productList.reduce((sum: number, item: any) => sum + (item.goodsvolume || 0), 0);
    }, [productList]);

    // 计算商品总价
    const totalPrice = useMemo(() => {
        return productList.reduce((sum: number, item: any) => sum + parseFloat(item.goodsprice) * item.goodsnum, 0);
    }, [productList]);
    
    let isOpen = false;

    //  商品类型
    const goodsType = productList.map((item: any) => item.goodstype)

    const handleAddressSelect = (addressId: number, id: number) => {
        setSelectedAddressId(addressId);
        setSelectedAddress_Id(id);
        // 当地址变化时，重置运输方式
        setSelectedShippingMethod(null);
        setShippingFee(0);
    };

    const handleShippingMethodSelect = async(method: any) => {
        setSelectedShippingMethod(method.template_id);
        // 使用普通运单的运费计算接口，而不是预演包裹的接口
        const res = await Api.changeSendOrderShipping({template_id:method.template_id,order_goods_ids:productList.map(item => item.id)})
        if(res.success){
            if (res.data && res.data.freight) {
                setShippingFee(res.data.freight || 0);
                setShippingFeeInfo({
                    freightdiscount:res.data.freightdiscount,
                    freight:res.data.freight,
                    oldfreight:res.data.oldfreight
                })
            } else {
                setShippingFeeInfo({
                    freightdiscount:0,
                    freight:0,
                    oldfreight:0
                })
                setShippingFee(0);
            }
        }
    };

    // 更新 URL 参数
    useEffect(() => {
        const searchParams = new URLSearchParams(window.location.search);
        if (selectedShippingMethod) {
            searchParams.set('template_id', selectedShippingMethod.toString());
        }
        if (selectedAddress_Id) {
            searchParams.set('address_id', selectedAddress_Id.toString());
        }
        const newUrl = `${window.location.pathname}?${searchParams.toString()}`;
        window.history.replaceState({}, '', newUrl);
    }, [selectedShippingMethod, selectedAddress_Id]);
    return (
        <>
            {/* 地址卡片区域 */}
            <div className="bg-white rounded-lg mb-6 p-4">
                <AddressSelect onSelect={handleAddressSelect} />
            </div>

            <h2 className="text-lg font-medium mb-3 pt-10">{dict?.confirm?.order?.transport?.title}</h2>
            
            {/* 国际运输方式区域 */}
            <div className="mb-6">
                <ShippingMethods 
                    dict={dict}
                    addressId={selectedAddressId}
                    weight={totalWeight}
                    volume={totalVolume}
                    goodstype={isTp5? goodsType: goodsType.join(',')}
                    stotalmoney={totalPrice}
                    onSelect={handleShippingMethodSelect}
                />
            </div>
            
            {/* 使用flex布局创建左右分栏 */}
            <div className="flex flex-col md:flex-row gap-6 pt-10">
                <div className="flex-1">
                    <div className="mb-4">
                        <h2 className="text-lg font-medium mb-3">{dict?.confirm?.order?.productInfo}</h2>
                        <div className="bg-white p-4 rounded-lg">
                            <OrderItem products={productList}  dict={dict} showAccessorialService={false}/>
                        </div>
                    </div>
                </div>

                {/* 右侧栏 - 订单摘要 */}
                <div className="md:w-[380px]">
                    <OrderSummary 
                        couponIsOpen={isOpen} 
                        dict={dict} 
                        localkey={searchKey}
                        products={productList} 
                        totalProductPrice={totalPrice}
                        shippingFee={shippingFee}
                        order_goods_ids={productList.map(item => item.id)}
                        type={'sendorder'}
                        shippingFeeInfo={shippingFeeInfo || { freightdiscount: 0, freight: 0, oldfreight: 0 }}
                    />
                </div>
            </div>
        </>
    );
} 