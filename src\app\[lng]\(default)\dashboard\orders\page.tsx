'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Form, Input, DatePicker, Button, Pagination } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import Tabs from '@/components/Tabs';
import { Api } from '@/request/api';
import OrderItem from './(component)/OrderItem';
import type { Locale } from '@/config';
import { useParams } from 'next/navigation';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import Loading from '@/components/Loading';
import OrderProgress from '@/components/OrderProgress';
import { getDictionary } from '@/dictionaries';
interface OrderData {
  id: string;
  ordersn: string;
  status_text: string;
  createtime: string;
  totalmoney: number;
  goods: Array<{
    id: string;
    goodsname: string;
    goodsnum: number;
    goodsprice: string;
    goodsimg: string;
    status_text: string;
  }>;
}

interface OrderListParams {
  status?: string;
  keyword?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
}



export default function OrdersPage() {
  const params = useParams();
  const lng = params.lng as Locale || 'zh-cn';

  const [activeTab, setActiveTab] = useState<string>('-1');
  const [orders, setOrders] = useState<OrderData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchLoading, setSearchLoading] = useState<boolean>(false);
  const [dict, setDict] = useState<any>(null); // 添加字典状态
  const [statusList, setStatusList] = useState<Array<{ key: string; label: string }>>([]);
  const [form] = Form.useForm();
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(3);
  const [total, setTotal] = useState<number>(0);
  const { RangePicker } = DatePicker;

  const fetchOrders = async (params: OrderListParams = {}) => {
    let paramters: {
      page: number;
      size: number;
      keywords: string;
      lang: string;
      clienttype: string;
      startDate: string | undefined;
      endDate: string | undefined;
      order_state?: string;
    } = {
      page: params.page || currentPage,
      size: params.pageSize || pageSize,
      keywords: params.keyword || '',
      lang: lng === 'zh-cn' ? 'zhCN' : lng === 'en' ? 'enUS' : 'jaJP',
      clienttype: 'pc',
      startDate: params.startDate,
      endDate: params.endDate
    }
    if (activeTab !== '-1') {
      paramters.order_state = activeTab;
    }
    console.log(paramters, params.page , currentPage)
    setLoading(true);
    const response = await Api.getOrderList(paramters);
    setOrders(response.data.data || []);
    setTotal(response.data.total || 0);
    setLoading(false);
    setSearchLoading(false);
  };

  // 当activeTab改变时获取订单数据，包括"全部"选项
  useEffect(() => {
    if (statusList.length > 0) {
      handleSearch();
    }
  }, [activeTab, statusList]);

  // 异步获取字典数据
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };

    fetchDictionary();
  }, [lng]);

  // 初始化状态列表
  useEffect(() => {
    const init = async () => {
      const statusListResponse = await Api.getOrderStatusList();
      if (statusListResponse.success) {
        let statusListData = Object.keys(statusListResponse.data).map((key) => ({
          key,
          label: statusListResponse.data[key]
        }));

        // 在状态列表前面添加"全部"选项
        const allTabsData = [
          {
            key: '-1',
            label: dict?.dashboard?.orders?.list?.status?.all || '全部'
          },
          ...statusListData
        ];

        setStatusList(allTabsData);
      }
    };
    init();
  }, [lng, dict]);
  const handleSearch = () => {
    const values = form.getFieldsValue();
    const [startDate, endDate] = values.dateRange || [null, null];
    setCurrentPage(1)
    fetchOrders({
      status: values.status,
      keyword: values.keyword,
      startDate: startDate ? startDate.format('YYYY-MM-DD') : undefined,
      endDate: endDate ? endDate.format('YYYY-MM-DD') : undefined,
      page: 1
    });
  };

  const handleTabChange = (activeKey: string) => {
    setActiveTab(activeKey);
  };

  const handlePageChange = (page: number, pageSize: number) => {
    setCurrentPage(page);
    setPageSize(pageSize);
    fetchOrders({ page, pageSize });
  };

  return (
    <AntdConfigProvider>
      <div className="min-h-screen px-6 py-4">
       <OrderProgress dict={dict}></OrderProgress>

        <Form
          form={form}
          layout="inline"
          className="mb-8 flex flex-wrap"
          style={{
            marginBottom: '20px'
          }}
          onFinish={() => {
            setSearchLoading(true);
            handleSearch();
          }}
          initialValues={{
            keyword: '',
            status: undefined,
            dateRange: undefined
          }}
        >
          <Form.Item name="keyword" className="mb-2">
            <Input
              placeholder={dict?.dashboard?.orders?.list?.searchPlaceholder}
              prefix={<SearchOutlined className="text-[#86909C]" />}
              style={{ width: 320 }}
              size="large"
            />
          </Form.Item>

          <Form.Item className="mb-2">
            <Button
              type="primary"
              htmlType="submit"
              loading={searchLoading}
              size="large"
            >
              {dict?.dashboard?.orders?.list?.searchButton}
            </Button>
          </Form.Item>
        </Form>

        <Tabs defaultActiveKey={activeTab.toString()} onChange={handleTabChange} items={statusList}>
        </Tabs>

        {/* 表格结构 */}
        {dict && (
          <div className="w-full">
            {/* 表头 */}
            <div className="bg-[#fafafa] border rounded font-bold text-[#333] min-h-[40px] grid grid-cols-[400px_140px_120px_120px_140px_120px] gap-4 p-4 mb-4">
              <div className="text-center">{dict.dashboard.orders.list.goodsName}</div>
              <div className="text-center">{dict.dashboard.orders.list.price}</div>
              <div className="text-center">{dict.dashboard.orders.list.quantity}</div>
              <div className="text-center">{dict.dashboard.orders.list.logistics}</div>
              <div className="text-center">{dict.dashboard.orders.list.operation}</div>
              <div className="text-center">{dict.dashboard.orders.list.freight}</div>
            </div>

            {/* 订单列表 */}
            {
              loading ? <Loading height="300px" /> : (
                <>
                  {orders.map((order) => (
                    <OrderItem key={order.id} data={order} onRefresh={fetchOrders} lng={lng} dict={dict} />
                  ))}
                  {total > pageSize && (
                    <div className="flex justify-end mt-4">
                      <Pagination
                        current={currentPage}
                        pageSize={pageSize}
                        total={total}
                        onChange={handlePageChange}
                        showSizeChanger
                        showQuickJumper
                        showTotal={(total) => dict?.dashboard?.orders?.list?.total?.replace('{total}', total)}
                      />
                    </div>
                  )}
                </>
              )
            }
          </div>
        )}
      </div>
    </AntdConfigProvider>
  );
}
