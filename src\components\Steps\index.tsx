'use client';

import { Steps, StepsProps } from 'antd';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import React, { ReactNode } from 'react';
import styles from './index.module.css';
import "./index.css"


// 自定义步骤图标组件
const StepIcon = ({ stepNumber, isActive, isCompleted }: { stepNumber: number, isActive: boolean, isCompleted: boolean }) => {
  // 为每个步骤定义不同的图标
  const stepIcons = [
    'fas fa-shopping-cart',    // 1. Select - 购物车
    'fas fa-clipboard-check',  // 2. Confirm Order - 确认订单
    'fas fa-credit-card',      // 3. Pay for Products - 支付产品
    'fas fa-warehouse',        // 4. Inspection & Storage - 检查和存储
    'fas fa-paper-plane',      // 5. Submit - 提交
    'fas fa-shipping-fast',    // 6. Pay for Shipping - 支付运费
    'fas fa-home'              // 7. Delivery - 配送
  ];

  const currentIcon = stepIcons[stepNumber - 1] || 'fas fa-tag';

  return (
    <div className={styles.stepIconWrapper}>
      <div className={`${styles.stepIcon} ${isActive ? styles.active : ''} ${isCompleted ? styles.completed : ''}`}>
        {isCompleted ? (
          <i className="fas fa-check"></i>
        ) : (
          <span>{stepNumber}</span>
        )}
      </div>
      {/* 价格标签图标 - 只有激活或完成的步骤才高亮显示 */}
      <div className={`${styles.priceTag} ${(isActive || isCompleted) ? styles.active : styles.inactive}`}>
        <i className={currentIcon}></i>
      </div>
    </div>
  );
};

export default function StepsComponent({ children, dict, current = 0, stepOverrides, ...props }: StepsProps & { children?: ReactNode } & { dict: any } & { stepOverrides?: { [key: number]: string } }) {
  // 步骤导航
  const stepTitles = [
    stepOverrides?.[0] || dict?.dashboard?.cart?.steps?.select || 'Select',
    stepOverrides?.[1] || dict?.dashboard?.cart?.steps?.confirm || 'Confirm',
    stepOverrides?.[2] || dict?.dashboard?.cart?.steps?.payProduct || 'Pay Items',
    stepOverrides?.[3] || dict?.dashboard?.cart?.steps?.inspect || 'Inspect',
    stepOverrides?.[4] || dict?.dashboard?.cart?.steps?.submit || 'Submit',
    stepOverrides?.[5] || dict?.dashboard?.cart?.steps?.payShipping || 'Ship',
    stepOverrides?.[6] || dict?.dashboard?.cart?.steps?.receive || 'Deliver',
  ];

  // Detect language for styling adjustments
  const isChineseLanguage = stepTitles.some(title => /[\u4e00-\u9fff]/.test(title));
  const languageClass = isChineseLanguage ? styles.chineseLayout : styles.westernLayout;

  const steps = stepTitles.map((title, index) => ({
    title,
    icon: (
      <StepIcon
        stepNumber={index + 1}
        isActive={current === index}
        isCompleted={current > index}
      />
    ),
  }));

  return (
    <AntdConfigProvider>
      <div className={`${styles.stepsWrapper} ${languageClass}`}>
        <Steps {...props} current={current} items={steps} className={`${styles.customSteps} ${languageClass}`}>
          {children && children}
        </Steps>
      </div>
    </AntdConfigProvider>
  );
}