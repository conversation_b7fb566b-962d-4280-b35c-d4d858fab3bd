'use client';

import React, { useState } from 'react';
import RechargeModal from '@/components/RechargeModal';
import { Api } from '@/request/api';
import { message } from 'antd';
import { useRouter } from 'next/navigation';    

interface rechargeProps {
  dict: any;
}

export default function RechargeButton({ dict }: rechargeProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const router = useRouter();
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleRecharge = async (amount: number) => {
    if (amount <= 0) {
      message.warning('请输入有效的充值金额');
      return;
    }

    try {
      // 获取当前选择的货币
      const selectedCurrency = localStorage.getItem('selectedCurrency') || 'CNY';
      const res = await Api.recharge({ money: amount, currency: selectedCurrency as any });
      if (res.success) {
        let trade_sn = isTp5 ? res.data : res.data.trade_sn
        router.push(`/pay?tradesn=${trade_sn}&type=cz`);
        setIsModalOpen(false);
      } else {
        message.error(res.msg || '充值失败');
      }

      // 目前模拟成功
      message.success('充值请求已提交，请在支付页面完成支付');
      setIsModalOpen(false);
    } catch (error) {
      message.error('充值失败，请稍后再试');
    }
  };

  return (
    <>
      <button
        className="bg-[#1E293B] text-white px-12 py-2 rounded-md hover:bg-[#1E293B]/90"
        onClick={handleOpenModal}
      >
        {dict.dashboard.wallet.recharge.title}
      </button>
      
      <RechargeModal
        open={isModalOpen}
        onCancel={handleCloseModal}
        onOk={handleRecharge}
        dict={dict}
      />
    </>
  );
} 