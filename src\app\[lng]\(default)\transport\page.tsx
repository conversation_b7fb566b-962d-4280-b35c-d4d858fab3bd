'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { getDictionary } from '@/dictionaries';
import type { Locale } from '@/config';
import Toast from '@/components/Toast';
import { Api } from '@/request/api';
import Loading from '@/components/Loading';

export default function SharePromotionPage() {
  const params = useParams();
  const router = useRouter();
  const lng = params.lng as Locale || 'zh-cn';
  const [dict, setDict] = useState<any>(null);
  const [activeStep, setActiveStep] = useState(1);
  const [packages, setPackages] = useState([{ title: '', expressno: '' }]);
  const [copySuccess, setCopySuccess] = useState(false);
  const [config, setConfig] = useState<any>(null);
  const [dictLoading, setDictLoading] = useState(true);
  const [configLoading, setConfigLoading] = useState(true);
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [userInfo, setUserInfo] = useState<{id?: number, username?: string} | null>(null);

  // Calculate total loading state
  const isLoading = dictLoading || configLoading;

  // Get dictionary data
  useEffect(() => {
    
    const loadDictionary = async () => {
      try {
        setDictLoading(true);
        const dictionary = await getDictionary(lng);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      } finally {
        setDictLoading(false);
      }
    };
    loadDictionary();
  }, [lng]);

  // 确保条款复选框状态同步
  useEffect(() => {
    // 组件挂载后确保checkbox状态正确
    const checkboxElement = document.getElementById('agree-terms') as HTMLInputElement;
    if (checkboxElement) {
      // 强制DOM与React状态同步
      checkboxElement.checked = agreeToTerms;
      console.log('组件挂载 - 同步checkbox状态:', agreeToTerms);
    }
  }, [agreeToTerms]);

  // 添加全局键盘事件监听，防止意外提交
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 防止Ctrl+Enter或其他快捷键意外触发提交
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        if (!agreeToTerms) {
          e.preventDefault();
          e.stopPropagation();
          console.log('阻止快捷键提交：条款未同意');
          Toast.error(dict?.transport?.agreeTerms || 'Please agree to the forwarding service agreement');
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [agreeToTerms, dict]);

  // Get page configuration information
  useEffect(() => {
    const getConfig = async () => {
      try {
        setConfigLoading(true);
        const res = await Api.getConfigList();
        if (res?.success && res.data) {
          setConfig(res.data);
          // Optional: Store configuration data to localStorage, consistent with other pages
          if (typeof window !== 'undefined' && res.data.site) {
            localStorage.setItem('siteData', JSON.stringify(res.data.site));
          }
        }
      } catch (error) {
        console.error('Failed to load config:', error);
      } finally {
        setConfigLoading(false);
      }
    };

    getConfig();
  }, []);

  // Get user information
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const infoStr = localStorage.getItem('info');
        if (infoStr) {
          const info = JSON.parse(infoStr);
          if (info?.success && info?.data?.userinfo) {
            const { id, username } = info.data.userinfo;
            setUserInfo({ id, username });
          }
        }
      } catch (error) {
        console.error('Failed to parse user info from localStorage:', error);
      }
    }
  }, []);
  
  // Add package information
  const addPackage = () => {
    setPackages([...packages, { title: '', expressno: '' }]);
  };
  
  // Delete package information
  const removePackage = (index: number) => {
    if (packages.length > 1) {
      const newPackages = [...packages];
      newPackages.splice(index, 1);
      setPackages(newPackages);
    }
  };
  
  // Update package information
  const updatePackage = (index: number, field: 'title' | 'expressno', value: string) => {
    const newPackages = [...packages];
    newPackages[index] = { ...newPackages[index], [field]: value };
    setPackages(newPackages);
  };

  // Copy address information
  const copyAddress = async () => {
    const receiver = config?.site?.shipforme_head || dict?.transport?.receiver || '';
    const phone = config?.site?.shipforme_tel || dict?.transport?.phone || '';
    const province = config?.site?.shipforme_rode || '';
    const address = config?.site?.shipforme_address || dict?.transport?.address || '';
    const fullAddress = province && address ? `${province}${address}` : (address || dict?.transport?.address || '');

    const addressText = `${receiver}\n${phone}\n${fullAddress}`;
    try {
      await navigator.clipboard.writeText(addressText);
      Toast.success(dict?.transport?.copySuccess || 'Copy successful');
    } catch (err) {
      console.error('Copy failed:', err);
      Toast.error(dict?.transport?.copyFailed || 'Copy failed');
    }
  };

  const handleSubmit = async () => {
    // 最高优先级验证：条款同意检查 - 必须在所有其他验证之前
    console.log('=== 开始条款验证 ===');
    console.log('agreeToTerms状态:', agreeToTerms);

    // 第一重验证：React状态检查
    if (!agreeToTerms) {
      console.error('❌ 第一重验证失败：React状态显示用户未同意条款');
      Toast.error(dict?.transport?.agreeTerms || 'Please agree to the forwarding service agreement');
      return;
    }

    // 第二重验证：DOM元素检查
    const checkboxElement = document.getElementById('agree-terms') as HTMLInputElement;
    console.log('复选框元素:', checkboxElement);
    console.log('复选框checked状态:', checkboxElement?.checked);

    if (!checkboxElement) {
      console.error('❌ 第二重验证失败：找不到条款复选框元素');
      Toast.error(dict?.transport?.agreeTerms || 'Please agree to the forwarding service agreement');
      return;
    }

    if (!checkboxElement.checked) {
      console.error('❌ 第二重验证失败：复选框未被选中');
      Toast.error(dict?.transport?.agreeTerms || 'Please agree to the forwarding service agreement');
      return;
    }

    // 第三重验证：实时状态双重检查
    // 由于React状态更新是异步的，这里再次检查状态和DOM的一致性
    if (!agreeToTerms || !checkboxElement.checked) {
      console.error('❌ 第三重验证失败：状态不一致，agreeToTerms:', agreeToTerms, 'checkbox.checked:', checkboxElement.checked);
      Toast.error(dict?.transport?.agreeTerms || 'Please agree to the forwarding service agreement');
      return;
    }

    console.log('✅ 所有条款验证通过');

    // Filter out completely empty objects
    const filteredData = packages.filter(item =>
      item.expressno || item.title
    );

    if(filteredData.length < 1){
        Toast.error(dict?.transport?.fillPackageInfo || 'Please fill in package information');
        return
    }
    // Check if there are objects with only one field filled
    const hasIncomplete = packages.some(item =>
      (item.expressno && !item.title) || (!item.expressno && item.title)
    );
    if (hasIncomplete) {
        Toast.error(dict?.transport?.fillComplete || 'Please fill in completely');
        return
    }
    let data = {
      "list": filteredData.map((item)=>{
        return {
          ...item,
          // 接口要求需要带上以下字段
          pic_url: "/assets/img/default/buyfromchina.jpg",
          goodsnum: 1,
          goodsremark: "",
          price: 0,
          showwuliu: false,
          detail_url: "",
          expressname:""
        }
      }),
      "clienttype":"pc"
    }
    try{
      const res = await Api.createTransshipmentOrder(data);
      if(res.success){
        Toast.success(dict?.transport?.success);
        router.push(`/${lng}/dashboard/orders`);
      }else{
        if(res.msg.includes('10000')){
           Toast.error(dict?.transport?.authError);
           const currentPath = window.location.pathname + window.location.search;
            const loginUrl = `/${lng}/login?callback=${encodeURIComponent(currentPath)}`;
            router.push(loginUrl);
        }else{
          Toast.error(res.msg || dict?.transport?.error);
        }
      }
    }catch(err){
       Toast.error(dict?.transport?.error);
    }

  };
  
  // 如果还在加载中，显示Loading组件
  if (isLoading) {
    return <Loading />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部横幅区域 */}
      <div className="w-full min-h-[400px] bg-cover bg-center py-16 relative flex justify-center"
        style={{
          backgroundImage: 'url(/images/transport.jpg)',
        }}>
        {/* 橙色遮罩 */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#f97316cc] to-[#ea580ccc] opacity-90"></div>
        
        <div className="max-w-[1200px] mx-auto px-4 lg:px-8 relative z-10 flex flex-col lg:flex-row justify-between items-center gap-10">
          <div className="mx-auto lg:w-1/2 text-white">  
            <h1 className="text-4xl font-bold mb-6 md:text-5xl sm:text-3xl text-center text-shadow-xl text-shadow-color-[#8f3b00cc]"> {dict?.transport?.guideTitle}
            </h1>
            <p className="text-lg opacity-90 mb-8 leading-relaxed text-shadow-xl text-shadow-color-[#8f3b00cc] text-center">
            {dict?.transport?.guideDesc}</p>
          </div>
        </div>
      </div>

      {/* Transport Process Steps */}
      <div className="max-w-[1200px] mx-auto px-4 py-12">
        <div className="mx-auto">
          {/* Step 1: Shipping Address */}
          <div className="bg-white rounded-md shadow-sm p-6 mb-6">
            <div className="flex items-start gap-2 mb-4">
              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-amber-50 text-amber-500 flex items-center justify-center font-bold text-sm">1</div>
              <h2 className="text-lg font-medium text-gray-900">{dict?.transport?.copyBtn?.replace('Onebuy', config?.site?.name || 'Onebuy')}</h2>
            </div>

            <div className="bg-gray-50 rounded-md py-5 px-4">
              <div className="space-y-1 text-gray-700 text-sm">
                <p>
                  <span className="font-medium">{dict?.transport?.receiverLabel || 'Recipient:'}</span>
                  {config?.site?.shipforme_head || dict?.transport?.receiver || ''}
                  {userInfo?.id && userInfo?.username ? `-${userInfo.id}-${userInfo.username}` : ''}
                </p>
                <p>
                  <span className="font-medium">{dict?.transport?.phoneLabel || 'Phone:'}</span>
                  {config?.site?.shipforme_tel || dict?.transport?.phone || ''}
                </p>
                <p>
                  <span className="font-medium">{dict?.transport?.addressLabel || 'Address:'}</span>
                  {config?.site?.shipforme_rode && config?.site?.shipforme_address
                    ? `${config.site.shipforme_rode}${config.site.shipforme_address}`
                    : (config?.site?.shipforme_address || dict?.transport?.address || '')
                  }
                </p>
              </div>
              
              <button
                onClick={copyAddress}
                className="mt-4 bg-orange-500 hover:bg-orange-600 text-white flex items-center gap-1 px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 5H6C4.89543 5 4 5.89543 4 7V19C4 20.1046 4.89543 21 6 21H16C17.1046 21 18 20.1046 18 19V18M8 5C8 6.10457 8.89543 7 10 7H12C13.1046 7 14 6.10457 14 5M8 5C8 3.89543 8.89543 3 10 3H12C13.1046 3 14 3.89543 14 5M14 5H16C17.1046 5 18 5.89543 18 7V12M20 14V16.5L22 14.5M20 21V18.5L22 20.5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                {dict?.transport?.copyBtn?.replace('Onebuy', config?.site?.name || 'Onebuy')}
              </button>
            </div>
          </div>

          {/* Step 2: Fill Package Information */}
          <div className="bg-white rounded-md shadow-sm p-6 mb-6">
            <div className="flex items-start gap-2 mb-4">
              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-amber-50 text-amber-500 flex items-center justify-center font-bold text-sm">2</div>
              <h2 className="text-lg font-medium text-gray-900">{dict?.transport?.packageTitle}</h2>
            </div>
            
            <div className="space-y-4">
              {packages.map((pkg, index) => (
                <div key={index} className="relative">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <input
                      type="text"
                      placeholder={dict?.transport?.packageNameHint}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                      value={pkg.title}
                      onChange={(e) => updatePackage(index, 'title', e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          console.log('阻止Enter键提交');
                        }
                      }}
                    />
                    <div className="relative">
                      <input
                        type="text"
                        placeholder={dict?.transport?.trackingNumberHint}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                        value={pkg.expressno}
                        onChange={(e) => updatePackage(index, 'expressno', e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            console.log('阻止Enter键提交');
                          }
                        }}
                      />
                      {packages.length > 1 && (
                        <button 
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-red-500"
                          onClick={() => removePackage(index)}
                          aria-label={dict?.transport?.deletePackage}
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                          </svg>
                        </button>
                      )}
                    </div>
                  </div>
                  {index < packages.length - 1 && <div className="border-b border-dashed border-gray-200 my-4"></div>}
                </div>
              ))}
            </div>
            <button 
              className="mt-4 text-orange-500 hover:text-orange-600 text-sm font-medium flex items-center" 
              onClick={addPackage}
            >
              <span className="mr-1">+</span>{dict?.transport?.addMore}
            </button>
          </div>

          {/* Step 3: Submit Package */}
          <div className="bg-white rounded-md shadow-sm p-6 mb-6">
            <div className="flex items-start gap-2 mb-4">
              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-amber-50 text-amber-500 flex items-center justify-center font-bold text-sm">3</div>
              <h2 className="text-lg font-medium text-gray-900">{dict?.transport?.steps?.submit || 'Submit Package'}</h2>
            </div>

            <div className="bg-amber-50 rounded-md py-4 px-4">
              <p className="text-gray-700 text-sm">{dict?.transport?.steps?.description || 'After receiving your package, you can select items to pack and ship in My Warehouse.'}</p>
              <Link href={`/${lng}/dashboard/warehouse`} className="inline-block mt-2 text-orange-500 hover:text-orange-600 text-sm font-medium">
                {dict?.transport?.steps?.action || 'Submit Package >'}
              </Link>
            </div>
          </div>

          {/* Step 4: Pay Shipping Fee */}
          <div className="bg-white rounded-md shadow-sm p-6 mb-6">
            <div className="flex items-start gap-2 mb-4">
              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-amber-50 text-amber-500 flex items-center justify-center font-bold text-sm">4</div>
              <h2 className="text-lg font-medium text-gray-900">{dict?.transport?.steps?.payment || 'Pay Shipping Fee'}</h2>
            </div>

            <div className="bg-amber-50 rounded-md py-4 px-4">
              <p className="text-gray-700 text-sm">{dict?.transport?.steps?.paymentDescription || 'Your international package will be automatically cancelled if the weight exceeds the limit. Please pay promptly.'}</p>
              <Link href={`/${lng}/dashboard/packages`} className="inline-block mt-2 text-orange-500 hover:text-orange-600 text-sm font-medium">
                {dict?.transport?.steps?.paymentAction || 'Pay International Shipping >'}
              </Link>
            </div>
          </div>

          {/* Bottom Confirmation Button */}
          <div className="text-center mt-8">
            <div className="flex items-center justify-center m-b-4">
              <input
                id="agree-terms"
                name="agree-terms"
                type="checkbox"
                checked={agreeToTerms}
                onChange={(e) => {
                  const newCheckedState = e.target.checked;
                  console.log('=== 复选框状态变更 ===');
                  console.log('新状态:', newCheckedState);
                  console.log('当前agreeToTerms状态:', agreeToTerms);

                  // 立即更新状态
                  setAgreeToTerms(newCheckedState);

                  // 强制DOM同步（防止状态不一致）
                  e.target.checked = newCheckedState;

                  console.log('状态更新后，agreeToTerms应该为:', newCheckedState);
                  console.log('DOM checkbox.checked:', e.target.checked);

                  // 验证状态是否正确设置
                  setTimeout(() => {
                    const checkboxElement = document.getElementById('agree-terms') as HTMLInputElement;
                    console.log('延迟验证 - agreeToTerms实际状态:', agreeToTerms);
                    console.log('延迟验证 - DOM checkbox.checked:', checkboxElement?.checked);

                    // 如果发现状态不一致，强制同步
                    if (checkboxElement && checkboxElement.checked !== newCheckedState) {
                      console.warn('⚠️ 发现状态不一致，强制同步');
                      checkboxElement.checked = newCheckedState;
                    }
                  }, 100);
                }}
                className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300 rounded"
                required
              />
              <label htmlFor="agree-terms" className="ml-2 block text-sm text-gray-900">
                {dict?.transport?.agreeAndAccept}
                {/* TODO: Get article ID */}
                <Link href={`/${lng}/information?id=2`} target='_blank' className="font-medium text-[#FF6B00] hover:text-[#E55A00]">
                  {dict?.transport?.agreement}
                </Link>
              </label>
            </div>

            <div>
              <button
                className={`px-12 py-3 rounded-md transition-colors ${
                  agreeToTerms
                    ? 'bg-orange-500 text-white hover:bg-orange-600'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
                onClick={(e) => {
                  // 按钮级别的多重验证
                  console.log('按钮点击验证 - agreeToTerms:', agreeToTerms);

                  // 阻止默认行为
                  e.preventDefault();
                  e.stopPropagation();

                  // 第一重：React状态验证
                  if (!agreeToTerms) {
                    console.error('按钮验证失败：React状态显示条款未同意');
                    Toast.error(dict?.transport?.agreeTerms || 'Please agree to the forwarding service agreement');
                    return;
                  }

                  // 第二重：DOM元素验证
                  const checkboxElement = document.getElementById('agree-terms') as HTMLInputElement;
                  if (!checkboxElement || !checkboxElement.checked) {
                    console.error('按钮验证失败：DOM复选框未选中');
                    Toast.error(dict?.transport?.agreeTerms || 'Please agree to the forwarding service agreement');
                    return;
                  }

                  // 第三重：双重状态一致性验证
                  if (!agreeToTerms || !checkboxElement.checked) {
                    console.error('按钮验证失败：状态不一致');
                    Toast.error(dict?.transport?.agreeTerms || 'Please agree to the forwarding service agreement');
                    return;
                  }

                  console.log('✅ 按钮验证通过，调用handleSubmit');
                  handleSubmit();
                }}
                disabled={!agreeToTerms}
                title={!agreeToTerms ? (dict?.transport?.agreeTerms || 'Please agree to the forwarding service agreement') : ''}
                type="button"
              >
               {dict?.transport?.submitBtn}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
