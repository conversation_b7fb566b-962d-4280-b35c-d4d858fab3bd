'use client'

import React, { useEffect, useState } from 'react'
import OnePayOrderItem from '../OnePayOrderItem'
import { Api } from '@/request/api'
import { useParams } from 'next/navigation'
import { useErrorHandler } from '@/utils/errorHandler'

interface CartItem {
    id: number;
    mall_goods_id: number;
    goods_id: string;
    goodsname: string;
    goodsimg: string;
    goodsprice: string;
    goodsnum: number;
    skuname: string;
    [key: string]: any;
}

interface OnePayOrderInfo {
    order_serve: {
        [key: string]: {
            name: string;
            title: string;
            value: string;
            surcharge: number;
            feetype: string;
            base: string;
        };
    };
    sendorder_server?: {
        [key: string]: {
            name: string;
            title: string;
            value: string;
            surcharge: number;
            feetype: string;
            base: string;
        };
    };
    serve_discount: number;
    freight_discount: number;
    totalweight: number;
    totalvolume: number;
    totalfreight: number; // 新增：到中国仓运费
    cart_ids: string[];
    shoplist: Array<{
        goodsmoney: number;
        sendmoney: string;
        totalmoney: number;
        goodsseller: string;
        sellerurl: string;
        goodslist: CartItem[];
    }>;
    totalmoney: string;
}

interface OnePayOrderClientWrapperProps {
    fallbackProducts: CartItem[];
    dict: any;
    itemData?: any;
    cart_ids: string;
}

export default function OnePayOrderClientWrapper({
    fallbackProducts,
    dict,
    itemData,
    cart_ids
}: OnePayOrderClientWrapperProps) {
    const [products, setProducts] = useState<CartItem[]>(fallbackProducts);
    const [loading, setLoading] = useState(true);
    const { lng } = useParams();
    const { handleError, checkResponseAuth } = useErrorHandler(lng as string);

    useEffect(() => {
        const fetchOnePayOrderInfo = async () => {
            if (!cart_ids) {
                setLoading(false);
                return;
            }

            try {
                const cart_ids_array = cart_ids.split(',');
                const response = await Api.getOnePayOrderInfo({ cart_ids: cart_ids_array });



                if (response.success && response.data) {
                    const orderInfo: OnePayOrderInfo = response.data;

                    // Extract products from shoplist
                    if (orderInfo.shoplist && orderInfo.shoplist.length > 0) {
                        const allProducts: CartItem[] = [];
                        orderInfo.shoplist.forEach(shop => {
                            if (shop.goodslist && Array.isArray(shop.goodslist)) {
                                allProducts.push(...shop.goodslist);
                            }
                        });

                        if (allProducts.length > 0) {
                            setProducts(allProducts);
                        }
                    }
                } else {
                    // 检查是否是认证错误
                    if (!checkResponseAuth(response)) {
                        return; // 认证错误已被处理，直接返回
                    }
                }
            } catch (error) {
                handleError(error, 'Failed to get order info');
                // Continue with fallback products if API fails
            } finally {
                setLoading(false);
            }
        };

        fetchOnePayOrderInfo();
    }, [cart_ids, fallbackProducts]);

    if (loading) {
        return (
            <div className="text-center py-8 text-gray-500">
                <p>{dict?.confirm?.onepayorder?.summary?.loading || 'Loading product information...'}</p>
            </div>
        );
    }

    return (
        <OnePayOrderItem 
            products={products} 
            dict={dict} 
            itemData={itemData} 
        />
    );
}
