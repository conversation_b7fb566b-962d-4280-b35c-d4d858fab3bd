import type { Locale } from '@/config';

// 导入字典
const dictionaries: Record<Locale, () => Promise<any>> = {
  'en': () => import('./en.json').then((module) => module.default),
  'zh-cn': () => import('./zh-CN.json').then((module) => module.default),
  'ja': () => import('./ja.json').then((module) => module.default),
};

// 获取字典函数
export const getDictionary = async (locale: Locale) => {
  return dictionaries[locale]?.() ?? dictionaries['en']();
}; 