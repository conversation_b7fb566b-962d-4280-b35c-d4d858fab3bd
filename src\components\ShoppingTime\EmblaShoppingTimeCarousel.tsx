'use client';

import { useMemo, useCallback, useEffect } from 'react';
import Image from 'next/image';
import useEmblaCarousel from 'embla-carousel-react';
import Autoplay from 'embla-carousel-autoplay';
import { ShoppingTimeProduct } from "@/types/product";
import { prepareImageForNextJs } from '@/utils/imageUtils';
import { formatCurrency } from '@/utils/currency';
import styles from './ShoppingTimeCarousel.module.css';

interface ShoppingTimeCarouselProps {
  products: ShoppingTimeProduct[];
  dict: any;
}

interface UserGroup {
  user: {
    username: string;
    nickname: string;
  };
  products: ShoppingTimeProduct[];
  latestTime: string;
  totalItems: number;
}

export default function EmblaShoppingTimeCarousel({ products, dict }: ShoppingTimeCarouselProps) {
  // 按用户nickname进行分组聚合商品数据
  const userGroups = useMemo(() => {
    const groupMap = new Map<string, UserGroup>();

    products.forEach(product => {
      // 优先使用nickname作为分组键，如果没有nickname则使用username，最后使用user_id
      const groupKey = product.user?.nickname || product.user?.username || `user_${product.user_id}` || 'unknown';

      if (groupMap.has(groupKey)) {
        const group = groupMap.get(groupKey)!;
        group.products.push(product);
        group.totalItems += product.goodsnum;
        // 更新最新时间
        if (new Date(product.createtime) > new Date(group.latestTime)) {
          group.latestTime = product.createtime;
        }
      } else {
        // 创建新的用户组
        groupMap.set(groupKey, {
          user: product.user,
          products: [product],
          latestTime: product.createtime,
          totalItems: product.goodsnum
        });
      }
    });

    // 按最新购买时间排序，最新的在前面
    return Array.from(groupMap.values()).sort((a, b) =>
      new Date(b.latestTime).getTime() - new Date(a.latestTime).getTime()
    );
  }, [products]);

  // Embla Carousel 配置 - 支持无缝无限轮播和拖拽
  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop: true,
      align: 'start',
      skipSnaps: false,
      dragFree: true, // 启用自由拖拽，支持动量滚动
      containScroll: false, // 禁用容器滚动限制，实现真正的无限滚动
      slidesToScroll: 'auto', // 自动计算滚动数量
      duration: 25, // 较快的滚动速度
    },
    [
      Autoplay({
        delay: 3000, // 3秒自动播放
        stopOnInteraction: false, // 交互后继续自动播放
        stopOnMouseEnter: true, // 鼠标悬停时暂停
        stopOnFocusIn: true, // 获得焦点时暂停
      })
    ]
  );

  // 导航按钮处理
  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  // 渲染用户卡片 - 优化为连续内容显示
  const renderUserCard = (userGroup: UserGroup, index: number) => {
    const keyPrefix = `user-${userGroup.user.username}-${index}`;

    return (
      <div
        key={keyPrefix}
        className="embla__slide flex-shrink-0"
        style={{ flex: '0 0 auto', minWidth: 'fit-content' }}
      >
        <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 h-full mr-6">
          <div className="flex items-center space-x-2 mb-4">
            <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
              {(userGroup.user.nickname || userGroup.user.username || 'U').charAt(0).toUpperCase()}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-gray-800 text-sm truncate">
                {userGroup.user.nickname || userGroup.user.username}
              </h3>
            </div>
          </div>

          <div className="flex gap-3">
            {userGroup.products.slice(0, 6).map((item, idx) => {
              const titleLength = item.goodsname?.length || 0;
              const titleLengthClass = titleLength <= 10 ? 'short' : titleLength <= 20 ? 'medium' : 'long';
              const detailUrl = item.goodsurl
                ? `/detail/${item.goodssite || 'taobao'}/?url=${encodeURIComponent(item.goodsurl)}`
                : '#';

              return (
                <a
                  key={`${keyPrefix}-product-${item.id || item.goodsimg || idx}`}
                  href={detailUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`${styles.productCard} ${styles.adaptive} group`}
                  data-title-length={titleLengthClass}
                >
                  <div className={styles.productImage}>
                    <Image
                      src={prepareImageForNextJs(item.goodsimg)}
                      alt={item.goodsname || '商品图片'}
                      fill
                      sizes="(max-width: 480px) 150px, (max-width: 768px) 250px, 288px"
                      className="object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/images/default.jpg';
                      }}
                    />
                    {item.recommend === 1 && (
                      <div className={styles.recommendTag}>推荐</div>
                    )}
                  </div>
                  <div className="p-3 flex-1 flex flex-col">
                    <h3 className="text-md text-gray-800 line-clamp-2 mb-2 custom-h-48px">
                      {item.goodsname}
                    </h3>
                    <div className="flex items-center justify-between mt-auto">
                      <span className="text-[#FF6B00] font-medium">
                        <span className='text-sm sm:text-base md:text-lg lg:text-xl font-bold'>
                          {formatCurrency(Number(item.goodsprice)).formatValue}
                        </span>
                      </span>
                    </div>
                  </div>
                </a>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="relative w-full group px-4">
      {userGroups.length > 0 ? (
        <>
          <div className="embla overflow-hidden" ref={emblaRef}>
            <div className="embla__container flex gap-0">
              {userGroups.map((userGroup, index) => renderUserCard(userGroup, index))}
            </div>
          </div>

          {/* 导航按钮 - 仅在hover时显示 */}
          <button
            className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-gray-700 hover:text-orange-600 p-3 rounded-full shadow-lg transition-all duration-300 z-30 border border-gray-200 opacity-0 group-hover:opacity-100"
            onClick={scrollPrev}
            aria-label="上一个"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-gray-700 hover:text-orange-600 p-3 rounded-full shadow-lg transition-all duration-300 z-30 border border-gray-200 opacity-0 group-hover:opacity-100"
            onClick={scrollNext}
            aria-label="下一个"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      ) : (
        <div className="text-center text-gray-500 py-12">
          {dict?.home?.emptyList || '列表为空'}
        </div>
      )}
    </div>
  );
}
