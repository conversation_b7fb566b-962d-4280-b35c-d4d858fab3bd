'use client'
import React, { useState, useEffect } from 'react'
import { Api, AddressItem } from '@/request/api'
import { message, App } from 'antd'
import AddressModal from '@/components/AddressModal'
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { useParams } from 'next/navigation';
import { getDictionary } from "@/dictionaries";
interface AddressSelectProps {
  onSelect?: (addressId: number, id: number) => void;
}

export default function AddressSelect({ onSelect }: AddressSelectProps) {
  const [showAddressModal, setShowAddressModal] = useState(false)
  const [loading, setLoading] = useState(true)
  const [addressList, setAddressList] = useState<AddressItem[]>([])
  const [selectedAddress, setSelectedAddress] = useState<AddressItem | null>(null)
  const [currentAddress, setCurrentAddress] = useState<AddressItem | null>(null)
  const [messageApi, contextHolder] = message.useMessage()
  const { lng } = useParams();
  const [dict, setDict] = useState<any>(null); // 添加字典状态
   // 异步获取字典数据
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };

    fetchDictionary();
  }, [lng]);
  // 只在组件首次渲染时获取地址列表，不依赖onSelect
  useEffect(() => {
    const fetchAddressList = async () => {
      const { data } = await Api.getAddressList();
      if (data.length > 0) {
        let list = data.sort((a: any, b: any) => b.isdefault - a.isdefault)
        setAddressList(list)
        // 默认选中第一个地址（通常是默认地址）
        setSelectedAddress(list[0])
        // 通知父组件选中的地址ID
        onSelect && onSelect(list[0].area_id, list[0].id)
      }
      setLoading(false);
    };
    fetchAddressList();
  }, []); // 移除onSelect依赖

  const handleSelectAddress = (address: AddressItem) => {
    setSelectedAddress(address);
    // 通知父组件选中的地址ID
    onSelect && onSelect(address.area_id, address.id);
  };

  const handleEdit = (item: AddressItem, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止冒泡，避免触发选中效果
    setCurrentAddress(item);
    setShowAddressModal(true);
  };

  const handleDelete = async (item: AddressItem, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止冒泡
    try {
      await Api.deleteAddress(item.id);
      messageApi.success(dict?.dashboard?.address?.deleteSuccess);
      // 重新获取地址列表
      const { data } = await Api.getAddressList();
      if (data.length > 0) {
        let list = data.sort((a: any, b: any) => b.isdefault - a.isdefault);
        setAddressList(list);
        
        // 如果删除的是当前选中的地址，则重新选择第一个地址
        if (selectedAddress && selectedAddress.id === item.id) {
          setSelectedAddress(list[0]);
          // 通知父组件选中的地址ID已更改
          onSelect && onSelect(list[0].area_id, list[0].id);
        }
      } else {
        setAddressList([]);
        setSelectedAddress(null);
        // 通知父组件没有选中的地址
        onSelect && onSelect(0, 0);
      }
    } catch (error) {
      messageApi.error(dict?.dashboard?.address?.deleteSuccess);
    }
  };

  const handleAddNewAddress = () => {
    setCurrentAddress(null);
    setShowAddressModal(true);
  };

  const handleCloseModal = () => {
    setShowAddressModal(false);
    setCurrentAddress(null);
  };

  if (loading) {
    return <div className="flex justify-center items-center h-40">
      <div className="w-10 h-10 border-t-transparent border-solid rounded-full animate-spin border-t-gray-900 border-8"></div>
    </div>
  }

  return (
    <App>
      {contextHolder}
      <div className="relative">
        <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-medium mb-3 pt-10">{dict?.dashboard?.address?.text}</h2>

          <button
            onClick={handleAddNewAddress}
            className="text-[#FF6000] border border-[#FF6000] px-4 py-1.5 rounded-full flex items-center gap-1.5 hover:bg-[#FF6000] hover:text-white transition-all duration-300 text-xs font-medium"
          >
            <PlusOutlined />
            <span>{dict?.dashboard?.address?.addNew}</span>
          </button>
        </div>

        <AddressModal
          open={showAddressModal}
          onCancel={handleCloseModal}
          onSuccess={() => {
            handleCloseModal();
            const fetchAddressList = async () => {
              const { data } = await Api.getAddressList();
              if (data.length > 0) {
                let list = data.sort((a: any, b: any) => b.isdefault - a.isdefault)
                setAddressList(list)
                // 如果是新增地址，则选中第一个地址
                if (!currentAddress) {
                  setSelectedAddress(list[0])
                  // 通知父组件选中的地址ID
                  onSelect && onSelect(list[0].area_id, list[0].id)
                }
              }
            };
            fetchAddressList();
          }}
          currentAddress={currentAddress}
          dict={dict}
        />
        
        <div className="flex flex-wrap gap-4">
          {addressList.map((item: AddressItem) => (
            <div
              key={item.id}
              onClick={() => handleSelectAddress(item)}
              className={`
                relative bg-white rounded-lg transition-all duration-300 ease-in-out
                hover:shadow-md cursor-pointer w-[calc(33.33%-16px)] p-4
                ${selectedAddress && selectedAddress.id === item.id
                  ? 'border-[#FF6000] border-2 shadow-md'
                  : 'border border-gray-200'
                }
              `}
            >
              {/* 选中状态圆圈 - 右上方 */}
              {selectedAddress && selectedAddress.id === item.id && (
                <div className="absolute top-3 right-3 w-6 h-6 bg-[#FF6000] rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}

              {/* 默认地址标签 - 左下方 */}
              {item.isdefault ? (
                <div
                  className="absolute bottom-3 left-3 bg-[#FF6000] text-white px-2 py-0.5 text-xs rounded-full"
                >
                  {dict?.dashboard?.address?.default}
                </div>
              ) : null}

              <div className="flex flex-col gap-2 mb-8 pr-8">
                <div className="flex items-center gap-3">
                  <span className="font-medium">{item.consignee}</span>
                  <span className="text-gray-500">{item.telephone}</span>
                </div>
                <div className="text-gray-600 text-sm">
                  {item.address}
                  {item.door_number && <span className="ml-1 text-gray-400">#{item.door_number}</span>}
                </div>
                {item.zipcode && (
                  <div className="text-gray-400 text-xs">
                      {dict?.dashboard?.address?.postcode}: {item.zipcode}
                  </div>
                )}
              </div>
              <div className="absolute bottom-3 right-3 flex gap-2">
                <button
                  onClick={(e) => handleEdit(item, e)}
                  className="text-[#FF6000] hover:bg-[#FF6000] hover:text-white px-2 py-1 rounded-full text-xs transition-all duration-300 border border-[#FF6000]"
                >
                  <EditOutlined />
                  <span className="ml-1">{dict?.dashboard?.address?.edit}</span>
                </button>
                <button
                  onClick={(e) => handleDelete(item, e)}
                  className="text-gray-500 hover:bg-gray-500 hover:text-white px-2 py-1 rounded-full text-xs transition-all duration-300 border border-gray-300"
                >
                  <DeleteOutlined />
                  <span className="ml-1">{dict?.dashboard?.address?.delete}</span>
                </button>
              </div>
            </div>
          ))}
        </div>

        {addressList.length === 0 && (
          <div className="bg-white rounded-lg p-8 border border-gray-200 text-center">
            <div className="flex flex-col items-center gap-3">
              <div className="text-gray-400 text-sm mb-3">{dict?.dashboard?.address?.empty}</div>
              <button
                onClick={handleAddNewAddress}
                className="text-[#FF6000] hover:bg-[#FF6000] hover:text-white px-4 py-1.5 rounded-full text-sm transition-all duration-300 border border-[#FF6000] flex items-center gap-1.5"
              >
                <PlusOutlined />
                <span>{dict?.dashboard?.address?.addNew}</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </App>
  )
} 