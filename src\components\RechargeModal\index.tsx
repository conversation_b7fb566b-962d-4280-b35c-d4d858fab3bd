'use client';

import React, { useState, useEffect } from 'react';
import ModalComponent from '@/components/Modal';
import { Input } from 'antd';
import { ModalProps } from 'antd';
import { getCurrencyInfo } from '@/utils/currency';

interface RechargeModalProps extends Omit<ModalProps, 'onOk'> {
  onOk?: (amount: number) => void;
  dict: any
}

const quickAmounts = [50, 100, 150, 200, 250, 300, 350, 400, 450];

export default function RechargeModal({ onOk,dict, ...props }: RechargeModalProps) {
  const [amount, setAmount] = useState<string>('0.00');
  const [currencySymbol, setCurrencySymbol] = useState<string>('CNY');

  useEffect(() => {
    // 获取当前货币信息
    const { symbol } = getCurrencyInfo();
    setCurrencySymbol(symbol);
  }, []);

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // 只允许输入数字和小数点，且最多两位小数
    const value = e.target.value;
    if (/^\d*\.?\d{0,2}$/.test(value) || value === '') {
      setAmount(value);
    }
  };

  const handleQuickAmountClick = (value: number) => {
    setAmount(value.toString());
  };

  const handleConfirm = () => {
    if (onOk) {
      onOk(parseFloat(amount) || 0);
    }
  };

  return (
    <ModalComponent
      title={dict?.dashboard?.wallet?.recharge?.title}
      centered
      closable
      width={500}
      footer={[
        <button
          key="submit"
          className="w-full h-12 bg-orange-500 text-white text-lg font-medium rounded-md hover:bg-orange-600 transition-colors"
          onClick={handleConfirm}
        >
          {dict?.dashboard?.wallet?.recharge?.confirm}
        </button>
      ]}
      {...props}
    >
      <div className="py-4">
        <p className="text-sm text-gray-500 mb-3">{dict?.dashboard?.wallet?.recharge?.tip}</p>
        <div className="relative bg-green-50 rounded-md p-3 mb-6">
          <div className="text-green-800 flex items-center pl-2">
            <span className="mr-2">{currencySymbol}</span>
            <Input
              value={amount}
              onChange={handleAmountChange}
              bordered={false}
              className="bg-transparent text-lg font-medium p-0"
              style={{ boxShadow: 'none' }}
            />
          </div>
        </div>

        <div className="mb-4">
          <p className="text-sm text-gray-500 mb-3">{dict?.dashboard?.wallet?.recharge?.quick}</p>
          <div className="grid grid-cols-3 gap-3">
            {quickAmounts.map((value) => (
              <button
                key={value}
                className="border border-orange-500 text-orange-500 rounded-md py-2 hover:bg-orange-50 transition-colors"
                onClick={() => handleQuickAmountClick(value)}
              >
                {currencySymbol} {value}
              </button>
            ))}
          </div>
        </div>
      </div>
    </ModalComponent>
  );
} 