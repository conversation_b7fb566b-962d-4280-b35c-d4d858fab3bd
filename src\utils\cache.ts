/**
 * Cache management utilities
 */

export const clearAllCache = () => {
  if (typeof window === 'undefined') return;
  
  try {
    // Clear localStorage
    localStorage.clear();
    
    // Clear sessionStorage
    sessionStorage.clear();
    
    // Clear all cookies
    document.cookie.split(";").forEach((cookie) => {
      const eqPos = cookie.indexOf("=");
      const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();
      
      // Clear cookie for current domain
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;
      
      // Also try with leading dot for subdomain cookies
      if (window.location.hostname.includes('.')) {
        const domain = '.' + window.location.hostname.split('.').slice(-2).join('.');
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${domain}`;
      }
    });
    
    console.log('✅ All cache cleared successfully');
    return true;
  } catch (error) {
    console.error('🚨 Error clearing cache:', error);
    return false;
  }
};

export const clearAuthCache = () => {
  if (typeof window === 'undefined') return;
  
  try {
    // Clear auth-related localStorage items
    const authKeys = ['info', 'browse_id', 'selectedCurrency', 'exchangeRates', 'currencySymbol', 'currentExchangeRate'];
    authKeys.forEach(key => {
      localStorage.removeItem(key);
    });
    
    // Clear auth-related cookies
    const authCookies = ['token', 'access_token', 'currency', 'selectedLanguage'];
    authCookies.forEach(cookieName => {
      document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
      document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;
      
      if (window.location.hostname.includes('.')) {
        const domain = '.' + window.location.hostname.split('.').slice(-2).join('.');
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${domain}`;
      }
    });
    
    console.log('✅ Auth cache cleared successfully');
    return true;
  } catch (error) {
    console.error('🚨 Error clearing auth cache:', error);
    return false;
  }
};

export const validateAuthState = () => {
  if (typeof window === 'undefined') return { isValid: false, reason: 'SSR' };
  
  try {
    const isTp6 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '6';
    const tokenName = isTp6 ? 'access_token' : 'token';
    
    // Check localStorage
    const infoStr = localStorage.getItem('info');
    let localStorageToken = null;
    if (infoStr) {
      try {
        const info = JSON.parse(infoStr);
        localStorageToken = info?.data?.userinfo?.token;
      } catch (parseError) {
        console.error('Failed to parse localStorage info:', parseError);
        // Clear invalid data
        localStorage.removeItem('info');
      }
    }
    
    // Check cookies
    let cookieToken = null;
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === tokenName && value) {
        cookieToken = value;
        break;
      }
    }
    
    // Validate consistency
    if (localStorageToken && cookieToken) {
      if (localStorageToken === cookieToken) {
        return { isValid: true, reason: 'consistent' };
      } else {
        return { isValid: false, reason: 'token_mismatch' };
      }
    } else if (localStorageToken || cookieToken) {
      return { isValid: false, reason: 'partial_auth' };
    } else {
      return { isValid: true, reason: 'no_auth' }; // No auth is also a valid state
    }
  } catch (error) {
    console.error('🚨 Error validating auth state:', error);
    return { isValid: false, reason: 'validation_error' };
  }
};

export const fixAuthState = () => {
  const validation = validateAuthState();

  if (!validation.isValid) {
    console.log('🔧 Fixing auth state, reason:', validation.reason);

    switch (validation.reason) {
      case 'token_mismatch':
      case 'partial_auth':
        clearAuthCache();
        return true;
      case 'validation_error':
        clearAllCache();
        return true;
      default:
        return false;
    }
  }

  return false;
};

/**
 * 自动检测并修复缓存问题，如果需要用户干预则重定向到友好页面
 */
export const autoFixCacheIssues = () => {
  if (typeof window === 'undefined') return false;

  try {
    const validation = validateAuthState();

    if (!validation.isValid) {
      console.log('🔧 Auto-fixing cache issues, reason:', validation.reason);

      // 对于严重问题，重定向到友好页面
      if (validation.reason === 'token_mismatch' || validation.reason === 'validation_error') {
        const pathSegments = window.location.pathname.split('/');
        const lng = pathSegments[1] || 'zh-cn';
        const returnUrl = encodeURIComponent(window.location.pathname + window.location.search);

        window.location.href = `/${lng}/cache-refresh?return=${returnUrl}`;
        return true;
      }

      // 对于轻微问题，尝试自动修复
      return fixAuthState();
    }

    return false;
  } catch (error) {
    console.error('🚨 Auto-fix error:', error);

    // 发生错误时重定向到友好页面
    const pathSegments = window.location.pathname.split('/');
    const lng = pathSegments[1] || 'zh-cn';
    const returnUrl = encodeURIComponent(window.location.pathname + window.location.search);

    window.location.href = `/${lng}/cache-refresh?return=${returnUrl}`;
    return true;
  }
};

/**
 * 强制清理所有缓存并重定向到指定页面
 */
export const forceClearAndRedirect = (redirectUrl?: string) => {
  if (typeof window === 'undefined') return;

  try {
    // 清理所有缓存
    clearAllCache();

    // 重定向
    const finalUrl = redirectUrl || window.location.pathname;
    setTimeout(() => {
      window.location.href = finalUrl;
    }, 100);

    return true;
  } catch (error) {
    console.error('🚨 Force clear error:', error);
    // 如果清理失败，至少尝试重新加载页面
    window.location.reload();
    return false;
  }
};
