import Image from 'next/image'
import ButtonComponent from '@/components/Button'
import { useRouter } from 'next/navigation'
import { Api } from '@/request/api'
import ModalComponent from '@/components/Modal'
import { useEffect, useState, useRef } from 'react'
import PhotoViewer from './PhotoViewer'
import Link from 'next/link'
import Toast from '@/components/Toast'
import { sendOrderStatus } from '@/types'
import { Tag, Input } from 'antd'
import Button from '@/components/Button'
import { formatCurrency } from '@/utils/currency'
import CommentModal from './CommentModal'
import { isPluginEnabled } from '@/utils/plugin'

export default function OrderItem({ data, onRefresh, lng, dict,externalApi }: { data: any, onRefresh?: () => void, lng: string, dict: any,externalApi:any }) {
    const router = useRouter()
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [isMessageModalOpen, setIsMessageModalOpen] = useState(false)
    const [message, setMessage] = useState('')
    const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
    data.service && (data._serviceFeeTotal = data.service.reduce((sum: number, item: any) => sum + Number(item.totalfee), 0))
    const [messageList, setMessageList] = useState([])
    const infoStr = localStorage.getItem('info');
    const userInfo: any = infoStr ? JSON.parse(infoStr)?.data?.userinfo : null;
    const [photoList, setPhotoList] = useState([])
    const [photoIsModal, setphotoIsModal] = useState(false)
    const [photoViewerModal, setPhotoViewerModal] = useState(false)
    const [selectedPhotoIndex, setSelectedPhotoIndex] = useState(0)
    const [YQ_TrackModal, setYQ_TrackModal] = useState(false)
    const modelRef = useRef<HTMLDivElement>(null);
    const [commentModalOpen, setCommentModalOpen] = useState(false)
    const [commentPluginEnabled, setCommentPluginEnabled] = useState(false)
    const [hasCommented, setHasCommented] = useState(false)
    const [cancelModalOpen, setCancelModalOpen] = useState(false)

    // 显示取消确认框
    const handleCancel = () => {
        setCancelModalOpen(true)
    }

    // 确认取消订单
    const handleConfirmCancel = async () => {
        try {
            const res = await Api.cancelSendOrder({
                sendorder_id: data.id,
            })
            if (res.success) {
                Toast.success(dict?.dashboard?.packages?.cancelOk)
                onRefresh?.()
            } else {
                Toast.error(res.msg || dict?.dashboard?.packages?.cancelFail)
            }
        } catch (error) {
            Toast.error(dict?.dashboard?.packages?.cancelFail)
        }
        setCancelModalOpen(false)
    }

    // 显示确认收货确认框
    const handleConfirmReceipt = () => {
        setIsModalOpen(true)
    }

    // 确认收货
    const handleConfirm = async () => {
        try {
            const res = await Api.OrderReceipt({
                sendorder_id: data.id,
            })
            if (res.success) {
                Toast.success(dict?.dashboard?.packages?.receiveOk)
                onRefresh?.()
            } else {
                Toast.error(res.msg || dict?.dashboard?.packages?.receiveFai)
            }
        } catch (error) {
            Toast.error(dict?.dashboard?.packages?.receiveFail)
        }
        setIsModalOpen(false)
    }

    const handleMessage = async (sendorder_id: number) => {
        const res = await Api.sendorderReplylist(sendorder_id)
        if (res.success) {
            setIsMessageModalOpen(true)
            // 按时间正序排列留言（最早的在上面，最新的在下面）
            const sortedMessages = res.data.sort((a: any, b: any) => {
                return new Date(a.createtime).getTime() - new Date(b.createtime).getTime()
            })
            setMessageList(sortedMessages)
        }
    }

    const handleMessageSubmit = async () => {
        try {
            const res = isTp5 ? await Api.OrderMessage({
                sendorder_id: data.id,
                message: message
            }) : await Api.sendOrderReply({
                sendorder_id: data.id,
                content: message
            })
            if (res.success) {
                Toast.success(dict?.dashboard?.packages?.msgOk)
                onRefresh?.()
                // 重新获取留言列表以保持时间排序
                const messageRes = await Api.sendorderReplylist(data.id)
                if (messageRes.success) {
                    const sortedMessages = messageRes.data.sort((a: any, b: any) => {
                        return new Date(a.createtime).getTime() - new Date(b.createtime).getTime()
                    })
                    setMessageList(sortedMessages)
                }
            } else {
                Toast.error(res.msg || dict?.dashboard?.packages?.msgFail)
            }
        } catch (error) {
            Toast.error(dict?.dashboard?.packages?.msgFai)
        }
        setIsMessageModalOpen(false)
        setMessage('')
    }

    let colorMap: Record<number, string> = {
        [sendOrderStatus.waitingPayment]: 'gold',
        [sendOrderStatus.paid]: 'green',
        [sendOrderStatus.shipped]: 'orange',
        [sendOrderStatus.invalid]: 'red',
        [sendOrderStatus.pendingReview]: 'blue',
        [sendOrderStatus.delivered]: 'green',
    }
    const handleViewPhoto = async() =>{
        console.log('点击拍照服务，运单ID:', data.id);
        console.log('运单服务数据:', data.service);

        const res = await Api.sendorderPhoto(data.id);
        if(res.success){
            if(res.data.length > 0){
                setPhotoList(res.data);
                setphotoIsModal(true)
            }else{
                setPhotoList([]);
                Toast.info(dict?.dashboard?.packages?.noPhoto || '暂无照片');
            }
        }else{
            Toast.error(res.msg);
        }
    }

    const handlePhotoClick = (index: number) => {
        setSelectedPhotoIndex(index);
        setPhotoViewerModal(true);
        setphotoIsModal(false);
    }

    const handleClosePhotoViewer = () => {
        setPhotoViewerModal(false);
        setphotoIsModal(true);
    }
    const language = localStorage.getItem('selectedLanguage') || 'zh-cn';

    // 检查评论插件状态和评论状态
    useEffect(() => {
        const checkCommentPlugin = async () => {
            try {
                const enabled = await isPluginEnabled('comment');
                setCommentPluginEnabled(enabled);

                // 如果插件启用且是已收货状态，检查是否已评论
                if (enabled && data.status_id === sendOrderStatus['delivered']) {
                    try {
                        const commentResponse = await Api.checkCommentStatus(data.id);
                        if (commentResponse.success && commentResponse.data && commentResponse.data.length > 0) {
                            setHasCommented(true);
                        } else {
                            setHasCommented(false);
                        }
                    } catch (error) {
                        console.error('检查评论状态失败:', error);
                        setHasCommented(false);
                    }
                }
            } catch (error) {
                console.error('检查评论插件状态失败:', error);
                setCommentPluginEnabled(false);
            }
        };
        checkCommentPlugin();
    }, [data.id, data.status_id]);

    const handleComment = () => {
        setCommentModalOpen(true);
    };

    const handleCommentSuccess = () => {
        // 评论成功后标记为已评论，并刷新数据
        setHasCommented(true);
        onRefresh?.();
    };

    const getTrack = (num:number) =>{
        setYQ_TrackModal(true)
        // if(!modelRef.current) return
       setTimeout(()=>{
        externalApi.trackSingle({
            //必须，指定承载内容的容器ID。
            YQ_ContainerId: 'TRNum',
            //可选，指定查询结果高度，最大为800px，默认为560px。
            YQ_Height: 560,
            //可选，指定运输商，默认为自动识别。
            YQ_Fc: '0',
            //可选，指定UI语言，默认根据浏览器自动识别。
            YQ_Lang: language,
            //必须，指定要查询的单号。
            YQ_Num: num,
            theme: 'light'
        });
       },1)
    }
    return (
        <>
            <ModalComponent
                title={dict?.dashboard?.packages?.YQTrackTitle}
                open={YQ_TrackModal}
                onCancel={() => setYQ_TrackModal(false)}
                centered
                footer={null}
            >
              <div ref={modelRef} id="TRNum"  className='w-full min-h-[520px]' />
            </ModalComponent>
            <ModalComponent
                title={dict?.dashboard?.packages?.sendorderPhoto}
                open={photoIsModal}
                onCancel={() => setphotoIsModal(false)}
                centered
                footer={null}
            >
              <div className="grid grid-cols-2 gap-4 mb-6 overflow-y-auto max-h-[500px]" >
                {
                    photoList.map((item: any, index: number) => (
                        <img
                            src={item.image}
                            key={item.id}
                            className="w-full h-36 object-contain rounded-lg cursor-pointer hover:opacity-80 transition-opacity"
                            onClick={() => handlePhotoClick(index)}
                            alt={`运单照片 ${index + 1}`}
                        />
                    ))
                }
              </div>
            </ModalComponent>

            {/* 照片查看器 */}
            <PhotoViewer
                photos={photoList}
                isOpen={photoViewerModal}
                onClose={handleClosePhotoViewer}
                initialIndex={selectedPhotoIndex}
            />
            {/* 确认收货模态框 */}
            <ModalComponent
                title={dict?.dashboard?.packages?.confirmReceipt}
                open={isModalOpen}
                onOk={handleConfirm}
                onCancel={() => setIsModalOpen(false)}
                okText={dict?.dashboard?.packages?.ok}
                centered
                cancelText={dict?.dashboard?.packages?.cancel}
            >
                <p>{dict?.dashboard?.packages?.confirmReceiptMsg}</p>
            </ModalComponent>

            {/* 取消订单确认模态框 */}
            <ModalComponent
                title={dict?.dashboard?.packages?.confirmCancel}
                open={cancelModalOpen}
                onOk={handleConfirmCancel}
                onCancel={() => setCancelModalOpen(false)}
                okText={dict?.dashboard?.packages?.ok}
                centered
                cancelText={dict?.dashboard?.packages?.cancel}
            >
                <p>{dict?.dashboard?.packages?.cancelAsk}</p>
            </ModalComponent>
            <ModalComponent
                title={dict?.dashboard?.packages?.orderMsg}
                open={isMessageModalOpen}
                onOk={handleMessageSubmit}
                onCancel={() => setIsMessageModalOpen(false)}
                okText={dict?.dashboard?.packages?.submit}
                centered
                cancelText={dict?.dashboard?.packages?.cancel}
                footer={null}
            >
                {/* <Input.TextArea 
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder={dict?.dashboard?.packages?.msgHint}
                    rows={4}
                /> */}
                <div>
                    {/* 留言列表区域 */}
                    <div className="p-6 max-h-[60vh] overflow-y-auto">
                        <div className="space-y-6" id="messageList">
                            {messageList.length === 0 ? (
                                <div className="text-center text-gray-500 py-8">
                                    {dict?.dashboard?.packages?.noMessages || '暂无留言'}
                                </div>
                            ) : (
                                messageList.map((remark: any, index: number) => (
                                <div key={index}>
                                    {remark.admin_id == 0 ? (
                                        <div className="flex gap-4 justify-end">
                                            <div className="w-80 flex px-4 py-2 border border-gray-200 rounded-lg">
                                                <div className="flex-1">
                                                    <div className="flex items-center justify-between mb-2">
                                                        <span className="text-sm text-gray-500">{remark.createtime}</span>
                                                    </div>
                                                    <p className="text-gray-700 text-sm">{remark.content}</p>
                                                </div>
                                                {remark?.userinfo?.avatar ? (
                                                    <div className="w-10 h-10 rounded-full flex">
                                                        <img src={remark?.userinfo?.avatar} alt="User avatar" className="rounded-full" />
                                                    </div>
                                                ) : (
                                                    <div
                                                        className="rounded-full overflow-hidden relative bg-orange-500 flex items-center justify-center text-white text-2xl font-semibold w-10 h-10"
                                                        id="avatarContainer"
                                                    >
                                                        <div className="absolute inset-0 flex items-center justify-center" id="avatarLetter">
                                                            {remark?.userinfo?.username?.charAt(0).toUpperCase()}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="flex gap-4 w-80 px-4 py-2 border border-gray-200 rounded-lg">
                                            {remark?.userinfo?.avatar ? (
                                                <div className="w-10 h-10 rounded-full flex">
                                                    <img src={remark?.userinfo?.avatar} alt="User avatar" className="rounded-full" />
                                                </div>
                                            ) : (
                                                <div
                                                    className="rounded-full overflow-hidden relative bg-orange-500 flex items-center justify-center text-white text-2xl font-semibold w-10 h-10"
                                                    id="avatarContainer"
                                                >
                                                    <div className="absolute inset-0 flex items-center justify-center" id="avatarLetter">
                                                        {remark?.userinfo?.username?.charAt(0).toUpperCase()}
                                                    </div>
                                                </div>
                                            )}
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between mb-2">
                                                    <div>
                                                        <span className="font-medium text-gray-900">{remark?.userinfo?.username}</span>
                                                    </div>
                                                    <span className="text-sm text-gray-500">{remark.createtime}</span>
                                                </div>
                                                <p className="text-gray-700 text-sm">{remark.content}</p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                                ))
                            )}
                        </div>
                    </div>
                    {/* 发送留言区域  */}
                    <div className="px-6 py-4 border-t bg-gray-50 rounded-lg">
                        <div className="flex gap-4">
                            <textarea
                                id="messageInput"
                                value={message}
                                onChange={(e) => setMessage(e.target.value)}
                                className="flex-1 h-20 p-3 border border-gray-200 rounded-lg resize-none focus:outline-none focus:border-orange-500 text-sm"
                                placeholder={dict.dashboard.orders.list.inputHint}
                            ></textarea>
                            <button
                                onClick={handleMessageSubmit}
                                className="px-6 h-10 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors whitespace-nowrap self-end"
                            >
                                {dict.dashboard.orders.list.sendMessage}
                            </button>
                        </div>
                    </div>
                </div>
            </ModalComponent>
            <div className="bg-white rounded-lg mb-6 px-6 py-4 shadow-sm">
                <div className="flex items-center text-[#86909C] text-sm mb-4">
                    <span>{dict?.dashboard?.packages?.packageNo}：{data.sn}</span>
                    <span className="ml-8">{dict?.dashboard?.packages?.createDay}：{data.createtime}</span>
                </div>

                {/* Grid layout for better alignment */}
                <div className="grid grid-cols-12 gap-4 items-center">
                    {/* Product Info Section - 5 columns */}
                    <div className="col-span-5">
                        <div className="flex flex-col gap-4">
                            {data.goods.map((g: any, idx: number) => (
                                <div className="flex items-center" key={g.id}>
                                    <img
                                        src={g.goodsimg}
                                        alt=""
                                        className="w-16 h-16 rounded-lg object-cover mr-4 flex-shrink-0"
                                    />
                                    <div className="flex-1 min-w-0">
                                        <div className="font-semibold text-base text-[#1D2129] truncate">{g.goodsname}</div>
                                        <div className="text-[#86909C] text-sm truncate">{g.cn_goodsname}</div>
                                    </div>
                                    {idx !== data.goods.length - 1 && (
                                        <div className="h-10 w-px bg-[#F2F3F5] mx-4 flex-shrink-0" />
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Weight/Volume & Status Section - 3 columns */}
                    <div className="col-span-2 flex flex-col items-center justify-center text-center">
                        <div className="mb-3">
                            <div className="font-bold text-lg text-[#1D2129] mb-1">{data.countweight}g</div>
                            <div className="text-[#86909C] text-sm">{data.countvolume}cm³</div>
                        </div>
                        <div className="flex flex-col items-center gap-2">
                            {isTp5 && (
                                <span className="inline-block bg-[#FFF7E6] text-[#FAAD14] rounded px-3 py-1 text-sm whitespace-nowrap">
                                    {data.shippingname}
                                </span>
                            )}
                            <span className="inline-block bg-[#FFF7E6] text-[#FAAD14] rounded px-3 py-1 text-sm whitespace-nowrap">
                                {data.status_text}
                            </span>
                        </div>
                    </div>
                    <div className='col-span-1 text-center' >
                        {data.expressno ? <button className='text-gray-400 hover:text-red-500 transition-colors' onClick={()=>getTrack(data.expressno)}>
                                <i className='fas fa-truck'></i>
                        </button>:'--'}
                    </div>
                    {/* Price Section - 2 columns */}
                    <div className="col-span-2 text-center">
                        <div className="text-2xl font-bold text-[#1D2129] mb-3">{formatCurrency(Number(data.totalmoney)).formatValue}</div>
                        <div className="space-y-1 text-sm">
                            <div className="text-[#86909C]">
                                {dict?.dashboard?.packages?.shipFee} {formatCurrency(Number(data.freight)).formatValue}
                            </div>
                            <div className="text-[#86909C]">
                                {dict?.dashboard?.packages?.extraFee} {formatCurrency(Number(data.serverfee)).formatValue}
                            </div>
                            <div className="text-[#86909C] text-sm mb-3" style={{ display: data.service.length ? 'block' : 'none' }}>
                                {dict?.dashboard?.packages?.additionalFee} {formatCurrency(Number(data._serviceFeeTotal)).formatValue}
                            </div>

                        </div>
                    </div>

                    {/* Actions Section - 2 columns */}
                    <div className="col-span-2 flex flex-col gap-2 items-center">
                        {data.status_id === sendOrderStatus['shipped'] && (
                            <Button type="primary" className="!h-10 !px-4 !w-full !max-w-[120px]" onClick={handleConfirmReceipt}>
                                {dict?.dashboard?.packages?.confirmGet}
                            </Button>
                        )}
                        {data.status_id === sendOrderStatus['waitingPayment'] && (
                            <Button type="primary" className="!h-10 !px-4 !w-full !max-w-[120px]" onClick={() => router.push(`/${lng}/pay?order_id=${data.id}&type=package`)}>
                                {dict?.dashboard?.packages?.payment}
                            </Button>
                        )}
                        {/* 评论按钮 - 已收货状态且评论插件开启且未评价时显示 */}
                        {data.status_id === sendOrderStatus['delivered'] && commentPluginEnabled && !hasCommented && (
                            <Button className="!h-10 !px-4 !w-full !max-w-[120px]" onClick={handleComment}>
                                {dict?.dashboard?.packages?.comment}
                            </Button>
                        )}
                        <Button className="!h-10 !px-4 !w-full !max-w-[120px]" onClick={() => { handleMessage(data.id) }}>
                            {dict?.dashboard?.packages?.leaveMsg}
                        </Button>
                        {(data.status_id === sendOrderStatus['pendingReview'] || data.status_id === sendOrderStatus['waitingPayment']) && (
                            <Button className="!h-10 !px-4 !w-full !max-w-[120px]" onClick={handleCancel}>
                                {dict?.dashboard?.packages?.cancelOrder}
                            </Button>
                        )}
                    </div>
                </div>
                <div className='flex align-center'>
                    {
                        data.service.map((item: any) => {
                            // 支持多种服务名称格式
                            const serviceName = item.name || item.name_code;
                            const isPhotoService = serviceName === 'photo';

                            return (
                                <p
                                    key={item.id}
                                    className={`inline-flex items-center mr-2 px-2.5 py-1 bg-orange-50/50 text-orange-500 rounded-full text-xs font-medium border border-orange-100 ${
                                        isPhotoService ? 'cursor-pointer hover:bg-orange-100 transition-colors' : ''
                                    }`}
                                    onClick={isPhotoService ? handleViewPhoto : undefined}
                                >
                                    {isPhotoService && <i className="fas fa-camera mr-1"></i>}
                                    {dict?.confirm.server[serviceName] || item.title || serviceName}
                                </p>
                            );
                        })
                    }

                </div>
            </div>

            {/* 评论模态框 */}
            <CommentModal
                open={commentModalOpen}
                onCancel={() => setCommentModalOpen(false)}
                sendorderId={data.id}
                dict={dict}
                onSuccess={handleCommentSuccess}
            />
        </>
    )
}
