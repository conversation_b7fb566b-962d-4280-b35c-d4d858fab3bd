

export enum OrderStatus {
    pendingReview = 0,      // "未审核"
    waitingPayment = 10,    // "待付款"
    paid = 20,              // "已付款"
    shipped = 25,           // "已邮寄"
    delivered = 29,         // "已收货"
    invalid = 30,           // "无效单"
    awaitingSupplement  = 130 // "待补款"
}
export enum sendOrderStatus {
    pendingReview = 0,      // "未审核"
    waitingPayment = 10,    // "待付款"
    paid = 20,              // "已付款"
    shipped = 30,           // "已发货"
    delivered = 40,         // "已收货"
    invalid = 50            // "无效单"
}

export enum OrderStatusText {
    "待付款" = "10",
    "已付款" = "20",
    "待采购" = "25",
    "处理中" = "30",
    "未发货" = "35",
    "已邮寄" = "40",
    "已入库" = "50",
    "可提交运单" = "60",
    "已提交" = "70",
    "无效单" = "100",
    "退款中" = "110",
    "退款成功" = "120",
    "待补款" = "130"
}
export interface LoginParams {
    account: string;
    password: string;
    is_remember?: number;  // 记住登录状态
    captcha?: string;      // 邮箱验证码（当mail_verify_type需要时）
}

export interface RegisterParams {
    email: string;      // 注册邮箱
    password: string;   // 登录密码(MD5加密)
    captcha?: string;   // 邮箱验证码（可选，取决于后台配置）
    inviter?: string;   // 邀请者名称（可选）
    clienttype: string; // 设备名称,后期转为header参数
    username?: string;  // 用户名（可选）
    mobile?: string;    // 验证手机号（可选，根据配置注册验证类型选择）
    mobilecode?: string; // 验证手机区号（可选，根据配置注册验证类型选择）
}

export interface AddressParams {
    consignee: string;      // 收货人
    telephone: string;      // 手机号码
    area_id: number;        // 地区ID
    address: string;        // 详细地址
    zipcode: string;        // 邮编
    isdefault: number;      // 是否默认 1是 0否
    door_number?: string;   // 门牌号
    telephone2?: string;    // 备用电话
    tax_number?: string;    // 税号
    address_tag?: string;   // 地址标签
    country_id: number;     // 国家ID
    custom_field?: object;  // 自定义字段【配合相关插件】
}

export interface UserInfo {
    id: number;
    username: string;
    nickname: string;
    email: string;
    mobilecode: string;
    mobile: string;
    avatar: string;
    level: number;
    gender: number;
    birthday: string;
    bio: string;
    money: string;
    score: number;
    status: string;
    rebate: string;
    spread_id: number;
    experience: number;
    qrcode_url: string | null;
    token: string;
    user_id: number;
    createtime: number;
    expiretime: number;
    expires_in: number;
    inviteCode?: string;
}

export interface ResponseData<T> {
    success: Boolean;
    data: T;
    code: number;
    msg: string;
    time?: string;
}


