'use client';

import React, { useState, useEffect, memo, useCallback } from 'react';
import { Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { Api } from '@/request/api';
import ProductCard from '@/components/ProductCard';
import Loading from '@/components/Loading';

interface GuessProduct {
  num_iid: string;
  title: string;
  pic_url: string;
  price: number;
  promotion_price: number;
  orginal_price: number;
  detail_url: string;
  area: string;
  recommend?: number | string; // 推荐标识字段
}

interface GuessYouLikeProps {
  dict: any;
  lng: string;
}

const GuessYouLike: React.FC<GuessYouLikeProps> = ({ dict, lng }) => {
  const [products, setProducts] = useState<GuessProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [pluginEnabled, setPluginEnabled] = useState(false);

  // 获取猜你喜欢数据的函数
  const fetchGuessData = useCallback(async () => {
    try {
      const response = await Api.getGuessGoodsList({ size: 10 });

      if (response.success) {
        setProducts(response.data || []);
      }
    } catch (error) {
      console.error('获取猜你喜欢数据失败:', error);
    }
  }, []);

  // 换一批按钮点击处理
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchGuessData();
    } finally {
      setRefreshing(false);
    }
  }, [fetchGuessData]);

  useEffect(() => {
    const checkPluginAndFetchData = async () => {
      try {
        // 检查like_to_guess插件是否启用
        const { isPluginEnabled: checkPlugin } = await import('@/utils/plugin');
        const isEnabled = await checkPlugin('guessgoods');

        if (isEnabled) {
          setPluginEnabled(true);
          // 插件启用时才获取数据
          await fetchGuessData();
        } else {
          setPluginEnabled(false);
        }
      } catch (error) {
        console.error('获取猜你喜欢数据失败:', error);
        setPluginEnabled(false);
      } finally {
        setLoading(false);
      }
    };

    checkPluginAndFetchData();
  }, []);

  // 如果正在加载或插件未启用，不显示组件
  if (loading || !pluginEnabled) {
    return loading ? (
      <div className="bg-white rounded-lg shadow-sm p-6 mt-6">
        <h3 className="text-lg font-medium mb-4 text-gray-800">
          {dict?.dashboard?.cart?.guessYouLike || '猜你喜欢'}
        </h3>
        <div className="flex justify-center items-center py-8">
          <Loading height="120px" />
        </div>
      </div>
    ) : null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 ">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-800">
          {dict?.dashboard?.cart?.guessYouLike || '猜你喜欢'}
        </h3>
        <Button
          type="text"
          icon={<ReloadOutlined />}
          loading={refreshing}
          onClick={handleRefresh}
          className="text-blue-500 hover:text-blue-600"
        >
          {dict?.dashboard?.cart?.refresh || '换一批'}
        </Button>
      </div>

      {products.length === 0 ? (
        <div className="text-center text-gray-500 py-12">
          {dict?.dashboard?.cart?.emptyList || '列表为空'}
        </div>
      ) : (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4">
          {products.map((item, index) => (
            <ProductCard
              key={item.num_iid || item.pic_url || index}
              product={{
                title: item.title,
                pic_url: item.pic_url,
                detail_url: item.detail_url,
                price: item.price.toString(),
                promotion_price: item.promotion_price.toString(),
                nick: item.area,
                recommend: item.recommend // 传递推荐字段
              }}
              platform="taobao"
              dict={dict}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default memo(GuessYouLike);
