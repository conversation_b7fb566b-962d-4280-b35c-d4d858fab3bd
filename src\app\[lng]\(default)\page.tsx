import Image from "next/image";
import { getDictionary } from "@/dictionaries";
import type { Locale } from "@/config";
import { ReactNode } from "react";
import SearchBar from "@/components/Search";
import CarouselMini from "@/components/CarouselMini";
import HotWords from "@/components/Search/HotWords";
import HotProducts from "@/components/HotProducts";
import ShoppingTime from "@/components/ShoppingTime";

import { getServerData } from '@/request/server';
import React from 'react';
import { Carousel } from 'antd';
interface FeatureItemProps {
  icon: ReactNode;
  title: string;
  description: string;
}

interface AdItem {
  data?: any[];
}

interface ApiResponse {
  data?: any[];
}

const FeatureItem = ({ icon, title, description }: FeatureItemProps) => (
  <div className="text-center">
    <div className="text-[var(--base-color)] text-5xl mb-4 flex justify-center">
      {icon}
    </div>
    <h3 className="text-lg font-bold mb-2 text-[var(--base-color)]">{title}</h3>
    <p className="text-sm text-gray-600">   <div dangerouslySetInnerHTML={{ __html: description }} /></p>
  </div>
);

export default async function Home({
  params
}: {
  params: Promise<{ lng: Locale }>
}) {
  try {
    const { lng } = await params;
    const dict = await getDictionary(lng);

    // Use Promise.allSettled to handle individual API failures gracefully
    const [slideAdResult, flagshipAdResult, homeServiceResult] = await Promise.allSettled([
      getServerData('/web/cms/ads/ads_list', 'POST', { mark: "slide_01", num: 10 }),
      getServerData('/web/cms/ads/ads_list', 'POST', { mark: "flagship_ad_01", num: 10 }),
      getServerData('/web/cms/ads/ads_list', 'POST', { mark: "home_service_01", num: 4 })
    ]);

    // Extract data with fallbacks
    const slideAd: ApiResponse = slideAdResult.status === 'fulfilled' ? slideAdResult.value as ApiResponse : { data: [] };
    const flagshipAd: ApiResponse = flagshipAdResult.status === 'fulfilled' ? flagshipAdResult.value as ApiResponse : { data: [] };
    const homeService: ApiResponse = homeServiceResult.status === 'fulfilled' ? homeServiceResult.value as ApiResponse : { data: [] };

    // Log any failures for debugging
    if (slideAdResult.status === 'rejected') {
      console.error('🚨 Failed to load slide ads:', slideAdResult.reason);
    }
    if (flagshipAdResult.status === 'rejected') {
      console.error('🚨 Failed to load flagship ads:', flagshipAdResult.reason);
    }
    if (homeServiceResult.status === 'rejected') {
      console.error('🚨 Failed to load home service ads:', homeServiceResult.reason);
    }

    // Safely process carousel items with null checks
    let carouselItems: any[] = [];
    if (flagshipAd?.data && Array.isArray(flagshipAd.data)) {
      carouselItems = flagshipAd.data.map((item: any) => {
        // Add safety checks for nested properties
        const lang = item?.lang?.[0];
        if (!lang) return null;

        return {
          title: lang.title || '',
          description: lang.content || '',
          image: lang.imageurl || '',
          linkurl: item.linkurl || '',
          target: item.target || '_self',
          color: "bg-blue-500"
        }
      }).filter(Boolean); // Remove null items
    }

    return (
    <div className="min-h-screen relative ">
      {/* 主页横幅 */}
      <div className="relative overflow-hidden">
        <div className="absolute w-full h-[680px] z-[-1]">
          <Carousel autoplay dots={false} autoplaySpeed={7000}>
            {slideAd?.data && Array.isArray(slideAd.data) && slideAd.data.length > 0 ?
              slideAd.data.map((item: any, index: number) => (
                <div key={index} className="w-full h-full">
                  {item?.lang && Array.isArray(item.lang) && item.lang.length > 0 &&
                    item.lang.map((lang: any, langIndex: number) => (
                      <div key={langIndex} className="relative h-[680px]">
                        <img
                          src={lang?.imageurl || '/placeholder-image.jpg'}
                          alt={lang?.title || 'Slide image'}
                          className="h-[680px] bg-cover bg-center w-full object-cover"
                        />
                        <div className="absolute inset-0 bg-black/25" />
                      </div>
                    ))
                  }
                </div>
              )) : (
                // Fallback slide when no data is available
                <div className="w-full h-full">
                  <div className="relative h-[680px] bg-gradient-to-r from-blue-500 to-purple-600">
                    <div className="absolute inset-0 bg-black/25" />
                  </div>
                </div>
              )
            }
          </Carousel>
        </div>
        <div className="h-[680px] bg-cover bg-center">
          <div className="container mx-auto px-4 py-20 text-white flex flex-col items-start justify-center h-full">
            <h1 className="text-3xl md:text-5xl font-bold lg:text-5xl mb-10 w-full">{dict.home.banner.title}</h1>
            <SearchBar dict={dict} />
            <HotWords
              hotSearchLabel={dict.search.hotSearch}
            />
          </div>
        </div>


      </div>
      {/* Service cards area */}
      <div className="container mx-auto px-4 py-12">
        <CarouselMini carouselItems={carouselItems} />
      </div>

      {/* One-stop shopping service */}
      <div className="container mx-auto px-4 py-12">
        <h2 className="text-2xl font-bold text-center mb-12 relative after:content-[''] after:absolute after:bottom-[-10px] after:left-1/2 after:transform after:translate-x-[-50%] after:w-20 after:h-1 after:bg-[var(--base-color)]">{dict.home.services.title}</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8">
          {homeService?.data && Array.isArray(homeService.data) && homeService.data.length > 0 ?
            homeService.data.map((item: any, index: number) => {
              // Safety check for item structure
              const lang = item?.lang?.[0];
              if (!lang) return null;

              return (
                <div key={index} className="relative">
                  <FeatureItem
                    icon={
                      <Image
                        src={lang.imageurl || '/placeholder-icon.png'}
                        alt={lang.title || 'Service icon'}
                        width={200}
                        height={200}
                        className="text-[var(--base-color)] rounded-full"
                        priority
                        draggable={false}

                      />
                    }
                    title={lang.title || ''}
                    description={lang.content || ''}
                  />
                  {/* Arrow separator */}
                  {index < (homeService.data?.length || 0) - 1 && (
                    <div className="hidden md:flex absolute right-[-35px] top-[100px] transform -translate-x-1/2 items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="var(--base-color)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M5 12h14"></path>
                        <path d="m12 5 7 7-7 7"></path>
                      </svg>
                    </div>
                  )}
                </div>
              );
            }).filter(Boolean) : (
              // Fallback content when no services are available
              <div className="col-span-full text-center py-8">
                <p className="text-gray-500">{dict?.home?.services?.noData}</p>
              </div>
            )
          }
        </div>
      </div>
      <ShoppingTime dict={dict} />
      <HotProducts dict={dict} />

    </div>
  );
} catch (error) {
    console.error('Homepage Error:', error);

    // Get language for redirect
    const { lng } = await params;
    const dict = await getDictionary(lng);

    // Redirect to friendly cache refresh page
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center px-4 bg-white rounded-2xl shadow-xl p-8 max-w-md">
          <div className="text-blue-500 text-6xl mb-4">
            🔄
          </div>
          <h1 className="text-2xl font-bold mb-4 text-gray-800">
            {dict?.home?.error?.title}
          </h1>
          <p className="text-gray-600 mb-6 leading-relaxed">
            {dict?.home?.error?.description}
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-blue-700 text-sm">
              {dict?.home?.error?.autoRedirect}
            </p>
          </div>
          <div className="mt-6">
            <a
              href={`/${lng}/cache-refresh?return=${encodeURIComponent(`/${lng}`)}`}
              className="inline-block bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors"
            >
              {dict?.home?.error?.goToOptimize}
            </a>
          </div>
          <script dangerouslySetInnerHTML={{
            __html: `
              setTimeout(function() {
                window.location.href = '/${lng}/cache-refresh?return=' + encodeURIComponent('/${lng}');
              }, 2000);
            `
          }} />
        </div>
      </div>
    );
  }
}
