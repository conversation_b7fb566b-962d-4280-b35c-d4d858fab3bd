'use client'

import { Api } from '@/request/api'
import { useState, useEffect } from 'react'
import message from '@/components/CustomMessage';

interface AnnoProps {
    dict: any
}

interface NewsType {
    id: number;
    title: string;
    content: string;
    createtime: string;
    updatetime: string;
}

export default function Announcement({ dict }: AnnoProps) {
    const [news, setNews] = useState<NewsType | null>(null);
    const [newsList, setNewsList] = useState<Array<NewsType>>([]);
    const [page, setPage] = useState<number>(0);

    useEffect(() => {
        getAnnouncement();
    }, []);

    const getAnnouncement = async () => {
        try {
            const res = await Api.newsList();
            if (res.success && res.data.length > 0) {
                setNewsList(res.data);
                setNews(res.data[0]); 
            } else {
                setNewsList([]);
                setNews(null); 
            }
        } catch (error) {
            console.error('Failed to fetch announcements:', error);
            message.error('Failed to load announcements');
        }
    };

    const onNextNotice = () => {
        if (newsList.length === 0) return; 
        const nextPage = (page + 1) % newsList.length;
        setPage(nextPage);
        setNews(newsList[nextPage]);
    };

    return (
        <div className="announcement-container">
            {/* Decorative circles - 360浏览器兼容 */}
            <div className="announcement-decoration top-right"></div>
            <div className="announcement-decoration bottom-left"></div>

            {/* Next notice button - 360浏览器兼容 */}
            <button
                className="announcement-next-btn"
                onClick={onNextNotice}
                disabled={newsList.length <= 1}
            >
                <i className="fas fa-chevron-right"></i>
            </button>

            {/* Main content - 360浏览器兼容 */}
            <div className="announcement-content">
                <div className="announcement-icon">
                    <i className="fas fa-bell text-white text-xl"></i>
                </div>
                <div className="announcement-text">
                    <h3 className="text-xl font-bold text-white mb-3">{dict.dashboard.home.anno.title}</h3>
                    {news ? (
                        <div className="announcement-box">
                            <p className="text-white font-medium mb-2">{news.title}</p>
                            <div className="text-white/90" dangerouslySetInnerHTML={{ __html: news.content }} />
                        </div>
                    ) : (
                        <div className="announcement-box flex flex-col items-center justify-center p-6">
                            <i className="fas fa-bell text-white/50 text-[100px] mb-6"></i>
                            <p className="text-white/70 text-base mb-2">{dict.dashboard.home.anno.empty}</p>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}