/* 楼层分类标签页全局样式 */
.floor-tabs-container .ant-tabs-nav {
  background: linear-gradient(to right, #f97316, #ea580c) !important;
  padding: 8px 16px !important;
  border-radius: 8px 8px 0 0 !important;
  margin-bottom: 0 !important;
}

.floor-tabs-container .ant-tabs-nav::before {
  display: none !important;
}

.floor-tabs-container .ant-tabs-nav-wrap {
  padding: 0 !important;
}

.floor-tabs-container .ant-tabs-tab {
  font-weight: 500 !important;
  color: rgba(255, 255, 255, 0.8) !important;
  padding: 12px 20px !important;
  font-size: 16px !important;
  border-radius: 8px 8px 0 0 !important;
  margin-right: 4px !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-bottom: none !important;
  transition: all 0.3s ease !important;
}

.floor-tabs-container .ant-tabs-tab:hover {
  color: white !important;
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
}

.floor-tabs-container .ant-tabs-tab-active {
  color: #f97316 !important;
  font-weight: 600 !important;
  background: white !important;
  border-color: white !important;
  position: relative !important;
  z-index: 1 !important;
}

.floor-tabs-container .ant-tabs-ink-bar {
  display: none !important;
}

.floor-tabs-container .ant-tabs-content-holder {
  padding: 0 !important;
  background: white !important;
}

.floor-tabs-container .ant-tabs-extra-content {
  margin-left: 16px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floor-tabs-container .ant-tabs-tab {
    padding: 8px 16px !important;
    font-size: 14px !important;
  }
  
  .floor-tabs-container .ant-tabs-nav {
    padding: 6px 12px !important;
  }
  
  .floor-tabs-container .ant-tabs-extra-content {
    margin-left: 12px !important;
  }
}
