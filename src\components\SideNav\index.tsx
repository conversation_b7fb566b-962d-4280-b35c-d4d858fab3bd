"use client";

import React, { useEffect, useState } from "react";
import { Tooltip } from "antd";
import { Api } from "@/request/api";

export default function SideNav() {
  const [configList, setConfigList] = useState<any>({});
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5';

  // 获取配置数据
  useEffect(() => {
    const fetchConfigList = async () => {
      try {
        const response = isTp5 ? await Api.getConfigList() : await Api.getConfigList();
        if (response?.success || response) {
          const config = response?.data || response;
          setConfigList(config);
        }
      } catch (error) {
        console.error('Failed to load config in SideNav:', error);
        setConfigList({});
      }
    };

    fetchConfigList();
  }, [isTp5]);

  let sideNavList = configList?.side_list;
  if (!sideNavList || sideNavList.length === 0) {
    return null;
  }
  return (
    <div>
      <div className="fixed right-6 top-1/2 -translate-y-1/2 z-50 flex flex-col bg-white rounded-lg shadow-lg">
        {sideNavList.map((item: any, index: number) => (
          <div
            key={index}
            className={`w-12 h-12 flex items-center justify-center ${
              index !== sideNavList.length - 1 ? "border-b border-gray-100" : ""
            }`}
          >
            <Tooltip title={item.name} placement="left" color="#FF6B00">
              <a
                href={item.linkurl}
                target={item.target}
                className="flex flex-col items-center"
              >
                <img src={item.icon} alt={item.name} width={24} height={24} />
              </a>
            </Tooltip>
          </div>
        ))}
      </div>
    </div>
  );
}
