'use client'
import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react'
import { Api, AddressItem } from '@/request/api'
import { message, App } from 'antd'
import AddressModal from '@/components/AddressModal'
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { useParams, useSearchParams, useRouter } from 'next/navigation';
import { getDictionary } from "@/dictionaries";

interface OnePayAddressCardProps {
  dict: any;
}

const OnePayAddressCard = forwardRef<any, OnePayAddressCardProps>(({ dict }, ref) => {
  const [addressList, setAddressList] = useState<AddressItem[]>([]);
  const [selectedAddress, setSelectedAddress] = useState<AddressItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [showAddressModal, setShowAddressModal] = useState(false);
  const [currentAddress, setCurrentAddress] = useState<AddressItem | null>(null);
  const { modal } = App.useApp();
  const { message: messageApi } = App.useApp();
  const searchParams = useSearchParams();
  const router = useRouter();

  // Get address list on component mount
  useEffect(() => {
    const fetchAddressList = async () => {
      try {
        const response = await Api.getAddressList();

        if (response && response.success && response.data) {
          const data = response.data;
          if (Array.isArray(data) && data.length > 0) {
            let list = data.sort((a: any, b: any) => b.isdefault - a.isdefault)
            setAddressList(list)

            // Check if there's a pre-selected address from URL params
            const addressIdFromUrl = searchParams.get('address_id');
            if (addressIdFromUrl) {
              const preSelectedAddress = list.find((addr: AddressItem) => addr.id === Number(addressIdFromUrl));
              if (preSelectedAddress) {
                setSelectedAddress(preSelectedAddress);
              } else {
                setSelectedAddress(list[0]);
                updateUrlParams(list[0].id);
              }
            } else {
              // Default to first address (usually default address)
              setSelectedAddress(list[0]);
              updateUrlParams(list[0].id);
            }
          } else {
            console.log('No addresses found');
            setAddressList([]);
          }
        } else {
          console.error('Failed to fetch address list:', response);
          messageApi.error('获取地址列表失败');
          setAddressList([]);
        }
      } catch (error) {
        console.error('Error fetching address list:', error);
        messageApi.error('获取地址列表失败');
        setAddressList([]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchAddressList();
  }, []);

  const updateUrlParams = (addressId: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('address_id', addressId.toString());
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
  };

  const handleSelectAddress = (address: AddressItem) => {
    setSelectedAddress(address);
    updateUrlParams(address.id);
  };

  const handleEdit = (item: AddressItem, e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentAddress(item);
    setShowAddressModal(true);
  };

  const handleDelete = async (item: AddressItem, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await Api.deleteAddress(item.id);
      messageApi.success(dict?.dashboard?.address?.deleteSuccess);
      
      // Refresh address list
      const { data } = await Api.getAddressList();
      if (data.length > 0) {
        let list = data.sort((a: any, b: any) => b.isdefault - a.isdefault);
        setAddressList(list);
        
        // If deleted address was selected, select first address
        if (selectedAddress && selectedAddress.id === item.id) {
          setSelectedAddress(list[0]);
          updateUrlParams(list[0].id);
        }
      } else {
        setAddressList([]);
        setSelectedAddress(null);
      }
    } catch (error) {
      messageApi.error(dict?.dashboard?.address?.deleteFail);
    }
  };

  const handleAddNew = () => {
    setCurrentAddress(null);
    setShowAddressModal(true);
  };

  // 暴露方法给父组件通过 ref 调用
  useImperativeHandle(ref, () => ({
    openAddressModal: handleAddNew
  }));

  const handleCloseModal = () => {
    setShowAddressModal(false);
    setCurrentAddress(null);
  };

  if (loading) {
    return (
      <div className="bg-white p-6 rounded-lg">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-20 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg">


      <AddressModal
        open={showAddressModal}
        onCancel={handleCloseModal}
        onSuccess={() => {
          handleCloseModal();
          const fetchAddressList = async () => {
            const { data } = await Api.getAddressList();
            if (data.length > 0) {
              let list = data.sort((a: any, b: any) => b.isdefault - a.isdefault)
              setAddressList(list)
              // If adding new address, select the first one
              if (!currentAddress) {
                setSelectedAddress(list[0])
                updateUrlParams(list[0].id)
              }
            }
          };
          fetchAddressList();
        }}
        currentAddress={currentAddress}
        dict={dict}
      />
      
      <div className="flex flex-wrap gap-4">
        {addressList.map((item: AddressItem) => (
          <div
            key={item.id}
            onClick={() => handleSelectAddress(item)}
            className={`
              relative bg-white rounded-lg transition-all duration-300 ease-in-out
              hover:shadow-md cursor-pointer w-[calc(33.33%-16px)] p-4 border
              ${selectedAddress && selectedAddress.id === item.id
                ? 'border-[#FF6000] border-2 shadow-md'
                : 'border-gray-200'
              }
            `}
          >
            {/* 选中状态圆圈 - 右上方 */}
            {selectedAddress && selectedAddress.id === item.id && (
              <div className="absolute top-3 right-3 w-6 h-6 bg-[#FF6000] rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}

            {/* 默认地址标签 - 左下方 */}
            {item.isdefault === 1 && (
              <div className="absolute bottom-3 left-3 bg-[#FF6000] text-white text-xs px-2 py-1 rounded">
                {dict?.confirm?.onepayorder?.address?.default || 'Default'}
              </div>
            )}

            {/* Address content */}
            <div className="space-y-2 pr-8 pb-8">
              <div className="font-medium text-gray-900">
                {item.consignee} {item.telephone}
              </div>
              <div className="text-sm text-gray-600 line-clamp-2">
                {item.address}
              </div>
            </div>
            
            {/* Action buttons */}
            <div className="absolute bottom-2 right-2 flex space-x-2">
              <button
                onClick={(e) => handleEdit(item, e)}
                className="text-gray-400 hover:text-[#FF6000] transition-colors"
              >
                <EditOutlined className="text-sm" />
              </button>
              <button
                onClick={(e) => handleDelete(item, e)}
                className="text-gray-400 hover:text-red-500 transition-colors"
              >
                <DeleteOutlined className="text-sm" />
              </button>
            </div>
          </div>
        ))}
      </div>
      
      {addressList.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>{dict?.confirm?.onepayorder?.address?.noAddressesFound || 'No addresses found. Please add a shipping address.'}</p>
          <button
            onClick={handleAddNew}
            className="mt-2 text-[var(--base-color)] hover:text-[var(--base-color-hover)]"
          >
            {dict?.confirm?.onepayorder?.address?.addNewAddress || 'Add Address'}
          </button>
        </div>
      )}
    </div>
  );
});

OnePayAddressCard.displayName = 'OnePayAddressCard';

export default OnePayAddressCard;
