'use client'

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { validateAuthState, fixAuthState } from '@/utils/cache';

export default function CacheValidator() {
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Don't run on the cache-refresh page itself
    if (pathname.includes('/cache-refresh')) return;

    // Add a small delay to ensure all components are mounted
    const timer = setTimeout(() => {
      try {
        const validation = validateAuthState();

        if (!validation.isValid) {
          console.log('🔧 Cache validation failed:', validation.reason);

          // For serious issues, redirect to friendly cache refresh page
          if (validation.reason === 'token_mismatch' || validation.reason === 'validation_error') {
            const pathSegments = pathname.split('/');
            const lng = pathSegments[1] || 'zh-cn';
            const returnUrl = encodeURIComponent(pathname + window.location.search);

            console.log('🔄 Redirecting to cache refresh page...');
            window.location.href = `/${lng}/cache-refresh?return=${returnUrl}`;
            return;
          }

          // For minor issues, attempt to fix silently
          const fixed = fixAuthState();

          if (fixed) {
            // Don't reload immediately, let the user continue
            setTimeout(() => {
              window.location.reload();
            }, 5000); // Reload after 5 seconds
          }
        }
      } catch (error) {
        console.error('Cache validation error:', error);

        // For critical errors, redirect to cache refresh page
        const pathSegments = pathname.split('/');
        const lng = pathSegments[1] || 'zh-cn';
        const returnUrl = encodeURIComponent(pathname + window.location.search);

        window.location.href = `/${lng}/cache-refresh?return=${returnUrl}`;
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [pathname, router]);

  // This component doesn't render anything
  return null;
}
