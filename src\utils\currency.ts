export const getCurrencyInfo = () => {
  if(typeof window === 'undefined'){
    return { symbol: 'CNY', rate: 1 }
  }

  // 获取当前选择的货币
  const selectedCurrency = localStorage.getItem('selectedCurrency');
  let symbol = localStorage.getItem('currencySymbol');
  let rate = Number(localStorage.getItem('currentExchangeRate'));

  // 如果符号与选择的货币不一致，尝试重新同步
  if (selectedCurrency && symbol && symbol !== selectedCurrency) {
    // 检查是否需要从货币数据中重新获取符号
    const currencyDataStr = localStorage.getItem('currencyData');
    if (currencyDataStr) {
      try {
        const currencyData = JSON.parse(currencyDataStr);
        let selectedCurrencyData = null;

        if (Array.isArray(currencyData)) {
          selectedCurrencyData = currencyData.find((item: any) => item.code === selectedCurrency);
        } else if (typeof currencyData === 'object' && currencyData !== null) {
          selectedCurrencyData = currencyData[selectedCurrency];
        }

        if (selectedCurrencyData) {
          // 更新符号
          if (selectedCurrencyData.symbol_left) {
            symbol = selectedCurrencyData.symbol_left;
            localStorage.setItem('currencySymbol', symbol || '');
          } else if (selectedCurrencyData.symbol) {
            symbol = selectedCurrencyData.symbol;
            localStorage.setItem('currencySymbol', symbol || '');
          }

          // 更新汇率
          if (selectedCurrencyData.value) {
            rate = parseFloat(selectedCurrencyData.value);
            localStorage.setItem('currentExchangeRate', rate.toString());
          } else if (selectedCurrencyData.rate) {
            rate = parseFloat(selectedCurrencyData.rate);
            localStorage.setItem('currentExchangeRate', rate.toString());
          }
        }
      } catch (e) {
        console.error('解析货币数据失败:', e);
      }
    }
  }

  // 如果没有符号或汇率，尝试从选择的货币获取默认值
  if (!symbol && selectedCurrency) {
    symbol = selectedCurrency;
  }
  if (!rate || isNaN(rate)) {
    rate = 1;
  }

  // 最终的默认值
  symbol = symbol || selectedCurrency || 'CNY';

  return { symbol, rate }
}

export const convertCurrency = (value: number) => {
  const { rate } = getCurrencyInfo()
  return value * rate
}

export const formatCurrency = (value: number) => {
  const { symbol } = getCurrencyInfo()
  let convertedValue = convertCurrency(value)
  convertedValue = Number(convertedValue.toFixed(2))
  return {
    symbol,
    value: convertedValue,
    formatValue: `${symbol} ${convertedValue.toLocaleString()}`,
    formatValueNoSpace: `${symbol}${convertedValue.toLocaleString()}`
  }
}

// 强制刷新货币信息，确保与系统选择的货币一致
export const refreshCurrencyInfo = () => {
  if(typeof window === 'undefined'){
    return
  }

  const selectedCurrency = localStorage.getItem('selectedCurrency');
  if (!selectedCurrency) {
    return;
  }

  // 检查货币数据
  const currencyDataStr = localStorage.getItem('currencyData');
  if (currencyDataStr) {
    try {
      const currencyData = JSON.parse(currencyDataStr);
      let selectedCurrencyData = null;

      if (Array.isArray(currencyData)) {
        selectedCurrencyData = currencyData.find((item: any) => item.code === selectedCurrency);
      } else if (typeof currencyData === 'object' && currencyData !== null) {
        selectedCurrencyData = currencyData[selectedCurrency];
      }

      if (selectedCurrencyData) {
        // 更新符号
        if (selectedCurrencyData.symbol_left) {
          localStorage.setItem('currencySymbol', selectedCurrencyData.symbol_left);
        } else if (selectedCurrencyData.symbol) {
          localStorage.setItem('currencySymbol', selectedCurrencyData.symbol);
        } else {
          localStorage.setItem('currencySymbol', selectedCurrency);
        }

        // 更新汇率
        if (selectedCurrencyData.value) {
          localStorage.setItem('currentExchangeRate', selectedCurrencyData.value.toString());
        } else if (selectedCurrencyData.rate) {
          localStorage.setItem('currentExchangeRate', selectedCurrencyData.rate.toString());
        } else {
          localStorage.setItem('currentExchangeRate', '1');
        }
      }
    } catch (e) {
      console.error('刷新货币信息失败:', e);
    }
  }
}

// 获取USD汇率并转换价格为美元
export const convertToUSD = (value: number) => {
  if(typeof window === 'undefined'){
    return { usdValue: 0, hasUSDRate: false }
  }

  try {
    // 获取货币数据
    const currencyData = localStorage.getItem('currencyData');
    if (!currencyData) {
      return { usdValue: 0, hasUSDRate: false };
    }

    const currencies = JSON.parse(currencyData);
    let usdRate = null;

    // 检查是否为数组格式
    if (Array.isArray(currencies)) {
      const usdCurrency = currencies.find((curr: any) => curr.code === 'USD');
      if (usdCurrency && usdCurrency.value) {
        usdRate = parseFloat(usdCurrency.value);
      }
    } else if (typeof currencies === 'object' && currencies.USD) {
      // 检查是否为对象格式
      if (currencies.USD.value) {
        usdRate = parseFloat(currencies.USD.value);
      } else if (currencies.USD.rate) {
        usdRate = parseFloat(currencies.USD.rate);
      }
    }

    if (usdRate && usdRate > 0) {
      // 根据项目中的汇率逻辑：
      // - 如果usdRate < 1，说明是"1人民币等于多少美元"，需要用 value * usdRate
      // - 如果usdRate > 1，说明是"1美元等于多少人民币"，需要用 value / usdRate
      let usdValue;
      if (usdRate < 1) {
        // 汇率小于1，表示1人民币等于多少美元
        usdValue = Number((value * usdRate).toFixed(2));
      } else {
        // 汇率大于1，表示1美元等于多少人民币
        usdValue = Number((value / usdRate).toFixed(2));
      }
      return { usdValue, hasUSDRate: true };
    }

    return { usdValue: 0, hasUSDRate: false };
  } catch (error) {
    console.error('转换USD失败:', error);
    return { usdValue: 0, hasUSDRate: false };
  }
}

// 检查双货币插件是否开启
export const checkCurrencyPluginStatus = async () => {
  if (typeof window === 'undefined') {
    return false;
  }

  try {
    // 使用新的插件管理工具
    const { isPluginEnabled } = await import('@/utils/plugin');
    return await isPluginEnabled('currency_pair');
  } catch (error) {
    console.error('检查双货币插件状态失败:', error);
    return false;
  }
};
 
// 格式化美元价格显示
export const formatUSDPrice = async (value: number) => {
  // 检查双货币插件是否开启
  const isPluginEnabled = await checkCurrencyPluginStatus();
  if (!isPluginEnabled) {
    return null; // 插件未开启时不显示美元价格
  }

  // 检查当前选择的货币是否已经是美元
  const currentCurrency = getCurrencyInfo();
  if (currentCurrency.symbol === 'USD' || currentCurrency.symbol === '$') {
    return null; // 如果当前已经是美元，不显示重复的美元价格
  }

  const { usdValue, hasUSDRate } = convertToUSD(value);

  if (!hasUSDRate || usdValue <= 0) {
    return null;
  }

  return {
    usdValue,
    formatValue: `$ ${usdValue.toLocaleString()}`
  };
}