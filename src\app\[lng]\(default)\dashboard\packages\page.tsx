'use client';

import { useState, useEffect } from 'react';
import { Form, Input, Button, Pagination } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import Tabs from '@/components/Tabs';
import { Api } from '@/request/api';
import OrderItem from './(component)/OrderItem';
import type { Locale } from '@/config';
import { useParams } from 'next/navigation';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import Loading from '@/components/Loading';
import OrderProgress from '@/components/OrderProgress';
import { getDictionary }  from '@/dictionaries';

// 扩展Window接口以包含YQV5属性
declare global {
  interface Window {
    YQV5?: any;
  }
}

interface OrderData {
  id: string;
  ordersn: string;
  status_text: string;
  createtime: string;
  totalmoney: number;
  goods: Array<{
    id: string;
    goodsname: string;
    goodsnum: number;
    goodsprice: string;
    goodsimg: string;
    status_text: string;
  }>;
}

interface OrderListParams {
  status?: string;
  keyword?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  size?: number;
}

export default function OrdersPage() {
  const params = useParams();
  const lng = params.lng as Locale || 'zh-cn';

  const [activeTab, setActiveTab] = useState<string>('-1');
  const [orders, setOrders] = useState<OrderData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchLoading, setSearchLoading] = useState<boolean>(false);
  const [statusList, setStatusList] = useState<any[]>([]);
  const [form] = Form.useForm();
  const [dict, setDict] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [total, setTotal] = useState<number>(0);
  const [externalApi, setExternalApi] = useState(null); // 用于存储外部脚本暴露的对象或函数

  const fetchOrders = async (params: OrderListParams = {}) => {
    let paramters: {
      page: number;
      size: number;
      keywords: string;
      lang: string;
      clienttype: string;
      startDate: string | undefined;
      endDate: string | undefined;
      order_state?: string;
    } = {
      page: params.page || currentPage,
      size: params.size || pageSize,
      keywords: params.keyword || '',
      lang: lng === 'zh-cn' ? 'zhCN' : lng === 'en' ? 'enUS' : 'jaJP',
      clienttype: 'pc',
      startDate: params.startDate,
      endDate: params.endDate
    }
    if (activeTab !== '-1') {
      paramters.order_state = activeTab;
    }
    setLoading(true);

    // 当选择"全部"时，不传递sendorder_state参数
    const apiParams: any = {
      keywords: params.keyword || '',
      page: paramters.page,
      size: paramters.size
    };

    // 只有当不是"全部"tab时才传递状态参数
    if (activeTab !== '-1') {
      apiParams.status_id = Number(activeTab);
      apiParams.sendorder_state = Number(activeTab);
    }

    const response = await Api.getSendOrderList(apiParams);
    if (response.success) {
      const sortedOrders = [...(response.data.data || [])].sort((a, b) =>
        new Date(b.createtime).getTime() - new Date(a.createtime).getTime()
      );
      setOrders(sortedOrders);
      setTotal(response.data.total || 0);
    }
    setLoading(false);
    setSearchLoading(false);
  };
 
  // 异步获取字典数据
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };
    fetchDictionary();
  }, [lng]);

  // 初始化状态列表
  useEffect(() => {
    const init = async () => {
      const statusList = await Api.getSendOrderStatusList();
      if (statusList.success) {
        let statusListData = Object.keys(statusList.data).map((key) => ({
          key,
          label: statusList.data[key]
        }));

        // 在状态列表前面添加"全部"选项
        const allTabsData = [
          {
            key: '-1',
            label: dict?.dashboard?.packages?.all || '全部'
          },
          ...statusListData
        ];

        setStatusList(allTabsData);
      }
    };
    init();
  }, [lng, dict]);

  // 当activeTab改变时获取订单数据，包括"全部"选项
  useEffect(() => {
    if (statusList.length > 0) {
      fetchOrders({ status: activeTab });
    }
  }, [activeTab, statusList]);

  const handleSearch = () => {
    const values = form.getFieldsValue();
    const [startDate, endDate] = values.dateRange || [null, null];

    setCurrentPage(1); // 重置页码到第一页
    fetchOrders({
      status: values.status,
      keyword: values.keyword,
      startDate: startDate ? startDate.format('YYYY-MM-DD') : undefined,
      endDate: endDate ? endDate.format('YYYY-MM-DD') : undefined,
      page: 1
    });
  };

  const handleTabChange = (activeKey: string) => {
    setActiveTab(activeKey);
  };

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) setPageSize(size);
    fetchOrders({ page, size });
  };
  useEffect(() => { 
    const script = document.createElement('script');
    script.src = '//www.17track.net/externalcall.js'; // 外部 JS 文件路径
    script.async = true;
    script.onload = () => {
      
      // 假设 externalcall.js 暴露了一个全局对象或函数，例如 window.YQ_Track
      if (window.YQV5) {
        setExternalApi(window.YQV5); // 将全局对象存储到状态
      }
    };
    document.body.appendChild(script);

    // 清理脚本
    return () => {
      document.body.removeChild(script);
    };
  }, [])
  return (
    <AntdConfigProvider>
      <div className="min-h-screen px-6 py-4">
       <OrderProgress dict={dict}></OrderProgress>

        <Form
          form={form}
          layout="inline"
          className="mb-8 flex flex-wrap"
          style={{
            marginBottom: '20px'
          }}
          onFinish={() => {
            setSearchLoading(true);
            handleSearch();
          }}
          initialValues={{
            keyword: '',
            status: undefined,
            dateRange: undefined
          }}
        >
          <Form.Item name="keyword" className="mb-2">
            <Input
              placeholder={dict?.dashboard?.packages?.placeholder}
              prefix={<SearchOutlined className="text-[#86909C]" />}
              style={{ width: 320 }}
              size="large"
            />
          </Form.Item>

          <Form.Item className="mb-2">
            <Button
              type="primary"
              htmlType="submit"
              loading={searchLoading}
              size="large"
            >
              {dict?.dashboard?.packages?.search}
            </Button>
          </Form.Item>
        </Form>

        <Tabs activeKey={activeTab.toString()} onChange={handleTabChange} items={statusList}>
        </Tabs>

        {/* Table Header */}
        {dict && orders.length > 0 && (
          <div className="bg-[#fafafa] rounded-lg mb-4 px-6 py-4 shadow-sm">
            <div className="grid grid-cols-12 gap-4 items-center text-sm font-medium text-[#86909C]">
              {/* Product Info Header - 5 columns */}
              <div className="col-span-5 text-center">
                {dict?.dashboard?.packages?.table?.productInfo}
              </div>

              {/* Weight/Volume & Status Header - 3 columns */}
              <div className="col-span-2 text-center">
                {dict?.dashboard?.packages?.table?.weightVolume} / {dict?.dashboard?.packages?.table?.status}
              </div>

              
              <div className="col-span-1 text-center">
                {dict?.dashboard?.packages?.table?.logistics}
              </div>

              {/* Price Header - 2 columns */}
              <div className="col-span-2 text-center">
                {dict?.dashboard?.packages?.table?.price}
              </div>

              {/* Actions Header - 2 columns */}
              <div className="col-span-2 text-center">
                {dict?.dashboard?.packages?.table?.actions}
              </div>
            </div>
          </div>
        )}

        {
          loading ? <Loading height="300px" /> : (
            <>
              {orders.map((order) => (
                <OrderItem key={order.id} data={order} onRefresh={fetchOrders} lng={lng} dict={dict} externalApi={externalApi} />
              ))}
              {Math.ceil(total / pageSize) >= 2 && (
                <div className="flex justify-center mt-4">
                  <Pagination
                    current={currentPage}
                    pageSize={pageSize}
                    total={total}
                    onChange={handlePageChange}
                    showSizeChanger
                    showTotal={(total) => dict?.dashboard?.packages?.total?.replace('{total}', total)}
                  />
                </div>
              )}
            </>
          )
        }
      </div>
      
    </AntdConfigProvider>
  );
}
