'use client';

import { Modal, ModalProps } from 'antd';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import React, { ReactNode } from 'react';

export default function ModalComponent({ children,className, ...props }: ModalProps & { children?: ReactNode,className?:string }) {
  return (
    <AntdConfigProvider>
      <Modal {...props} className={className}>
        {children&&children}
      </Modal>
    </AntdConfigProvider>
  );
}