'use client';

import { Input as AntdInput, InputProps, InputNumber as AntdInputNumber, InputNumberProps } from 'antd';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import React, { ReactNode } from 'react';

const InputComponent = ({ children, ...props }: InputProps & { children?: ReactNode }) => {
  return (
    <AntdConfigProvider>
      <AntdInput {...props}>
        {children && children}
      </AntdInput>
    </AntdConfigProvider>
  );
}

// 添加 Search 属性
InputComponent.Search = (props: any) => (
  <AntdConfigProvider>
    <AntdInput.Search {...props} />
  </AntdConfigProvider>
);

// 添加 TextArea 属性
InputComponent.TextArea = (props: any) => (
  <AntdConfigProvider>
    <AntdInput.TextArea {...props} />
  </AntdConfigProvider>
);

// 添加 Password 属性
InputComponent.Password = (props: any) => (
  <AntdConfigProvider>
    <AntdInput.Password {...props} />
  </AntdConfigProvider>
);

// 添加 InputNumber 组件
export const InputNumber = (props: InputNumberProps) => (
  <AntdConfigProvider>
    <AntdInputNumber {...props} />
  </AntdConfigProvider>
);

export default InputComponent;