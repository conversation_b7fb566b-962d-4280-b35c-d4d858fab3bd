'use client';

import React, { useState, useEffect, memo, useCallback, useMemo } from 'react';
import { Button, Empty } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { Api } from '@/request/api';
import ProductCard from '@/components/ProductCard';
import Loading from '@/components/Loading';
import TabsComponent from '@/components/Tabs';
import styles from './index.module.css';

interface PriceParityProduct {
  title: string;
  pic_url: string;
  promotion_price: number;
  orginal_price: number;
  price: number;
  num_iid: string;
  area: string;
  detail_url: string;
  recommend?: number | string; // 推荐标识字段
}

interface PriceParityProps {
  dict: any;
  lng: string;
  keyword?: string; // 商品关键词，用于搜索比价商品
}

// 平台映射函数，将API返回的平台名称映射到ProductCard支持的平台类型
const mapPlatformType = (platform: string): 'taobao' | '1688' | 'jd' | 'micro' | 'obmall' => {
  switch (platform.toLowerCase()) {
    case 'taobao':
      return 'taobao';
    case '1688':
      return '1688';
    case 'jd':
      return 'jd';
    case 'micro':
    case 'pinduoduo':
    case 'pdd':
      return 'micro';
    case 'obmall':
      return 'obmall';
    default:
      return 'taobao'; // 默认返回taobao
  }
};

const PriceParity: React.FC<PriceParityProps> = ({ dict, lng, keyword = '' }) => {
  const [platforms, setPlatforms] = useState<string[]>([]);
  const [defaultPlatform, setDefaultPlatform] = useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('');
  const [products, setProducts] = useState<PriceParityProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [pluginEnabled, setPluginEnabled] = useState(false);

  // 获取平台显示名称
  const getPlatformLabel = useCallback((platform: string) => {
    return dict?.detail?.priceComparison?.platforms?.[platform] || platform.toUpperCase();
  }, [dict]);

  // 获取支持的平台列表
  const fetchPlatforms = useCallback(async () => {
    try {
      const response = await Api.getPriceParityPlatforms();

      if (response.success) {
        const { lists, default: defaultPlatform } = response.data;
        setPlatforms(lists || []);
        setDefaultPlatform(defaultPlatform || '');
        setActiveTab(defaultPlatform || lists[0] || '');
      }
    } catch (error) {
      console.error('获取比价平台列表失败:', error);
      // 如果获取平台列表失败，使用默认平台
      const defaultPlatforms = ['1688', 'taobao'];
      setPlatforms(defaultPlatforms);
      setDefaultPlatform('1688');
      setActiveTab('1688');
    }
  }, []);

  // 获取比价商品列表
  const fetchPriceParityData = useCallback(async (platform: string, searchKeyword?: string) => {
    try {
      const response = await Api.getPriceParityList({
        keyword: searchKeyword || keyword,
        api_type: platform
      });

      if (response && response.success) {
        setProducts(response.data || []);
      } else {
        console.warn('获取比价商品列表失败:', response);
        setProducts([]);
      }
    } catch (error) {
      console.error('获取比价商品列表失败:', error);
      setProducts([]);
    }
  }, [keyword]);

  // 换一批按钮点击处理
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchPriceParityData(activeTab);
    } finally {
      setRefreshing(false);
    }
  }, [activeTab, fetchPriceParityData]);

  // 切换平台标签
  const handleTabChange = useCallback(async (key: string) => {
    setActiveTab(key);
    setLoading(true);
    try {
      await fetchPriceParityData(key);
    } finally {
      setLoading(false);
    }
  }, [fetchPriceParityData]);

  // 构建标签页数据
  const tabItems = useMemo(() => platforms.map(platform => ({
    key: platform,
    label: getPlatformLabel(platform),
    children: (
      <div className={styles.productGrid}>
        {products.length > 0 ? (
          products.map((item, index) => (
            <ProductCard
              key={item.num_iid || item.pic_url || index}
              product={{
                title: item.title,
                pic_url: item.pic_url,
                detail_url: item.detail_url,
                price: item.price.toString(),
                promotion_price: item.promotion_price.toString(),
                nick: item.area,
                recommend: item.recommend // 传递推荐字段
              }}
              platform={mapPlatformType(platform)}
              dict={dict}
            />
          ))
        ) : (
          <div className={styles.emptyState}>
            <Empty
              description={dict?.detail?.priceComparison?.noProducts || '暂无比价商品'}
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        )}
      </div>
    )
  })), [platforms, products, getPlatformLabel, dict]);

  useEffect(() => {
    const checkPluginAndFetchData = async () => {
      try {
        // 检查priceparity插件是否启用
        const { isPluginEnabled: checkPlugin } = await import('@/utils/plugin');
        const isEnabled = await checkPlugin('priceparity');

        console.log('AI比价插件状态:', isEnabled);

        if (isEnabled) {
          setPluginEnabled(true);
          // 插件启用时才获取数据
          await fetchPlatforms();
        } else {
          setPluginEnabled(false);
          console.log('AI比价插件未启用');
        }
      } catch (error) {
        console.error('检查AI比价插件状态失败:', error);
        setPluginEnabled(false);
      } finally {
        setLoading(false);
      }
    };

    checkPluginAndFetchData();
  }, [fetchPlatforms]);

  // 当平台列表加载完成且有默认平台时，获取商品数据
  useEffect(() => {
    if (pluginEnabled && activeTab && keyword) {
      fetchPriceParityData(activeTab);
    }
  }, [activeTab, pluginEnabled, keyword, fetchPriceParityData]);

  // 如果正在加载或插件未启用，不显示组件
  if (loading || !pluginEnabled) {
    return loading ? (
      <div className="bg-white rounded-lg shadow-sm p-6 ">
        <h3 className="text-lg font-medium mb-4 text-gray-800">
          {dict?.detail?.priceComparison?.title || '商品比价'}
        </h3>
        <div className="flex justify-center items-center py-8">
          <Loading height="120px" />
        </div>
      </div>
    ) : null;
  }

  // 如果没有平台数据，不显示组件
  if (!platforms.length) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 ">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-800">
          {dict?.detail?.priceComparison?.title || '商品比价'}
        </h3>
        <Button
          type="text"
          icon={<ReloadOutlined />}
          loading={refreshing}
          onClick={handleRefresh}
          className="text-blue-500 hover:text-blue-600"
        >
          {dict?.detail?.priceComparison?.refresh || '换一批'}
        </Button>
      </div>

      <TabsComponent
        activeKey={activeTab}
        onChange={handleTabChange}
        items={tabItems}
        className={styles.priceParityTabs}
        size="large"
      />
    </div>
  );
};

export default memo(PriceParity);
