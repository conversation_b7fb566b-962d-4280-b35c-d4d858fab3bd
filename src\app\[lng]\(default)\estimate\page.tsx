'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import ButtonComponent from '@/components/Button';
import { Modal } from 'antd';
import AntdConfigProvider from '@/components/AntdConfigProvider';
import { useRouter, useParams } from 'next/navigation';
import SelectComponent from '@/components/Select';
import InputComponent, { InputNumber } from '@/components/Input';
import { Api } from '@/request/api';
import { formatCurrency } from '@/utils/currency';
import { getDictionary } from "@/dictionaries";
import Message from '@/components/CustomMessage';

// 声明接口，解决类型错误
interface ProvinceOptions {
  [key: string]: { value: string; label: string }[];
}

// 区域数据类型
interface AreaOption {
  id: number;
  name: string;
  level: number;
  pid: number;
  value?: number;
}

// 运费估算结果类型
interface Fee {
  totalfee: number;
  firstfee: string;
  continuefee: number;
  sendfee: string;
  customsfee: string;
  serverfee: string;
  fuelfee: string;
  freemoney: string;
}

interface EstimateResult {
  template_id: number;
  fee: Fee;
  weigh: number;
  firstweight: number;
  continueweight: number;
  continuefee: string;
  firstfee: string;
  ban: string;
  cycle: string;
  feature: string;
  minweight: number;
  maxweight: number;
  logo: string;
  customsfee: string;
  name: string;
  type: string[];
  insurance_rate: string;
}

export default function SharePromotionPage() {
  const params = useParams();
  const lng = params.lng as string;

  // 运费计算状态
  const [destination, setDestination] = useState<number | null>(null);
  const [province, setProvince] = useState<number | null>(null);
  const [productType, setProductType] = useState<number[]>([]);
  const [weight, setWeight] = useState<number | null>(null);
  const [length, setLength] = useState<number | null>(null);
  const [width, setWidth] = useState<number | null>(null);
  const [height, setHeight] = useState<number | null>(null);
  const [estimateResults, setEstimateResults] = useState<EstimateResult[]>([]);
  const [showResult, setShowResult] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  // 地区数据
  const [countries, setCountries] = useState<AreaOption[]>([]);
  const [provinces, setProvinces] = useState<AreaOption[]>([]);
  // 商品类型数据
  const [productTypes, setProductTypes] = useState<{ [key: string]: string }>({});
  const [dict, setDict] = useState<any>(null); // 添加字典状态
  // 异步获取字典数据
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };

    fetchDictionary();
  }, [lng]);

  // 获取商品类型列表
  useEffect(() => {
    const fetchProductTypes = async () => {
      try {
        const res = await Api.getGoodsType();
        if (res.success) {
          setProductTypes(res.data);
        }
      } catch (error) {
        console.error('获取商品类型失败', error);
      }
    };

    fetchProductTypes();
  }, []);

  // 获取国家列表
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const res = await Api.getAreaList();
        if (res.success) {
          setCountries(res.data);
        }
      } catch (error) {
        console.error('获取国家列表失败', error);
      }
    };

    fetchCountries();
  }, []);

  // 获取省份列表
  useEffect(() => {
    const fetchProvinces = async () => {
      if (!destination) return;

      try {
        const res = await Api.getAreaList({ country: destination });
        if (res.success) {
          setProvinces(res.data);
        }
      } catch (error) {
        console.error('获取省份列表失败', error);
      }
    };

    if (destination) {
      fetchProvinces();
      setProvince(null); // 重置省份选择
    }
  }, [destination]);

  // 商品类型选项
  const productTypeOptions = Object.entries(productTypes).map(([value, label]) => ({
    value: parseInt(value),
    label
  }));

  // 计算体积
  const calculateVolume = () => {
    if (length && width && height) {
      return (length * width * height) / 1000000; // 转换为立方米
    }
    return 0;
  };

  // 计算运费
  const calculateShipping = async () => {
    if (!province || !weight || productType.length === 0) {
      Message.error('请填写所有必填项');
      return;
    }

    setLoading(true);
    try {
      const volume = calculateVolume();

      const params = {
        area_id: province,
        weight: weight,
        volume: volume,
        goodstype: productType,
        stotalmoney: 0, // 商品总价，此处可设为0
        clienttype: 'web'
      };

      const res = await Api.getEstimates(params);
      console.log(res);
      if (res.success) {
        setEstimateResults(res.data);
        setShowResult(true);
      } else {
        Message.error(res.data || '运费估算失败');
      }
    } catch (error) {
      console.error('运费估算失败', error);
      Message.error('运费估算失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full flex flex-col items-center">
      {/* 顶部横幅区域 */}
      <div className="w-full bg-[#fff4e7] bg-cover bg-center py-10 relative flex justify-center">
        <div className="px-[300px] w-full relative z-10 flex flex-row justify-between items-center gap-10">
          <div>  <h1 className="text-4xl font-bold text-[#333] mb-4 md:text-5xl sm:text-2xl ">{dict?.estimate?.bannerTitle}</h1>
            <p className="text-md text-[#666] mb-6 md:text-md">{dict?.estimate?.bannerDesc}
            </p></div>
          <Image src="/images/estimate_bg.jpg" draggable={false} className='rounded-2xl shadow-xl' alt="estimate" width={600} height={400} />
        </div>
      </div>

      {/* 运费估算模块 */}
      <div className="px-[300px] w-full mx-auto bg-white rounded-lg shadow-md p-8  relative z-20 my-10">
        <h2 className="text-2xl font-bold text-[#333] mb-6">{dict?.estimate?.title}</h2>

        <div className="w-full">
          {/* 目的地和省份/州 */}
          <div className="flex flex-row gap-4 mb-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {dict?.estimate?.destination} <span className="text-red-500">*</span>
              </label>
              {/* todo input  select */}
              <SelectComponent
                showSearch
                placeholder={dict?.estimate?.selectDestination}
                className="w-full"
                filterOption={(input, option) =>
                  (option?.label?.toString() ?? '').toLowerCase().includes(input.toLowerCase())
                }
                options={countries.map(item => ({ value: item.value, label: item.name } ))}
                value={destination}
                onChange={(value: number) => {
                  console.log('选择目的地:', value);
                  setDestination(value);
                }}
              />
            </div>

            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {dict?.estimate?.province} <span className="text-red-500">*</span>
              </label>
              <SelectComponent
                placeholder={dict?.estimate?.selectProvince}
                className="w-full"
                showSearch
                options={provinces.map(item => ({ value: item.value, label: item.name }))}
                filterOption={(input, option) =>
                  (option?.label?.toString() ?? '').toLowerCase().includes(input.toLowerCase())
                }
                value={province}
                onChange={(value: number) => setProvince(value)}
                disabled={!destination}
              />
            </div>
          </div>

          {/* 商品类型和重量 */}
          <div className="flex flex-row gap-4 mb-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {dict?.estimate?.goodsType} <span className="text-red-500">*</span>
              </label>
              <SelectComponent
                mode="multiple"
                placeholder={dict?.estimate?.selectGoodsType}
                className="w-full"
                options={productTypeOptions}
                value={productType}
                onChange={(value: number[]) => setProductType(value || [])}
              />
            </div>

            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {dict?.estimate?.weight} <span className="text-red-500">*</span>
              </label>
              <div className="flex items-center">
                <InputNumber
                  placeholder={dict?.estimate?.inputWeight}
                  className="flex-1"
                  min={0}
                  value={weight}
                  onChange={(value) => setWeight(typeof value === 'number' ? value : null)}
                />
                <span className="ml-2">g</span>
              </div>
            </div>
          </div>

          {/* 尺寸输入 */}
          <div className="mb-6">
            <div className="flex flex-row gap-4">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1"> {dict?.estimate?.length}</label>
                <div className="flex items-center">
                  <InputNumber
                    placeholder={dict?.estimate?.inputLength}
                    className="flex-1"
                    min={0}
                    value={length}
                    onChange={(value) => setLength(typeof value === 'number' ? value : null)}
                  />
                  <span className="ml-2">cm</span>
                </div>
              </div>

              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1"> {dict?.estimate?.width}</label>
                <div className="flex items-center">
                  <InputNumber
                    placeholder={dict?.estimate?.inputWidth}
                    className="flex-1"
                    min={0}
                    value={width}
                    onChange={(value) => setWidth(typeof value === 'number' ? value : null)}
                  />
                  <span className="ml-2">cm</span>
                </div>
              </div>

              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1"> {dict?.estimate?.height}</label>
                <div className="flex items-center">
                  <InputNumber
                    placeholder={dict?.estimate?.inputHeight}
                    className="flex-1"
                    min={0}
                    value={height}
                    onChange={(value) => setHeight(typeof value === 'number' ? value : null)}
                  />
                  <span className="ml-2">cm</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-center">
          <ButtonComponent
            type="primary"
            className="px-8 h-10 bg-[#ff6600]"
            onClick={calculateShipping}
            disabled={!destination || !province || productType.length === 0 || !weight}
            loading={loading}
          >
            {dict?.estimate?.calculateBtn}
          </ButtonComponent>
        </div>

        {/* 运费计算结果 */}
        {showResult && estimateResults.length > 0 && (
          <div className="mt-6 p-4 bg-[#f9f9f9] rounded-lg">
            <h3 className="text-lg font-medium text-[#333] mb-2">{dict?.estimate?.resultTitle}</h3>
            {estimateResults.map((result, index) => (
              <div key={index} className="mb-3 pb-3 border-b border-gray-200 last:border-0">
                <div className="flex justify-between">
                  <div className="flex  gap-4 w-[85%]">
                    {result.logo && (
                      <Image
                        src={result.logo}
                        alt={result.name}
                        style={{ width: '60px', height: '40px'}}
                        width={40}
                        height={40}
                        className="rounded"
                      />
                    )}
                    <div>
                      <div className="text-md font-medium">{result.name}</div>
                      <div className="text-sm text-gray-500">{dict?.estimate?.feature}：{result.feature}</div>
                      <div className="text-sm text-gray-500">{dict?.estimate?.deliveryTime}: {result.cycle}</div>
                    </div>
                  </div>
                  <div className="text-right w-[15%]">
                    <div className="text-xl font-bold text-[#ff6600]">{formatCurrency(Number(result.fee.totalfee)).formatValue}</div>
                    <div className="text-xs text-gray-500">
                      <div>{dict?.estimate?.baseWeight}: {result.firstweight}g {formatCurrency(Number(result.firstfee)).formatValue}</div> 
                       <div>{dict?.estimate?.additionalWeight}: {result.continueweight}g {formatCurrency(Number(result.continuefee)).formatValue}</div>
                    </div>
                  </div>
                </div>
                <div className="mt-2 text-sm text-gray-600">
                  <div className="font-medium">{dict?.estimate?.feeDetails}:</div>
                  <div>{dict?.estimate?.shippingFee}: {formatCurrency(Number(result.fee.sendfee)).formatValue}</div>
                  <div>{dict?.estimate?.tax}: {formatCurrency(Number(result.fee.customsfee)).formatValue}</div>
                  <div>{dict?.estimate?.serviceFee}: {formatCurrency(Number(result.fee.serverfee)).formatValue}</div>
                  <div>{dict?.estimate?.fuelFee}: {formatCurrency(Number(result.fee.fuelfee)).formatValue}</div>
                </div>
                <div className="mt-2 text-sm text-red-500">
                    {dict?.estimate?.restrictions}: {result.ban}
                  </div>
              </div>
            ))}
            <p className="text-sm text-gray-500 mt-2">
              {dict?.estimate?.disclaimer}
            </p>
          </div>
        )}

        {/* 无结果提示 */}
        {showResult && estimateResults.length === 0 && (
          <div className="mt-6 p-4 bg-[#f9f9f9] rounded-lg">
            <h3 className="text-lg font-medium text-[#333] mb-2">{dict?.estimate?.noShippingMethods}</h3>
            <p className="text-sm text-gray-500">
              {dict?.estimate?.noShippingTips}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
