@unocss all;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --banner-top: 0px;
  --base-color: var(--base-color-value);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  margin: 0px;
  padding: 0px;
  display: flex;
  flex-direction: column; /* 垂直排列子元素 */
  height: 100vh; /* 使body占满整个视口高度 */
  
}

a {
  text-decoration: auto;
}

.top-banner {
  top: var(--banner-top);
}

/* 404 页面动画 - 360浏览器兼容 */
@keyframes float {
  0%, 100% {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-20px);
    -moz-transform: translateY(-20px);
    -ms-transform: translateY(-20px);
    -o-transform: translateY(-20px);
    transform: translateY(-20px);
  }
}

@keyframes glow {
  0%, 100% {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5),
      0 0 40px rgba(255, 255, 255, 0.3);
  }
  50% {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3),
      0 0 20px rgba(255, 255, 255, 0.2);
  }
}

@keyframes slideUp {
  0% {
    -webkit-transform: translateY(20px);
    -moz-transform: translateY(20px);
    -ms-transform: translateY(20px);
    -o-transform: translateY(20px);
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-float {
  -webkit-animation: float 6s ease-in-out infinite;
  -moz-animation: float 6s ease-in-out infinite;
  -o-animation: float 6s ease-in-out infinite;
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  -webkit-animation: glow 2s ease-in-out infinite;
  -moz-animation: glow 2s ease-in-out infinite;
  -o-animation: glow 2s ease-in-out infinite;
  animation: glow 2s ease-in-out infinite;
}

.animate-slide-up {
  -webkit-animation: slideUp 0.5s ease-out forwards;
  -moz-animation: slideUp 0.5s ease-out forwards;
  -o-animation: slideUp 0.5s ease-out forwards;
  animation: slideUp 0.5s ease-out forwards;
}

.animate-slide-up-delay-200 {
  -webkit-animation: slideUp 0.5s ease-out 0.2s forwards;
  -moz-animation: slideUp 0.5s ease-out 0.2s forwards;
  -o-animation: slideUp 0.5s ease-out 0.2s forwards;
  animation: slideUp 0.5s ease-out 0.2s forwards;
}

.animate-slide-up-delay-400 {
  -webkit-animation: slideUp 0.5s ease-out 0.4s forwards;
  -moz-animation: slideUp 0.5s ease-out 0.4s forwards;
  -o-animation: slideUp 0.5s ease-out 0.4s forwards;
  animation: slideUp 0.5s ease-out 0.4s forwards;
}


.parabola-ball {
  position: fixed;
  width: 40px;
  height: 40px;
  background-color: #f97316;
  border-radius: 50%;
  z-index: 9999;
  pointer-events: none;
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  will-change: transform, left, top;
  /* 性能优化 */
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.parabola-trail {
  position: fixed;
  width: 12px;
  height: 12px;
  background-color: #f97316;
  border-radius: 50%;
  z-index: 9998;
  pointer-events: none;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.cart-ripple {
  position: fixed;
  width: 30px;
  height: 30px;
  border: 3px solid #f97316;
  border-radius: 50%;
  z-index: 9998;
  pointer-events: none;
  animation: ripple 0.6s ease-out;
  opacity: 0;
}

@keyframes ripple {
  0% {
    -webkit-transform: scale(0.1);
    -moz-transform: scale(0.1);
    -ms-transform: scale(0.1);
    -o-transform: scale(0.1);
    transform: scale(0.1);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(2);
    -moz-transform: scale(2);
    -ms-transform: scale(2);
    -o-transform: scale(2);
    transform: scale(2);
    opacity: 0;
  }
}

.cart-bounce {
  -webkit-animation: bounce 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  -moz-animation: bounce 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  -o-animation: bounce 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: bounce 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes bounce {
  0%, 100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  50% {
    -webkit-transform: scale(1.3);
    -moz-transform: scale(1.3);
    -ms-transform: scale(1.3);
    -o-transform: scale(1.3);
    transform: scale(1.3);
  }
}

/* 360浏览器全局兼容性修复 */
* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

/* 确保flexbox在360浏览器中正常工作 */
.flex {
  display: -webkit-box !important;
  display: -webkit-flex !important;
  display: -moz-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
}

.items-center {
  -webkit-box-align: center !important;
  -webkit-align-items: center !important;
  -moz-box-align: center !important;
  -ms-flex-align: center !important;
  align-items: center !important;
}

.justify-center {
  -webkit-box-pack: center !important;
  -webkit-justify-content: center !important;
  -moz-box-pack: center !important;
  -ms-flex-pack: center !important;
  justify-content: center !important;
}

/* 确保圆角在360浏览器中正常显示 */
.rounded-full {
  -webkit-border-radius: 9999px !important;
  -moz-border-radius: 9999px !important;
  border-radius: 9999px !important;
}

/* 确保过渡效果在360浏览器中正常工作 */
.transition-all {
  -webkit-transition: all 0.3s ease !important;
  -moz-transition: all 0.3s ease !important;
  -o-transition: all 0.3s ease !important;
  transition: all 0.3s ease !important;
}