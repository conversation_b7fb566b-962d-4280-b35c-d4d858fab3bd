import type { <PERSON>ada<PERSON> } from "next";
import "@unocss/reset/tailwind.css";
import "@/app/globals.css";
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { getDictionary } from "@/dictionaries";
import type { Locale } from "@/config";
import { getConfigList } from "@/request/server";
export async function generateMetadata({
  params,
}: {
  params: Promise<{ lng: Locale }>;
}): Promise<Metadata> {
  const { lng } = await params;

  try {
    const configList = await getConfigList();
    return {
      title: configList?.site?.name || 'onebuy',
      description: configList?.site?.description,
      keywords: configList?.site?.keywords,
      icons: configList?.site?.ico_logo,
    };
  } catch (error) {
    console.error('🚨 Failed to load config for metadata:', error);
    // Return fallback metadata
    return {
      title: 'onebuy',
      description: 'Online shopping platform',
      keywords: 'shopping, online, ecommerce',
      icons: '/favicon.ico',
    };
  }
}

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ lng: Locale }>;
}>) {
  const { lng } = await params;
  const dict = await getDictionary(lng);

  let config: any = {};
  let site: any = {};

  try {
    config = await getConfigList();
    site = config?.site || {};
  } catch (error) {
    console.error('Failed to load config in layout:', error);
    // Use fallback values
    site = {
      description: 'Online shopping platform',
      keywords: 'shopping, online, ecommerce',
      ico_logo: '/favicon.ico'
    };
  }
  return (
    <html lang={lng} style={{ '--base-color': process.env.NEXT_PUBLIC_BASE_COLOR || 'black', '--base-color-hover': process.env.NEXT_PUBLIC_BASE_COLOR_HOVER || 'black' } as React.CSSProperties}>
      <head>
        <link
          rel="stylesheet"
          href="/font-awesome/all.min.css"
        />
        <meta name="description" content={site?.description} />
        <meta name="keywords" content={site?.keywords} />
        <link rel="icon" href={site?.ico_logo} />
      </head>
      <body
        className={`antialiased`}
      >
      <AntdRegistry>{children}</AntdRegistry>
      </body>
    </html>
  );
}
