import React from 'react'
import StepsComponent from '@/components/Steps'
import PreviewPageClient from './PreviewPageClient'
import { getServerData } from '@/request/server'
import { getDictionary } from '@/dictionaries'
import { Locale } from '@/config'

export default async function PreviewPackagePage({
    params,
    searchParams
}: {
    params: Promise<{ lng: Locale }>,
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
    const { lng } = await params
    const dict = await getDictionary(lng)
    const resolvedSearchParams = await searchParams
    const key = resolvedSearchParams.key as string
    return (
        <div className="min-h-screen pb-6">
            <div className="max-w-[1200px] mx-auto p-5">
                <StepsComponent
                    current={4}
                    labelPlacement="vertical"
                    dict={dict}
                    stepOverrides={{
                        4: dict?.dashboard?.cart?.steps?.submitPreview || 'Submit Preview'
                    }}
                />
                <PreviewPageClient lng={lng} dict={dict} searchKey={key} />
            </div>
        </div>
    )
}
