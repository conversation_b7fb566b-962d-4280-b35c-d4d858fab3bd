import { cookies, headers } from 'next/headers';
import { defaultLocale, locales } from '@/config';

/**
 * 从请求头中获取真实的host
 */
function getRealHostFromHeaders(headersList: Awaited<ReturnType<typeof headers>>) {
    // 获取真实的host，优先级：x-forwarded-host > host
    let host = headersList.get('x-forwarded-host') || headersList.get('host')

    // 如果还是没有host，这种情况很少见，使用默认值
    if (!host) {
        console.warn('⚠️ 无法获取host信息，使用默认值')
        host = 'localhost:3000'
    }

    return host
}

/**
 * 从请求头中获取真实的协议
 */
function getRealProtocolFromHeaders(headersList: Awaited<ReturnType<typeof headers>>, host: string) {
    // 获取协议，优先级：x-forwarded-proto > x-forwarded-ssl > 默认判断
    let protocol = headersList.get('x-forwarded-proto')
    if (!protocol) {
        // 检查是否有SSL标识
        const forwardedSsl = headersList.get('x-forwarded-ssl')
        const forwardedPort = headersList.get('x-forwarded-port')

        if (forwardedSsl === 'on' || forwardedPort === '443') {
            protocol = 'https'
        } else if (host.includes('localhost') || host.includes('127.0.0.1')) {
            // 本地开发环境默认使用http
            protocol = 'http'
        } else {
            // 生产环境默认使用https
            protocol = 'https'
        }
    }

    return protocol
}
const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
let BASE_URL = process.env.NEXT_PUBLIC_BASE_URL
if (!isTp5) {
    BASE_URL = 'https://api.v6.daigouxt.com/api/v1.0'
}

/**
 * 获取cookie字符串，确保只包含ASCII字符
 * @returns cookie字符串
 */
async function getCookieString() {
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();

    // 已知的分析/追踪cookie，包含非ASCII字符是正常的
    const knownAnalyticsCookies = [
        'sensorsdata2015jssdkcross',
        'sensorsdata2015session',
        'sensorsdata2015',
        '_ga',
        '_gid',
        '_gat',
        '_gtag',
        'baidu_analytics',
        'cnzz_analytics'
    ];

    // 过滤掉包含非ASCII字符的cookie值
    const safeCookies = allCookies.filter(cookie => {
        const cookieStr = `${cookie.name}=${cookie.value}`;
        if (!/^[\x00-\x7F]*$/.test(cookieStr)) {
            // 对于已知的分析cookie，只在开发环境下记录详细日志
            if (knownAnalyticsCookies.includes(cookie.name)) {
                if (process.env.NODE_ENV === 'development') {
                    console.debug('🔍 跳过分析cookie (包含非ASCII字符):', cookie.name);
                }
            } else {
                // 对于未知的非ASCII cookie，仍然记录警告
                console.warn('⚠️ 跳过包含非ASCII字符的cookie:', cookie.name, cookie.value.substring(0, 100) + '...');
            }
            return false;
        }
        return true;
    });

    const cookieString = safeCookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
    return cookieString
}

/**
 * 获取user-agent
 * @returns user-agent
 */
async function getUserAgent() {
    const headersList = await headers()
    const userAgent = headersList.get('user-agent')
    return userAgent || ''
}

/**
 * 获取用户真实访问的URL信息
 * @returns 包含host、origin、referer、originWithoutPort的对象
 */
async function getRealUrlInfo() {
    const headersList = await headers()

    let host: string
    let protocol: string
    let origin: string

    // 优先从 referer 头中提取域名信息，因为它包含了用户真实访问的完整URL
    const referer = headersList.get('referer')
    if (referer) {
        try {
            const refererUrl = new URL(referer)
            host = refererUrl.host
            protocol = refererUrl.protocol.replace(':', '')
            origin = `${protocol}://${host}`
        } catch (error) {
            // 如果 referer 解析失败，回退到其他方法
            console.warn('⚠️ referer 解析失败，使用备用方法:', referer)
            host = getRealHostFromHeaders(headersList)
            protocol = getRealProtocolFromHeaders(headersList, host)
            origin = `${protocol}://${host}`
        }
    } else {
        // 如果没有 referer，使用传统方法
        host = getRealHostFromHeaders(headersList)
        protocol = getRealProtocolFromHeaders(headersList, host)
        origin = `${protocol}://${host}`
    }

    // 创建不带端口号的origin，用于http_origin头信息
    let originWithoutPort: string
    try {
        const url = new URL(origin)
        // 移除端口号，只保留协议和主机名
        originWithoutPort = `${url.protocol}//${url.hostname}`
    } catch (error) {
        // 如果解析失败，手动处理
        const hostWithoutPort = host.split(':')[0]
        originWithoutPort = `${protocol}://${hostWithoutPort}`
    }

    // 最终的 referer 值
    const finalReferer = referer || origin

    // 在开发环境中添加调试日志
    if (process.env.NODE_ENV === 'development') {
        // 打印所有请求头信息
        const allHeaders: Record<string, string> = {};
        headersList.forEach((value, key) => {
            allHeaders[key] = value;
        });
        console.log('🔍 服务端请求头信息 (所有headers):', allHeaders);
        console.log('🔍 最终结果:', { host, protocol, origin, originWithoutPort, referer: finalReferer });
    }

    return {
        host,
        origin,
        originWithoutPort,
        referer: finalReferer
    }
}

/**
 * 从context中解析语言代码
 * 根据路径 /en/products 解析出 en
 */
function extractLangFromPath(path: string): string | null {
    if (!path) return null

    const segments = path.split('/')
    for (const segment of segments) {
        if (locales.includes(segment)) {
            return segment
        }
    }
    return null
}

/**
 * 从URL参数获取语言和货币信息
 * @returns {Object} 包含语言和货币的对象
 */
async function getLangAndCurrency() {
    const cookieStore = await cookies()
    // 尝试从请求相关信息获取语言
    let lang = defaultLocale
    let langSource = '默认值'

    // 获取货币信息
    let currency = 'CNY' // 默认货币
    let currencySource = '默认值'


    const currencyCookie = cookieStore.get('currency')
    if (currencyCookie) {
        currency = currencyCookie.value
        currencySource = 'currency cookie'
    }

    const langCookie = cookieStore.get('selectedLanguage')
    if (langCookie) {
        lang = langCookie.value
        langSource = 'lang cookie'
    }

    return { lang, currency }
}

/**
 * 获取 JWT Token
 * @returns Token字符串
 */
async function getAccessToken() {
    const cookieStore = await cookies();

    // Check both token names based on backend type
    const isTp6 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '6'
    const tokenName = isTp6 ? 'access_token' : 'token'

    const token = cookieStore.get(tokenName)?.value;

    return token || '';
}

/**
 * 从服务器端获取用户信息
 * @returns 用户信息对象
 */
export async function getServerData<T>(url: string, method: 'POST' | 'GET' = 'POST', params?: any): Promise<T> {
    try {
        const cookieString = await getCookieString()
        const userAgent = await getUserAgent()
        const { lang, currency } = await getLangAndCurrency()
        const accessToken = await getAccessToken()
        const { host, origin, originWithoutPort, referer } = await getRealUrlInfo()

        params = Object.assign(params || {}, { lang_code: lang })

        const fetchUrl = `${BASE_URL}${url.replace('/api/', '/api/v1/')}`

        // 安全处理可能包含非ASCII字符的头信息
        const safeHeaders: Record<string, string> = {
            'Content-Type': 'application/json',
            'lang': lang,
            'currency': currency,
        };

        // 安全添加可能包含非ASCII字符的头信息
        const headersToCheck = [
            { key: 'cookie', value: cookieString },
            { key: 'user-agent', value: userAgent },
            { key: 'host', value: host },
            { key: 'origin', value: originWithoutPort },
            { key: 'referer', value: referer },
            { key: 'Authorization', value: `Bearer ${accessToken}` }
        ];

        headersToCheck.forEach(({ key, value }) => {
            if (value) {
                try {
                    // 检查是否包含非ASCII字符
                    if (/^[\x00-\x7F]*$/.test(value)) {
                        safeHeaders[key] = value;
                        // 在开发环境中记录自定义头信息的设置
                        if (process.env.NODE_ENV === 'development') {
                            if (key === 'http_origin') {
                                console.log(`✅ 设置 http_origin 头信息: ${key} = ${value}`);
                            }
                            if (key.includes('onebound') || key.includes('Onebound') ||
                                key.includes('Request-Source') || key.includes('Client-Version') ||
                                key.includes('Custom')) {
                                console.log(`✅ 设置自定义头信息: ${key} = ${value}`);
                            }
                        }
                    } else {
                        console.warn(`⚠️ ${key} 包含非ASCII字符，跳过设置:`, value.substring(0, 100) + '...');
                    }
                } catch (error) {
                    console.warn(`⚠️ ${key} 处理失败:`, error);
                }
            }
        });

        // 在开发环境中打印实际发送的头信息
        if (process.env.NODE_ENV === 'development') {
            console.log('📤 实际发送的请求头信息:', safeHeaders);
            console.log('📤 请求URL:', fetchUrl);
        }

        const res = await fetch(fetchUrl, {
            method: method,
            headers: safeHeaders,
            body: method === 'POST' ? JSON.stringify(params) : undefined
        });

        // Check if the response is ok
        if (!res.ok) {
            console.error('Server API Error:', {
                status: res.status,
                statusText: res.statusText,
                url: fetchUrl
            })
            throw new Error(`HTTP ${res.status}: ${res.statusText}`)
        }

        const response = await res.json();

        // Validate response structure
        if (response === null || response === undefined) {
            console.error('🚨 Invalid response: null or undefined')
            throw new Error('Invalid response from server')
        }

        // Return the data, but handle cases where data might not exist
        return response.data || response;

    } catch (error) {
        console.error('🚨 getServerData Error:', {
            url,
            method,
            error: error instanceof Error ? error.message : 'Unknown error'
        })

        // Re-throw the error to be handled by the calling component
        throw error
    }
}

/**
 * 获取导航列表
 * @returns 导航列表
 */
export async function getNavList(params: { type: 'top' | 'foot' | 'side' }): Promise<any> {
    return await getServerData('/api/nav/lists', 'POST', params);
}


export async function getConfigList(): Promise<any> {
    try {
        return await getServerData(isTp5 ? '/api/other/configList' : '/other/common/config/configList', isTp5 ? 'GET' : 'POST');
    } catch (error) {
        console.error('🚨 getConfigList failed, returning fallback config:', error);
        // Return a fallback configuration to prevent complete failure
        return {
            site: {
                name: 'onebuy',
                description: 'Online shopping platform',
                keywords: 'shopping, online, ecommerce',
                ico_logo: '/favicon.ico',
                language: 'zh-cn',
                currency: 'CNY',
                logo: null,
                beian: '',
                basic_privacy_policy: '',
                service_agreement: '',
                cookie_statement: '',
                disclaimer: '',
                weixin_way: '',
                facebook: '',
                instagram_way: ''
            },
            nav_list: [],
            foot_list: [],
            side_list: []
        };
    }
}

/**
 * 服务端检查插件是否启用
 * @param pluginCode 插件代码
 * @param mer_id 商户ID（可选）
 * @returns 插件是否启用
 */
export async function checkServerPluginStatus(pluginCode: string, mer_id?: string): Promise<boolean> {
    try {
        // 获取插件列表
        const response = await getServerData('/web/plugin/merchant/list', 'POST', mer_id ? { mer_id } : {});

        if (response && Array.isArray(response)) {
            // 查找指定插件
            const plugin = response.find((merchantPlugin: any) =>
                merchantPlugin.plugin && merchantPlugin.plugin.code === pluginCode
            );

            // 返回插件状态，status 为 1 表示启用
            return plugin ? plugin.status === 1 : false;
        }

        return false;
    } catch (error) {
        console.error(`🚨 检查插件 ${pluginCode} 状态失败:`, error);
        return false;
    }
}

