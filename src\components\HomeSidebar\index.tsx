'use client'

import { locales } from '@/config'
import Link from 'next/link'
import { usePathname, useParams } from 'next/navigation'
import { useEffect, useState } from 'react';

interface HomeSidebarProps {
  dict: any;
}

export default function MemberSidebar({ dict }: HomeSidebarProps) {
  const pathname = usePathname()
  const params = useParams()
  const lng = params.lng as string
  const baseColor = process.env.NEXT_PUBLIC_BASE_COLOR || 'black'
  const [menuItems, setMenuItems] = useState([
    { icon: 'fas fa-user', label: dict?.dashboard?.home?.menu?.profile || '会员中心', path: '/dashboard/home' },
    { icon: 'fas fa-shopping-cart', label: dict?.dashboard?.home?.menu?.cart || '购物车', path: '/dashboard/cart' },
    { icon: 'fas fa-heart', label: dict?.dashboard?.home?.menu?.favorites || '收藏夹', path: '/dashboard/favorites' },
    { icon: 'fas fa-file-invoice', label: dict?.nav?.orders || '订单', path: '/dashboard/orders' },
    { icon: 'fas fa-credit-card', label: dict?.dashboard?.onepayorders?.title || '一次付款订单', path: '/dashboard/onepayorders' },
    { icon: 'fas fa-warehouse', label: dict?.nav?.warehouse || '仓库', path: '/dashboard/warehouse' },
    { icon: 'fas fa-box', label: dict?.nav?.packages || '包裹', path: '/dashboard/packages' },
    { icon: 'fas fa-eye', label: dict?.nav?.previewPackages || '预演包裹', path: '/dashboard/preview-packages' },
    { icon: 'fas fa-wallet', label: dict?.dashboard?.home?.balance?.title || '钱包', path: '/dashboard/wallet' },
    // { icon: 'fas fa-wallet', label: dict?.dashboard?.remittance?.title || '汇款记录', path: '/dashboard/home/<USER>' },
    { icon: 'fas fa-bell', label: dict?.dashboard?.sidebar?.message || '消息', path: '/dashboard/message' },
    { icon: 'fas fa-comment', label: dict?.dashboard?.sidebar?.question || '咨询', path: '/dashboard/question' },
    { icon: 'fas fa-cog', label: dict?.dashboard?.account?.changeMobile?.title || '账户', path: '/dashboard/account' },
  ])

  useEffect(() => {
    async function checkPluginAndAddReferralMenu() {
      try {
        const { isPluginEnabled } = await import('@/utils/plugin');
        const pluginEnabled = await isPluginEnabled('invitefriendspro');

        const referralMenuItem = {
          icon: 'fas fa-user-plus',
          label: dict?.dashboard?.sidebar?.referral || '推荐官',
          path: pluginEnabled ? '/dashboard/referralpro' : '/dashboard/referral'
        };

        setMenuItems(prevItems => {
          // 检查是否已经存在推荐官菜单项，避免重复添加
          const hasReferralItem = prevItems.some(item =>
            item.path === '/dashboard/referral' || item.path === '/dashboard/referralpro'
          );

          if (!hasReferralItem) {
            return [...prevItems, referralMenuItem];
          }

          return prevItems;
        });
      } catch (error) {
        console.error('Failed to check plugin status:', error);
        // 如果插件检查失败，默认使用普通推荐页面
        const referralMenuItem = {
          icon: 'fas fa-user-plus',
          label: dict?.dashboard?.sidebar?.referral || '推荐官',
          path: '/dashboard/referral'
        };

        setMenuItems(prevItems => {
          const hasReferralItem = prevItems.some(item =>
            item.path === '/dashboard/referral' || item.path === '/dashboard/referralpro'
          );

          if (!hasReferralItem) {
            return [...prevItems, referralMenuItem];
          }

          return prevItems;
        });
      }
    }

    checkPluginAndAddReferralMenu();
  }, [dict?.dashboard?.sidebar?.referral])

  return (
    <ul className="space-y-1 sticky border-3 border-transparent rounded-lg p-5 bg-white bg-clip-padding shadow-lg  transition-all duration-300 ease-in-out relative ">
      {menuItems.map((item, index) => {
        const isActive = pathname === `/${lng}${item.path}`
        return (
          <li key={item.path}>
            <Link
              href={`/${lng}${item.path}`}
              data-tg-menu-item
              data-tg-menu-item-index={index + 1}
              className={`flex items-center p-3 text-gray-700 hover:bg-gradient-to-r hover:from-orange-50 hover:to-transparent hover:text-orange-500 text-base rounded-lg transition-all duration-200 group banner-left ${isActive
                  ? 'text-orange-500 bg-gradient-to-r from-orange-50 to-transparent'
                  : ''
                }`}
            >
              <span className={`w-10 h-10 flex items-center justify-center rounded-lg bg-gray-100 group-hover:bg-orange-100 transition-all duration-200 mr-4 ${isActive ? 'bg-orange-100' : ''
                }`}>
                <i className={`${item.icon} text-gray-500 group-hover:text-orange-500 transition-all duration-200 ${isActive ? 'text-orange-500' : ''
                  }`}></i>
              </span>
              <span className="font-medium">{item.label}</span>
            </Link>
          </li>
        )
      })}
    </ul>
  )
} 