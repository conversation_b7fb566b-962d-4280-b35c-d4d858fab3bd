'use client';

import React from 'react';
import ProductCard from '@/components/ProductCard';
import { normalizeImageUrl } from '@/utils/imageUtils';

// 定义接口类型 - 复用自营商城的数据结构
interface MallProduct {
  id: number;
  goodsname: string;
  image: string;
  images?: string;
  price: string;
  orginal_price?: string;
  sales?: number;
  date_available_text: string;
  date_available: number;
  recommend?: number | string; // 推荐标识字段
  [key: string]: any;
}

interface MallGoodsList {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  data: MallProduct[];
}

interface RecommendedItem {
  id: number;
  pid: number;
  flag: string;
  createtime: string;
  updatetime: string;
  weigh: number;
  status: string;
  mall_category_id: number;
  langcode: string;
  name: string;
  image: string;
  spacer: string;
  childlist: any[];
  goodslist: MallGoodsList;
}

interface RecommendedProductsProps {
  recommendedItems: RecommendedItem[];
  dict: any;
  lng: string;
}

const RecommendedProducts: React.FC<RecommendedProductsProps> = ({
  recommendedItems,
  dict,
  lng
}) => {
  // 如果没有推荐商品，不显示组件
  if (!recommendedItems || recommendedItems.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
      {/* 推荐商品标题 - 复用HotProducts的样式 */}
      <div className="bg-gradient-to-r from-red-500 to-pink-600 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-lg overflow-hidden bg-white/20 flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            </div>
            <h2 className="text-xl font-bold text-white">
              {dict?.mall?.recommended || '推荐商品'}
            </h2>
          </div>
          <div className="bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium">
            {dict?.mall?.hot || '热门'}
          </div>
        </div>
      </div>

      {/* 推荐商品列表 - 复用现有的商品网格布局 */}
      <div className="p-6">
        {recommendedItems.map((item) => (
          <div key={item.id} className="mb-6 last:mb-0">
            {/* 如果有分类名称，显示分类标题 */}
            {item.name && (
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-800 border-l-4 border-red-500 pl-3">
                  {item.name}
                </h3>
              </div>
            )}
            
            {/* 商品网格 - 复用HotProducts的网格布局 */}
            {item.goodslist?.data && item.goodslist.data.length > 0 ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                {item.goodslist.data.map((product: MallProduct) => {
                  // 处理图片URL - 复用自营商城的图片处理逻辑
                  let imageUrl = product.image;
                  if (imageUrl && !imageUrl.startsWith('http')) {
                    if (imageUrl.startsWith('//')) {
                      imageUrl = `https:${imageUrl}`;
                    } else if (imageUrl.startsWith('/')) {
                      imageUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'https://api.v6.daigouxt.com'}${imageUrl}`;
                    }
                  }
                  imageUrl = normalizeImageUrl(imageUrl);

                  return (
                    <div key={product.id} className="relative group">
                      {/* 推荐标签 - 增强样式 */}
                      <div className="absolute top-2 left-2 z-10 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-lg animate-pulse">
                        <span className="flex items-center space-x-1">
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                          <span>{dict?.mall?.recommendTag || '推荐'}</span>
                        </span>
                      </div>

                      {/* 推荐商品边框效果 */}
                      <div className="relative overflow-hidden rounded-lg border-2 border-transparent group-hover:border-red-300 transition-all duration-300">
                        {/* 闪光效果背景 */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-red-100/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                        {/* 复用现有的ProductCard组件 */}
                        <ProductCard
                          product={{
                            title: product.goodsname,
                            pic_url: imageUrl,
                            price: parseFloat(product.price),
                            promotion_price: product.orginal_price ? parseFloat(product.orginal_price) : undefined,
                            detail_url: `/${lng}/detail/obmall?id=${product.id}`,
                            nick: '',
                            recommend: product.recommend // 传递推荐字段
                          }}
                          platform={'obmall'}
                          dict={dict}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <div className="text-sm">{dict?.mall?.empty?.noRecommended || '暂无推荐商品'}</div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecommendedProducts;
