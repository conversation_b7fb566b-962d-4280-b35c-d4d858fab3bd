export interface FavoriteItem {
  id: number;
  user_id: number;
  goods_id: string;
  goodsurl: string;
  goodsname: string;
  goodsprice: string;
  goodsimg: string;
  goodsseller: string;
  sellerurl: string;
  goodssite: string;
  mall_goods_id: number;
  createtime: string;
  deletetime: string | null;
  langcode: string;
}

export interface FavoriteResponse {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  data: FavoriteItem[];
} 