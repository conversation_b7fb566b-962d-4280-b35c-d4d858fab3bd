import type { Metadata } from "next";
import "@unocss/reset/tailwind.css";
import "@/app/globals.css";

import { getDictionary } from "@/dictionaries";
import type { Locale } from "@/config";
import HomeSidebar from "@/components/HomeSidebar";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ lng: Locale }>;
}): Promise<Metadata> {
  const { lng } = await params;
  const dict = await getDictionary(lng);

  return {
    title: dict.title,
    description: dict.description,
  };
}

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ lng: Locale }>;
}>) {
  const { lng } = await params;
  const dict = await getDictionary(lng);

  return (
    <div className="container mx-auto flex pt-10">
      <div className="w-1/6">
        <HomeSidebar dict={dict} />
      </div>
      <div className="w-5/6 ml-10 border-3 border-transparent rounded-lg p-5 bg-white bg-clip-padding shadow-lg  transition-all duration-300 ease-in-out relative shadow-[0_-4px_8px_rgba(255,215,0,0.7)]">
        {children}
      </div>
    </div>
  );
}
