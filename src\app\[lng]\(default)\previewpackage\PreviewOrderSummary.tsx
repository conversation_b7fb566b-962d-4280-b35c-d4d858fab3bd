'use client';

import React, { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Checkbox, Modal, Input, App, InputNumber } from 'antd';
import { Api } from '@/request/api';
import message from '@/components/CustomMessage';
import Button from '@/components/Button';
import { formatCurrency } from '@/utils/currency';

interface Product {
  id: number;
  goodsimg: string;
  goodsname: string;
  skuname: string;
  goodsprice: string;
  goodsnum: number;
  [key: string]: any;
}

interface FeeInfo {
  freightdiscount:number,
  freight:number,
  oldfreight:number
}

export default function PreviewOrderSummary({
  couponIsOpen,
  dict,
  products = [],
  localkey,
  totalProductPrice = 0,
  shippingFee = 0,
  order_goods_ids = [],
  type = 'previewpackage',
  shippingFeeInfo
}: {
  couponIsOpen: boolean,
  dict: any,
  products: Product[],
  localkey: string,
  totalProductPrice: number,
  shippingFee?: number,
  order_goods_ids?: number[],
  type: string,
  shippingFeeInfo:FeeInfo
}) {
  console.log('products', products);
  const searchParams = useSearchParams();
  const router = useRouter();
  const [couponCode, setCouponCode] = useState('');
  const [couponList, setCouponList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedCouponId, setSelectedCouponId] = useState<number | null>(null);
  const [selectedCoupon, setSelectedCoupon] = useState<CouponItem | null>(null);
  const [serverList, setServerList] = useState<any>({});
  const [selectedServices, setSelectedServices] = useState<Record<string, boolean>>({});
  const [photoCount, setPhotoCount] = useState<number>(1);
  const [photoRemarks, setPhotoRemarks] = useState<string[]>(['']);
  const [submitting, setSubmitting] = useState(false);
  const [config, setConfig] = useState({ sendneedcheck: 1 });
  // Order service fee switch status - always true for previewpackage page
  const [orderServerFeeEnabled, setOrderServerFeeEnabled] = useState(true);
  // Preview package fee configuration
  const [previewPackageConfig, setPreviewPackageConfig] = useState({ rehearsal_fee: '0' });
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
  const serverFee =products.length ? products.reduce((sum, item) => sum + item.serverfee, 0): 0
  
  interface CouponItem {
    id: number;
    money: string;
    status: string;
    status_text: string;
    endtime_text: string | number;
    type_text: string;
    usetype_text: string;
    coupon_rule: {
      name: string;
      code: string;
    };
  }

  // Get site configuration information
  const getSiteConfig = () => {
    try {
      if (typeof window !== 'undefined') {
        const siteData = localStorage.getItem('siteData');
        if (siteData) {
          const config = JSON.parse(siteData);
          return config;
        }
      }
    } catch (error) {
      console.error('Failed to parse siteData:', error);
    }
    return null;
  };

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const res = await Api.getConfigList();
        if(res.success){
          setConfig({sendneedcheck: Number(res.data.site.sendneedcheck)})
        }
      } catch (error) {
        console.error('Failed to load config:', error);
      }
    };
    fetchConfig();
  }, []);

  // Get preview package configuration
  useEffect(() => {
    const fetchPreviewPackageConfig = async () => {
      try {
        const res = await Api.getPreviewPackageConfig();
        if (res.success) {
          setPreviewPackageConfig(res.data);
        }
      } catch (error) {
        console.error('Failed to load preview package config:', error);
      }
    };
    fetchPreviewPackageConfig();
  }, []);

  // Initialize order service fee switch status - always enable service fee for previewpackage page
  useEffect(() => {
    // Always enable order service fee for previewpackage page, not affected by configuration
    setOrderServerFeeEnabled(true);
  }, []);

  // Ensure currency information is correctly initialized when component mounts
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Check if there are currency symbol and exchange rate, if not try to set default values from selected currency
      const currencySymbol = localStorage.getItem('currencySymbol');
      const exchangeRate = localStorage.getItem('currentExchangeRate');
      const selectedCurrency = localStorage.getItem('selectedCurrency');

      if (!currencySymbol && selectedCurrency) {
        localStorage.setItem('currencySymbol', selectedCurrency);
      }
      if (!exchangeRate || exchangeRate === 'NaN') {
        localStorage.setItem('currentExchangeRate', '1');
      }
    }
  }, []);

  // Get cart_ids
  const cart_ids = searchParams.get('cart_ids');
  const cart_ids_list = cart_ids ? cart_ids.split(',') : [];

  const discount = 0.00;
  // Use passed international shipping fee
  const freight = shippingFee;
  const insurance = 0.00;
  const coupon = selectedCoupon ? parseFloat(selectedCoupon.money) : 0.00;

  // Calculate additional service fees
  const serviceFee = Object.entries(serverList).reduce((total, [key, service]: [string, any]) => {
    if (key === 'photo') {
      return total + (selectedServices[key] ? parseFloat(service.value) * photoCount : 0);
    }else if(key == 'insurance'){
      return total + (selectedServices[key] ? parseFloat(service.value) * totalProductPrice : 0);
    }
    return total + (selectedServices[key] ? parseFloat(service.value) : 0);
  }, 0);

  // 预演包裹总价只包含附加服务费和预演包裹费，不包含国际运费和服务费
  const previewPackageFee = parseFloat(previewPackageConfig.rehearsal_fee || '0');
  const total = serviceFee + previewPackageFee;
  const payableTotal = Math.max(0, total - coupon - discount);

  // Get coupon list
  const fetchCouponList = async () => {
    try {
      setLoading(true)
      const { data } = await Api.getCouponList()
      setCouponList(data.data || [])
    } catch (error) {
      message.error(dict?.confirm?.order?.coupons?.fetchFail || 'Failed to fetch coupon list')
    } finally {
      setLoading(false)
    }
  }


  const handleSubmitPreviewPackage = async () => {
    // 首先验证条款同意状态
    const agreementCheckbox = document.getElementById('agreement') as HTMLInputElement;
    if (!agreementCheckbox || !agreementCheckbox.checked) {
      message.error(dict?.confirm?.order?.summary?.agreeTermsFirst || '请先阅读并同意服务条款');
      return;
    }

    setSubmitting(true);

    try {
      const addressId = searchParams.get('address_id');
      const templateId = searchParams.get('template_id');

      if (!addressId) {
        message.error(dict?.confirm?.order?.errors?.missingAddress || 'Please select shipping address');
        setSubmitting(false);
        return;
      }

      // 如果没有运输方式ID，使用默认值1（预演包裹会自动选择第一个可用的运输方式）
      const finalTemplateId = templateId || '1';

      // Process service data - convert to string array format
      const server: string[] = [];

      // Process selected services
      Object.entries(selectedServices).forEach(([key, selected]) => {
        if (selected) {
          server.push(key);
        }
      });
      
      const params = {
        order_goods_ids: order_goods_ids,
        address_id: Number(addressId),
        template_id: Number(finalTemplateId),
        server
      }
      
      const res = await Api.toPreviewPackage(params);
      console.log('Preview package API response:', res);

      // 修复判断逻辑：@catchError装饰器会将成功响应包装为{success: true, data: 实际数据}
      if (res.success) {
        message.success(dict?.confirm?.order?.errors?.previewSubmitSuccess || 'Preview package submitted successfully');
        // Process subsequent logic based on returned data
        console.log('Preview package response data:', res.data);

        // 处理返回的数据：res.data可能是数组[68]或对象{id: 68}或直接是数字68
        let sendorderId;
        if (Array.isArray(res.data) && res.data.length > 0) {
          sendorderId = res.data[0]; // 如果data是数组，取第一个元素
        } else if (typeof res.data === 'object' && res.data !== null) {
          sendorderId = res.data.sendorder_id || res.data.id || res.data;
        } else {
          sendorderId = res.data; // 如果data直接是数字
        }

        // Get current language parameter
        const currentPath = window.location.pathname;
        const lngMatch = currentPath.match(/^\/([^\/]+)\//);
        const lng = lngMatch ? lngMatch[1] : 'zh-cn';
        router.push(`/${lng}/pay?order_id=${sendorderId}&type=packageview`);
        window.localStorage.removeItem(localkey);
      } else {
        message.error(res.msg || dict?.confirm?.order?.errors?.previewSubmitFail || 'Failed to submit preview package!');
        setSubmitting(false);
      }
    } catch (error) {
      console.error(dict?.confirm?.order?.errors?.previewSubmitError || 'Error submitting preview package', error);
      message.error(dict?.confirm?.order?.errors?.previewSubmitError || 'Error submitting preview package');
      setSubmitting(false);
    }
  }

  // Get additional service list
  const fetchServerList = async () => {
    try {
      const res = await Api.getServerList('sendorder')
      if (res.success) {
        setServerList(res.data)
      }
    } catch (error) {
      message.error(dict?.confirm?.order?.errors?.fetchServerListFail || 'Failed to fetch additional services')
    }
  }

  useEffect(() => {
    fetchServerList()
  }, [])

  // Handle service selection
  const handleServiceSelect = (key: string) => {
    if (key === 'photo') {
      setSelectedServices(prev => ({
        ...prev,
        [key]: !prev[key]
      }));
      if (!selectedServices[key]) {
        setPhotoCount(1);
        setPhotoRemarks(['']);
      } else {
        setPhotoCount(0);
        setPhotoRemarks(['']);
      }
    } else {
      setSelectedServices(prev => ({
        ...prev,
        [key]: !prev[key]
      }));
    }
  };

  const handlePhotoCountChange = (count: number) => {
    setPhotoCount(count);
    setPhotoRemarks(prev => {
      const newRemarks = [...prev];
      if (count > prev.length) {
        // If adding photos, add empty remarks
        newRemarks.push(...Array(count - prev.length).fill(''));
      } else {
        // If reducing photos, truncate array
        newRemarks.length = count;
      }
      return newRemarks;
    });
  };

  const handlePhotoRemarkChange = (index: number, remark: string) => {
    const newRemarks = [...photoRemarks];
    newRemarks[index] = remark;
    setPhotoRemarks(newRemarks);
  };

  return (
    <div className="bg-white p-4 rounded-lg sticky top-40">
      <h2 className="text-lg font-medium mb-4">{dict?.confirm?.order?.summary?.title || '订单摘要'}</h2>

      <div className="space-y-3">
        {/* 预演包裹页面不显示国际运费和服务费，这些只在提交运单中显示 */}
        {previewPackageFee > 0 && (
          <div className="flex justify-between items-center">
            <span>{dict.confirm.order.summary.previewPackageFee}</span>
            <span>{formatCurrency(previewPackageFee).formatValue}</span>
          </div>
        )}
        {coupon > 0 && (
          <div className="flex justify-between items-center text-[var(--base-color)]">
            <span>{dict.confirm.order.summary.coupon}</span>
            <span>- {formatCurrency(coupon).formatValue}</span>
          </div>
        )}

        {/* Additional service list */}
        <div className="py-2 border-t border-gray-100">
          <div className="text-sm font-medium mb-2">{dict.confirm.order.summary.extraService}</div>
          <div className="space-y-2">
            {Object.entries(serverList).map(([key, service]: [string, any]) => (
              <div 
                key={key} 
                className="flex justify-between items-center text-sm cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors"
                onClick={() => handleServiceSelect(key)}
              >
                <div className="flex items-center gap-2">
                  <Checkbox 
                    checked={selectedServices[key] || false}
                  />
                  <span>{dict.confirm.server[key]}</span>
                </div>
                <span>{formatCurrency(parseFloat(service.value)).formatValue}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="mt-4 pt-4 border-t border-gray-100">
        <div className="flex justify-between items-center">
          <div className="text-base font-medium">{dict.confirm.order.summary.totalFee}</div>
          <div className="text-xl font-bold">{formatCurrency(total).formatValue}</div>
        </div>
        {/* 只在有附加服务费用时显示提示，避免重复显示预演包裹费用信息 */}
        {serviceFee > 0 && (
          <div className="text-xs text-gray-400">
            <span>{dict?.confirm?.order?.summary?.includeAdditionalServiceFee || '包含附加服务费用'}</span>
          </div>
        )}
      </div>

      {/* Service agreement and links */}
      <div className="flex flex-wrap gap-2 mt-3 text-xs text-gray-500">
        <a href="#" className="text-gray-500">{dict.confirm.order.summary.returnPolicy}</a>
        <a href="#" className="text-gray-500">{dict.confirm.order.summary.terms}</a>
        <a href="#" className="text-gray-500">{dict.confirm.order.summary.privacy}</a>
        <a href="#" className="text-gray-500">{dict.confirm.order.summary.faq}</a>
      </div>
      
      {/* Submit preview package area */}
      <div className="mt-4">
        <div className="flex items-center mb-3">
          <Checkbox id="agreement" className="mr-2">
            <span className="text-sm">{dict.confirm.order.summary.agreement}</span>
          </Checkbox>
        </div>
        <Button
          className="w-full mb-5 bg-[var(--base-color)] text-white py-3 rounded-md font-medium hover:bg-[var(--base-color-hover)] transition-colors disabled:opacity-70"
          onClick={handleSubmitPreviewPackage}
          loading={submitting}
          size="large"
          variant="solid"
          color='primary'
        >
          {dict.confirm.order.summary.submitPreviewPackage}
        </Button>
        <div className="bg-gray-100 p-3 rounded-md mb-4">
          <div className="text-sm font-medium">{dict.confirm.order.summary.tips.title}</div>
          <div className="text-xs text-gray-500">{dict.confirm.order.summary.tips.content}</div>
        </div>
      </div>
    </div>
  )
}
