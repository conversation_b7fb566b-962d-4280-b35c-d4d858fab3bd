'use client';

import { useEffect } from 'react';

export default function GlobalErrorHandler() {
  useEffect(() => {
    // Handle unhandled JavaScript errors
    const handleError = (event: ErrorEvent) => {
      console.error('Unhandled JavaScript error:', event.error);

      // Check if this is the specific destructuring error we're trying to fix
      if (event.error && event.error.message && event.error.message.includes("Cannot destructure property 'auth' of 'e' as it is undefined")) {
        // Try to clear potentially corrupted state
        try {
          localStorage.removeItem('info');
          localStorage.removeItem('siteData');
          localStorage.removeItem('exchangeRates');
          localStorage.removeItem('localCartItems');

          // Prevent the error from propagating
          event.preventDefault();

          // Try to reload the page after clearing data
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } catch (clearError) {
          console.error('Failed to clear localStorage:', clearError);
        }
      }
    };

    // Handle unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason);
      
      // Check if this is the specific destructuring error we're trying to fix
      if (event.reason && event.reason.message && event.reason.message.includes("Cannot destructure property 'auth' of 'e' as it is undefined")) {
        console.error('🔍 Detected auth destructuring error in promise rejection:', event.reason);
        
        // Try to clear potentially corrupted state
        try {
          localStorage.removeItem('info');
          localStorage.removeItem('siteData');
          localStorage.removeItem('exchangeRates');
          localStorage.removeItem('localCartItems');
          console.log('🧹 Cleared potentially corrupted localStorage data');
          
          // Prevent the error from propagating
          event.preventDefault();
          
          // Try to reload the page after clearing data
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } catch (clearError) {
          console.error('Failed to clear localStorage:', clearError);
        }
      }
    };

    // Add event listeners
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    // Cleanup function
    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  // This component doesn't render anything
  return null;
}
