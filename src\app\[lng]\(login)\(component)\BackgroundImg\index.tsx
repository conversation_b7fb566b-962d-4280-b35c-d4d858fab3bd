"use client";

import Image from "next/image";
import React, { useState, useEffect } from 'react'
import { Api } from '@/request/api'
import { prepareImageForNextJs } from '@/utils/imageUtils'
export default function BackgroundImg() {
  const [loginBgImage, setLoginBgImage] = useState("");
  useEffect(() => {
    // 登录注册的背景图
    async function getBgImage() {
      let bg = {
        mark: "login_register_01",
        num: 1,
      };
      const { success, data } = await Api.getAdList(bg);
      const bgImage =
        success && data?.data?.length
          ? data.data[0].lang[0].imageurl
          : "/images/login_bg.png";
      setLoginBgImage(bgImage);
    }
    getBgImage();
  }, []);
  return (
    <div>
      {loginBgImage && (
        <Image
          src={prepareImageForNextJs(loginBgImage)}
          alt="Login background"
          fill
          style={{ objectFit: "cover" }}
          draggable={false}
          priority
        />
      )}
    </div>
  );
}
