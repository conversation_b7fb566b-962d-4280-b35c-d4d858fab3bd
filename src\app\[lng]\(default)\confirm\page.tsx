import StepsComponent from '@/components/Steps'
import OrderItem from './(component)/OrderItem'
import OrderSummary from './(component)/OrderSummary'
import { getServerData, checkServerPluginStatus } from '@/request/server';
import { getDictionary } from '@/dictionaries';
import { Locale } from '@/config';
interface CartItem {
    id: number;
    mall_goods_id: number;
    goods_id: string;
    goodsname: string;
    goodsimg: string;
    goodsprice: string;
    goodsnum: number;
    skuname: string;
    [key: string]: any;
}
interface ProductListResponse {
    cartlist: CartItem[];
    serverfee: number; 
    sendmoney: number; 
}
export default async function ConfirmPage({
    params,
    searchParams
}: {
    params: Promise<{ lng: Locale }>,
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
    const { lng } = await params;
    const dict = await getDictionary(lng);
    const resolvedSearchParams = await searchParams;
    let cart_ids = resolvedSearchParams.cart_ids;
    const onePayOrder = resolvedSearchParams.onepayorder === '1';
    const itemDataParam = resolvedSearchParams.itemData as string;



    const isTp6 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '6';

    // 使用新的服务端插件检查方法
    const isOpen = await checkServerPluginStatus('couponrule');

    // 初始化默认数据结构
    let cart_list: ProductListResponse = {
        cartlist: [],
        serverfee: 0,
        sendmoney: 0
    };

    // 如果有cart_ids，尝试从API获取购物车数据
    if (cart_ids) {
        try {
            cart_list = await getServerData<ProductListResponse>(isTp6 ? '/web/goods/cart/list' : '/api/cart/getList', 'POST', {cart_ids});
        } catch (error) {
            console.error('Failed to fetch cart data:', error);
        }
    }

    // 如果有itemData参数，处理商品数据
    if (itemDataParam && itemDataParam.trim() !== '') {
        try {
            // 先尝试解码URL参数
            let decodedParam;
            try {
                decodedParam = decodeURIComponent(itemDataParam);
            } catch (decodeError) {
                console.warn('Failed to decode itemData URL parameter, trying direct parse:', decodeError);
                decodedParam = itemDataParam;
            }

            // 然后尝试解析JSON
            const itemData = JSON.parse(decodedParam);

            // 检查是否是从购物车来的数据（包含多个商品）
            if (itemData.fromCart && itemData.cartItems && Array.isArray(itemData.cartItems)) {
                // 从购物车来的多个商品数据
                if (!cart_list.cartlist || cart_list.cartlist.length === 0) {
                    const mockCartItems: CartItem[] = itemData.cartItems.map((item: any, index: number) => ({
                        id: item.id || (index + 1),
                        mall_goods_id: 0,
                        goods_id: String(item.id || 0),
                        goodsname: item.goodsname || '',
                        goodsimg: item.goodsimg || '',
                        goodsprice: String(item.goodsprice || 0),
                        goodsnum: item.goodsnum || 1,
                        skuname: item.skuname || '',
                        goodsurl: item.itemurl || '',
                        goodsseller: item.goodsseller || '',
                        goodsweight: item.goodsweight || 0,
                        goodsvolume: item.goodsvolume || 0,
                        goodsremark: item.goodsremark || '',
                        originalprice: item.originalprice || 0,
                        goodssite: item.goodssite || '',
                        sku_id: item.sku_id || '',
                        goodssn: item.goodssn || item.goods_sn || item.sn || item.num_iid || ''
                    }));

                    cart_list = {
                        cartlist: mockCartItems,
                        serverfee: 0,
                        sendmoney: 0 // 购物车的运费会在后续计算
                    };
                }
            } else if (!cart_list.cartlist || cart_list.cartlist.length === 0) {
                // 从详情页来的单个商品数据（保持原有逻辑）
                const mockCartItem: CartItem = {
                    id: 1, // 临时ID
                    mall_goods_id: 0,
                    goods_id: '0',
                    goodsname: itemData.goodsname || '',
                    goodsimg: itemData.goodsimg || '',
                    goodsprice: String(itemData.goodsprice || 0),
                    goodsnum: itemData.goodsnum || 1,
                    skuname: itemData.skuname || '',
                    goodsurl: itemData.goodsurl || '',
                    goodsseller: itemData.goodsseller || '',
                    goodsweight: itemData.goodsweight || 0,
                    goodsvolume: itemData.goodsvolume || 0,
                    goodsremark: itemData.goodsremark || '',
                    originalprice: itemData.originalprice || 0,
                    goodssn: itemData.goodssn || itemData.goods_sn || itemData.sn || itemData.num_iid || ''
                };

                cart_list = {
                    cartlist: [mockCartItem],
                    serverfee: 0,
                    sendmoney: itemData.sendprice || 0 // 使用商品的运费信息
                };
            }
        } catch (error) {
            console.error('Failed to parse itemData from URL:', error);
            console.log('Raw itemDataParam:', itemDataParam);
            console.log('itemDataParam length:', itemDataParam?.length);
        }
    }

    // 确保cart_list有正确的结构
    if (!cart_list) {
        cart_list = {
            cartlist: [],
            serverfee: 0,
            sendmoney: 0
        };
    }
   
    return (
        <div className="min-h-screen pb-6">
            <div className="max-w-[1200px] mx-auto p-5">
                <StepsComponent current={1} labelPlacement="vertical" dict={dict} />
                {/* 使用flex布局创建左右分栏 */}
                <div className="flex flex-col md:flex-row gap-6 pt-10">
                    <div className="flex-1">
                        <div className="mb-4">
                            <h2 className="text-lg font-medium mb-3">{dict.confirm.order.productInfo}</h2>
                            <div className="bg-white p-4 rounded-lg">
                                <OrderItem products={cart_list.cartlist} dict={dict} />
                            </div>
                        </div>
                    </div>
                    <div className="md:w-[380px]">
                        <OrderSummary couponIsOpen={isOpen} dict={dict} products={cart_list} onePayOrder={onePayOrder} />
                    </div>
                </div>
            </div>
        </div>
    )
}
