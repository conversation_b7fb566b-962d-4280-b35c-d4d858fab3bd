'use client';

import { useMemo, useCallback } from 'react';
import Image from 'next/image';
import useEmblaCarousel from 'embla-carousel-react';
import Autoplay from 'embla-carousel-autoplay';
import { ShoppingTimeProduct } from "@/types/product";
import { prepareImageForNextJs } from '@/utils/imageUtils';
import { formatCurrency } from '@/utils/currency';
import styles from './ShoppingTimeCarousel.module.css';

interface ShoppingTimeCarouselProps {
  products: ShoppingTimeProduct[];
  dict: any;
}

interface UserGroup {
  user: {
    username: string;
    nickname: string;
  };
  products: ShoppingTimeProduct[];
  latestTime: string;
  totalItems: number;
}

export default function OptimizedEmblaCarousel({ products, dict }: ShoppingTimeCarouselProps) {
  // 按用户nickname进行分组聚合商品数据
  const userGroups = useMemo(() => {
    const groupMap = new Map<string, UserGroup>();

    products.forEach(product => {
      // 优先使用nickname作为分组键，如果没有nickname则使用username，最后使用user_id
      const groupKey = product.user?.nickname || product.user?.username || `user_${product.user_id}` || 'unknown';

      if (groupMap.has(groupKey)) {
        const group = groupMap.get(groupKey)!;
        group.products.push(product);
        group.totalItems += product.goodsnum;
        // 更新最新时间
        if (new Date(product.createtime) > new Date(group.latestTime)) {
          group.latestTime = product.createtime;
        }
      } else {
        // 创建新的用户组
        groupMap.set(groupKey, {
          user: product.user,
          products: [product],
          latestTime: product.createtime,
          totalItems: product.goodsnum
        });
      }
    });

    // 按最新购买时间排序，最新的在前面
    return Array.from(groupMap.values()).sort((a, b) =>
      new Date(b.latestTime).getTime() - new Date(a.latestTime).getTime()
    );
  }, [products]);

  // 将用户组展开为单个商品，每个商品最多显示3个，并标记用户信息
  const expandedProducts = useMemo(() => {
    const result: Array<{ 
      product: ShoppingTimeProduct; 
      user: UserGroup['user']; 
      isFirstInGroup: boolean;
      totalProductsInGroup: number;
      indexInGroup: number;
    }> = [];
    
    userGroups.forEach((userGroup) => {
      // 每个用户最多显示3个商品
      const productsToShow = userGroup.products.slice(0, 3);
      
      productsToShow.forEach((product, index) => {
        result.push({
          product,
          user: userGroup.user,
          isFirstInGroup: index === 0,
          totalProductsInGroup: userGroup.products.length,
          indexInGroup: index
        });
      });
    });

    return result;
  }, [userGroups]);

  // Embla Carousel 配置 - 支持无缝无限轮播和拖拽
  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop: true,
      align: 'start',
      skipSnaps: false,
      dragFree: true, // 启用自由拖拽，支持动量滚动
      containScroll: false, // 禁用容器滚动限制，实现真正的无限滚动
      slidesToScroll: 'auto', // 自动计算滚动数量
      duration: 25, // 较快的滚动速度
    },
    [
      Autoplay({
        delay: 3000, // 3秒自动播放
        stopOnInteraction: false, // 交互后继续自动播放
        stopOnMouseEnter: true, // 鼠标悬停时暂停
        stopOnFocusIn: true, // 获得焦点时暂停
      })
    ]
  );

  // 导航按钮处理
  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  // 渲染单个商品卡片，与ProductCard完全一致的尺寸
  const renderProductCard = (item: { 
    product: ShoppingTimeProduct; 
    user: UserGroup['user']; 
    isFirstInGroup: boolean;
    totalProductsInGroup: number;
    indexInGroup: number;
  }, index: number) => {
    const { product, user, isFirstInGroup, totalProductsInGroup, indexInGroup } = item;
    const titleLength = product.goodsname?.length || 0;
    const titleLengthClass = titleLength <= 10 ? 'short' : titleLength <= 20 ? 'medium' : 'long';
    const detailUrl = product.goodsurl
      ? `/detail/${product.goodssite || 'taobao'}/?url=${encodeURIComponent(product.goodsurl)}`
      : '#';

    return (
      <div
        key={`product-${product.id || product.goodsimg || index}`}
        className="embla__slide flex-shrink-0"
        style={{ flex: '0 0 auto', minWidth: '288px', maxWidth: '288px' }} // 与为你推荐保持一致的宽度
      >
        <div className="relative mr-4">
          {/* 用户信息标签 - 仅在该用户的第一个商品显示 */}
          {isFirstInGroup && (
            <div className="absolute -top-3 left-0 right-0 z-20 flex justify-center">
              <div className="bg-gradient-to-r from-orange-400 to-orange-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-white/30 rounded-full flex items-center justify-center text-xs font-bold">
                    {(user.nickname || user.username || 'U').charAt(0).toUpperCase()}
                  </div>
                  <span className="truncate max-w-24 text-xs">
                    {user.nickname || user.username}
                  </span>
                  {totalProductsInGroup > 3 && (
                    <span className="text-xs opacity-80">
                      +{totalProductsInGroup - 3}
                    </span>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 商品卡片 - 与ProductCard完全一致的样式 */}
          <a
            href={detailUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="custom-card flex flex-col h-full relative group block bg-white rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-200 border border-gray-100"
            data-title-length={titleLengthClass}
            style={{ marginTop: isFirstInGroup ? '16px' : '0' }} // 为用户标签留出空间
          >
            {/* 商品图片 - 与ProductCard保持一致的正方形比例 */}
            <div className="relative aspect-square bg-white">
              <Image
                src={prepareImageForNextJs(product.goodsimg)}
                alt={product.goodsname || '商品图片'}
                fill
                sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
                className="object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/images/default.jpg';
                }}
              />
              {/* 推荐标签 - 与ProductCard保持一致的样式 */}
              {product.recommend === 1 && (
                <div style={{
                  position: 'absolute',
                  top: '0',
                  right: '0',
                  zIndex: 49,
                  width: '50px',
                  height: '50px',
                  overflow: 'hidden'
                }}>
                  {/* 三角形背景 */}
                  <div style={{
                    position: 'absolute',
                    top: '0',
                    right: '0',
                    width: '0',
                    height: '0',
                    borderTop: `50px solid var(--base-color, #ff6b6b)`,
                    borderLeft: '50px solid transparent'
                  }}></div>
                  {/* 文字 */}
                  <div style={{
                    position: 'absolute',
                    top: '8px',
                    right: '8px',
                    color: 'white',
                    fontSize: '10px',
                    fontWeight: '700',
                    textShadow: '0 1px 2px rgba(0,0,0,0.4)',
                    letterSpacing: '0.5px',
                    transform: 'rotate(45deg)',
                    transformOrigin: 'center',
                    width: '30px',
                    textAlign: 'center'
                  }}>
                    推荐
                  </div>
                </div>
              )}
            </div>
            {/* 商品信息 - 与ProductCard保持一致的样式 */}
            <div className="p-3 flex-1 flex flex-col">
              <h3 className="text-md text-gray-800 line-clamp-2 mb-2 group-hover:text-[#FF6B00] transition-colors custom-h-48px">
                {product.goodsname}
              </h3>
              <div className="flex items-center justify-between mt-auto">
                <span className="text-[#FF6B00] font-medium">
                  <span className='text-sm sm:text-base md:text-lg lg:text-xl font-bold'>
                    {formatCurrency(Number(product.goodsprice)).formatValue}
                  </span>
                </span>
              </div>
            </div>
          </a>
        </div>
      </div>
    );
  };

  return (
    <div className="relative w-full group px-4">
      {expandedProducts.length > 0 ? (
        <>
          <div className="embla overflow-hidden pt-6" ref={emblaRef}>
            <div className="embla__container flex">
              {expandedProducts.map((item, index) => renderProductCard(item, index))}
            </div>
          </div>

          {/* 导航按钮 - 仅在hover时显示 */}
          <button
            className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-gray-700 hover:text-orange-600 p-3 rounded-full shadow-lg transition-all duration-300 z-30 border border-gray-200 opacity-0 group-hover:opacity-100"
            onClick={scrollPrev}
            aria-label="上一个"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-gray-700 hover:text-orange-600 p-3 rounded-full shadow-lg transition-all duration-300 z-30 border border-gray-200 opacity-0 group-hover:opacity-100"
            onClick={scrollNext}
            aria-label="下一个"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      ) : (
        <div className="text-center text-gray-500 py-12">
          {dict?.home?.emptyList || '列表为空'}
        </div>
      )}
    </div>
  );
}
