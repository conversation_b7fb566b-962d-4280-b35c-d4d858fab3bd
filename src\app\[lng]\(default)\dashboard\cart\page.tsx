'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import StepsComponent from '@/components/Steps';
import { Api } from '@/request/api';
import { useParams, useRouter } from 'next/navigation';
import Loading from '@/components/Loading';
import Button from '@/components/Button';
import Toast from '@/components/Toast';
import { fixedUrl } from '@/utils';
import Link from 'next/link';
import { getDictionary } from "@/dictionaries";
import { useCartStore } from '@/store/cartStore';
import GuessYouLike from '@/components/GuessYouLike';
import Pagination from '@/components/Pagination';
import { useErrorHandler } from '@/utils/errorHandler';
const formatPrice = (price: number | string): string => {
  return Number(price).toFixed(2);
};


interface GoodsList {
  id: number;
  goodsname: string;
  goodsimg: string;
  goodsprice: string;
  goodsnum: number;
  sku_id: string;
  skuname: string;
  goodsseller: string;
  checked?: boolean;
  itemurl: string;
  goodsremark?: string;
  goodssite?: string; // 商品来源：taobao, 1688, jd等
}

interface ShopItem {
  goodsmoney: number;
  sendmoney: number;
  totalmoney: number;
  goodsseller: string;
  sellerurl: string;
  goodslist: GoodsList[];
}

interface CartData {
  subtotalmoney: number;
  totalmoney: number;
  totalweight: number;
  serverfee: number;
  sendmoney: number;
  goodsmoney: number;
  totalnum: number;
  shoplist: ShopItem[];
  cartlist: GoodsList[];
}

const EmptyCart = ({dict}:any) => (
  <div className="text-center py-10">
    <div className="w-32 h-32 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
      <svg className="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
      </svg>
    </div>
    <p className="text-gray-500 mt-4">{dict.dashboard.cart.empty}</p>
    <Button className="mt-4 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors" onClick={() => {
      window.location.href = '/';
    }}>
      {dict.dashboard.cart.actions.goShopping}
    </Button>
  </div>
);

const CartSummary = ({ data, selectedItems, onSubmitOrder, submitting,dict }: { 
  data: CartData; 
  selectedItems: GoodsList[]; 
  onSubmitOrder: () => void;
  submitting: boolean;
  dict: any;
}) => {
  // 计算选中商品的零售总价
  const retailTotal = selectedItems.reduce((sum, item) => sum + Number(item.goodsprice) * item.goodsnum, 0);
  // 计算最终价格
  const finalTotal = retailTotal;

  return (
    <div className="w-2/5 bg-white p-6 rounded-lg shadow-sm h-fit">
      <h3 className="text-lg font-medium mb-1">{dict.dashboard.cart.summary.title}</h3>
      <p className="text-gray-500 text-sm mb-6">{dict.dashboard.cart.summary.tip}</p>
      
      <div className="space-y-4">
        {/* <div className="flex justify-between text-gray-600">
          <span>{dict.dashboard.cart.summary.retailPrice}:</span>
          <span>¥{formatPrice(retailTotal)}</span>
        </div> */}
        <div className="flex justify-between text-gray-600 pt-4 border-t">
          <span>{dict.dashboard.cart.summary.total}:</span>
          <span className="text-xl font-medium text-[#FF6000]">¥{formatPrice(finalTotal)}</span>
        </div>
        <div className="text-right text-gray-500 text-xs">{dict.dashboard.cart.summary.excludeShipping}</div>
      </div>
      <Button 
        className={`w-full mt-6 py-3 rounded-lg transition-colors ${
          selectedItems.length > 0 
            ? 'bg-[#FF6000] hover:bg-[#FF7000] text-white' 
            : 'bg-gray-200 text-gray-500 cursor-not-allowed'
        }`}
        disabled={selectedItems.length === 0}
        onClick={onSubmitOrder}
        loading={submitting}
        size="large"
        variant="solid"
        color='primary'
      >
        {dict.dashboard.cart.actions.submit}
      </Button>
      
      <div className="mt-4 p-4 bg-[#FFF7F5] rounded-lg text-xs text-gray-600 leading-relaxed">
        <div className="font-medium mb-1">{dict.dashboard.cart.hint.title}</div>
        {dict.dashboard.cart.hint.content}
        
        {/* <a href="/shipping" className="text-[#FF6000] hover:underline">{dict.dashboard.cart.hint.viewShipping}</a>。 */}
      </div>
    </div>
  );
};

const CartItem = ({ 
  item, 
  onToggleSelect, 
  onUpdateQuantity, 
  onRemove,
  onUpdateRemark,
  lng,
  dict
}: { 
  item: GoodsList; 
  onToggleSelect: (id: number) => void;
  onUpdateQuantity: (id: number, quantity: number) => void;
  onRemove: (id: number) => void;
  onUpdateRemark: (id: number, remark: string) => void;
  lng: string | string[];
  dict: any;
}) => {
  const [isEditingRemark, setIsEditingRemark] = useState(false);
  const [remarkText, setRemarkText] = useState(item.goodsremark || '');

  // 检查是否为1688商品，1688商品不允许修改数量
  const is1688Product = item.goodssite === '1688';

  const handleRemarkSubmit = () => {
    // 限制字符数在255以内
    const trimmedText = remarkText.slice(0, 255);
    onUpdateRemark(item.id, trimmedText);
    setIsEditingRemark(false);
  };

  const getmallid = (url: string) => {
   
    url = decodeURIComponent(url);
    console.log(url)
    const match = url.match(/tid=([^&]+)/);
    if (match && match[1]) {
      return match[1];
    }
    return '';
  };

  return (
    <div className="flex items-center p-4 hover:bg-gray-50 transition-colors">
      {/* 选择框 */}
      <div className="w-10 flex-shrink-0">
        <input
          type="checkbox"
          checked={item.checked}
          onChange={() => onToggleSelect(item.id)}
          className="w-4 h-4 text-[#FF6000] border-gray-300 rounded focus:ring-[#FF6000]"
        />
      </div>

      {/* 商品信息列 */}
      <div className="w-[40%] flex gap-4 pr-4">
        <div className="w-24 h-24 relative flex-shrink-0 bg-gray-50 rounded">
          <Image 
            src={fixedUrl(item.goodsimg)} 
            alt={item.goodsname}
            fill
            className="object-contain p-2"
          />
        </div>
        <div className="flex flex-col justify-center min-w-0">
          {item.goodsseller === 'Buy Yourself' || item.goodsseller === 'OneBuy' || item.goodsseller === 'By yourself' ? (
            <h3 className="text-sm line-clamp-2 mb-2">{item.goodsname}</h3>
          ) : (
            <Link href={`/${lng}/detail/${item.goodssite}?id=${item.goodssite=='obmall'?getmallid(item.itemurl):item.itemurl}`} target="_blank"><h3 className="text-sm line-clamp-2 mb-2 hover:text-[#FF6000] cursor-pointer">{item.goodsname}</h3></Link>
          )}
          <p className="text-xs text-gray-500 truncate">{item.skuname}</p>
          
          {/* 备注区域 */}
          <div className="mt-2 flex items-center">
            {isEditingRemark ? (
              <div className="flex flex-col w-full">
                <div className="flex items-center">
                  <span className="text-xs text-gray-500 truncate">{dict.dashboard.cart.table.remarks}：</span>
                  <input
                    type="text"
                    value={remarkText}
                    onChange={(e) => {
                      const value = e.target.value;
                      // 限制输入长度不超过255个字符
                      if (value.length <= 255) {
                        setRemarkText(value);
                      }
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleRemarkSubmit();
                      }
                    }}
                    onBlur={() => {
                      if (remarkText !== (item.goodsremark || '')) {
                        handleRemarkSubmit(); // 如果内容有变化，失去焦点时保存
                      } else {
                        setIsEditingRemark(false); // 内容未变，直接取消编辑状态
                        setRemarkText(item.goodsremark || '');
                      }
                    }}
                    placeholder={dict.dashboard.cart.table.enterRemarks}
                    className="px-2 py-1 text-xs border rounded flex-1"
                    autoFocus
                    maxLength={255}
                  />
                </div>
                <div className="flex justify-end mt-1">
                  <span className="text-xs text-gray-400">
                    {remarkText.length}/255
                  </span>
                </div>
              </div>
            ) : (
              <div 
                className="text-xs flex items-center cursor-pointer w-full"
                onClick={() => setIsEditingRemark(true)}
              >          
                <span className="text-xs text-gray-500 whitespace-nowrap">{dict.dashboard.cart.table.remarks}：</span>
                {item.goodsremark ? (
                  <span title={item.goodsremark} className="text-gray-600 truncate">{item.goodsremark}</span>
                ) : (
                  <span className="text-gray-400 truncate">{dict.dashboard.cart.table.addRemarks}</span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* 单价列 */}
      <div className="w-[15%] text-center">
        <div className="text-[#FF6000] whitespace-nowrap">¥{formatPrice(item.goodsprice)}</div>
      </div>
      
      {/* 数量列 */}
      <div className="w-[15%] flex items-center justify-center flex-col">
        <div className="flex border rounded overflow-hidden mx-auto" style={{ width: '80px' }}>
          <button
            className={`w-6 h-7 flex items-center justify-center border-r ${
              is1688Product
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed'
            }`}
            disabled={item.goodsnum <= 1 || is1688Product}
            onClick={() => !is1688Product && onUpdateQuantity(item.id, item.goodsnum - 1)}
            title={is1688Product ? '1688商品不允许修改数量' : ''}
          >
            -
          </button>
          <div
            className={`w-8 h-7 flex items-center justify-center text-sm ${
              is1688Product ? 'bg-gray-100 text-gray-600' : ''
            }`}
            title={is1688Product ? '1688商品不允许修改数量' : ''}
          >
            {item.goodsnum}
          </div>
          <button
            className={`w-6 h-7 flex items-center justify-center border-l ${
              is1688Product
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'hover:bg-gray-50'
            }`}
            disabled={is1688Product}
            onClick={() => !is1688Product && onUpdateQuantity(item.id, item.goodsnum + 1)}
            title={is1688Product ? '1688商品不允许修改数量' : ''}
          >
            +
          </button>
        </div>
        {is1688Product && (
          <div className="text-xs text-gray-500 mt-1 text-center">
            1688商品
          </div>
        )}
      </div>
      
      {/* 小计列 */}
      <div className="w-[15%] text-center">
        <span className="text-[#FF6000] whitespace-nowrap font-medium">
         ¥{formatPrice(Number(item.goodsprice) * item.goodsnum)}
        </span>
      </div>
      
      {/* 操作列 */}
      <div className="w-[15%] text-center">
        <button 
          className="text-gray-400 hover:text-[#FF6000] transition-colors p-2"
          onClick={() => onRemove(item.id)}
        >
          <svg className="w-5 h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>
    </div>
  );
};

const CartSkeleton = () => (
  <div className="max-w-[1200px] mx-auto p-5">
    <div className="animate-pulse">
      {/* 步骤条骨架屏 */}
      <div className="h-20 bg-gray-200 rounded-lg mb-5"></div>
      
      <div className="flex gap-6">
        {/* 左侧购物车列表骨架屏 */}
        <div className="w-3/5">
          <div className="bg-white rounded-lg p-4">
            {/* 表头骨架屏 */}
            <div className="h-10 bg-gray-200 rounded mb-4"></div>
            
            {/* 商品项骨架屏 */}
            {[1, 2].map((i) => (
              <div key={i} className="flex items-center gap-4 py-4">
                <div className="w-4 h-4 bg-gray-200 rounded"></div>
                <div className="w-24 h-24 bg-gray-200 rounded"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* 右侧结算骨架屏 */}
        <div className="w-2/5">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

const CartPage =  () => {
  const { lng } = useParams();
  const router = useRouter();
  const [cartData, setCartData] = useState<CartData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedItems, setSelectedItems] = useState<Set<number>>(new Set());
  const [submitting, setSubmitting] = useState(false);
  const isTp5 = process.env.NEXT_PUBLIC_BACKEND_TYPE === '5'
  const [dict, setDict] = useState<any>(null); // 添加字典状态
  const { handleError, checkResponseAuth } = useErrorHandler(lng as string);

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5; // 每页5条数据
   const cartStore = useCartStore();
   const { fetchCartCount = () => Promise.resolve() } = cartStore || {};
   // 异步获取字典数据
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const dictionary = await getDictionary(lng as string);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
      }
    };

    fetchDictionary();
  }, [lng]);

  // 获取购物车数据的函数
  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await Api.getCartList();
      if(res.success){
        setCartData(res.data);
      } else {
        // 检查是否是认证错误
        if (!checkResponseAuth(res)) {
          return; // 认证错误已被处理，直接返回
        }
        console.error('Failed to fetch cart data:', res.data);
      }
    } catch (error) {
      handleError(error, '获取购物车数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // 监听登录成功和购物车更新事件
  useEffect(() => {
    const handleLoginSuccess = () => {
      // 登录成功后重新获取购物车数据
      fetchData();
    };

    const handleCartUpdate = () => {
      // 购物车更新后重新获取数据
      fetchData();
    };

    // 添加事件监听器
    window.addEventListener('loginSuccess', handleLoginSuccess);
    window.addEventListener('cartUpdated', handleCartUpdate);

    // 清理事件监听器
    return () => {
      window.removeEventListener('loginSuccess', handleLoginSuccess);
      window.removeEventListener('cartUpdated', handleCartUpdate);
    };
  }, []);

  const handleToggleSelect = (id: number) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const handleToggleSelectAll = (checked: boolean) => {
    if (checked && cartData) {
      setSelectedItems(new Set(cartData.cartlist.map(item => item.id)));
    } else {
      setSelectedItems(new Set());
    }
  };

  const handleToggleSelectShop = (shopSeller: string) => {
    if (!cartData) return;
    
    const shopItems = cartData.cartlist.filter(item => item.goodsseller === shopSeller);
    const shopItemIds = new Set(shopItems.map(item => item.id));
    
    // 检查是否所有商品都已选中
    const allSelected = shopItems.every(item => selectedItems.has(item.id));
    
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (allSelected) {
        // 如果所有商品都已选中，则取消选中
        shopItems.forEach(item => newSet.delete(item.id));
      } else {
        // 否则选中所有商品
        shopItems.forEach(item => newSet.add(item.id));
      }
      return newSet;
    });
  };

  const handleUpdateQuantity = async (id: number, quantity: number) => {
    if (!cartData) return;

    try {
      // 获取当前商品的备注
      const currentItem = cartData.cartlist.find(item => item.id === id);
      if (!currentItem) return;

      // 检查是否为1688商品，如果是则不允许修改数量
      if (currentItem.goodssite === '1688') {
        Toast.error('1688商品不允许修改数量');
        return;
      }

      await Api.updateCartQuantity({
        cart_id: id,
        goodsnum: quantity,
        goodsremark: currentItem.goodsremark || ''
      });

      setCartData(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          cartlist: prev.cartlist.map(item =>
            item.id === id ? { ...item, goodsnum: quantity } : item
          ),
        };
      });
    } catch (error) {
      console.error('Failed to update quantity:', error);
      Toast.error( dict.dashboard.cart.notifications.quantityUpdateFail );
    }
  };

  const handleUpdateRemark = async (id: number, remark: string) => {
    if (!cartData) return;
    
    try {
      // 获取当前商品数量，确保不改变数量
      const currentItem = cartData.cartlist.find(item => item.id === id);
      if (!currentItem) return;
      
      await Api.updateCartQuantity({ cart_id: id, goodsnum: currentItem.goodsnum, goodsremark: remark });
      setCartData(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          cartlist: prev.cartlist.map(item =>
            item.id === id ? { ...item, goodsremark: remark } : item
          ),
        };
      });
      Toast.success(remark ? dict.dashboard.cart.notifications.remarkUpdated : dict.dashboard.cart.notifications.remarkCleared);
    } catch (error) {
      console.error('Failed to update remark:', error);
      Toast.error(dict.dashboard.cart.notifications.remarkUpdateFail);
    }
  };

  const handleRemoveItem = async (id: number) => {
    if (!cartData) return;
    
    try {
      // 使用正确的deleteCart接口删除单个商品
      await Api.deleteCart({ cart_ids:isTp5? [id]:id });
      // 删除后重新获取购物车数据
      setLoading(true);
      const res = await Api.getCartList();
      if(res.success){
        setCartData(res.data);
        // 更新选中状态，移除已不存在的商品ID
        setSelectedItems(prev => {
          const newSet = new Set<number>();
          res.data.cartlist.forEach((item: GoodsList) => {
            if(prev.has(item.id)) {
              newSet.add(item.id);
            }
          });
          return newSet;
        });
        Toast.success(dict.dashboard.cart.notifications.itemDeleted);
        fetchCartCount()
      } else {
        // 检查是否是认证错误
        if (!checkResponseAuth(res)) {
          return; // 认证错误已被处理，直接返回
        }
        console.error('Failed to fetch cart data:', res.data);
      }
      setLoading(false);
    } catch (error) {
      handleError(error, dict.dashboard.cart.notifications.deleteFail);
      setLoading(false);
    }
  };

  // 批量删除选中的商品
  const handleBatchRemove = async () => {
    if (selectedItems.size === 0) {
      Toast.error(dict.dashboard.cart.notifications.noSelection);
      return;
    }

    try {
      setLoading(true);
      // 将Set转为数组传给API
      await Api.deleteCart({ cart_ids:isTp5? Array.from(selectedItems):Array.from(selectedItems).join(',') });

      // 删除后重新获取购物车数据
      const res = await Api.getCartList();
      if(res.success){
        setCartData(res.data);
        // 清空选中状态
        setSelectedItems(new Set());
        Toast.success(dict.dashboard.cart.notifications.batchDeleted);
        fetchCartCount()
      } else {
        // 检查是否是认证错误
        if (!checkResponseAuth(res)) {
          return; // 认证错误已被处理，直接返回
        }
        console.error('Failed to fetch cart data:', res.data);
        Toast.error(dict.dashboard.cart.notifications.fetchFail);
      }
      setLoading(false);
    } catch (error) {
      handleError(error, dict.dashboard.cart.notifications.batchDeleteFail);
      setLoading(false);
    }
  };

  // 清空购物车
  const handleClearCart = async () => {
    if (!cartData || cartData.cartlist.length === 0) {
      Toast.error(dict.dashboard.cart.notifications.alreadyEmpty);
      return;
    }

    try {
      setLoading(true);
      await Api.clearCart();
      
      // 清空后重新获取购物车数据
      const res = await Api.getCartList();
      if(res.success){
        setCartData(res.data);
        // 清空选中状态
        setSelectedItems(new Set());
        Toast.success(dict.dashboard.cart.notifications.cleared);
      }else{
        console.error('Failed to fetch cart data:', res.data);
        Toast.error(dict.dashboard.cart.notifications.fetchFail);
      }
      setLoading(false);
    } catch (error) {
      console.error('Failed to clear cart:', error);
      Toast.error(dict.dashboard.cart.notifications.clearFail);
      setLoading(false);
    }
  };

  // 处理提交订单
  const handleSubmitOrder = () => {
    if (selectedItems.size === 0) {
      Toast.error(dict.dashboard.cart.notifications.selectItems);
      return;
    }
    setSubmitting(true);

    // 获取选中的商品数据
    const selectedItemIds = Array.from(selectedItems);
    const selectedItemsData: GoodsList[] = [];

    // 从购物车数据中找到选中的商品
    if (cartData?.shoplist) {
      cartData.shoplist.forEach(shop => {
        shop.goodslist.forEach(item => {
          if (selectedItemIds.includes(item.id)) {
            selectedItemsData.push(item);
          }
        });
      });
    }

    // 如果有选中的商品数据，构建 itemData 参数
    if (selectedItemsData.length > 0) {
      // 构建商品数据，类似详情页的格式
      const itemData = {
        cartItems: selectedItemsData.map(item => ({
          id: item.id,
          goodsname: item.goodsname,
          goodsimg: item.goodsimg,
          goodsprice: item.goodsprice,
          goodsnum: item.goodsnum,
          skuname: item.skuname,
          goodsseller: item.goodsseller,
          goodssite: item.goodssite,
          goodsremark: item.goodsremark || '',
          itemurl: item.itemurl,
          sku_id: item.sku_id,
          goodssn: (item as any).goodssn || (item as any).goods_sn || (item as any).sn || (item as any).num_iid || '', // 添加商品编号
          goodsweight: (item as any).goodsweight || 0, // 添加重量
          goodsvolume: (item as any).goodsvolume || 0 // 添加体积
        })),
        fromCart: true, // 标识这是从购物车来的
        totalItems: selectedItemsData.length
      };

      // 编码商品数据
      console.log('Cart itemData before encoding:', itemData);
      const jsonString = JSON.stringify(itemData);
      console.log('Cart JSON string:', jsonString);
      const encodedItemData = encodeURIComponent(jsonString);
      console.log('Cart encoded itemData:', encodedItemData);

      // 跳转到确认页面，同时传递 itemData 和 cart_ids
      router.push(`/${lng}/confirm?itemData=${encodedItemData}&cart_ids=${selectedItemIds.join(',')}`);
    } else {
      // 如果没有找到商品数据，使用原来的方式
      console.log('No selected items data found, using cart_ids only');
      router.push(`/${lng}/confirm?cart_ids=${selectedItemIds.join(',')}`);
    }
  };

  // 分页处理函数
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 计算分页数据
  const getPaginatedData = () => {
    if (!cartData) return { paginatedShoplist: [], totalPages: 0 };

    // 计算总页数
    const totalItems = cartData.cartlist.length;
    const totalPages = Math.ceil(totalItems / pageSize);

    // 计算当前页的数据范围
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    // 获取当前页的商品
    const currentPageItems = cartData.cartlist.slice(startIndex, endIndex);

    // 重新组织shoplist，只包含当前页的商品
    const paginatedShoplist = cartData.shoplist.map(shop => {
      const shopItems = currentPageItems.filter(item => item.goodsseller === shop.goodsseller);
      return {
        ...shop,
        goodslist: shopItems
      };
    }).filter(shop => shop.goodslist.length > 0); // 只保留有商品的店铺

    return { paginatedShoplist, totalPages };
  };

  if (loading || !cartData || !dict) {
    return <CartSkeleton />;
  }

  const selectedItemsList = cartData.cartlist.filter(item => selectedItems.has(item.id));
  const { paginatedShoplist, totalPages } = getPaginatedData();

  return (
    <div className="min-h-screen">
      <div className="max-w-[1200px] mx-auto p-5">
      <StepsComponent current={0} labelPlacement="vertical" dict={dict}/>
        
        <div className="flex gap-6 pt-5">
          <div className="w-3/5">
            {cartData.cartlist.length === 0 ? (
              <EmptyCart dict={dict} />
            ) : (
              <div className="bg-white rounded-lg">
                {/* 操作按钮区域 - 移到顶部 */}
                <div className="p-4 border-b border-gray-100 flex justify-between items-center">
                  <div className="flex items-center gap-4">
                    <input
                      type="checkbox"
                      checked={selectedItems.size === cartData.cartlist.length}
                      onChange={(e) => handleToggleSelectAll(e.target.checked)}
                      className="w-4 h-4 text-[#FF6000] border-gray-300 rounded focus:ring-[#FF6000]"
                    />
                    <span className="text-sm text-gray-600">{dict?.dashboard?.cart?.actions?.selectAll || '全选'}</span>
                  </div>
                  <div className="flex gap-3">
                    <button
                      className="px-4 py-2 text-sm text-gray-600 hover:text-[#FF6000] border border-gray-300 rounded-md hover:border-[#FF6000] transition-colors"
                      onClick={handleBatchRemove}
                      disabled={selectedItems.size === 0}
                    >
                     {dict.dashboard.cart.actions.deleteSelected}
                    </button>
                    <button
                      className="px-4 py-2 text-sm text-gray-600 hover:text-[#FF6000] border border-gray-300 rounded-md hover:border-[#FF6000] transition-colors"
                      onClick={handleClearCart}
                    >
                       {dict.dashboard.cart.actions.clearCart}
                    </button>
                  </div>
                </div>

                {/* 表头 */}
                <div className="flex items-center p-4 border-b text-gray-500 text-sm">
                  <div className="w-10 flex-shrink-0">
                    {/* 空白区域，保持对齐 */}
                  </div>
                  <div className="w-[40%] pl-28">{dict?.dashboard?.cart?.table?.productInfo}</div>
                  <div className="w-[15%] text-center">{dict?.dashboard?.cart?.table?.unitPrice}</div>
                  <div className="w-[15%] text-center">{dict?.dashboard?.cart?.table?.quantity}</div>
                  <div className="w-[15%] text-center">{dict?.dashboard?.cart?.table?.subtotal}</div>
                  <div className="w-[15%] text-center">{dict?.dashboard?.cart?.table?.action}</div>
                </div>

                {/* 商品列表 */}
                <div className="divide-y divide-gray-100">
                  {paginatedShoplist.map((shop, shopIndex) => {
                    const shopItems = shop.goodslist;
                    const isShopSelected = shopItems.every(
                      item => selectedItems.has(item.id)
                    );

                    return (
                      <div key={shopIndex}>
                        <div className="p-4 bg-[#F9FAFB] flex items-center gap-2">
                          <div className="w-10 flex-shrink-0">
                            <input
                              type="checkbox"
                              checked={isShopSelected}
                              onChange={() => handleToggleSelectShop(shop.goodsseller)}
                              className="w-4 h-4 text-[#FF6000] border-gray-300 rounded focus:ring-[#FF6000]"
                            />
                          </div>
                          <span className="i-solar:shop-bold-duotone text-[#FF6B00] text-2xl"></span>
                          <a href={shop.sellerurl} target="_blank" rel="noopener noreferrer"
                             className="text-[#FF6000] hover:underline truncate">
                            {shop.goodsseller || 'By yourself'}
                          </a>
                        </div>

                        <div className="divide-y divide-gray-50">
                          {shopItems.map((item, itemIndex) => (
                            <CartItem
                              key={itemIndex}
                              item={{
                                ...item,
                                checked: selectedItems.has(item.id)
                              }}
                              onToggleSelect={handleToggleSelect}
                              onUpdateQuantity={handleUpdateQuantity}
                              onRemove={handleRemoveItem}
                              onUpdateRemark={handleUpdateRemark}
                              lng={lng as string}
                              dict={dict}
                            />
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* 分页组件 */}
                {totalPages > 1 && (
                  <div className="flex justify-center py-4">
                    <Pagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      pageSize={pageSize}
                      onPageChange={handlePageChange}
                      prevText={dict?.dashboard?.cart?.pagination?.prev || '上一页'}
                      nextText={dict?.dashboard?.cart?.pagination?.next || '下一页'}
                    />
                  </div>
                )}
              </div>
            )}

          </div>
          
          <CartSummary 
            data={cartData} 
            selectedItems={selectedItemsList} 
            onSubmitOrder={handleSubmitOrder} 
            submitting={submitting}
            dict={dict}
          />
        </div>

        {/* 猜你喜欢模块 */}
        <GuessYouLike dict={dict} lng={lng as string} />
      </div>
    </div>
  );
};

export default CartPage;
