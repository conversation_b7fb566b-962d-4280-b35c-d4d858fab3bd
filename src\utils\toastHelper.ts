import Toast from '@/components/Toast';

/**
 * 国际化Toast错误信息工具
 */
export class ToastHelper {
  /**
   * 获取当前语言
   */
  static getCurrentLanguage(): string {
    if (typeof window === 'undefined') return 'zh-cn';
    
    const pathSegments = window.location.pathname.split('/');
    const currentLng = pathSegments[1] && ['en', 'zh-cn', 'ja'].includes(pathSegments[1])
      ? pathSegments[1]
      : 'zh-cn';
    
    return currentLng;
  }

  /**
   * 获取国际化错误信息
   */
  static getLocalizedMessage(key: string, lng?: string): string {
    const language = lng || this.getCurrentLanguage();
    
    const messages: Record<string, Record<string, string>> = {
      'zh-cn': {
        'auth_required': '请登录后重试',
        'permission_denied': '权限不足',
        'param_error': '参数错误',
        'system_error': '系统错误，请稍后重试',
        'network_error': '网络错误，请稍后重试',
        'operation_failed': '操作失败，请重试',
        'add_cart_failed': '添加失败',
        'fetch_points_failed': '获取积分记录失败',
        'buy_failed': '购买失败，请重试',
        'fetch_bank_list_failed': '获取银行列表失败',
        'add_to_cart_failed': '添加商品到购物车失败',
        'load_failed': '加载失败',
        'fetch_failed': '获取数据失败'
      },
      'en': {
        'auth_required': 'Please login and try again',
        'permission_denied': 'Permission denied',
        'param_error': 'Parameter error',
        'system_error': 'System error, please try again later',
        'network_error': 'Network error, please try again later',
        'operation_failed': 'Operation failed, please try again',
        'add_cart_failed': 'Failed to add to cart',
        'fetch_points_failed': 'Failed to fetch points',
        'buy_failed': 'Purchase failed, please try again',
        'fetch_bank_list_failed': 'Failed to fetch bank list',
        'add_to_cart_failed': 'Failed to add product to cart',
        'load_failed': 'Loading failed',
        'fetch_failed': 'Failed to fetch data'
      },
      'ja': {
        'auth_required': 'ログイン後に再試行してください',
        'permission_denied': '権限が不足しています',
        'param_error': 'パラメータエラー',
        'system_error': 'システムエラー、後でもう一度お試しください',
        'network_error': 'ネットワークエラー、後でもう一度お試しください',
        'operation_failed': '操作に失敗しました、再試行してください',
        'add_cart_failed': 'カートへの追加に失敗しました',
        'fetch_points_failed': 'ポイント記録の取得に失敗しました',
        'buy_failed': '購入に失敗しました、再試行してください',
        'fetch_bank_list_failed': '銀行リストの取得に失敗しました',
        'add_to_cart_failed': '商品をカートに追加できませんでした',
        'load_failed': '読み込みに失敗しました',
        'fetch_failed': 'データの取得に失敗しました'
      }
    };

    return messages[language]?.[key] || messages['zh-cn'][key] || key;
  }

  /**
   * 显示国际化错误信息
   */
  static showError(key: string, lng?: string, fallbackMessage?: string) {
    const message = this.getLocalizedMessage(key, lng) || fallbackMessage || key;
    Toast.error(message);
  }

  /**
   * 显示国际化成功信息
   */
  static showSuccess(key: string, lng?: string, fallbackMessage?: string) {
    const message = this.getLocalizedMessage(key, lng) || fallbackMessage || key;
    Toast.success(message);
  }

  /**
   * 显示网络错误
   */
  static showNetworkError(lng?: string) {
    this.showError('network_error', lng);
  }

  /**
   * 显示认证错误
   */
  static showAuthError(lng?: string) {
    this.showError('auth_required', lng);
  }

  /**
   * 显示操作失败错误
   */
  static showOperationFailed(lng?: string) {
    this.showError('operation_failed', lng);
  }

  /**
   * 显示加载失败错误
   */
  static showLoadFailed(lng?: string) {
    this.showError('load_failed', lng);
  }

  /**
   * 显示获取数据失败错误
   */
  static showFetchFailed(lng?: string) {
    this.showError('fetch_failed', lng);
  }
}

export default ToastHelper;
