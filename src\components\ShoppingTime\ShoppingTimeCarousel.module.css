/* 购物时报轮播组件样式 */

/* Embla Carousel 基础样式 */
.embla {
  overflow: hidden;
}

.embla__container {
  display: flex;
}

.embla__slide {
  flex: 0 0 auto;
  min-width: 0;
}

/* 商品卡片自适应容器 - 调整尺寸与为你推荐保持一致 (288px) */
.productCard {
  flex-shrink: 0;
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  cursor: pointer;
  transition: box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  min-width: 280px;
  max-width: 300px;
  width: 288px;
  border: 1px solid #e5e7eb;
}

.productCard:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* hover效果现在由Tailwind的group-hover:text-[#FF6B00]处理 */

/* 商品图片区域 - 与为你推荐商品图片大小统一，使用正方形比例 */
.productImage {
  position: relative;
  aspect-ratio: 1 / 1;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* Next.js Image组件样式由className="object-cover"和fill属性控制 */

/* 推荐标签 */
.recommendTag {
  position: absolute;
  top: 2px;
  right: 2px;
  background-color: #f97316;
  color: white;
  font-size: 8px;
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: 600;
}

/* 商品信息区域现在使用Tailwind类：p-3 flex-1 flex flex-col */
/* 商品标题现在使用Tailwind类：text-md text-gray-800 line-clamp-2 mb-2 group-hover:text-[#FF6B00] transition-colors custom-h-48px */
/* 价格信息现在使用Tailwind类：flex items-center justify-between mt-auto + text-[#FF6B00] font-medium + text-sm sm:text-base md:text-lg lg:text-xl font-bold */

/* 商品容器布局 */
.productsContainer {
  overflow: visible;
  width: 100%;
}

.productsList {
  display: flex;
  gap: 6px;
  padding-bottom: 8px;
  width: 100%;
  flex-wrap: wrap;
  justify-content: flex-start;
}

/* 根据商品数量调整布局 */
.productsList.flexWrap {
  flex-wrap: wrap;
}

.productsList.justifyStart {
  justify-content: flex-start;
}

.productsList.justifyCenter {
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .productCard {
    min-width: 90px;
    max-width: 120px;
  }

  .productImage {
    height: 80px;
  }

  .productTitle {
    font-size: 13px;
  }

  .productPrice span {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .productCard {
    min-width: 140px;
    max-width: 160px;
    width: 150px;
  }

  /* 移动端也使用正方形比例，不设置固定高度 */
  .productImage {
    /* 保持正方形比例，移除固定高度 */
  }

  /* 移动端样式现在由Tailwind的响应式类处理 */

  .recommendTag {
    font-size: 7px;
    padding: 1px 3px;
  }
}

/* 自适应宽度计算 - 调整为与为你推荐一致的尺寸 (288px) */
.productCard.adaptive {
  width: 288px;
  min-width: clamp(260px, 25vw, 280px);
  max-width: clamp(280px, 30vw, 300px);
}

/* 自适应样式现在由Tailwind类处理 */

.productPrice.adaptive span {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: clamp(10px, 2.5vw, 12px);
}

/* 动态内容适配 */
.productCard.adaptive .productTitle {
  font-size: clamp(10px, 2.2vw, 12px);
}

/* 容器级别的自适应 */
.productsList {
  min-height: 0;
  align-items: stretch;
}

.productsList > .productCard {
  flex: 0 0 auto;
}

/* 基于内容长度的动态调整 - 调整为与为你推荐一致的尺寸 (288px) */
.productCard[data-title-length="short"] {
  min-width: 280px;
  max-width: 290px;
  width: 285px;
}

.productCard[data-title-length="medium"] {
  min-width: 285px;
  max-width: 295px;
  width: 288px;
}

.productCard[data-title-length="long"] {
  min-width: 288px;
  max-width: 300px;
  width: 295px;
}

/* 用户容器自适应宽度样式 */
.userContainer {
  width: fit-content;
  min-width: 200px;
  max-width: 100%;
  flex-shrink: 0;
}

.userContainer.hasMany {
  min-width: 400px;
}

.userContainer.hasMedium {
  min-width: 300px;
}

.userContainer.hasLess {
  min-width: 200px;
}

/* 用户容器内的商品列表自适应 */
.userContainer .productsContainer {
  width: 100%;
  overflow: visible;
}

.userContainer .productsList {
  width: 100%;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 6px;
}

/* 确保商品卡片在用户容器内合理排列 */
.userContainer .productCard {
  flex: 0 0 auto;
  margin: 0;
}
